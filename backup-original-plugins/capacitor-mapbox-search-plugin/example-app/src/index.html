<!doctype html>
<html lang="en" dir="ltr" class="hydrated">
  <head>
    <meta charset="UTF-8" />
    <title>Example Capacitor App</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
  </head>
  <body>
    <main>
      <h1>Capacitor Test Plugin Project</h1>
      <p>
        This project can be used to test out the functionality of your plugin. Nothing in the
        <em>example-app/</em> folder will be published to npm when using this template, so you can create away!
      </p>
      <label for="echoInput">Echo Text:</label>
      <input type="text" id="echoInput" name="echoInput" value="Hello World!" />
      <button onclick="testEcho()">Click Me!</button>
      <div>
        <label>Show Search Box:</label>
        <button onclick="openSearchBox()">Click Show Search Box!</button>
      </div>
      <div>
        <label>Show Autocomplete:</label>
        <button onclick="testMapboxShow()">Click Show Autocomplete!</button>
      </div>
    </main>

    <script src="./js/example.js" type="module"></script>
  </body>
</html>
