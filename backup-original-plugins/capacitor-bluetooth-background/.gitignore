# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Build outputs
dist/
build/
lib/
*.tgz

# TypeScript
*.tsbuildinfo

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Android specific
android/build/
android/.gradle/
android/local.properties
android/app/build/
android/**/*.dex
android/**/*.jar
android/**/*.class
android/**/build/
android/.transforms/
*.iml
.gradle
/local.properties
/build
/captures
.externalNativeBuild
.cxx

# iOS specific  
ios/build/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/DerivedData/

# Logs
logs
*.log

# Temporary files
*.tmp
*.temp

# Documentation auto-generated files
docs.json
api.md 