import Foundation
import UIKit

@objc public class BluetoothBackground: NSObject {
    
    @objc public func isBackgroundModeEnabled() -> Bool {
        guard let backgroundModes = Bundle.main.object(forInfoDictionaryKey: "UIBackgroundModes") as? [String] else {
            return false
        }
        
        return backgroundModes.contains("bluetooth-central") || 
               backgroundModes.contains("bluetooth-peripheral")
    }
    
    @objc public func echo(_ value: String) -> String {
        return "BluetoothBackground: \(value)"
    }
}