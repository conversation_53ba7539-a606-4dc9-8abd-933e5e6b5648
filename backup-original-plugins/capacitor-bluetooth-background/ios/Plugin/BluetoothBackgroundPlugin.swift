import Foundation
import Capacitor

/**
 * Please read the Capacitor iOS Plugin Development Guide
 * here: https://capacitorjs.com/docs/plugins/ios
 */
@objc(BluetoothBackgroundPlugin)
public class BluetoothBackgroundPlugin: CAPPlugin {
    private let implementation = BluetoothBackground()
    
    @objc func startForegroundService(_ call: CAPPluginCall) {
        // iOS doesn't have foreground services like Android
        // Instead, we rely on background modes in Info.plist
        call.resolve()
    }
    
    @objc func stopForegroundService(_ call: CAPPluginCall) {
        // iOS doesn't have foreground services like Android
        call.resolve()
    }
    
    @objc func requestIgnoreBatteryOptimizations(_ call: CAPPluginCall) {
        // iOS doesn't have battery optimization settings like Android
        // Background app refresh is managed by the system
        call.resolve()
    }
    
    @objc func isServiceRunning(_ call: CA<PERSON>luginCall) {
        // For iOS, we can check if background modes are enabled
        let isRunning = implementation.isBackgroundModeEnabled()
        call.resolve([
            "isRunning": isRunning
        ])
    }
    
    @objc func updateNotification(_ call: CAPPluginCall) {
        // iOS notifications are handled differently
        // This is a no-op for iOS
        call.resolve()
    }
}