package com.smartbicycle.bluetoothbackground;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.os.PowerManager;
import android.content.Context;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;

import com.getcapacitor.JSObject;
import com.getcapacitor.JSArray;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "BluetoothBackground")
public class BluetoothBackgroundPlugin extends Plugin {

    private BluetoothForegroundService serviceInstance;

    @Override
    public void load() {
        super.load();
    }

    @PluginMethod
    public void startForegroundService(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "START");
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                getContext().startForegroundService(serviceIntent);
            } else {
                getContext().startService(serviceIntent);
            }
            
            // 通知状态变化
            notifyServiceStateChanged(true);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to start foreground service: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void stopForegroundService(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "STOP");
            getContext().startService(serviceIntent);
            
            // 通知状态变化
            notifyServiceStateChanged(false);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to stop foreground service: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void requestIgnoreBatteryOptimizations(PluginCall call) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + getContext().getPackageName()));
                getActivity().startActivity(intent);
            }
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to request battery optimization exemption: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void isServiceRunning(PluginCall call) {
        try {
            boolean isRunning = BluetoothForegroundService.isServiceRunning();
            JSObject result = new JSObject();
            result.put("isRunning", isRunning);
            call.resolve(result);
        } catch (Exception e) {
            call.reject("Failed to check service status: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void updateNotification(PluginCall call) {
        try {
            String title = call.getString("title", "智能单车蓝牙连接");
            String message = call.getString("message", "正在保持蓝牙连接...");
            
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "UPDATE_NOTIFICATION");
            serviceIntent.putExtra("title", title);
            serviceIntent.putExtra("message", message);
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to update notification: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void checkDozeMode(PluginCall call) {
        try {
            JSObject result = new JSObject();
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager powerManager = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
                if (powerManager != null) {
                    boolean isInDozeMode = powerManager.isDeviceIdleMode();
                    boolean isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(getContext().getPackageName());
                    
                    result.put("isInDozeMode", isInDozeMode);
                    result.put("isIgnoringBatteryOptimizations", isIgnoringBatteryOptimizations);
                } else {
                    result.put("isInDozeMode", false);
                    result.put("isIgnoringBatteryOptimizations", false);
                }
            } else {
                result.put("isInDozeMode", false);
                result.put("isIgnoringBatteryOptimizations", true); // Android 6.0以下没有Doze模式
            }
            
            call.resolve(result);
        } catch (Exception e) {
            call.reject("Failed to check Doze mode status: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void openBatteryOptimizationSettings(PluginCall call) {
        try {
            Intent intent = new Intent();
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 尝试直接打开应用的电池优化设置页面
                try {
                    intent.setAction(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
                    getActivity().startActivity(intent);
                } catch (Exception e) {
                    // 如果失败，打开通用的电池优化设置页面
                    intent = new Intent(Settings.ACTION_SETTINGS);
                    getActivity().startActivity(intent);
                }
            }
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to open battery optimization settings: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void sendKeepAlive(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "KEEP_ALIVE");
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to send keep alive signal: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getDeviceInfo(PluginCall call) {
        try {
            JSObject result = new JSObject();
            
            result.put("manufacturer", Build.MANUFACTURER);
            result.put("model", Build.MODEL);
            result.put("androidVersion", Build.VERSION.RELEASE);
            result.put("sdkVersion", Build.VERSION.SDK_INT);
            
            // 检查是否为特定厂商的设备（这些厂商通常有更严格的电源管理）
            String manufacturer = Build.MANUFACTURER.toLowerCase();
            boolean isAggressivePowerManagement = manufacturer.contains("huawei") || 
                                                 manufacturer.contains("honor") ||
                                                 manufacturer.contains("xiaomi") ||
                                                 manufacturer.contains("oppo") ||
                                                 manufacturer.contains("vivo") ||
                                                 manufacturer.contains("oneplus") ||
                                                 manufacturer.contains("samsung");
            
            result.put("hasAggressivePowerManagement", isAggressivePowerManagement);
            
            call.resolve(result);
        } catch (Exception e) {
            call.reject("Failed to get device info: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void openAutoStartSettings(PluginCall call) {
        try {
            String manufacturer = Build.MANUFACTURER.toLowerCase();
            Intent intent = new Intent();
            
            // 针对不同厂商打开自启动设置页面
            try {
                if (manufacturer.contains("xiaomi")) {
                    intent.setComponent(new ComponentName("com.miui.securitycenter", 
                        "com.miui.permcenter.autostart.AutoStartManagementActivity"));
                } else if (manufacturer.contains("oppo")) {
                    intent.setComponent(new ComponentName("com.coloros.safecenter", 
                        "com.coloros.safecenter.permission.startup.FakeActivity"));
                } else if (manufacturer.contains("vivo")) {
                    intent.setComponent(new ComponentName("com.vivo.permissionmanager", 
                        "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"));
                } else if (manufacturer.contains("huawei") || manufacturer.contains("honor")) {
                    intent.setComponent(new ComponentName("com.huawei.systemmanager", 
                        "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"));
                } else if (manufacturer.contains("oneplus")) {
                    intent.setComponent(new ComponentName("com.oneplus.security", 
                        "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"));
                } else {
                    // 默认打开应用设置页面
                    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    intent.setData(Uri.parse("package:" + getContext().getPackageName()));
                }
                
                getActivity().startActivity(intent);
                call.resolve();
            } catch (Exception e) {
                // 如果特定厂商设置页面打开失败，尝试通用设置
                intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + getContext().getPackageName()));
                getActivity().startActivity(intent);
                call.resolve();
            }
        } catch (Exception e) {
            call.reject("Failed to open auto start settings: " + e.getMessage(), e);
        }
    }

    // =================== 新增原生蓝牙发送方法 ===================

    @PluginMethod
    public void startNativeBluetoothSending(PluginCall call) {
        try {
            String deviceId = call.getString("deviceId");
            String serviceUUID = call.getString("serviceUUID");
            String characteristicUUID = call.getString("characteristicUUID");
            Integer sendInterval = call.getInt("sendInterval", 106);
            JSArray dataArray = call.getArray("data");
            
            if (deviceId == null || serviceUUID == null || characteristicUUID == null || dataArray == null) {
                call.reject("Missing required parameters");
                return;
            }
            
            // 转换数据数组
            int[] data = new int[dataArray.length()];
            for (int i = 0; i < dataArray.length(); i++) {
                data[i] = dataArray.optInt(i);
            }
            
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "START_BLUETOOTH_SENDING");
            serviceIntent.putExtra("deviceId", deviceId);
            serviceIntent.putExtra("serviceUUID", serviceUUID);
            serviceIntent.putExtra("characteristicUUID", characteristicUUID);
            serviceIntent.putExtra("sendInterval", sendInterval);
            serviceIntent.putExtra("data", data);
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to start native bluetooth sending: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void stopNativeBluetoothSending(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "STOP_BLUETOOTH_SENDING");
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to stop native bluetooth sending: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void updateBluetoothSendData(PluginCall call) {
        try {
            JSArray dataArray = call.getArray("data");
            if (dataArray == null) {
                call.reject("Missing data parameter");
                return;
            }
            
            // 转换数据数组
            int[] data = new int[dataArray.length()];
            for (int i = 0; i < dataArray.length(); i++) {
                data[i] = dataArray.optInt(i);
            }
            
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "UPDATE_BLUETOOTH_DATA");
            serviceIntent.putExtra("data", data);
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to update bluetooth send data: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void isNativeBluetoothSending(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "GET_BLUETOOTH_STATUS");
            
            // 由于这是同步调用，我们需要通过服务的静态方法获取状态
            JSObject result = BluetoothForegroundService.getBluetoothSendingStatus();
            call.resolve(result);
        } catch (Exception e) {
            call.reject("Failed to check bluetooth sending status: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void getBluetoothSendingStats(PluginCall call) {
        try {
            JSObject result = BluetoothForegroundService.getBluetoothSendingStats();
            call.resolve(result);
        } catch (Exception e) {
            call.reject("Failed to get bluetooth sending stats: " + e.getMessage(), e);
        }
    }

    @PluginMethod
    public void reconnectBluetoothDevice(PluginCall call) {
        try {
            Intent serviceIntent = new Intent(getContext(), BluetoothForegroundService.class);
            serviceIntent.putExtra("action", "RECONNECT_BLUETOOTH");
            getContext().startService(serviceIntent);
            
            call.resolve();
        } catch (Exception e) {
            call.reject("Failed to reconnect bluetooth device: " + e.getMessage(), e);
        }
    }

    private void notifyServiceStateChanged(boolean isRunning) {
        JSObject data = new JSObject();
        data.put("isRunning", isRunning);
        notifyListeners("serviceStateChanged", data);
    }

    // 通知蓝牙发送状态变化
    public void notifyBluetoothSendingStateChanged(boolean isActive, long lastSendTime, long sendCount, long errorCount) {
        JSObject data = new JSObject();
        data.put("isActive", isActive);
        data.put("lastSendTime", lastSendTime);
        data.put("sendCount", sendCount);
        data.put("errorCount", errorCount);
        notifyListeners("bluetoothSendingStateChanged", data);
    }

    // 通知蓝牙数据接收
    public void notifyBluetoothDataReceived(int[] data, String deviceId) {
        JSObject eventData = new JSObject();
        JSArray dataArray = new JSArray();
        for (int value : data) {
            dataArray.put(value);
        }
        eventData.put("data", dataArray);
        eventData.put("timestamp", System.currentTimeMillis());
        eventData.put("deviceId", deviceId);
        notifyListeners("bluetoothDataReceived", eventData);
    }

    // 通知蓝牙错误
    public void notifyBluetoothError(String error, int errorCode, String deviceId) {
        JSObject eventData = new JSObject();
        eventData.put("error", error);
        eventData.put("errorCode", errorCode);
        eventData.put("timestamp", System.currentTimeMillis());
        eventData.put("deviceId", deviceId);
        notifyListeners("bluetoothError", eventData);
    }
}