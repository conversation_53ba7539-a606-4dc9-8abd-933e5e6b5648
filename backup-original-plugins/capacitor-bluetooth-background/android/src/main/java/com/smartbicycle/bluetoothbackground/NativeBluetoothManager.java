package com.smartbicycle.bluetoothbackground;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;
import java.util.UUID;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

public class NativeBluetoothManager {
    private static final String TAG = "NativeBluetoothManager";
    
    // 蓝牙相关
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothGatt bluetoothGatt;
    private BluetoothGattCharacteristic targetCharacteristic;
    private Context context;
    
    // 连接参数
    private String deviceId;
    private UUID serviceUUID;
    private UUID characteristicUUID;
    
    // 发送参数
    private int[] sendData;
    private int sendInterval = 106; // 默认106ms
    private AtomicBoolean isSending = new AtomicBoolean(false);
    private AtomicBoolean isConnected = new AtomicBoolean(false);
    
    // 统计信息
    private AtomicLong totalSent = new AtomicLong(0);
    private AtomicLong successCount = new AtomicLong(0);
    private AtomicLong errorCount = new AtomicLong(0);
    private AtomicLong lastSendTime = new AtomicLong(0);
    private String lastError = null;
    
    // 定时器相关
    private ScheduledExecutorService sendExecutor;
    private ScheduledFuture<?> sendTask;
    private HandlerThread bluetoothThread;
    private Handler bluetoothHandler;
    
    // 连接重试
    private static final int MAX_RETRY_COUNT = 5;
    private int retryCount = 0;
    private Handler retryHandler;
    
    // 回调接口
    public interface BluetoothCallback {
        void onConnectionStateChanged(boolean connected);
        void onDataReceived(int[] data);
        void onSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount);
        void onError(String error, int errorCode);
    }
    
    private BluetoothCallback callback;
    
    public NativeBluetoothManager(Context context) {
        this.context = context;
        this.bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        this.retryHandler = new Handler(Looper.getMainLooper());
        
        // 创建专用的蓝牙线程
        bluetoothThread = new HandlerThread("BluetoothThread");
        bluetoothThread.start();
        bluetoothHandler = new Handler(bluetoothThread.getLooper());
        
        Log.d(TAG, "NativeBluetoothManager initialized");
    }
    
    public void setCallback(BluetoothCallback callback) {
        this.callback = callback;
    }
    
    // 启动蓝牙发送服务
    public boolean startBluetoothSending(String deviceId, String serviceUUID, String characteristicUUID, 
                                       int sendInterval, int[] data) {
        try {
            this.deviceId = deviceId;
            this.serviceUUID = UUID.fromString(serviceUUID);
            this.characteristicUUID = UUID.fromString(characteristicUUID);
            this.sendInterval = sendInterval;
            this.sendData = data.clone();
            
            Log.d(TAG, "Starting bluetooth sending - Device: " + deviceId + ", Interval: " + sendInterval + "ms");
            
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                Log.e(TAG, "Bluetooth adapter not available or disabled");
                notifyError("蓝牙适配器不可用或未启用", -1);
                return false;
            }
            
            // 如果已经在发送，先停止
            if (isSending.get()) {
                stopBluetoothSending();
            }
            
            // 连接设备
            connectToDevice();
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start bluetooth sending: " + e.getMessage());
            notifyError("启动蓝牙发送失败: " + e.getMessage(), -2);
            return false;
        }
    }
    
    // 停止蓝牙发送服务
    public void stopBluetoothSending() {
        Log.d(TAG, "Stopping bluetooth sending");
        
        isSending.set(false);
        
        // 停止发送定时器
        if (sendTask != null && !sendTask.isCancelled()) {
            sendTask.cancel(true);
            sendTask = null;
        }
        
        if (sendExecutor != null && !sendExecutor.isShutdown()) {
            sendExecutor.shutdown();
            sendExecutor = null;
        }
        
        // 断开蓝牙连接
        disconnectDevice();
        
        // 通知状态变化
        notifySendingStateChanged(false, lastSendTime.get(), totalSent.get(), errorCount.get());
    }
    
    // 更新发送数据
    public void updateSendData(int[] data) {
        if (data != null) {
            this.sendData = data.clone();
            Log.d(TAG, "Send data updated, length: " + data.length);
        }
    }
    
    // 连接到蓝牙设备
    private void connectToDevice() {
        bluetoothHandler.post(() -> {
            try {
                BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceId);
                if (device == null) {
                    Log.e(TAG, "Device not found: " + deviceId);
                    notifyError("设备未找到: " + deviceId, -3);
                    return;
                }
                
                Log.d(TAG, "Connecting to device: " + device.getName() + " (" + deviceId + ")");
                
                // 如果已经连接，先断开
                if (bluetoothGatt != null) {
                    bluetoothGatt.disconnect();
                    bluetoothGatt.close();
                    bluetoothGatt = null;
                }
                
                // 建立GATT连接
                bluetoothGatt = device.connectGatt(context, false, gattCallback);
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to connect to device: " + e.getMessage());
                notifyError("连接设备失败: " + e.getMessage(), -4);
            }
        });
    }
    
    // 断开蓝牙设备
    private void disconnectDevice() {
        bluetoothHandler.post(() -> {
            if (bluetoothGatt != null) {
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
                bluetoothGatt = null;
            }
            isConnected.set(false);
            targetCharacteristic = null;
            
            if (callback != null) {
                callback.onConnectionStateChanged(false);
            }
            
            Log.d(TAG, "Device disconnected");
        });
    }
    
    // GATT回调
    private final BluetoothGattCallback gattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "Connected to GATT server");
                isConnected.set(true);
                retryCount = 0; // 重置重试计数
                
                // 发现服务
                gatt.discoverServices();
                
                if (callback != null) {
                    callback.onConnectionStateChanged(true);
                }
                
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                Log.d(TAG, "Disconnected from GATT server");
                isConnected.set(false);
                
                if (callback != null) {
                    callback.onConnectionStateChanged(false);
                }
                
                // 如果正在发送且意外断开，尝试重连
                if (isSending.get() && retryCount < MAX_RETRY_COUNT) {
                    retryCount++;
                    Log.w(TAG, "Connection lost, attempting to reconnect (" + retryCount + "/" + MAX_RETRY_COUNT + ")");
                    
                    retryHandler.postDelayed(() -> {
                        if (isSending.get()) {
                            connectToDevice();
                        }
                    }, 2000); // 2秒后重试
                } else if (retryCount >= MAX_RETRY_COUNT) {
                    Log.e(TAG, "Max retry count reached, stopping bluetooth sending");
                    stopBluetoothSending();
                    notifyError("连接失败，已达到最大重试次数", -5);
                }
            }
        }
        
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "Services discovered");
                
                // 查找目标服务和特征
                BluetoothGattService service = gatt.getService(serviceUUID);
                if (service != null) {
                    targetCharacteristic = service.getCharacteristic(characteristicUUID);
                    if (targetCharacteristic != null) {
                        Log.d(TAG, "Target characteristic found, starting data sending");
                        
                        // 启用通知（如果支持）
                        if ((targetCharacteristic.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                            gatt.setCharacteristicNotification(targetCharacteristic, true);
                        }
                        
                        // 开始发送数据
                        startDataSending();
                    } else {
                        Log.e(TAG, "Target characteristic not found");
                        notifyError("目标特征未找到", -6);
                    }
                } else {
                    Log.e(TAG, "Target service not found");
                    notifyError("目标服务未找到", -7);
                }
            } else {
                Log.e(TAG, "Service discovery failed with status: " + status);
                notifyError("服务发现失败", status);
            }
        }
        
        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                successCount.incrementAndGet();
                lastSendTime.set(System.currentTimeMillis());
            } else {
                errorCount.incrementAndGet();
                lastError = "写入特征失败，状态码: " + status;
                Log.e(TAG, lastError);
            }
            
            totalSent.incrementAndGet();
        }
        
        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            // 处理接收到的数据
            byte[] data = characteristic.getValue();
            if (data != null && data.length > 0) {
                int[] intData = new int[data.length];
                for (int i = 0; i < data.length; i++) {
                    intData[i] = data[i] & 0xFF; // 转换为无符号整数
                }
                
                Log.d(TAG, "Data received, length: " + data.length);
                
                if (callback != null) {
                    callback.onDataReceived(intData);
                }
            }
        }
    };
    
    // 开始数据发送
    private void startDataSending() {
        if (isSending.get()) {
            Log.w(TAG, "Data sending already active");
            return;
        }
        
        isSending.set(true);
        
        // 创建高精度定时发送器
        sendExecutor = Executors.newSingleThreadScheduledExecutor();
        
        Log.d(TAG, "Starting precise " + sendInterval + "ms data sending");
        
        sendTask = sendExecutor.scheduleWithFixedDelay(() -> {
            if (!isSending.get() || !isConnected.get() || targetCharacteristic == null) {
                return;
            }
            
            try {
                // 准备发送数据
                byte[] dataToSend = new byte[sendData.length];
                for (int i = 0; i < sendData.length; i++) {
                    dataToSend[i] = (byte) (sendData[i] & 0xFF);
                }
                
                // 写入特征
                targetCharacteristic.setValue(dataToSend);
                boolean writeResult = bluetoothGatt.writeCharacteristic(targetCharacteristic);
                
                if (!writeResult) {
                    errorCount.incrementAndGet();
                    lastError = "写入特征失败";
                    Log.e(TAG, lastError);
                }
                
                // 定期通知状态变化（每100次发送通知一次，避免过于频繁）
                if (totalSent.get() % 100 == 0) {
                    notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());
                }
                
            } catch (Exception e) {
                errorCount.incrementAndGet();
                lastError = "发送数据异常: " + e.getMessage();
                Log.e(TAG, lastError);
            }
            
        }, 0, sendInterval, TimeUnit.MILLISECONDS);
        
        // 通知发送状态变化
        notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());
        
        Log.d(TAG, "Data sending started with " + sendInterval + "ms interval");
    }
    
    // 重新连接设备
    public void reconnectDevice() {
        Log.d(TAG, "Reconnecting device...");
        
        bluetoothHandler.post(() -> {
            if (bluetoothGatt != null) {
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
                bluetoothGatt = null;
            }
            
            isConnected.set(false);
            retryCount = 0;
            
            // 延迟重连
            retryHandler.postDelayed(() -> {
                if (isSending.get()) {
                    connectToDevice();
                }
            }, 1000);
        });
    }
    
    // 获取发送状态
    public BluetoothSendingStatus getSendingStatus() {
        return new BluetoothSendingStatus(
            isSending.get(),
            lastSendTime.get(),
            totalSent.get(),
            errorCount.get()
        );
    }
    
    // 获取发送统计
    public BluetoothSendingStats getSendingStats() {
        long total = totalSent.get();
        long success = successCount.get();
        long errors = errorCount.get();
        
        // 计算平均间隔（基于成功发送的数据）
        double averageInterval = success > 1 ? 
            (double) (System.currentTimeMillis() - (lastSendTime.get() - (success * sendInterval))) / success : 
            sendInterval;
        
        return new BluetoothSendingStats(
            total,
            success,
            errors,
            averageInterval,
            lastError,
            isConnected.get()
        );
    }
    
    // 清理资源
    public void cleanup() {
        Log.d(TAG, "Cleaning up NativeBluetoothManager");
        
        stopBluetoothSending();
        
        if (bluetoothThread != null) {
            bluetoothThread.quitSafely();
            bluetoothThread = null;
        }
        
        if (retryHandler != null) {
            retryHandler.removeCallbacksAndMessages(null);
            retryHandler = null;
        }
    }
    
    // 通知方法
    private void notifyError(String error, int errorCode) {
        lastError = error;
        if (callback != null) {
            callback.onError(error, errorCode);
        }
    }
    
    private void notifySendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
        if (callback != null) {
            callback.onSendingStateChanged(active, lastSendTime, sendCount, errorCount);
        }
    }
    
    // 内部数据类
    public static class BluetoothSendingStatus {
        public final boolean isActive;
        public final long lastSendTime;
        public final long sendCount;
        public final long errorCount;
        
        public BluetoothSendingStatus(boolean isActive, long lastSendTime, long sendCount, long errorCount) {
            this.isActive = isActive;
            this.lastSendTime = lastSendTime;
            this.sendCount = sendCount;
            this.errorCount = errorCount;
        }
    }
    
    public static class BluetoothSendingStats {
        public final long totalSent;
        public final long successCount;
        public final long errorCount;
        public final double averageInterval;
        public final String lastError;
        public final boolean isConnected;
        
        public BluetoothSendingStats(long totalSent, long successCount, long errorCount, 
                                   double averageInterval, String lastError, boolean isConnected) {
            this.totalSent = totalSent;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.averageInterval = averageInterval;
            this.lastError = lastError;
            this.isConnected = isConnected;
        }
    }
}