{"compilerOptions": {"module": "esnext", "target": "es2017", "lib": ["es2017", "dom", "dom.iterable", "es6"], "declaration": true, "declarationDir": "dist/esm", "outDir": "dist/esm", "moduleResolution": "node", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true}, "include": ["src"], "exclude": ["node_modules"]}