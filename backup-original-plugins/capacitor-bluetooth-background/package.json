{"name": "capacitor-bluetooth-background", "version": "1.0.0", "description": "Capacitor plugin for maintaining Bluetooth communication in background with 106ms precision timing", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Plugin/", "CapacitorBluetoothBackground.podspec"], "author": "Smart Bicycle Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/capacitor-bluetooth-background.git"}, "bugs": {"url": "https://github.com/your-org/capacitor-bluetooth-background/issues"}, "keywords": ["capacitor", "plugin", "bluetooth", "background", "foreground-service", "ios", "android"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "cd ios && pod lib lint", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api BluetoothBackgroundPlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"@capacitor/android": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/docgen": "^0.2.2", "@capacitor/ios": "^6.0.0", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^8.57.0", "prettier": "~3.2.5", "prettier-plugin-java": "~2.5.0", "rimraf": "^5.0.5", "rollup": "^4.9.6", "swiftlint": "^1.0.2", "typescript": "~5.3.3"}, "peerDependencies": {"@capacitor/core": "^6.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslint": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}