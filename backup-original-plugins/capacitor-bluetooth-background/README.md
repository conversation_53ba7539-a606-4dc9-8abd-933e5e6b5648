# capacitor-bluetooth-background

Capacitor plugin for maintaining Bluetooth communication in background with 106ms precision timing.

## Install

```bash
npm install capacitor-bluetooth-background
npx cap sync
```

## Features

- ✅ **Android Foreground Service**: Maintains Bluetooth communication when app is backgrounded
- ✅ **Wake Lock**: Prevents <PERSON> from sleeping to ensure 106ms precision
- ✅ **Battery Optimization**: Guides users to whitelist the app
- ✅ **iOS Background Modes**: Supports bluetooth-central and bluetooth-peripheral modes
- ✅ **Service State Monitoring**: Real-time service status updates
- ✅ **Notification Management**: Customizable foreground service notifications

## API

<docgen-index>

* [`startForegroundService()`](#startforegroundservice)
* [`stopForegroundService()`](#stopforegroundservice)
* [`requestIgnoreBatteryOptimizations()`](#requestignorebatteryoptimizations)
* [`isServiceRunning()`](#isservicerunning)
* [`updateNotification(...)`](#updatenotification)
* [`addListener('serviceStateChanged', ...)`](#addlistenerservicestatechanged)
* [`removeAllListeners()`](#removealllisteners)

</docgen-index>

<docgen-api>
<!--Update the source file JSDoc comments and rerun docgen to update the docs below-->

### startForegroundService()

```typescript
startForegroundService() => Promise<void>
```

Start the foreground service to maintain Bluetooth communication in background

--------------------


### stopForegroundService()

```typescript
stopForegroundService() => Promise<void>
```

Stop the foreground service

--------------------


### requestIgnoreBatteryOptimizations()

```typescript
requestIgnoreBatteryOptimizations() => Promise<void>
```

Request battery optimization exemption for the app

--------------------


### isServiceRunning()

```typescript
isServiceRunning() => Promise<{ isRunning: boolean; }>
```

Check if the foreground service is running

**Returns:** <code>Promise&lt;{ isRunning: boolean; }&gt;</code>

--------------------


### updateNotification(...)

```typescript
updateNotification(options: { title?: string; message?: string; }) => Promise<void>
```

Update the foreground service notification

| Param         | Type                                                  |
| ------------- | ----------------------------------------------------- |
| **`options`** | <code>{ title?: string; message?: string; }</code> |

--------------------


### addListener('serviceStateChanged', ...)

```typescript
addListener(eventName: 'serviceStateChanged', listenerFunc: (data: { isRunning: boolean; }) => void) => Promise<PluginListenerHandle>
```

Add a listener for service state changes

| Param              | Type                                                        |
| ------------------ | ----------------------------------------------------------- |
| **`eventName`**    | <code>'serviceStateChanged'</code>                         |
| **`listenerFunc`** | <code>(data: { isRunning: boolean; }) =&gt; void</code> |

**Returns:** <code>Promise&lt;<a href="#pluginlistenerhandle">PluginListenerHandle</a>&gt;</code>

--------------------


### removeAllListeners()

```typescript
removeAllListeners() => Promise<void>
```

Remove all listeners for this plugin

--------------------


### Interfaces


#### PluginListenerHandle

| Prop         | Type                                      |
| ------------ | ----------------------------------------- |
| **`remove`** | <code>() =&gt; Promise&lt;void&gt;</code> |

</docgen-api>

## Usage

### Basic Usage

```typescript
import { BluetoothBackground } from 'capacitor-bluetooth-background';

// Start foreground service
await BluetoothBackground.startForegroundService();

// Check service status
const { isRunning } = await BluetoothBackground.isServiceRunning();
console.log('Service running:', isRunning);

// Update notification
await BluetoothBackground.updateNotification({
  title: '智能单车',
  message: '正在发送数据...'
});

// Stop service
await BluetoothBackground.stopForegroundService();
```

### Advanced Usage with Event Listeners

```typescript
import { BluetoothBackground } from 'capacitor-bluetooth-background';

// Listen for service state changes
const listener = await BluetoothBackground.addListener('serviceStateChanged', (data) => {
  console.log('Service state changed:', data.isRunning);
  if (data.isRunning) {
    console.log('Background service is now active');
  } else {
    console.log('Background service has stopped');
  }
});

// Request battery optimization exemption
await BluetoothBackground.requestIgnoreBatteryOptimizations();

// Start service
await BluetoothBackground.startForegroundService();

// Clean up listener when done
await listener.remove();
```

## Platform-Specific Configuration

### Android

Add the following permissions to your `android/app/src/main/AndroidManifest.xml`:

```xml
<!-- Already included in the plugin, but ensure they're not overridden -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
```

### iOS

Add background modes to your `ios/App/App/Info.plist`:

```xml
<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-central</string>
    <string>bluetooth-peripheral</string>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>
```

## Important Notes

### Android
- Users need to manually add the app to battery optimization whitelist
- The foreground service will show a persistent notification
- Service automatically restarts if killed by the system

### iOS
- Background execution time is limited by iOS
- Relies on bluetooth background modes for extended background operation
- No foreground service concept - uses background app refresh

## Troubleshooting

### Android
1. **Service stops after screen lock**: Check battery optimization settings
2. **No notification shown**: Verify notification permissions
3. **Service not starting**: Check foreground service permissions

### iOS
1. **Background stops working**: Verify background modes in Info.plist
2. **Limited background time**: Expected iOS behavior for non-critical apps
3. **Bluetooth stops**: Check Bluetooth permissions and background app refresh

## License

MIT