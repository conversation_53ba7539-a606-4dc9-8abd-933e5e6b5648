import { WebPlugin } from '@capacitor/core';

import type { BluetoothBackgroundPlugin } from './definitions';

export class BluetoothBackgroundWeb extends WebPlugin implements BluetoothBackgroundPlugin {
  async startForegroundService(): Promise<void> {
    console.log('startForegroundService - Web implementation (no-op)');
  }

  async stopForegroundService(): Promise<void> {
    console.log('stopForegroundService - Web implementation (no-op)');
  }

  async requestIgnoreBatteryOptimizations(): Promise<void> {
    console.log('requestIgnoreBatteryOptimizations - Web implementation (no-op)');
  }

  async isServiceRunning(): Promise<{ isRunning: boolean }> {
    console.log('isServiceRunning - Web implementation');
    return { isRunning: false };
  }

  async updateNotification(options: { title?: string; message?: string }): Promise<void> {
    console.log('updateNotification - Web implementation (no-op)', options);
  }

  async checkDozeMode(): Promise<{ isInDozeMode: boolean; isIgnoringBatteryOptimizations: boolean }> {
    console.log('checkDozeMode - Web implementation');
    return { 
      isInDozeMode: false, 
      isIgnoringBatteryOptimizations: true 
    };
  }

  async openBatteryOptimizationSettings(): Promise<void> {
    console.log('openBatteryOptimizationSettings - Web implementation (no-op)');
  }

  async sendKeepAlive(): Promise<void> {
    console.log('sendKeepAlive - Web implementation (no-op)');
  }

  async getDeviceInfo(): Promise<{
    manufacturer: string;
    model: string;
    androidVersion: string;
    sdkVersion: number;
    hasAggressivePowerManagement: boolean;
  }> {
    console.log('getDeviceInfo - Web implementation');
    return {
      manufacturer: 'Web',
      model: 'Browser',
      androidVersion: 'N/A',
      sdkVersion: 0,
      hasAggressivePowerManagement: false
    };
  }

  async openAutoStartSettings(): Promise<void> {
    console.log('openAutoStartSettings - Web implementation (no-op)');
  }

  // =================== 新增原生蓝牙发送方法的Web实现 ===================

  async startNativeBluetoothSending(options: {
    deviceId: string;
    serviceUUID: string;
    characteristicUUID: string;
    sendInterval: number;
    data: number[];
  }): Promise<void> {
    console.log('startNativeBluetoothSending - Web implementation (no-op)', options);
    console.warn('原生蓝牙发送功能仅在Android平台可用');
  }

  async stopNativeBluetoothSending(): Promise<void> {
    console.log('stopNativeBluetoothSending - Web implementation (no-op)');
  }

  async updateBluetoothSendData(data: number[]): Promise<void> {
    console.log('updateBluetoothSendData - Web implementation (no-op)', data);
  }

  async isNativeBluetoothSending(): Promise<{ 
    isActive: boolean; 
    lastSendTime: number;
    sendCount: number;
    errorCount: number;
  }> {
    console.log('isNativeBluetoothSending - Web implementation');
    return {
      isActive: false,
      lastSendTime: 0,
      sendCount: 0,
      errorCount: 0
    };
  }

  async getBluetoothSendingStats(): Promise<{
    totalSent: number;
    successCount: number;
    errorCount: number;
    averageInterval: number;
    lastError?: string;
    isConnected: boolean;
  }> {
    console.log('getBluetoothSendingStats - Web implementation');
    return {
      totalSent: 0,
      successCount: 0,
      errorCount: 0,
      averageInterval: 0,
      isConnected: false
    };
  }

  async reconnectBluetoothDevice(): Promise<void> {
    console.log('reconnectBluetoothDevice - Web implementation (no-op)');
  }
}