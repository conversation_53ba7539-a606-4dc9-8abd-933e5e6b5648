export interface BluetoothBackgroundPlugin {
  /**
   * 启动前台服务
   */
  startForegroundService(): Promise<void>;

  /**
   * 停止前台服务
   */
  stopForegroundService(): Promise<void>;

  /**
   * 请求忽略电池优化
   */
  requestIgnoreBatteryOptimizations(): Promise<void>;

  /**
   * 检查服务是否正在运行
   */
  isServiceRunning(): Promise<{ isRunning: boolean }>;

  /**
   * 更新通知内容
   */
  updateNotification(options: { title?: string; message?: string }): Promise<void>;

  /**
   * 检查Doze模式状态
   */
  checkDozeMode(): Promise<{ 
    isInDozeMode: boolean; 
    isIgnoringBatteryOptimizations: boolean; 
  }>;

  /**
   * 打开电池优化设置页面
   */
  openBatteryOptimizationSettings(): Promise<void>;

  /**
   * 发送保活信号
   */
  sendKeepAlive(): Promise<void>;

  /**
   * 获取设备信息
   */
  getDeviceInfo(): Promise<{
    manufacturer: string;
    model: string;
    androidVersion: string;
    sdkVersion: number;
    hasAggressivePowerManagement: boolean;
  }>;

  /**
   * 打开自启动设置页面（针对不同厂商）
   */
  openAutoStartSettings(): Promise<void>;

  // =================== 新增原生蓝牙发送功能 ===================

  /**
   * 启动原生蓝牙发送服务
   * @param options 蓝牙发送配置
   */
  startNativeBluetoothSending(options: {
    deviceId: string;
    serviceUUID: string;
    characteristicUUID: string;
    sendInterval: number; // 发送间隔（毫秒）
    data: number[]; // 要发送的数据数组
  }): Promise<void>;

  /**
   * 停止原生蓝牙发送服务
   */
  stopNativeBluetoothSending(): Promise<void>;

  /**
   * 更新蓝牙发送数据
   * @param data 新的数据数组
   */
  updateBluetoothSendData(data: number[]): Promise<void>;

  /**
   * 检查原生蓝牙发送状态
   */
  isNativeBluetoothSending(): Promise<{ 
    isActive: boolean; 
    lastSendTime: number;
    sendCount: number;
    errorCount: number;
  }>;

  /**
   * 获取蓝牙发送统计信息
   */
  getBluetoothSendingStats(): Promise<{
    totalSent: number;
    successCount: number;
    errorCount: number;
    averageInterval: number;
    lastError?: string;
    isConnected: boolean;
  }>;

  /**
   * 重新连接蓝牙设备（原生实现）
   */
  reconnectBluetoothDevice(): Promise<void>;

  // =================== 监听器扩展 ===================

  /**
   * 添加监听器
   */
  addListener(
    eventName: 'serviceStateChanged' | 'bluetoothSendingStateChanged' | 'bluetoothDataReceived' | 'bluetoothError',
    listenerFunc: (data: any) => void,
  ): Promise<any>;

  /**
   * 移除所有监听器
   */
  removeAllListeners(): Promise<void>;
}

export interface PluginListenerHandle {
  remove(): Promise<void>;
}

// =================== 新增接口定义 ===================

export interface BluetoothSendingConfig {
  deviceId: string;
  serviceUUID: string;
  characteristicUUID: string;
  sendInterval: number;
  data: number[];
}

export interface BluetoothSendingState {
  isActive: boolean;
  lastSendTime: number;
  sendCount: number;
  errorCount: number;
}

export interface BluetoothSendingStats {
  totalSent: number;
  successCount: number;
  errorCount: number;
  averageInterval: number;
  lastError?: string;
  isConnected: boolean;
}

export interface BluetoothDataReceivedEvent {
  data: number[];
  timestamp: number;
  deviceId: string;
}

export interface BluetoothErrorEvent {
  error: string;
  errorCode?: number;
  timestamp: number;
  deviceId?: string;
}