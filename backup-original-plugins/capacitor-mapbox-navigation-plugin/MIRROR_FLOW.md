# 镜像功能流程图

## 功能流程

```mermaid
graph TD
    A[开始导航] --> B[路线计算完成]
    B --> C[显示投屏确认弹框]
    C --> D{用户选择}
    D -->|开启| E[设置镜像状态为开启]
    D -->|取消| F[设置镜像状态为关闭]
    E --> G[发送开启事件到web端]
    F --> H[发送关闭事件到web端]
    G --> I[按钮显示绿色]
    H --> J[按钮显示灰色]
    I --> K[显示镜像按钮]
    J --> K
    K --> L[用户点击镜像按钮]
    L --> M{当前镜像状态}
    M -->|已开启| N[显示"是否关闭投屏？"弹框]
    M -->|已关闭| O[显示"是否开启投屏？"弹框]
    N --> P{用户选择}
    O --> Q{用户选择}
    P -->|关闭| R[设置镜像状态为关闭]
    P -->|取消| S[保持当前状态]
    Q -->|开启| T[设置镜像状态为开启]
    Q -->|取消| U[保持当前状态]
    R --> V[发送关闭事件到web端]
    T --> W[发送开启事件到web端]
    S --> X[无操作]
    U --> X
    V --> Y[按钮显示灰色]
    W --> Z[按钮显示绿色]
    X --> AA[无状态变化]
    Y --> BB[继续监听]
    Z --> BB
    AA --> BB
```

## 事件触发时机

### 1. 初始导航时

- **触发条件**: 路线计算完成后
- **弹框内容**: "是否开启投屏？"
- **按钮选项**: "开启" / "取消"
- **事件发送**: 根据用户选择发送相应事件

### 2. 点击镜像按钮时

- **触发条件**: 用户点击镜像按钮
- **弹框内容**:
  - 当前关闭状态: "是否开启投屏？"
  - 当前开启状态: "是否关闭投屏？"
- **按钮选项**:
  - 开启状态: "关闭" / "取消"
  - 关闭状态: "开启" / "取消"
- **事件发送**: 仅在用户确认时发送事件

## 状态管理

### 镜像状态变量

```kotlin
private var isMirrorEnabled = false
```

### 按钮状态显示

- **灰色**: 镜像功能关闭
- **绿色**: 镜像功能开启

### 事件数据格式

```typescript
interface ScreenMirroringChangeEvent {
  enabled: boolean; // 镜像是否启用
  timestamp?: number; // 时间戳（毫秒）
}
```

## 用户体验

1. **一致性**: 所有镜像操作都通过确认弹框进行
2. **安全性**: 避免误操作，用户必须确认
3. **反馈**: 按钮颜色变化提供视觉反馈
4. **可追溯**: 事件包含时间戳，便于调试和记录

## 注意事项

- 只有在用户确认弹框后才会发送事件
- 取消操作不会触发任何事件
- 按钮状态与实际的镜像状态保持同步
- 初始导航和后续点击按钮使用相同的确认机制
