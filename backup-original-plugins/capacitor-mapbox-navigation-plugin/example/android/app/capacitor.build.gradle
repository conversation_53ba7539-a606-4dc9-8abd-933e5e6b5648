// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':dongsp-capacitor-mapbox-navigation')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-splash-screen')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
