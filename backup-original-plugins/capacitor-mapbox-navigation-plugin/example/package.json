{"name": "capacitor-app", "version": "1.0.0", "description": "An Amazing Capacitor App", "main": "index.js", "keywords": ["capacitor", "mobile"], "scripts": {"start": "vite", "build": "vite build && npx cap sync", "preview": "vite preview", "sync": "npx cap sync"}, "dependencies": {"@dongsp/capacitor-mapbox-navigation": "../", "@capacitor/camera": "latest", "@capacitor/core": "latest", "@capacitor/geolocation": "^6.0.1", "@capacitor/splash-screen": "latest"}, "devDependencies": {"@capacitor/cli": "latest", "prettier": "^3.3.3", "vite": "^2.9.13"}, "author": "", "license": "ISC"}