<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.capacitor.mapbox.navigation.plugin">
    <uses-permission android:name="android.permission.INTERNET" />

    <application>
        <activity
            android:name="com.capacitor.mapbox.navigation.plugin.NavigationDialogFragment"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|screenSize"
            android:theme="@style/AppTheme.NoActionBar"
            android:exported="false"
            tools:ignore="Instantiatable">
            <intent-filter>
                <action android:name="com.capacitor.mapbox.navigation.plugin.NavigationViewActivity"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
    </application>

</manifest>
