import XCTest
@testable import Plugin

class CapacitorMapboxNavigationTests: XCTestCase {

    func testEcho() {
        // This is an example of a functional test case for a plugin.
        // Use XCTAssert and related functions to verify your tests produce the correct results.

        let implementation = CapacitorMapboxNavigation()
        let value = "Hello, <PERSON>!"
        let result = implementation.echo(value)

        XCTAssertEqual(value, result)
    }
}
