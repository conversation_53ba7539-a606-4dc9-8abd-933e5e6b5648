/**
 * 蓝牙重连和设置更新安全验证脚本
 * 测试所有边缘情况和潜在漏洞
 */

(async function bluetoothSecurityValidation() {
  console.log('🔒 开始蓝牙安全验证...');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };
  
  const addTest = (name, status, details) => {
    results.tests.push({ name, status, details, timestamp: new Date().toISOString() });
    results.summary[status]++;
    console.log(`${status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⚠️'} ${name}: ${details}`);
  };
  
  try {
    // 测试1: sessionStorage安全性
    console.log('📊 测试1: sessionStorage安全性');
    try {
      // 模拟sessionStorage不可用的情况
      const originalSessionStorage = window.sessionStorage;
      delete window.sessionStorage;
      
      // 测试安全的sessionStorage操作
      const testKey = 'testBluetoothKey';
      const testValue = 'testValue';
      
      // 这应该使用内存备份存储
      if (window.__bluetoothSessionBackup) {
        window.__bluetoothSessionBackup[testKey] = testValue;
        const retrieved = window.__bluetoothSessionBackup[testKey];
        if (retrieved === testValue) {
          addTest('sessionStorage备份机制', 'passed', '内存备份存储工作正常');
        } else {
          addTest('sessionStorage备份机制', 'failed', '内存备份存储读取失败');
        }
      } else {
        addTest('sessionStorage备份机制', 'warning', '备份存储未初始化');
      }
      
      // 恢复sessionStorage
      window.sessionStorage = originalSessionStorage;
      
    } catch (storageError) {
      addTest('sessionStorage安全性', 'failed', `存储测试失败: ${storageError.message}`);
    }
    
    // 测试2: 快速连接/断开保护
    console.log('📊 测试2: 快速连接/断开保护');
    try {
      const deviceId = 'test-device-123';
      const connectionId1 = `${deviceId}_${Date.now()}`;
      const connectionId2 = `${deviceId}_${Date.now() + 1}`;
      
      // 模拟第一个连接
      window.__currentConnectionId = connectionId1;
      
      // 模拟快速的第二个连接
      setTimeout(() => {
        window.__currentConnectionId = connectionId2;
      }, 10);
      
      // 检查第一个连接是否被正确取消
      setTimeout(() => {
        if (window.__currentConnectionId === connectionId2) {
          addTest('快速连接/断开保护', 'passed', '连接ID正确更新，旧连接会被取消');
        } else {
          addTest('快速连接/断开保护', 'failed', '连接ID更新失败');
        }
      }, 50);
      
    } catch (connectionError) {
      addTest('快速连接/断开保护', 'failed', `连接保护测试失败: ${connectionError.message}`);
    }
    
    // 测试3: 并发设置更新保护
    console.log('📊 测试3: 并发设置更新保护');
    try {
      let updateCount = 0;
      const mockUpdate = async (name, delay) => {
        console.log(`开始${name}更新`);
        await new Promise(resolve => setTimeout(resolve, delay));
        updateCount++;
        console.log(`完成${name}更新`);
        return name;
      };
      
      // 模拟并发更新
      const updates = [
        mockUpdate('传统方案', 100),
        mockUpdate('原生方案', 150),
        mockUpdate('智能方案', 120)
      ];
      
      const startTime = Date.now();
      const updateResults = await Promise.all(updates);
      const endTime = Date.now();
      
      // 检查是否按预期完成
      if (updateResults.length === 3 && updateCount === 3) {
        addTest('并发设置更新', 'passed', `所有更新完成，耗时${endTime - startTime}ms`);
      } else {
        addTest('并发设置更新', 'failed', `更新不完整: ${updateResults.length}/3`);
      }
      
    } catch (concurrentError) {
      addTest('并发设置更新保护', 'failed', `并发测试失败: ${concurrentError.message}`);
    }
    
    // 测试4: 事件监听器内存泄漏检查
    console.log('📊 测试4: 事件监听器内存泄漏检查');
    try {
      const initialListenerCount = getEventListenerCount();
      
      // 添加测试监听器
      const testListener = () => {};
      window.addEventListener('settingDataUpdated', testListener);
      
      const afterAddCount = getEventListenerCount();
      
      // 移除监听器
      window.removeEventListener('settingDataUpdated', testListener);
      
      const afterRemoveCount = getEventListenerCount();
      
      if (afterRemoveCount <= initialListenerCount) {
        addTest('事件监听器清理', 'passed', '监听器正确清理');
      } else {
        addTest('事件监听器清理', 'warning', '可能存在监听器泄漏');
      }
      
    } catch (listenerError) {
      addTest('事件监听器检查', 'warning', `无法检测监听器: ${listenerError.message}`);
    }
    
    // 测试5: 错误处理机制
    console.log('📊 测试5: 错误处理机制');
    try {
      // 模拟各种错误情况
      const errorTests = [
        {
          name: '空设备ID',
          test: () => {
            const device = { deviceId: '', name: 'Test' };
            return device.deviceId || 'empty';
          }
        },
        {
          name: 'undefined设备',
          test: () => {
            const device = undefined;
            return device?.deviceId || 'undefined';
          }
        },
        {
          name: '无效连接状态',
          test: () => {
            const connectedDevice = { isPaired: false, deviceId: 'test' };
            return connectedDevice.isPaired ? 'connected' : 'disconnected';
          }
        }
      ];
      
      let errorHandlingPassed = 0;
      for (const errorTest of errorTests) {
        try {
          const result = errorTest.test();
          if (result) {
            errorHandlingPassed++;
          }
        } catch (error) {
          // 错误被正确捕获
          errorHandlingPassed++;
        }
      }
      
      if (errorHandlingPassed === errorTests.length) {
        addTest('错误处理机制', 'passed', '所有错误情况都被正确处理');
      } else {
        addTest('错误处理机制', 'failed', `${errorHandlingPassed}/${errorTests.length}个错误处理正确`);
      }
      
    } catch (errorHandlingError) {
      addTest('错误处理机制', 'failed', `错误处理测试失败: ${errorHandlingError.message}`);
    }
    
    // 测试6: 数据完整性验证
    console.log('📊 测试6: 数据完整性验证');
    try {
      const testData = [0x0F, 0x04, 0xF5, 0x58, 0x2E, 0xB2, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E];
      
      // 验证数据长度
      if (testData.length === 18) {
        addTest('数据长度验证', 'passed', '数据长度正确');
      } else {
        addTest('数据长度验证', 'failed', `数据长度错误: ${testData.length}/18`);
      }
      
      // 验证数据范围
      const invalidBytes = testData.filter(byte => byte < 0 || byte > 255);
      if (invalidBytes.length === 0) {
        addTest('数据范围验证', 'passed', '所有字节都在有效范围内');
      } else {
        addTest('数据范围验证', 'failed', `发现无效字节: ${invalidBytes}`);
      }
      
      // 验证帧头和结尾
      if (testData[0] === 0x0F && testData[17] === 0x0E) {
        addTest('帧格式验证', 'passed', '帧头和结尾正确');
      } else {
        addTest('帧格式验证', 'failed', `帧格式错误: 头=${testData[0].toString(16)}, 尾=${testData[17].toString(16)}`);
      }
      
    } catch (dataError) {
      addTest('数据完整性验证', 'failed', `数据验证失败: ${dataError.message}`);
    }
    
    // 测试7: 平台兼容性
    console.log('📊 测试7: 平台兼容性');
    try {
      const platformTests = {
        isAndroid: navigator.userAgent.toLowerCase().includes('android'),
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
        isWebView: window.navigator.userAgent.includes('wv'),
        hasCapacitor: !!(window.Capacitor),
        hasSessionStorage: !!(window.sessionStorage),
        hasLocalStorage: !!(window.localStorage)
      };
      
      const platformInfo = Object.entries(platformTests)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      
      addTest('平台兼容性检查', 'passed', platformInfo);
      
    } catch (platformError) {
      addTest('平台兼容性检查', 'failed', `平台检查失败: ${platformError.message}`);
    }
    
    // 生成最终报告
    console.log('📋 生成安全验证报告...');
    
    const report = {
      ...results,
      recommendations: [
        results.summary.failed > 0 ? '❌ 发现关键问题，需要立即修复' : '✅ 未发现关键安全问题',
        results.summary.warnings > 0 ? '⚠️ 存在潜在风险，建议优化' : '✅ 未发现潜在风险',
        '🔒 建议定期运行安全验证',
        '📊 在生产环境部署前进行完整测试',
        '🔧 监控实际使用中的错误日志'
      ],
      securityLevel: results.summary.failed === 0 ? 
        (results.summary.warnings === 0 ? 'HIGH' : 'MEDIUM') : 'LOW'
    };
    
    console.log('🎯 安全验证完成！');
    console.table(results.tests);
    console.log('📊 汇总:', results.summary);
    console.log('🔒 安全等级:', report.securityLevel);
    console.log('💡 建议:', report.recommendations);
    
    // 将结果保存到全局变量供进一步分析
    window.bluetoothSecurityReport = report;
    
    return report;
    
  } catch (error) {
    console.error('❌ 安全验证过程中出现错误:', error);
    addTest('整体安全验证', 'failed', error.message);
    return results;
  }
  
  // 辅助函数
  function getEventListenerCount() {
    // 这是一个简化的监听器计数，实际实现可能需要更复杂的逻辑
    try {
      return Object.keys(window).filter(key => key.startsWith('on')).length;
    } catch (error) {
      return 0;
    }
  }
})();

// 提供额外的调试工具
window.bluetoothSecurityUtils = {
  // 强制清理所有蓝牙相关状态
  forceCleanup: () => {
    try {
      // 清理sessionStorage
      ['needRestartDataSending', 'lastDisconnectedDevice'].forEach(key => {
        sessionStorage.removeItem(key);
      });
      
      // 清理内存备份
      if (window.__bluetoothSessionBackup) {
        delete window.__bluetoothSessionBackup;
      }
      
      // 清理连接ID
      if (window.__currentConnectionId) {
        delete window.__currentConnectionId;
      }
      
      console.log('✅ 蓝牙状态已强制清理');
    } catch (error) {
      console.error('❌ 强制清理失败:', error);
    }
  },
  
  // 模拟各种异常情况
  simulateErrors: {
    sessionStorageFailure: () => {
      const original = window.sessionStorage;
      window.sessionStorage = {
        setItem: () => { throw new Error('SessionStorage disabled'); },
        getItem: () => { throw new Error('SessionStorage disabled'); },
        removeItem: () => { throw new Error('SessionStorage disabled'); }
      };
      console.log('⚠️ 已模拟sessionStorage失败');
      return () => { window.sessionStorage = original; };
    },
    
    rapidConnections: async () => {
      console.log('⚠️ 模拟快速连接/断开');
      for (let i = 0; i < 5; i++) {
        window.__currentConnectionId = `test_${i}_${Date.now()}`;
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      console.log('✅ 快速连接模拟完成');
    }
  },
  
  // 检查当前状态
  getCurrentState: () => {
    return {
      sessionStorage: {
        needRestart: sessionStorage.getItem('needRestartDataSending'),
        lastDevice: sessionStorage.getItem('lastDisconnectedDevice')
      },
      memoryBackup: window.__bluetoothSessionBackup,
      currentConnection: window.__currentConnectionId,
      timestamp: new Date().toISOString()
    };
  }
};

console.log('🛠️ 安全工具已加载到 window.bluetoothSecurityUtils');
console.log('📚 可用方法:');
console.log('  - bluetoothSecurityUtils.forceCleanup() // 强制清理状态');
console.log('  - bluetoothSecurityUtils.simulateErrors.sessionStorageFailure() // 模拟存储失败');
console.log('  - bluetoothSecurityUtils.simulateErrors.rapidConnections() // 模拟快速连接');
console.log('  - bluetoothSecurityUtils.getCurrentState() // 检查当前状态');