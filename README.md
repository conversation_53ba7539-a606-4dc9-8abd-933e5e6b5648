# KT Smart

一个基于 Ionic Vue 的智能蓝牙设备管理应用，支持跨平台的蓝牙通信和设备控制。

## 📱 项目概述

KT Smart 是一个现代化的移动应用，专为智能设备的蓝牙通信和控制而设计。应用采用三层蓝牙架构，支持 iOS、Android 和 Web 平台，提供统一的用户体验和高效的设备管理功能。

### 🎯 核心功能

- 🔗 **跨平台蓝牙通信** - 支持 iOS、Android、Web
- ⚙️ **设备参数配置** - 实时设置和同步设备参数
- 📊 **数据可视化** - 图表展示设备状态和数据
- 🗺️ **地图导航** - 集成 Mapbox 地图和导航功能
- 🔧 **调试工具** - 完整的蓝牙调试和测试工具

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI库**: Ionic Vue 8.6.2
- **状态管理**: Pinia
- **构建工具**: Vite 6.1.0
- **移动端**: Capacitor 7.4.0

### 蓝牙架构（三层设计）

```
┌─────────────────────────────────────────┐
│              应用层                     │
│  SettingPage, HomePage, App.vue 等     │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           API接口层                     │
│      useSmartBluetoothMessage          │
│   (统一API，隐藏平台差异)               │
└─────────────┬───────────┬───────────────┘
              │           │
              │           │ ┌─────────────────┐
              │           └─│ 数据管理层      │
              │             │bluetoothDataManager│
              │             │(数据协调和更新) │
              │             └─────────┬───────┘
              │                       │
    ┌─────────▼─────────┐             │
    │   底层实现层      │◄────────────┘
    │                   │
    │ ┌───────────────┐ │ ┌───────────────┐
    │ │   传统方案    │ │ │   原生方案    │
    │ │  useMessage   │ │ │useNativeBluetoothMessage│
    │ │  (iOS/Web)    │ │ │  (Android)    │
    │ └───────────────┘ │ └───────────────┘
    └───────────────────┘
```

#### 架构层级说明

1. **底层实现层**（两种方案）
   - **传统方案** (`useMessage`): iOS/Web 使用 `@capacitor-community/bluetooth-le`
   - **原生方案** (`useNativeBluetoothMessage`): Android 使用 `capacitor-kt-service`

2. **API接口层**
   - **智能选择器** (`useSmartBluetoothMessage`): 统一API接口，自动选择底层方案

3. **数据管理层**
   - **蓝牙数据管理器** (`bluetoothDataManager`): 专注于数据协调和更新

### 核心依赖

```json
{
  "@ionic/vue": "8.6.2",
  "@capacitor/core": "7.0.1",
  "@capacitor-community/bluetooth-le": "7.1.1",
  "capacitor-kt-service": "file:capacitor-kt-service",
  "mapbox-gl": "3.13.0",
  "@antv/g2": "5.2.12",
  "vue": "3.5.17",
  "pinia": "3.0.1"
}
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn
- iOS 开发需要 Xcode
- Android 开发需要 Android Studio

### 安装依赖

```bash
npm install
```

### 开发命令

```bash
# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 代码检查和修复
npm run lint
```

### 构建命令

```bash
# 生产版本构建（完整优化）
npm run build

# 开发版本快速构建（用于调试）
npm run build:fast

# 构建分析
npm run build:analyze
```

### 移动端调试

```bash
# iOS 调试
npm run debug:ios

# Android 调试  
npm run debug:android

# iOS 实时重载
npm run live:ios

# Android 实时重载
npm run live:android
```

## 🔧 开发指南

### 蓝牙数据管理

#### 设置更新和数据发送
```typescript
import { bluetoothDataManager } from "@/utils/bluetoothDataManager";

// 更新设置并发送蓝牙数据
const success = await bluetoothDataManager.updateSettingsAndSend();
```

#### 蓝牙操作接口
```typescript
import { useSmartBluetoothMessage } from "@/hooks/useSmartBluetoothMessage";

const { 
  startSending, 
  stopSending, 
  isServiceRunning 
} = useSmartBluetoothMessage();
```

### 环境变量配置

项目支持不同环境的配置：

#### 开发环境 (.env.development)
```bash
NODE_ENV=development
VITE_APP_TITLE=KT Smart (Dev)
VITE_DEBUG=true
VITE_BLUETOOTH_DEBUG=true
```

#### 生产环境 (.env.production)
```bash
NODE_ENV=production
VITE_APP_TITLE=KT Smart
VITE_DEBUG=false
VITE_BLUETOOTH_DEBUG=false
```

### 蓝牙数据协议

- **发送频率**: 106毫秒
- **数据格式**: 18字节，以0x0F开头，0x0E结尾
- **数据结构**: 
  - 字节1-11: 设置参数
  - 字节12-16: 导航数据
  - 字节5,16: 校验位

## 📱 平台特性

### iOS
- 使用 `@capacitor-community/bluetooth-le`
- 支持后台蓝牙通信
- 完整的权限管理

### Android  
- 使用自定义 `capacitor-kt-service`
- 原生蓝牙优化
- 高性能数据传输

### Web
- 使用 Web Bluetooth API
- 渐进式 Web 应用支持
- 跨浏览器兼容

## 🧪 测试

### 单元测试
```bash
npm run test:unit
```

### E2E 测试
```bash
npm run test:e2e
```

### 蓝牙功能测试
在开发环境中，设置页面提供了蓝牙数据管理器测试工具：
```typescript
import { testBluetoothDataManager } from "@/utils/bluetoothDataManagerTest";

// 运行完整测试套件
await testBluetoothDataManager();
```

## 📦 构建优化

### 开发版本 (build:fast)
- 无代码分割 - 加快构建速度
- 无压缩 - 大幅提升构建速度
- 保留调试信息 - 便于开发调试
- 启用源码映射 - 便于错误定位

### 生产版本 (build)
- 代码分割 - 优化加载性能
- Terser 压缩 - 减小包体积
- 移除 console - 清理生产代码
- TypeScript 检查 - 确保代码质量

## 🔍 调试工具

### 蓝牙调试
- 实时数据监控
- 连接状态检查
- 数据发送验证
- 平台兼容性测试

### 开发工具
- Vue DevTools 支持
- 热重载开发
- 源码映射调试
- 性能分析工具

## 📄 许可证

本项目为私有项目，版权所有。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📂 项目结构

```text
kt-smart/
├── src/
│   ├── components/          # 可复用组件
│   ├── hooks/              # 组合式API
│   │   ├── useMessage.ts           # 传统蓝牙方案
│   │   ├── useNativeBluetoothMessage.ts  # 原生蓝牙方案
│   │   └── useSmartBluetoothMessage.ts   # 智能选择器
│   ├── store/              # Pinia 状态管理
│   ├── utils/              # 工具函数
│   │   ├── bluetoothDataManager.ts       # 蓝牙数据管理器
│   │   └── bluetoothDataManagerTest.ts   # 测试工具
│   ├── views/              # 页面组件
│   │   └── SettingPage.vue             # 设置页面
│   └── router/             # 路由配置
├── capacitor-kt-service/   # Android 原生蓝牙服务
├── public/                 # 静态资源
└── dist/                   # 构建输出
```

## 🔧 常见问题

### Q: 蓝牙连接失败怎么办？

A:

1. 检查设备蓝牙权限
2. 确认设备支持蓝牙功能
3. 查看控制台错误日志
4. 使用调试工具测试连接

### Q: Android 和 iOS 数据不同步？

A:

1. 检查平台特定的设置更新逻辑
2. 验证数据管理器的方案选择
3. 确认校验位计算正确

### Q: 构建速度慢怎么优化？

A:

1. 使用 `npm run build:fast` 进行开发构建
2. 检查依赖项是否过大
3. 考虑使用代码分割优化

## 📈 性能优化

### 蓝牙通信优化
- 106ms 高频发送优化
- 数据缓存机制
- 智能方案选择
- 避免重复调用

### 构建优化
- 代码分割策略
- 依赖项优化
- 压缩和混淆
- 资源懒加载

### 运行时优化

- 组件懒加载
- 状态管理优化
- 内存泄漏防护
- 性能监控

## 📞 支持

如有问题或建议，请联系开发团队。

---

**KT Smart** - 智能设备蓝牙管理的最佳选择 🚀
