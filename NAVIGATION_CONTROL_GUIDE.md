# 导航界面控制功能使用指南

## 🎯 功能概述

新的导航界面控制功能允许通过Web端手动控制导航Fragment的显示和隐藏，同时保持导航逻辑继续运行。这意味着用户可以暂时隐藏导航界面，但导航服务仍在后台持续工作，随时可以重新显示界面继续导航。

## 🚀 核心特性

### ✅ **无缝控制**
- **隐藏界面**: 导航Fragment变为不可见，但导航逻辑继续运行
- **显示界面**: 恢复导航Fragment显示，用户可以继续看到导航信息
- **状态查询**: 实时获取当前界面的显示/隐藏状态

### ✅ **导航连续性**
- 隐藏界面时，导航引擎继续工作（路线计算、语音提示等）
- 蓝牙数据发送不中断
- GPS定位和路线跟踪持续进行
- 显示界面时，立即恢复到当前导航状态

## 📱 访问方式

### 方式一：通过设置页面
1. 打开应用
2. 导航到"设置"页面
3. 滚动到底部的"开发者工具"区域  
4. 点击"导航界面控制"

### 方式二：直接访问
在浏览器中访问：`http://localhost:5173/navigation-control`

## 🛠️ API 接口说明

### 1. 隐藏导航界面
```javascript
import { CapacitorKtService } from 'capacitor-kt-service';

const hideNavigation = async () => {
  try {
    const result = await CapacitorKtService.hideNavigationFragment();
    if (result.success) {
      console.log('导航界面已隐藏');
    } else {
      console.error('隐藏失败:', result.error);
    }
  } catch (error) {
    console.error('操作失败:', error);
  }
};
```

### 2. 显示导航界面
```javascript
const showNavigation = async () => {
  try {
    const result = await CapacitorKtService.showNavigationFragment();
    if (result.success) {
      console.log('导航界面已显示');
    } else {
      console.error('显示失败:', result.error);
    }
  } catch (error) {
    console.error('操作失败:', error);
  }
};
```

### 3. 查询界面状态
```javascript
const checkVisibility = async () => {
  try {
    const result = await CapacitorKtService.getNavigationFragmentVisibility();
    console.log('界面可见性:', result.isVisible);
    return result.isVisible;
  } catch (error) {
    console.error('查询失败:', error);
    return false;
  }
};
```

## 🎮 演示页面功能

### 📊 **状态监控**
- 实时显示导航界面的可见性状态
- 显示最后更新时间
- 自动定时刷新状态（每5秒）
- 错误信息展示

### 🎛️ **界面控制**
- **显示界面**按钮：恢复导航Fragment显示
- **隐藏界面**按钮：隐藏导航Fragment
- **刷新状态**按钮：手动更新状态信息

### 🚀 **快速测试**
- 内置测试导航路线（北京天安门到故宫）
- 一键启动测试导航
- 便于快速验证控制功能

### 📝 **操作日志**
- 记录所有操作的时间戳
- 显示操作成功/失败状态
- 详细的错误信息记录
- 日志清理功能

## 🔧 技术实现原理

### Android端实现
```kotlin
class NavigationDialogFragment : DialogFragment() {
    private var isFragmentVisible = true
    
    // 隐藏界面（保持导航运行）
    fun hideNavigationInterface() {
        if (::binding.isInitialized && isFragmentVisible) {
            binding.root.visibility = View.GONE
            isFragmentVisible = false
        }
    }
    
    // 显示界面
    fun showNavigationInterface() {
        if (::binding.isInitialized && !isFragmentVisible) {
            binding.root.visibility = View.VISIBLE
            isFragmentVisible = true
        }
    }
}
```

### Web端调用
```typescript
// TypeScript接口定义
interface CapacitorKtService {
  hideNavigationFragment(): Promise<{ success: boolean }>;
  showNavigationFragment(): Promise<{ success: boolean }>;
  getNavigationFragmentVisibility(): Promise<{ isVisible: boolean }>;
}
```

## 📋 使用场景

### 1. **临时隐藏界面**
- 用户需要查看地图下方的内容
- 避免导航界面遮挡其他UI元素
- 临时需要全屏显示其他内容

### 2. **省电模式**
- 在长途导航中暂时隐藏界面减少耗电
- 保持导航功能但降低屏幕使用

### 3. **多任务处理**
- 在导航过程中处理其他应用功能
- 快速切换到其他页面后再回到导航

### 4. **开发测试**
- 测试导航后台服务的稳定性
- 验证蓝牙数据发送的连续性
- UI自动化测试中的界面控制

## ⚠️ 注意事项

### 使用前提
1. **必须先启动导航**: 控制功能只在导航激活时有效
2. **权限确认**: 确保应用有必要的位置权限
3. **设备兼容**: 功能仅在移动设备上可用（iOS/Android）

### 状态管理
- 界面状态在Fragment重新创建时会重置为显示
- 应用程序重启后状态会重置
- 建议通过API查询当前状态而不是缓存

### 性能考虑
- 频繁切换显示/隐藏可能影响UI性能
- 建议合理控制操作频率
- 长时间隐藏不会影响导航核心功能

## 🔍 故障排除

### 常见问题

#### 1. "导航界面未初始化"错误
**原因**: 还没有启动导航服务
**解决**: 先调用 `showMapboxNavigation()` 启动导航

#### 2. 操作无响应
**原因**: 可能在Web环境或导航服务异常
**解决**: 检查设备平台和导航服务状态

#### 3. 状态不同步
**原因**: 网络延迟或Fragment状态异常
**解决**: 调用 `getNavigationFragmentVisibility()` 手动刷新

### 调试技巧

#### 1. 开启详细日志
```javascript
// 监听所有操作的结果
const result = await CapacitorKtService.hideNavigationFragment();
console.log('隐藏操作结果:', result);
```

#### 2. 状态轮询检查
```javascript
// 定期检查状态一致性
setInterval(async () => {
  const status = await CapacitorKtService.getNavigationFragmentVisibility();
  console.log('当前状态:', status.isVisible);
}, 2000);
```

#### 3. 错误处理
```javascript
try {
  await CapacitorKtService.showNavigationFragment();
} catch (error) {
  console.error('详细错误:', error);
  // 实施降级策略
}
```

## 📈 扩展可能

### 未来增强
- **动画效果**: 添加显示/隐藏的过渡动画
- **部分隐藏**: 支持只隐藏特定UI元素
- **自动控制**: 基于场景自动隐藏/显示
- **手势控制**: 支持手势操作控制界面

### 集成建议
- 与现有UI状态管理系统集成
- 结合用户偏好设置保存状态
- 与其他应用功能联动控制

---

**🎊 导航界面控制功能让您拥有更灵活的导航体验！**

现在您可以根据需要随时控制导航界面的显示，同时确保导航服务的连续性和可靠性。 