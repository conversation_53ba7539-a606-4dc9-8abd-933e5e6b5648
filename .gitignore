# Specifies intentionally untracked files to ignore when using Git
# http://git-scm.com/docs/gitignore

*~
*.sw[mnpcod]
.tmp
*.tmp
*.tmp.*
*.sublime-project
*.sublime-workspace
.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate
$RECYCLE.BIN/

*.log
log.txt
npm-debug.log*

/.idea
/.ionic
/.sass-cache
/.sourcemaps
/.versions
/.vscode/*
!/.vscode/extensions.json
/coverage
/dist
/node_modules
/platforms
/plugins
/www
.qodo

# TypeScript build info
.tsbuildinfo

# Vite cache
.vite/
node_modules/.vite/

# Vite ignore file (keep this in repo)
# .viteignore

# Capacitor plugin specific rules
# iOS files
Pods
Podfile.lock
Package.resolved
Build
xcuserdata
/.build
/Packages
xcuserdata/
DerivedData/
.swiftpm/configuration/registries.json
.swiftpm/xcode/package.xcworkspace/contents.xcworkspacedata
.netrc

# Android files
*.apk
*.ap_
*.dex
*.class
bin
gen
out
.gradle
build
local.properties
proguard
*.log
.navigation
captures
*.iml
.externalNativeBuild

# Capacitor KT Service - ignore development dependencies and build artifacts
capacitor-kt-service/node_modules/
capacitor-kt-service/dist/
capacitor-kt-service/.DS_Store

# Capacitor KT Service - Android build artifacts
capacitor-kt-service/android/build/
capacitor-kt-service/android/.gradle/
capacitor-kt-service/android/local.properties

# Capacitor KT Service - iOS build artifacts (if any Pods are used)
capacitor-kt-service/ios/Pods/
capacitor-kt-service/ios/Podfile.lock
capacitor-kt-service/ios/.DS_Store

# Capacitor KT Service - Example app dependencies and build artifacts
capacitor-kt-service/example-app/node_modules/
capacitor-kt-service/example-app/dist/
capacitor-kt-service/example-app/.DS_Store

# Capacitor KT Service - Example app Android
capacitor-kt-service/example-app/android/build/
capacitor-kt-service/example-app/android/.gradle/
capacitor-kt-service/example-app/android/local.properties
capacitor-kt-service/example-app/android/app/build/

# Capacitor KT Service - Example app iOS
capacitor-kt-service/example-app/ios/Pods/
capacitor-kt-service/example-app/ios/Podfile.lock
capacitor-kt-service/example-app/ios/.DS_Store
capacitor-kt-service/example-app/ios/App.xcworkspace/
capacitor-kt-service/example-app/ios/App.xcodeproj/project.xcworkspace/
capacitor-kt-service/example-app/ios/App.xcodeproj/xcuserdata/

# Environment variables
.env.local
.env.*.local
