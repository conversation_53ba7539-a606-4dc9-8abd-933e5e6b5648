import legacy from '@vitejs/plugin-legacy'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // 只在生产环境使用legacy插件
    ...(process.env.NODE_ENV === 'production' ? [legacy({
      targets: ['defaults', 'not IE 11']
    })] : [])
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        manualChunks: process.env.NODE_ENV === 'development' ? undefined : {
          // 开发模式不分割chunk，加快构建速度
          // 生产模式才进行代码分割优化
          vue: ['vue', 'vue-router', 'pinia'],
          ionic: ['@ionic/vue', '@ionic/core', '@ionic/vue-router'],
          mapbox: ['mapbox-gl', '@mapbox/search-js-web'],
          capacitor: ['@capacitor/core', '@capacitor/app', '@capacitor/haptics', '@capacitor/keyboard', '@capacitor/status-bar'],
          bluetooth: ['@capacitor-community/bluetooth-le'],
          utils: ['@vueuse/core', '@vueuse/components'],
          charts: ['@antv/g2']
        }
      }
    },
    // 增加chunk大小警告限制
    chunkSizeWarningLimit: 1000,
    // 开发模式禁用压缩，加快构建速度
    minify: process.env.NODE_ENV === 'development' ? false : 'terser',
    terserOptions: process.env.NODE_ENV === 'development' ? undefined : {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true
      }
    },
    // 启用源码映射（开发时有用）
    sourcemap: process.env.NODE_ENV === 'development'
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      '@ionic/vue',
      '@ionic/core',
      '@vueuse/core'
    ],
    exclude: [
      // 排除大型依赖的预构建，让它们按需加载
      'mapbox-gl',
      '@mapbox/search-js-web',
      // 排除不存在的依赖
      'capacitor-kt-plugin',
      'capacitor-mapbox-search-plugin',
      '@capacitor/geolocation',
      '@dongsp/capacitor-mapbox-navigation'
    ],
    // 排除示例应用和备份插件目录
    entries: [
      'src/**/*.{vue,ts,js}',
      '!**/example/**',
      '!**/example-app/**',
      '!backup-original-plugins/**',
      '!capacitor-kt-service/example-app/**'
    ]
  },
  // 开发服务器优化
  server: {
    fs: {
      // 允许访问工作区根目录之外的文件
      allow: ['..'],
      // 拒绝访问示例应用和备份插件
      deny: [
        '**/example/**',
        '**/example-app/**',
        'backup-original-plugins/**',
        'capacitor-kt-service/example-app/**'
      ]
    }
  }
})
