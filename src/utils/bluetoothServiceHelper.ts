import { CapacitorKtService } from 'capacitor-kt-service';

/**
 * 蓝牙服务状态接口
 */
export interface BluetoothServiceStatus {
  isServiceRunning: boolean;
  bluetoothManagerInitialized: boolean;
  lastSendingStatusCached: boolean;
  lastSendingStatsCached: boolean;
  bluetoothManagerDetails: string;
  timestamp: number;
  error?: string;
}

/**
 * 蓝牙服务助手类
 * 提供安全的服务调用和状态检查功能
 */
export class BluetoothServiceHelper {
  private static instance: BluetoothServiceHelper;
  private initializationPromise: Promise<boolean> | null = null;
  private isInitialized = false;
  private retryCount = 0;
  private maxRetries = 3;

  private constructor() {}

  public static getInstance(): BluetoothServiceHelper {
    if (!BluetoothServiceHelper.instance) {
      BluetoothServiceHelper.instance = new BluetoothServiceHelper();
    }
    return BluetoothServiceHelper.instance;
  }

  /**
   * 安全启动蓝牙前台服务
   */
  public async startBluetoothForegroundService(): Promise<void> {
    try {
      console.log('🚀 启动蓝牙前台服务...');
      await CapacitorKtService.startBluetoothForegroundService();
      
      // 等待服务初始化
      await this.waitForServiceInitialization();
      
      console.log('✅ 蓝牙前台服务启动成功');
    } catch (error) {
      console.error('❌ 启动蓝牙前台服务失败:', error);
      throw error;
    }
  }

  /**
   * 等待服务初始化完成
   */
  public async waitForServiceInitialization(timeout = 10000): Promise<boolean> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = new Promise((resolve) => {
      const startTime = Date.now();
      const checkInterval = 500; // 每500ms检查一次

      const checkStatus = async () => {
        try {
          const status = await this.getServiceInitializationStatus();
          
          if (status.bluetoothManagerInitialized) {
            console.log('✅ 蓝牙管理器初始化完成');
            this.isInitialized = true;
            resolve(true);
            return;
          }

          // 检查超时
          if (Date.now() - startTime > timeout) {
            console.warn('⚠️ 等待服务初始化超时');
            resolve(false);
            return;
          }

          // 继续等待
          setTimeout(checkStatus, checkInterval);
        } catch (error) {
          console.error('检查服务状态失败:', error);
          setTimeout(checkStatus, checkInterval);
        }
      };

      checkStatus();
    });

    return this.initializationPromise;
  }

  /**
   * 获取服务初始化状态
   */
  public async getServiceInitializationStatus(): Promise<BluetoothServiceStatus> {
    try {
      const result = await CapacitorKtService.getServiceInitializationStatus();
      return result as BluetoothServiceStatus;
    } catch (error) {
      console.error('获取服务初始化状态失败:', error);
      return {
        isServiceRunning: false,
        bluetoothManagerInitialized: false,
        lastSendingStatusCached: false,
        lastSendingStatsCached: false,
        bluetoothManagerDetails: '获取状态失败',
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 安全获取当前蓝牙发送数据
   */
  public async getCurrentBluetoothSendData(): Promise<{ success: boolean; data?: number[]; error?: string }> {
    try {
      // 检查服务是否已初始化
      if (!this.isInitialized) {
        const isReady = await this.waitForServiceInitialization(3000);
        if (!isReady) {
          return {
            success: false,
            error: '蓝牙服务未完全初始化，请稍后重试'
          };
        }
      }

      const result = await CapacitorKtService.getCurrentBluetoothSendData();
      
      // 检查返回结果的格式
      if (result && typeof result === 'object') {
        if (result.success === false && result.error) {
          console.warn('⚠️ 获取蓝牙发送数据失败:', result.error);
        }
        return result;
      }

      // 兼容旧版本返回格式
      if (result && Array.isArray(result)) {
        return {
          success: true,
          data: result as number[]
        };
      }

      return {
        success: false,
        error: '返回数据格式不正确'
      };
    } catch (error) {
      console.error('获取当前蓝牙发送数据异常:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 安全获取蓝牙发送状态
   */
  public async getBluetoothSendingStatus(): Promise<any> {
    try {
      if (!this.isInitialized) {
        const isReady = await this.waitForServiceInitialization(3000);
        if (!isReady) {
          return {
            success: false,
            error: '蓝牙服务未完全初始化',
            isActive: false,
            lastSendTime: 0,
            sendCount: 0,
            errorCount: 0
          };
        }
      }

      const result = await CapacitorKtService.isNativeBluetoothSending();
      return result;
    } catch (error) {
      console.error('获取蓝牙发送状态异常:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        isActive: false,
        lastSendTime: 0,
        sendCount: 0,
        errorCount: 0
      };
    }
  }

  /**
   * 安全获取蓝牙发送统计
   */
  public async getBluetoothSendingStats(): Promise<any> {
    try {
      if (!this.isInitialized) {
        const isReady = await this.waitForServiceInitialization(3000);
        if (!isReady) {
          return {
            success: false,
            error: '蓝牙服务未完全初始化',
            totalSent: 0,
            successCount: 0,
            errorCount: 0,
            averageInterval: 0,
            lastError: null,
            isConnected: false
          };
        }
      }

      const result = await CapacitorKtService.getBluetoothSendingStats();
      return result;
    } catch (error) {
      console.error('获取蓝牙发送统计异常:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        totalSent: 0,
        successCount: 0,
        errorCount: 0,
        averageInterval: 0,
        lastError: null,
        isConnected: false
      };
    }
  }

  /**
   * 重置初始化状态（用于服务重启后）
   */
  public resetInitializationStatus(): void {
    this.isInitialized = false;
    this.initializationPromise = null;
    this.retryCount = 0;
  }

  /**
   * 检查服务是否健康
   */
  public async isServiceHealthy(): Promise<boolean> {
    try {
      const status = await this.getServiceInitializationStatus();
      return status.isServiceRunning && status.bluetoothManagerInitialized;
    } catch (error) {
      console.error('检查服务健康状态失败:', error);
      return false;
    }
  }
}

/**
 * 导出单例实例
 */
export const bluetoothServiceHelper = BluetoothServiceHelper.getInstance();