import { useDataValidator } from '@/hooks/useDataValidator';

// 用户提供的数据
const serialData = "0F 03 F6 58 29 D1 38 CA 84 14 05 32 81 1C A1 22 CF 0E";
const androidData = "[0x0F, 0x03, 0xF6, 0x58, 0x29, 0xD1, 0x38, 0xCA, 0x84, 0x14, 0x05, 0x32, 0x81, 0x1C, 0xA1, 0x22, 0xCF, 0x0E]";
const webData = "0xof 0x05 0xf5 0x5 0x2e 0x00 0x3 0xca 0x84 0x14 0x65 0x32 0x81 0x1c 0xa1 0x22 0xad 0x0e";

export function analyzeUserData() {
  console.log("🔍 开始分析用户提供的数据差异");
  console.log("=".repeat(60));
  
  const validator = useDataValidator();
  
  // 执行数据分析
  const analysis = validator.analyzeDataIssues(serialData, androidData, webData);
  
  console.log("\n🎯 关键发现:");
  
  // 分析关键差异
  if (!analysis.serialVsAndroid.isMatch) {
    console.log("❌ 串口数据与Android数据不匹配");
    console.log("   这表明Android端可能没有正确接收或处理数据");
  } else {
    console.log("✅ 串口数据与Android数据匹配");
  }
  
  if (!analysis.serialVsWeb.isMatch) {
    console.log("❌ 串口数据与Web端数据不匹配");
    console.log("   这是主要问题 - Web端数据与预期不符");
    
    // 分析具体的差异点
    const criticalDifferences = analysis.serialVsWeb.differences.filter(diff => 
      diff.position <= 11 // 前12个字节是设置数据
    );
    
    if (criticalDifferences.length > 0) {
      console.log("\n🚨 关键设置数据差异:");
      criticalDifferences.forEach(diff => {
        console.log(`   位置${diff.position} (${diff.description}): 期望 ${diff.expected}, 实际 ${diff.actual}`);
        
        // 分析可能的原因
        switch(diff.position) {
          case 1:
            console.log("     -> 可能是档位或灯光状态设置问题");
            break;
          case 2:
            console.log("     -> 可能是最大速度或轮径设置问题");
            break;
          case 4:
            console.log("     -> 可能是P2/P3/P4参数设置问题");
            break;
          case 5:
            console.log("     -> 校验和错误，表明前面的数据计算有误");
            break;
          case 10:
            console.log("     -> 可能是C13再生制动设置问题");
            break;
        }
      });
    }
  }
  
  console.log("\n💡 建议的修复步骤:");
  console.log("1. 检查settingPage.vue中的数据保存流程");
  console.log("2. 验证useSetting.ts中的updateSetting方法");
  console.log("3. 确认useNativeBluetoothMessage.ts中的数据同步");
  console.log("4. 检查Android原生端的数据更新逻辑");
  
  return analysis;
}

// 立即执行分析（用于调试）
if (typeof window !== 'undefined') {
  // 在浏览器环境中执行
  setTimeout(() => {
    try {
      analyzeUserData();
    } catch (error) {
      console.error("数据分析失败:", error);
    }
  }, 1000);
}