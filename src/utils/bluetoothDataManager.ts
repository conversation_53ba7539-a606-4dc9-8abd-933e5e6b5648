import { isPlatform } from '@ionic/vue';
import { ref } from 'vue';

/**
 * 蓝牙数据管理器
 * 统一管理不同平台的蓝牙数据发送，避免重复调用和数据不一致
 *
 * 项目蓝牙架构：
 * - traditional: iOS/Web 使用 @capacitor-community/bluetooth-le (useMessage)
 * - native: Android 使用 capacitor-kt-service (useNativeBluetoothMessage)
 * - useSmartBluetoothMessage: 统一API接口层，为上层组件提供一致的蓝牙操作接口
 *
 * 数据管理器直接操作底层的两种方案，专注于数据协调和更新
 */
export class BluetoothDataManager {
  private static instance: BluetoothDataManager | null = null;

  // 当前活跃的蓝牙方案
  private activeScheme = ref<'traditional' | 'native' | null>(null);

  // 蓝牙方案实例缓存
  private schemeInstances: {
    traditional?: any;  // useMessage hook 实例
    native?: any;       // useNativeBluetoothMessage hook 实例
  } = {};
  
  // 数据更新状态
  private isUpdating = ref(false);
  private lastUpdateTime = ref(0);
  
  private constructor() {}
  
  /**
   * 获取单例实例
   */
  public static getInstance(): BluetoothDataManager {
    if (!BluetoothDataManager.instance) {
      BluetoothDataManager.instance = new BluetoothDataManager();
    }
    return BluetoothDataManager.instance;
  }
  
  /**
   * 初始化蓝牙方案实例
   * 直接导入两种基础蓝牙方案，无需智能选择器
   */
  private async initializeSchemeInstances() {
    try {
      // 动态导入两种蓝牙方案
      const [
        { useMessage },
        { useNativeBluetoothMessage }
      ] = await Promise.all([
        import("@/hooks/useMessage"),
        import("@/hooks/useNativeBluetoothMessage")
      ]);

      this.schemeInstances = {
        traditional: useMessage(),    // iOS/Web 方案
        native: useNativeBluetoothMessage()  // Android 方案
      };

      console.log("✅ 蓝牙方案实例初始化完成");
    } catch (error) {
      console.error("❌ 初始化蓝牙方案实例失败:", error);
      throw error;
    }
  }
  
  /**
   * 检测当前活跃的蓝牙方案
   * 根据平台和服务运行状态智能选择方案
   */
  private async detectActiveScheme(): Promise<'traditional' | 'native' | null> {
    try {
      if (!this.schemeInstances.traditional) {
        await this.initializeSchemeInstances();
      }

      const { traditional, native } = this.schemeInstances;

      // 检查各方案的运行状态
      const serviceStates = {
        native: native?.isNativeSending?.value ?? false,
        traditional: traditional?.isServiceRunning?.value ?? false
      };

      console.log("📊 蓝牙方案服务状态:", serviceStates);

      // 优先选择正在运行的方案
      if (serviceStates.native) {
        console.log("🎯 检测到原生方案正在运行");
        return 'native';
      } else if (serviceStates.traditional) {
        console.log("🎯 检测到传统方案正在运行");
        return 'traditional';
      }

      // 如果没有活跃服务，根据平台返回默认方案
      const isAndroid = isPlatform('android');
      const defaultScheme = isAndroid ? 'native' : 'traditional';
      console.log(`🎯 没有活跃服务，根据平台选择默认方案: ${defaultScheme}`);
      return defaultScheme;

    } catch (error) {
      console.error("❌ 检测活跃蓝牙方案失败:", error);
      return null;
    }
  }
  
  /**
   * 统一的蓝牙数据更新方法
   * @param forceUpdate 是否强制更新（即使没有活跃服务）
   * @param updateSettings 是否先更新设置数据
   */
  public async updateBluetoothData(forceUpdate = false, updateSettings = false): Promise<boolean> {
    // 防止重复更新
    if (this.isUpdating.value) {
      console.log("⚠️ 蓝牙数据正在更新中，跳过重复调用");
      return false;
    }

    this.isUpdating.value = true;

    try {
      console.log("🔧 开始统一蓝牙数据更新");

      // 如果需要，先更新设置数据
      if (updateSettings) {
        console.log("🔧 更新设置数据到writeData");
        try {
          // 对于原生方案，需要确保其内部的writeDataRef也被更新
          if (this.schemeInstances.native?.updateSetting) {
            console.log("🔧 更新原生方案的设置数据");
            this.schemeInstances.native.updateSetting();
          }

          // 对于其他方案，使用通用的useSetting
          const { useSetting } = await import("@/hooks/useSetting");
          const { updateSetting } = useSetting();
          updateSetting();
          console.log("✅ 设置数据已更新");
        } catch (error) {
          console.error("❌ 更新设置数据失败:", error);
          // 继续执行，不因为设置更新失败而中断
        }
      }

      // 检测当前活跃方案
      const activeScheme = await this.detectActiveScheme();
      this.activeScheme.value = activeScheme;

      if (!activeScheme) {
        console.warn("⚠️ 未检测到任何蓝牙方案");
        return false;
      }

      console.log(`🎯 使用 ${activeScheme} 方案更新数据`);

      // 根据活跃方案更新数据
      let updateSuccess = false;
      const schemeInstance = this.schemeInstances[activeScheme];

      switch (activeScheme) {
        case 'native':
          if (schemeInstance?.updateNativeBluetoothData) {
            await schemeInstance.updateNativeBluetoothData();
            updateSuccess = true;
            console.log("✅ 原生蓝牙方案数据已更新");
          }
          break;

        case 'traditional':
          if (schemeInstance?.updateSendDataCache) {
            await schemeInstance.updateSendDataCache();
            updateSuccess = true;
            console.log("✅ 传统蓝牙方案数据已更新");
          }
          break;
      }

      if (updateSuccess) {
        this.lastUpdateTime.value = Date.now();

        // 发送全局更新事件
        window.dispatchEvent(new CustomEvent('bluetoothDataUpdated', {
          detail: {
            activeScheme,
            timestamp: this.lastUpdateTime.value,
            source: 'BluetoothDataManager.updateBluetoothData',
            forceUpdate,
            updateSettings
          }
        }));

        console.log(`✅ 蓝牙数据更新完成 - 方案: ${activeScheme}`);
        return true;
      } else {
        console.warn(`⚠️ ${activeScheme} 方案数据更新失败`);
        return false;
      }

    } catch (error) {
      console.error("❌ 统一蓝牙数据更新失败:", error);
      return false;
    } finally {
      this.isUpdating.value = false;
    }
  }
  
  /**
   * 获取当前活跃方案
   */
  public getActiveScheme() {
    return this.activeScheme.value;
  }
  
  /**
   * 获取更新状态
   */
  public getUpdateStatus() {
    return {
      isUpdating: this.isUpdating.value,
      lastUpdateTime: this.lastUpdateTime.value,
      activeScheme: this.activeScheme.value
    };
  }
  
  /**
   * 重置管理器状态
   */
  public reset() {
    this.activeScheme.value = null;
    this.isUpdating.value = false;
    this.lastUpdateTime.value = 0;
    this.schemeInstances = {};
  }
}

/**
 * 导出单例实例
 */
export const bluetoothDataManager = BluetoothDataManager.getInstance();
