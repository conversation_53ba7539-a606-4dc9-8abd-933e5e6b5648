/**
 * 蓝牙数据验证工具
 * 提供全面的数据验证、比对和修复功能
 */

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
  details: {
    protocol: { valid: boolean, issues: string[] }
    navigation: { valid: boolean, issues: string[] }
    checksum: { valid: boolean, expected: number, actual: number }
    mirror: { valid: boolean, expected: boolean, actual: boolean }
  }
}

export interface ComparisonResult {
  isMatch: boolean
  differences: Array<{
    index: number
    field: string
    expected: number
    actual: number
    severity: 'error' | 'warning' | 'info'
  }>
  analysis: {
    protocolConsistency: number // 0-1
    navigationConsistency: number // 0-1
    overallConsistency: number // 0-1
  }
  recommendations: string[]
}

/**
 * 验证蓝牙数据包的完整性
 */
export function validateBluetoothData(data: number[]): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
    details: {
      protocol: { valid: true, issues: [] },
      navigation: { valid: true, issues: [] },
      checksum: { valid: true, expected: 0, actual: 0 },
      mirror: { valid: true, expected: false, actual: false }
    }
  }

  // 1. 基本结构验证
  if (!data || !Array.isArray(data)) {
    result.isValid = false
    result.errors.push('数据不是有效的数组')
    return result
  }

  if (data.length !== 18) {
    result.isValid = false
    result.errors.push(`数据长度错误: 期望18字节，实际${data.length}字节`)
    return result
  }

  // 2. 数据范围验证
  for (let i = 0; i < data.length; i++) {
    const byte = data[i]
    if (typeof byte !== 'number' || isNaN(byte) || byte < 0 || byte > 255) {
      result.isValid = false
      result.errors.push(`字节${i}值无效: ${byte}，应在0-255范围内`)
    }
  }

  // 3. 协议头验证
  const expectedProtocolHeader = [0xf, 0x5, 0xf5, 0x58, 0x2e, 0x0]
  for (let i = 0; i < 6; i++) {
    if (data[i] !== expectedProtocolHeader[i]) {
      result.details.protocol.valid = false
      result.details.protocol.issues.push(
        `协议头字节${i}不匹配: 期望0x${expectedProtocolHeader[i].toString(16).toUpperCase()}, 实际0x${data[i].toString(16).toUpperCase()}`
      )
      result.warnings.push(`协议头可能已损坏`)
    }
  }

  // 4. 截止位验证
  if (data[17] !== 0x0e) {
    result.details.protocol.valid = false
    result.details.protocol.issues.push(`截止位错误: 期望0x0E, 实际0x${data[17].toString(16).toUpperCase()}`)
    result.errors.push('数据包截止位错误')
  }

  // 5. 校验和验证
  const { isValid: checksumValid, expected, actual } = validateChecksum(data)
  result.details.checksum = { valid: checksumValid, expected, actual }
  
  if (!checksumValid) {
    result.isValid = false
    result.errors.push(`校验和错误: 期望0x${expected.toString(16).toUpperCase()}, 实际0x${actual.toString(16).toUpperCase()}`)
    result.suggestions.push('重新计算并更新校验和')
  }

  // 6. 导航数据验证
  const navigationValidation = validateNavigationData(data.slice(12, 16))
  if (!navigationValidation.isValid) {
    result.details.navigation.valid = false
    result.details.navigation.issues = navigationValidation.issues
    result.warnings.push(...navigationValidation.issues)
  }

  // 7. 镜像位验证
  const mirrorBit = (data[12] & 0x80) !== 0
  result.details.mirror.actual = mirrorBit

  return result
}

/**
 * 验证校验和
 */
export function validateChecksum(data: number[]): { isValid: boolean, expected: number, actual: number } {
  let expected = 0
  
  // 按照协议规范计算校验和：对字节1-15进行异或运算（跳过字节0和字节5）
  const bytesToCheck = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
  
  for (const byteIndex of bytesToCheck) {
    if (data[byteIndex] !== undefined) {
      expected ^= data[byteIndex]
    }
  }
  
  expected &= 0xFF
  const actual = data[16] || 0
  
  return {
    isValid: expected === actual,
    expected,
    actual
  }
}

/**
 * 验证导航数据部分
 */
function validateNavigationData(navData: number[]): { isValid: boolean, issues: string[] } {
  const issues: string[] = []
  
  if (navData.length !== 4) {
    issues.push(`导航数据长度错误: 期望4字节, 实际${navData.length}字节`)
    return { isValid: false, issues }
  }

  // 验证方向值
  const direction = navData[0] & 0x0F
  if (direction < 1 || direction > 9) {
    issues.push(`方向值异常: ${direction}, 应在1-9范围内`)
  }

  // 验证距离规则
  const singleRule = (navData[0] >> 4) & 0x03
  const totalRule = (navData[2] >> 4) & 0x03
  
  if (singleRule > 3) {
    issues.push(`单次距离规则异常: ${singleRule}, 应在0-3范围内`)
  }
  
  if (totalRule > 3) {
    issues.push(`总距离规则异常: ${totalRule}, 应在0-3范围内`)
  }

  // 验证高位数据
  const singleHigh = (navData[2] >> 6) & 0x03
  const totalHigh = navData[2] & 0x03
  
  if (singleHigh > 3) {
    issues.push(`单次距离高位异常: ${singleHigh}`)
  }
  
  if (totalHigh > 3) {
    issues.push(`总距离高位异常: ${totalHigh}`)
  }

  return { isValid: issues.length === 0, issues }
}

/**
 * 比较两个蓝牙数据包
 */
export function compareBluetoothData(expected: number[], actual: number[]): ComparisonResult {
  const differences: ComparisonResult['differences'] = []
  const recommendations: string[] = []

  // 基本长度检查
  if (expected.length !== actual.length) {
    recommendations.push(`数据长度不一致: 期望${expected.length}字节, 实际${actual.length}字节`)
  }

  const maxLength = Math.max(expected.length, actual.length)
  let protocolDiffs = 0
  let navigationDiffs = 0

  // 逐字节比较
  for (let i = 0; i < maxLength; i++) {
    const expectedByte = expected[i] || 0
    const actualByte = actual[i] || 0

    if (expectedByte !== actualByte) {
      let field = '未知'
      let severity: 'error' | 'warning' | 'info' = 'info'

      // 确定字段类型和严重程度
      if (i <= 11) {
        field = i <= 5 ? '协议头' : '协议数据'
        protocolDiffs++
        severity = 'warning'
      } else if (i >= 12 && i <= 15) {
        field = getNavigationFieldName(i)
        navigationDiffs++
        severity = 'error'
      } else if (i === 16) {
        field = '校验和'
        severity = 'error'
      } else if (i === 17) {
        field = '截止位'
        severity = 'error'
      }

      differences.push({
        index: i,
        field,
        expected: expectedByte,
        actual: actualByte,
        severity
      })
    }
  }

  // 计算一致性指标
  const totalBytes = Math.max(expected.length, actual.length)
  const protocolBytes = 12
  const navigationBytes = 4
  
  const protocolConsistency = protocolBytes > 0 ? 1 - (protocolDiffs / protocolBytes) : 1
  const navigationConsistency = navigationBytes > 0 ? 1 - (navigationDiffs / navigationBytes) : 1
  const overallConsistency = totalBytes > 0 ? 1 - (differences.length / totalBytes) : 1

  // 生成建议
  if (protocolDiffs > 0) {
    recommendations.push('协议部分存在差异，建议检查设备设置')
  }
  
  if (navigationDiffs > 0) {
    recommendations.push('导航数据不一致，建议检查导航逻辑')
  }
  
  if (differences.some(d => d.field === '校验和')) {
    recommendations.push('校验和不匹配，建议重新计算')
  }

  return {
    isMatch: differences.length === 0,
    differences,
    analysis: {
      protocolConsistency,
      navigationConsistency,
      overallConsistency
    },
    recommendations
  }
}

/**
 * 获取导航字段名称
 */
function getNavigationFieldName(index: number): string {
  const fieldNames: { [key: number]: string } = {
    12: '方向+规则',
    13: '单次距离低位',
    14: '距离高位+规则',
    15: '总距离低位'
  }
  return fieldNames[index] || `字节${index}`
}

/**
 * 修复蓝牙数据包
 */
export function repairBluetoothData(data: number[], options: {
  fixChecksum?: boolean
  fixProtocolHeader?: boolean
  fixEndByte?: boolean
} = {}): { repaired: number[], changes: string[] } {
  const repaired = [...data]
  const changes: string[] = []

  // 修复协议头
  if (options.fixProtocolHeader) {
    const expectedHeader = [0xf, 0x5, 0xf5, 0x58, 0x2e, 0x0]
    for (let i = 0; i < 6; i++) {
      if (repaired[i] !== expectedHeader[i]) {
        repaired[i] = expectedHeader[i]
        changes.push(`修复协议头字节${i}: 0x${data[i].toString(16).toUpperCase()} -> 0x${expectedHeader[i].toString(16).toUpperCase()}`)
      }
    }
  }

  // 修复截止位
  if (options.fixEndByte && repaired[17] !== 0x0e) {
    const oldValue = repaired[17]
    repaired[17] = 0x0e
    changes.push(`修复截止位: 0x${oldValue.toString(16).toUpperCase()} -> 0x0E`)
  }

  // 修复校验和（应该最后执行）
  if (options.fixChecksum) {
    const { expected } = validateChecksum(repaired)
    if (repaired[16] !== expected) {
      const oldValue = repaired[16]
      repaired[16] = expected
      changes.push(`修复校验和: 0x${oldValue.toString(16).toUpperCase()} -> 0x${expected.toString(16).toUpperCase()}`)
    }
  }

  return { repaired, changes }
}

/**
 * 生成数据诊断报告
 */
export function generateDiagnosticReport(
  vueData: number[], 
  nativeData: number[], 
  metadata?: any
): string {
  const vueValidation = validateBluetoothData(vueData)
  const nativeValidation = validateBluetoothData(nativeData)
  const comparison = compareBluetoothData(vueData, nativeData)

  let report = `# 蓝牙数据诊断报告\n\n`
  report += `生成时间: ${new Date().toLocaleString()}\n\n`

  // Vue端数据分析
  report += `## Vue端数据分析\n`
  report += `- 数据有效性: ${vueValidation.isValid ? '✅ 有效' : '❌ 无效'}\n`
  report += `- 错误数量: ${vueValidation.errors.length}\n`
  report += `- 警告数量: ${vueValidation.warnings.length}\n`
  if (vueValidation.errors.length > 0) {
    report += `- 错误详情: ${vueValidation.errors.join(', ')}\n`
  }
  report += `- 数据: ${vueData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ')}\n\n`

  // Native端数据分析
  report += `## Native端数据分析\n`
  report += `- 数据有效性: ${nativeValidation.isValid ? '✅ 有效' : '❌ 无效'}\n`
  report += `- 错误数量: ${nativeValidation.errors.length}\n`
  report += `- 警告数量: ${nativeValidation.warnings.length}\n`
  if (nativeValidation.errors.length > 0) {
    report += `- 错误详情: ${nativeValidation.errors.join(', ')}\n`
  }
  report += `- 数据: ${nativeData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ')}\n\n`

  // 数据比对分析
  report += `## 数据比对分析\n`
  report += `- 数据匹配: ${comparison.isMatch ? '✅ 完全匹配' : '❌ 存在差异'}\n`
  report += `- 差异数量: ${comparison.differences.length}\n`
  report += `- 协议一致性: ${(comparison.analysis.protocolConsistency * 100).toFixed(1)}%\n`
  report += `- 导航一致性: ${(comparison.analysis.navigationConsistency * 100).toFixed(1)}%\n`
  report += `- 整体一致性: ${(comparison.analysis.overallConsistency * 100).toFixed(1)}%\n\n`

  // 差异详情
  if (comparison.differences.length > 0) {
    report += `### 差异详情\n`
    comparison.differences.forEach(diff => {
      report += `- 字节${diff.index} (${diff.field}): 期望0x${diff.expected.toString(16).toUpperCase()}, 实际0x${diff.actual.toString(16).toUpperCase()} [${diff.severity}]\n`
    })
    report += `\n`
  }

  // 修复建议
  if (comparison.recommendations.length > 0) {
    report += `## 修复建议\n`
    comparison.recommendations.forEach((rec, index) => {
      report += `${index + 1}. ${rec}\n`
    })
    report += `\n`
  }

  // 元数据
  if (metadata) {
    report += `## 元数据\n`
    report += `\`\`\`json\n${JSON.stringify(metadata, null, 2)}\n\`\`\`\n\n`
  }

  return report
}