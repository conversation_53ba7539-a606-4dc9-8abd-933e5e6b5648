/**
 * 数据验证工具测试
 * 用于验证 dataValidation.ts 中的各项功能
 */

import { 
  validateBluetoothData, 
  compareBluetoothData, 
  repairBluetoothData,
  generateDiagnosticReport 
} from './dataValidation'

// 测试用的蓝牙数据
export const testBluetoothData = {
  // 正常的蓝牙数据包
  valid: [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x01, 0x96, 0x10, 0x64, 0x37, 0x0e],
  
  // 校验和错误的数据包
  invalidChecksum: [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x01, 0x96, 0x10, 0x64, 0xFF, 0x0e],
  
  // 协议头错误的数据包
  invalidHeader: [0xFF, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x01, 0x96, 0x10, 0x64, 0x37, 0x0e],
  
  // 截止位错误的数据包
  invalidEndByte: [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x01, 0x96, 0x10, 0x64, 0x37, 0xFF],
  
  // 导航数据不同的数据包
  differentNavigation: [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x82, 0x32, 0x20, 0x28, 0x6C, 0x0e]
}

/**
 * 运行所有测试
 */
export function runDataValidationTests(): void {
  console.log('🧪 开始运行数据验证工具测试...\n')
  
  // 测试1: 验证正常数据
  console.log('📋 测试1: 验证正常数据')
  const validResult = validateBluetoothData(testBluetoothData.valid)
  console.log('结果:', validResult.isValid ? '✅ 通过' : '❌ 失败')
  if (!validResult.isValid) {
    console.log('错误:', validResult.errors)
  }
  console.log()
  
  // 测试2: 验证校验和错误的数据
  console.log('📋 测试2: 验证校验和错误的数据')
  const invalidChecksumResult = validateBluetoothData(testBluetoothData.invalidChecksum)
  console.log('结果:', !invalidChecksumResult.isValid ? '✅ 通过（正确检测到错误）' : '❌ 失败')
  if (!invalidChecksumResult.isValid) {
    console.log('检测到的错误:', invalidChecksumResult.errors)
  }
  console.log()
  
  // 测试3: 数据比较
  console.log('📋 测试3: 数据比较')
  const comparisonResult = compareBluetoothData(testBluetoothData.valid, testBluetoothData.differentNavigation)
  console.log('结果:', !comparisonResult.isMatch ? '✅ 通过（正确检测到差异）' : '❌ 失败')
  console.log('差异数量:', comparisonResult.differences.length)
  console.log('整体一致性:', `${(comparisonResult.analysis.overallConsistency * 100).toFixed(1)}%`)
  console.log()
  
  // 测试4: 自动修复
  console.log('📋 测试4: 自动修复')
  const repairResult = repairBluetoothData(testBluetoothData.invalidChecksum, {
    fixChecksum: true,
    fixProtocolHeader: true,
    fixEndByte: true
  })
  console.log('修复变更数量:', repairResult.changes.length)
  console.log('修复变更:', repairResult.changes)
  
  // 验证修复后的数据
  const repairedValidation = validateBluetoothData(repairResult.repaired)
  console.log('修复后数据有效性:', repairedValidation.isValid ? '✅ 有效' : '❌ 无效')
  console.log()
  
  // 测试5: 诊断报告生成
  console.log('📋 测试5: 诊断报告生成')
  const diagnosticReport = generateDiagnosticReport(
    testBluetoothData.valid, 
    testBluetoothData.differentNavigation,
    { testType: 'unit_test', timestamp: new Date().toISOString() }
  )
  console.log('诊断报告生成:', diagnosticReport.length > 0 ? '✅ 成功' : '❌ 失败')
  console.log('报告长度:', diagnosticReport.length, '字符')
  console.log()
  
  console.log('🎉 数据验证工具测试完成！')
}

/**
 * 测试特定的数据包
 */
export function testSpecificData(vueData: number[], nativeData: number[]): void {
  console.log('🔍 测试特定数据包...')
  
  // 验证Vue端数据
  const vueValidation = validateBluetoothData(vueData)
  console.log('Vue端数据有效性:', vueValidation.isValid ? '✅ 有效' : '❌ 无效')
  if (!vueValidation.isValid) {
    console.log('Vue端错误:', vueValidation.errors)
  }
  
  // 验证Native端数据
  const nativeValidation = validateBluetoothData(nativeData)
  console.log('Native端数据有效性:', nativeValidation.isValid ? '✅ 有效' : '❌ 无效')
  if (!nativeValidation.isValid) {
    console.log('Native端错误:', nativeValidation.errors)
  }
  
  // 比较数据
  const comparison = compareBluetoothData(vueData, nativeData)
  console.log('数据匹配:', comparison.isMatch ? '✅ 完全匹配' : '❌ 存在差异')
  console.log('差异数量:', comparison.differences.length)
  console.log('整体一致性:', `${(comparison.analysis.overallConsistency * 100).toFixed(1)}%`)
  
  if (comparison.differences.length > 0) {
    console.log('差异详情:')
    comparison.differences.forEach(diff => {
      console.log(`  字节${diff.index} (${diff.field}): 期望0x${diff.expected.toString(16).toUpperCase()}, 实际0x${diff.actual.toString(16).toUpperCase()}`)
    })
  }
  
  // 生成诊断报告
  const report = generateDiagnosticReport(vueData, nativeData, {
    testType: 'specific_test',
    timestamp: new Date().toISOString()
  })
  
  console.log('\n📋 诊断报告:')
  console.log(report)
}

/**
 * 性能测试
 */
export function performanceTest(): void {
  console.log('⚡ 开始性能测试...')
  
  const iterations = 1000
  const testData = testBluetoothData.valid
  
  // 测试验证性能
  const validationStart = performance.now()
  for (let i = 0; i < iterations; i++) {
    validateBluetoothData(testData)
  }
  const validationEnd = performance.now()
  const validationTime = validationEnd - validationStart
  
  console.log(`验证性能: ${iterations}次验证耗时 ${validationTime.toFixed(2)}ms`)
  console.log(`平均每次验证: ${(validationTime / iterations).toFixed(4)}ms`)
  
  // 测试比较性能
  const comparisonStart = performance.now()
  for (let i = 0; i < iterations; i++) {
    compareBluetoothData(testData, testBluetoothData.differentNavigation)
  }
  const comparisonEnd = performance.now()
  const comparisonTime = comparisonEnd - comparisonStart
  
  console.log(`比较性能: ${iterations}次比较耗时 ${comparisonTime.toFixed(2)}ms`)
  console.log(`平均每次比较: ${(comparisonTime / iterations).toFixed(4)}ms`)
  
  console.log('✅ 性能测试完成')
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runDataValidationTests()
  performanceTest()
}