import { ref, watch } from 'vue';
import { useDataValidator } from '@/hooks/useDataValidator';

interface DiagnosticLog {
  timestamp: number;
  source: string;
  action: string;
  data: number[];
  hexString: string;
  isValid: boolean;
  notes?: string;
}

export function useRuntimeDiagnostics() {
  const diagnosticLogs = ref<DiagnosticLog[]>([]);
  const isMonitoring = ref(false);
  const validator = useDataValidator();
  
  // 记录诊断日志
  const logDiagnostic = (source: string, action: string, data: number[], notes?: string) => {
    const hexString = validator.arrayToHexString(data);
    const checksum = validator.validateChecksum(data);
    
    const log: DiagnosticLog = {
      timestamp: Date.now(),
      source,
      action,
      data: [...data],
      hexString,
      isValid: checksum.isValid,
      notes
    };
    
    diagnosticLogs.value.push(log);
    
    // 限制日志数量，避免内存泄漏
    if (diagnosticLogs.value.length > 100) {
      diagnosticLogs.value = diagnosticLogs.value.slice(-50);
    }
    
    console.log(`🔍 [${source}] ${action}:`, {
      hex: hexString,
      valid: checksum.isValid ? '✅' : '❌',
      notes: notes || '',
      timestamp: new Date(log.timestamp).toISOString()
    });
    
    return log;
  };
  
  // 开始监控
  const startMonitoring = () => {
    isMonitoring.value = true;
    console.log("🔍 开始运行时诊断监控");
  };
  
  // 停止监控
  const stopMonitoring = () => {
    isMonitoring.value = false;
    console.log("🔍 停止运行时诊断监控");
  };
  
  // 清除日志
  const clearLogs = () => {
    diagnosticLogs.value = [];
    console.log("🔍 诊断日志已清除");
  };
  
  // 导出日志
  const exportLogs = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      logs: diagnosticLogs.value.map(log => ({
        ...log,
        timestamp: new Date(log.timestamp).toISOString()
      }))
    };
    
    const jsonString = JSON.stringify(exportData, null, 2);
    console.log("📄 诊断日志导出:");
    console.log(jsonString);
    
    return jsonString;
  };
  
  // 分析数据流
  const analyzeDataFlow = () => {
    if (diagnosticLogs.value.length === 0) {
      console.log("🔍 没有可分析的数据");
      return;
    }
    
    console.log("🔍 数据流分析:");
    console.log(`总日志数: ${diagnosticLogs.value.length}`);
    
    // 按来源分组
    const sourceGroups = diagnosticLogs.value.reduce((groups, log) => {
      if (!groups[log.source]) {
        groups[log.source] = [];
      }
      groups[log.source].push(log);
      return groups;
    }, {} as Record<string, DiagnosticLog[]>);
    
    Object.entries(sourceGroups).forEach(([source, logs]) => {
      const validCount = logs.filter(log => log.isValid).length;
      const invalidCount = logs.length - validCount;
      
      console.log(`📊 ${source}: ${logs.length}条记录, ${validCount}条有效, ${invalidCount}条无效`);
      
      // 显示最近的几条记录
      const recentLogs = logs.slice(-3);
      recentLogs.forEach(log => {
        console.log(`   ${log.action}: ${log.hexString} ${log.isValid ? '✅' : '❌'}`);
      });
    });
    
    // 检查数据一致性
    const uniqueData = new Set(diagnosticLogs.value.map(log => log.hexString));
    console.log(`🔍 发现 ${uniqueData.size} 种不同的数据模式`);
    
    if (uniqueData.size > 5) {
      console.warn("⚠️ 数据模式过多，可能存在数据不一致问题");
    }
  };
  
  // 比较两个时间点的数据
  const compareDataAtTimes = (time1: number, time2: number) => {
    const log1 = diagnosticLogs.value.find(log => Math.abs(log.timestamp - time1) < 1000);
    const log2 = diagnosticLogs.value.find(log => Math.abs(log.timestamp - time2) < 1000);
    
    if (!log1 || !log2) {
      console.log("🔍 无法找到指定时间点的数据");
      return;
    }
    
    const comparison = validator.compareData(log1.data, log2.data, [
      `${log1.source}-${log1.action}`,
      `${log2.source}-${log2.action}`
    ]);
    
    console.log("🔍 数据比较结果:");
    console.log(comparison.summary);
    
    if (!comparison.isMatch) {
      comparison.differences.forEach(diff => {
        console.log(`   位置${diff.position} (${diff.description}): ${diff.expected} → ${diff.actual}`);
      });
    }
    
    return comparison;
  };
  
  // 监控特定数据变化
  const watchDataChanges = (watchCallback?: (oldData: number[], newData: number[]) => void) => {
    let lastData: number[] | null = null;
    
    return (data: number[], source: string, action: string) => {
      logDiagnostic(source, action, data);
      
      if (lastData && watchCallback) {
        const hasChanged = JSON.stringify(lastData) !== JSON.stringify(data);
        if (hasChanged) {
          watchCallback(lastData, data);
        }
      }
      
      lastData = [...data];
    };
  };
  
  // 获取统计信息
  const getStatistics = () => {
    const total = diagnosticLogs.value.length;
    const valid = diagnosticLogs.value.filter(log => log.isValid).length;
    const invalid = total - valid;
    
    const sources = [...new Set(diagnosticLogs.value.map(log => log.source))];
    const actions = [...new Set(diagnosticLogs.value.map(log => log.action))];
    
    return {
      total,
      valid,
      invalid,
      validRate: total > 0 ? (valid / total * 100).toFixed(2) + '%' : '0%',
      sources: sources.length,
      actions: actions.length,
      timeSpan: total > 0 ? {
        start: new Date(diagnosticLogs.value[0].timestamp).toISOString(),
        end: new Date(diagnosticLogs.value[total - 1].timestamp).toISOString()
      } : null
    };
  };
  
  return {
    // 状态
    diagnosticLogs,
    isMonitoring,
    
    // 方法
    logDiagnostic,
    startMonitoring,
    stopMonitoring,
    clearLogs,
    exportLogs,
    analyzeDataFlow,
    compareDataAtTimes,
    watchDataChanges,
    getStatistics
  };
}

// 全局诊断实例
export const globalDiagnostics = useRuntimeDiagnostics();