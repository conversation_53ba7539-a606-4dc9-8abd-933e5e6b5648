<template>
  <ion-item>
    <ion-input :id="modalId" readonly :value="displayValue" :label="label" :name="name"
      :placeholder="placeholder">
      <ion-icon slot="end" :icon="chevronForwardOutline"></ion-icon>
    </ion-input>

    <ion-modal ref="modal" :trigger="modalId" @didDismiss="onDidDismiss">
      <ion-header>
        <ion-toolbar>
          <ion-title>{{ label }}</ion-title>
          <ion-buttons slot="start">
            <ion-button @click="onCancel">Cancel</ion-button>
          </ion-buttons>
          <ion-buttons slot="end">
            <ion-button color="primary" @click="onConfirm">OK</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-picker>
        <ion-picker-column ref="pickerColumn" :value="selectedValue" @ionChange="onIonChange">
          <ion-picker-column-option v-for="item in list" :key="item.value" :value="item.value">
            {{ item.name }}
          </ion-picker-column-option>
        </ion-picker-column>
      </ion-picker>
    </ion-modal>
  </ion-item>
</template>

<script setup lang="ts">
import { ComponentPublicInstance, ref, computed } from 'vue'
import {
  IonTitle,
  IonItem,
  IonModal,
  IonToolbar,
  IonButtons,
  IonButton,
  IonPicker,
  IonPickerColumn,
  IonPickerColumnOption,
  IonInput,
  IonHeader,
  IonIcon
} from '@ionic/vue'
import { chevronForwardOutline } from 'ionicons/icons'

interface Props {
  modelValue: number | string
  list: Array<{ name: string; value: number | string }>
  label?: string
  name?: string
  placeholder?: string
  modalName?: string
}

const props = withDefaults(defineProps<Props>(), {
  label: 'Select',
  name: '',
  placeholder: 'Please select',
  modalName: 'modal'
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | string): void
}>()

const modal = ref<ComponentPublicInstance | null>(null)
const selectedValue = ref(props.modelValue)
const pickerColumn = ref<ComponentPublicInstance | null>(null)

const modalId = computed(() => `open-modal-${props.modalName}-${Math.random().toString(36).slice(2, 11)}`)

const displayValue = computed(() => {
  const selectedItem = props.list.find(item => item.value === props.modelValue)
  return selectedItem?.name || ''
})

const onIonChange = (event: CustomEvent) => { // TODO: onIonChange 不起作用
  selectedValue.value = event.detail.value
}

const onDidDismiss = (event: CustomEvent) => {
  if (event.detail.role === 'confirm') {
    emit('update:modelValue', event.detail.data)
  }
}

const onCancel = () => {
  modal.value?.$el.dismiss(null, 'cancel')
}

const onConfirm = () => {
  const activeItem = pickerColumn.value?.$el.activeItem
  if (activeItem) {
    selectedValue.value = activeItem.value
  }
  modal.value?.$el.dismiss(selectedValue.value, 'confirm')
}
</script>

<style lang="scss" scoped>
ion-modal {
  --height: auto;

  align-items: end;
}

ion-title {
  text-align: center;
}

ion-picker {
  margin-bottom: var(--ion-safe-area-bottom);
}

.sc-ion-input-ios-s>[slot=end]:first-of-type {
  margin-inline-start: 4px;
  fill: #828384;
}
.sc-ion-input-md-s>[slot=end]:first-of-type {
  margin-inline-start: 4px;
  fill: #828384;
}
</style>
