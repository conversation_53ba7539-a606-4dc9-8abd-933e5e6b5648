<template>
  <ion-modal :is-open="visible" :showBackdrop="true" :initial-breakpoint="0.25" :breakpoints="[0, 0.25, 0.5, 0.75]"
    @didDismiss="emit('update:visible', false)">
    <ion-header class="ion-no-border">
      <ion-toolbar>
        <ion-title>February 24th 06:21 PM - 07:32 PM</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding ride-analysis">
      <!-- <div class="ride-info">
        <div class="info-item">
          <div class="value">{{ rideData.distance }}km</div>
          <div class="label">Distance</div>
        </div>
        <div class="info-item">
          <div class="value">{{ rideData.time }}</div>
          <div class="label">Time</div>
        </div>
        <div class="info-item">
          <div class="value">{{ rideData.altitude }}m</div>
          <div class="label">Altitude</div>
        </div>
      </div>

      <div class="speed-info">
        <div class="info-item">
          <div class="value">{{ rideData.speed }}km/h</div>
          <div class="label">Speed</div>
        </div>
        <div class="info-item">
          <div class="value">{{ rideData.extremeSpeed }}km/h</div>
          <div class="label">Extreme speed</div>
        </div>
        <div class="info-item">
          <div class="value">{{ rideData.uniformSpeed }}km/h</div>
          <div class="label">Uniform speed</div>
        </div>
      </div> -->
      <div class="ride-analysis__info"></div>
      <div ref="chartContainer" class="chart-container"></div>
    </ion-content>
  </ion-modal>
</template>

<script lang="ts" setup>
import { IonModal, IonHeader, IonToolbar, IonTitle, IonContent, IonButtons, IonButton } from "@ionic/vue";
import { defineEmits, defineProps, onMounted, ref, watch } from "vue";
import { Chart } from '@antv/g2';

interface RideData {
  startPoint: string;
  endPoint: string;
  distance: number;
  time: string;
  altitude: number;
  speed: number;
  extremeSpeed: number;
  uniformSpeed: number;
  speedChart: Array<{ time: string; speed: number }>;
}

const props = defineProps<{
  presentingElement: HTMLElement;
  visible: boolean;
  rideData: RideData;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const chartContainer = ref<HTMLElement>();

// 初始化图表
const initChart = () => {
  if (!chartContainer.value || !props.rideData.speedChart) return;

  const chart = new Chart({
    container: chartContainer.value,
    autoFit: true,
    height: 300,
  });

  chart.data(props.rideData.speedChart);

  // chart.line()
  //   .position('time*speed')
  //   .color('#10B981')
  //   .shape('smooth');

  chart.axis('speed', {
    title: {
      text: 'Speed (km/h)',
    },
  });

  chart.axis('time', {
    title: {
      text: 'Time',
    },
  });

  chart.render();
};

onMounted(() => {
  initChart();
});

watch(
  () => props.visible,
  (value) => {
    if (value) {
      (props.presentingElement as HTMLElement).dispatchEvent(
        new CustomEvent("ionModalOpen")
      );
      // 当模态框打开时重新初始化图表
      setTimeout(initChart, 100);
    } else {
      (props.presentingElement as HTMLElement).dispatchEvent(
        new CustomEvent("ionModalDidDismiss")
      );
    }
  }
);
</script>

<style lang="scss" scoped>
ion-modal {
  --border-radius: 24px;
  --background: rgba(0, 0, 0, 0.8);
  --backdrop-filter: blur(8px) !important;
}

ion-toolbar, ion-content {
  --background: transparent;
}


ion-title {
  padding-inline: 20px;
  text-align: left;
}

.ride-analysis {

  .ride-analysis__info {
    width: 100%;
    height: 218px;
    background: #040608;
    border-radius: 16px;
    opacity: 0.5;
  }
}

.chart-container {
  height: 300px;
  margin-top: 20px;
}
</style>
