<template>
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon :icon="informationCircleOutline" />
        智能蓝牙平台信息
      </ion-card-title>
    </ion-card-header>
    
    <ion-card-content>
      <!-- 平台信息 -->
      <div class="platform-info">
        <h3>📱 当前平台信息</h3>
        <ion-item lines="none">
          <ion-label>
            <h4>平台类型</h4>
            <p>{{ platformInfo.platform }}</p>
          </ion-label>
          <ion-badge :color="platformInfo.method === 'native' ? 'success' : 'primary'" slot="end">
            {{ platformInfo.method === 'native' ? '原生方案' : 'WebView方案' }}
          </ion-badge>
        </ion-item>
        
        <ion-item lines="none">
          <ion-label>
            <h4>方案特性</h4>
            <div class="capabilities">
              <ion-chip 
                v-for="(capability, key) in platformInfo.capabilities" 
                :key="key"
                :color="capability ? 'success' : 'medium'"
              >
                <ion-icon :icon="capability ? checkmarkCircle : closeCircle" />
                <ion-label>{{ getCapabilityLabel(key) }}</ion-label>
              </ion-chip>
            </div>
          </ion-label>
        </ion-item>
      </div>
      
      <!-- 推荐方案信息 -->
      <div class="recommendation">
        <h3>💡 {{ recommendation.current }}</h3>
        <p><strong>状态:</strong> {{ recommendation.status }}</p>
        
        <div v-if="recommendation.benefits" class="benefits">
          <h4>✅ 优势:</h4>
          <ul>
            <li v-for="benefit in recommendation.benefits" :key="benefit">
              {{ benefit }}
            </li>
          </ul>
        </div>
        
        <div v-if="recommendation.limitations" class="limitations">
          <h4>⚠️ 限制:</h4>
          <ul>
            <li v-for="limitation in recommendation.limitations" :key="limitation">
              {{ limitation }}
            </li>
          </ul>
        </div>
      </div>
      
      <!-- 实时状态 (Android原生方案) -->
      <div v-if="isAndroidNative && sendingStats" class="native-stats">
        <h3>📊 原生发送统计</h3>
        <ion-grid>
          <ion-row>
            <ion-col size="6">
              <div class="stat-item">
                <h4>总发送次数</h4>
                <p class="stat-value">{{ sendingStats.totalSent }}</p>
              </div>
            </ion-col>
            <ion-col size="6">
              <div class="stat-item">
                <h4>成功次数</h4>
                <p class="stat-value success">{{ sendingStats.successCount }}</p>
              </div>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="6">
              <div class="stat-item">
                <h4>错误次数</h4>
                <p class="stat-value error">{{ sendingStats.errorCount }}</p>
              </div>
            </ion-col>
            <ion-col size="6">
              <div class="stat-item">
                <h4>平均间隔</h4>
                <p class="stat-value">{{ sendingStats.averageInterval.toFixed(2) }}ms</p>
              </div>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col size="12">
              <div class="stat-item">
                <h4>成功率</h4>
                <p class="stat-value">
                  {{ sendingStats.totalSent > 0 ? ((sendingStats.successCount / sendingStats.totalSent) * 100).toFixed(2) : 0 }}%
                </p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
      
      <!-- 服务状态 -->
      <div class="service-status">
        <h3>🔌 服务状态</h3>
        <ion-item lines="none">
          <ion-icon 
            :icon="isServiceRunning ? playCircle : stopCircle" 
            :color="isServiceRunning ? 'success' : 'medium'"
            slot="start"
          />
          <ion-label>
            <h4>{{ isServiceRunning ? '服务运行中' : '服务已停止' }}</h4>
            <p>{{ isServiceRunning ? '蓝牙数据正在发送' : '点击开始按钮启动发送' }}</p>
          </ion-label>
        </ion-item>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <ion-button 
          v-if="isAndroidNative"
          @click="updateStats" 
          fill="outline"
          size="small"
        >
          <ion-icon :icon="refreshOutline" slot="start" />
          更新统计
        </ion-button>
        
        <ion-button 
          @click="showPlatformDetails" 
          fill="outline"
          size="small"
        >
          <ion-icon :icon="informationCircleOutline" slot="start" />
          详细信息
        </ion-button>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon,
  IonItem,
  IonLabel,
  IonBadge,
  IonChip,
  IonGrid,
  IonRow,
  IonCol,
  IonButton,
  alertController
} from '@ionic/vue';
import {
  informationCircleOutline,
  checkmarkCircle,
  closeCircle,
  playCircle,
  stopCircle,
  refreshOutline
} from 'ionicons/icons';
import { computed, onMounted, onUnmounted } from 'vue';
import { useSmartBluetoothMessage } from '@/hooks/useSmartBluetoothMessage';

const smartBluetoothHook = useSmartBluetoothMessage();
const {
  currentMethod,
  isAndroidNative,
  isServiceRunning,
  getPlatformInfo,
  getSolutionRecommendation,
  sendingStats,
  updateSendingStats
} = smartBluetoothHook;

// 获取平台信息
const platformInfo = computed(() => getPlatformInfo());
const recommendation = computed(() => getSolutionRecommendation());

// 获取能力标签
const getCapabilityLabel = (key: string) => {
  const labels: Record<string, string> = {
    backgroundSending: '后台发送',
    preciseTimer: '精确定时',
    systemIntegration: '系统集成'
  };
  return labels[key] || key;
};

// 更新统计信息
const updateStats = async () => {
  if (isAndroidNative && updateSendingStats) {
    await updateSendingStats();
  }
};

// 显示平台详细信息
const showPlatformDetails = async () => {
  const alert = await alertController.create({
    header: '平台详细信息',
    message: `
      <div style="text-align: left;">
        <p><strong>平台:</strong> ${platformInfo.value.platform}</p>
        <p><strong>方案:</strong> ${platformInfo.value.method}</p>
        <p><strong>原生支持:</strong> ${platformInfo.value.isNativeSupported ? '是' : '否'}</p>
        <br>
        <p><strong>功能支持:</strong></p>
        <ul>
          <li>后台发送: ${platformInfo.value.capabilities.backgroundSending ? '✅' : '❌'}</li>
          <li>精确定时: ${platformInfo.value.capabilities.preciseTimer ? '✅' : '❌'}</li>
          <li>系统集成: ${platformInfo.value.capabilities.systemIntegration ? '✅' : '❌'}</li>
        </ul>
      </div>
    `,
    buttons: ['确定']
  });
  
  await alert.present();
};

// 定期更新统计信息（仅Android原生）
let statsUpdateInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  console.log('🎯 SmartBluetoothDebug 组件已挂载');
  console.log('📊 平台信息:', platformInfo.value);
  console.log('💡 方案推荐:', recommendation.value);
  
  // 如果是Android原生方案，定期更新统计信息
  if (isAndroidNative) {
    statsUpdateInterval = setInterval(async () => {
      await updateStats();
    }, 3000); // 每3秒更新一次
  }
});

onUnmounted(() => {
  if (statsUpdateInterval) {
    clearInterval(statsUpdateInterval);
    statsUpdateInterval = null;
  }
});
</script>

<style scoped>
.platform-info {
  margin-bottom: 16px;
}

.capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.recommendation {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--ion-color-step-50);
  border-radius: 8px;
}

.recommendation h3 {
  margin: 0 0 8px 0;
  color: var(--ion-color-primary);
}

.benefits, .limitations {
  margin-top: 12px;
}

.benefits ul, .limitations ul {
  margin: 8px 0;
  padding-left: 20px;
}

.benefits li {
  color: var(--ion-color-success);
  margin: 4px 0;
}

.limitations li {
  color: var(--ion-color-warning);
  margin: 4px 0;
}

.native-stats {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--ion-color-success-tint);
  border-radius: 8px;
}

.native-stats h3 {
  margin: 0 0 12px 0;
  color: var(--ion-color-success-shade);
}

.stat-item {
  text-align: center;
  padding: 8px;
}

.stat-item h4 {
  margin: 0 0 4px 0;
  font-size: 0.9em;
  color: var(--ion-color-medium);
}

.stat-value {
  margin: 0;
  font-size: 1.2em;
  font-weight: bold;
  color: var(--ion-color-dark);
}

.stat-value.success {
  color: var(--ion-color-success);
}

.stat-value.error {
  color: var(--ion-color-danger);
}

.service-status {
  margin-bottom: 16px;
}

.actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

h3 {
  margin: 0 0 8px 0;
  font-size: 1.1em;
  font-weight: 600;
}
</style> 