<template>
  <div class="timer-monitor" v-if="showMonitor">
    <div class="monitor-header">
      <h3>数据传输监控</h3>
      <button @click="toggleExpanded" class="expand-btn">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="isExpanded" class="monitor-content">
      <!-- 基本状态 -->
      <div class="status-section">
        <div class="status-item">
          <span class="label">定时器状态:</span>
          <span :class="['status', timerStatus.class]">{{ timerStatus.text }}</span>
        </div>
        
        <div class="status-item">
          <span class="label">最后发送:</span>
          <span class="value">{{ lastSendDisplay }}</span>
        </div>
        
        <div class="status-item">
          <span class="label">发送成功率:</span>
          <span class="value">{{ successRate }}%</span>
        </div>
        
        <div class="status-item" v-if="isPlatform('android')">
          <span class="label">后台服务:</span>
          <span :class="['status', serviceStatus.class]">{{ serviceStatus.text }}</span>
        </div>
        
        <div class="status-item" v-if="isPlatform('android')">
          <span class="label">Doze模式:</span>
          <span :class="['status', dozeStatus.class]">{{ dozeStatus.text }}</span>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-value">{{ totalSends }}</span>
          <span class="stat-label">总发送次数</span>
        </div>
        
        <div class="stat-item">
          <span class="stat-value">{{ failureCount }}</span>
          <span class="stat-label">失败次数</span>
        </div>
        
        <div class="stat-item">
          <span class="stat-value">{{ restartCount }}</span>
          <span class="stat-label">重启次数</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions-section">
        <button 
          @click="manualRestart" 
          :disabled="!canRestart"
          class="action-btn restart-btn"
        >
          手动重启定时器
        </button>
        
        <button 
          @click="resetStats" 
          class="action-btn reset-btn"
        >
          重置统计
        </button>
        
        <button 
          v-if="isPlatform('android')"
          @click="openBatterySettings" 
          class="action-btn settings-btn"
        >
          电池优化设置
        </button>
      </div>
      
      <!-- 健康度指示器 -->
      <div class="health-indicator">
        <div class="health-bar">
          <div 
            class="health-fill" 
            :style="{ width: healthPercentage + '%' }"
            :class="healthClass"
          ></div>
        </div>
        <span class="health-text">连接健康度: {{ healthPercentage }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { isPlatform } from '@ionic/vue';
import { useBackgroundBluetooth } from '@/hooks/useBackgroundBluetooth';
import { useBleStore } from '@/store/useBleStore';
import { storeToRefs } from 'pinia';

// Props
interface Props {
  isTimerActive: boolean;
  lastSendTime: number;
  lastSuccessTime: number;
  failureCount: number;
  restartCount: number;
  onManualRestart?: () => void;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  manualRestart: [];
}>();

// Stores
const bleStore = useBleStore();
const { connectedDevice } = storeToRefs(bleStore);
const { 
  isServiceRunning, 
  isInDozeMode, 
  openBatteryOptimizationSettings,
  checkDozeStatus 
} = useBackgroundBluetooth();

// State
const showMonitor = ref(false);
const isExpanded = ref(false);
const totalSends = ref(0);
const successfulSends = ref(0);
const monitorInterval = ref<NodeJS.Timeout | null>(null);

// 监控定时器状态
watch(() => props.isTimerActive, (active) => {
  showMonitor.value = active;
  if (active) {
    startMonitoring();
  } else {
    stopMonitoring();
  }
});

// 监控最后发送时间，用于统计
watch(() => props.lastSendTime, (newTime, oldTime) => {
  if (newTime > oldTime) {
    totalSends.value++;
  }
});

// 监控最后成功时间，用于统计
watch(() => props.lastSuccessTime, (newTime, oldTime) => {
  if (newTime > oldTime) {
    successfulSends.value++;
  }
});

// Computed
const timerStatus = computed(() => {
  if (!props.isTimerActive) {
    return { text: '已停止', class: 'stopped' };
  }
  
  const now = Date.now();
  const timeSinceLastSend = now - props.lastSendTime;
  
  if (timeSinceLastSend < 1000) {
    return { text: '正常运行', class: 'normal' };
  } else if (timeSinceLastSend < 3000) {
    return { text: '轻微延迟', class: 'warning' };
  } else {
    return { text: '异常停止', class: 'error' };
  }
});

const serviceStatus = computed(() => {
  if (isServiceRunning.value) {
    return { text: '运行中', class: 'normal' };
  } else {
    return { text: '已停止', class: 'error' };
  }
});

const dozeStatus = computed(() => {
  if (isInDozeMode.value) {
    return { text: '活跃', class: 'warning' };
  } else {
    return { text: '正常', class: 'normal' };
  }
});

const lastSendDisplay = computed(() => {
  if (props.lastSendTime === 0) return '从未发送';
  
  const now = Date.now();
  const diff = now - props.lastSendTime;
  
  if (diff < 1000) return '刚刚';
  if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`;
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  return `${Math.floor(diff / 3600000)}小时前`;
});

const successRate = computed(() => {
  if (totalSends.value === 0) return 100;
  return Math.round((successfulSends.value / totalSends.value) * 100);
});

const healthPercentage = computed(() => {
  if (!props.isTimerActive) return 0;
  
  const now = Date.now();
  const timeSinceLastSuccess = now - props.lastSuccessTime;
  
  // 基于多个因素计算健康度
  let health = 100;
  
  // 时间因素（最重要）
  if (timeSinceLastSuccess > 10000) {
    health -= Math.min(50, (timeSinceLastSuccess - 10000) / 1000 * 5);
  }
  
  // 失败率因素
  if (successRate.value < 90) {
    health -= (90 - successRate.value) * 0.5;
  }
  
  // 重启次数因素
  if (props.restartCount > 0) {
    health -= Math.min(20, props.restartCount * 5);
  }
  
  // Doze模式因素
  if (isInDozeMode.value) {
    health -= 10;
  }
  
  return Math.max(0, Math.round(health));
});

const healthClass = computed(() => {
  const health = healthPercentage.value;
  if (health >= 80) return 'health-good';
  if (health >= 50) return 'health-warning';
  return 'health-error';
});

const canRestart = computed(() => {
  return props.isTimerActive && connectedDevice.value.isPaired;
});

// Methods
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const manualRestart = () => {
  emit('manualRestart');
};

const resetStats = () => {
  totalSends.value = 0;
  successfulSends.value = 0;
};

const openBatterySettings = async () => {
  try {
    await openBatteryOptimizationSettings();
  } catch (error) {
    console.error('Failed to open battery settings:', error);
  }
};

const startMonitoring = () => {
  if (monitorInterval.value) return;
  
  monitorInterval.value = setInterval(async () => {
    // 定期检查Doze状态
    if (isPlatform('android')) {
      await checkDozeStatus();
    }
  }, 10000); // 每10秒检查一次
};

const stopMonitoring = () => {
  if (monitorInterval.value) {
    clearInterval(monitorInterval.value);
    monitorInterval.value = null;
  }
};

// Lifecycle
onMounted(() => {
  if (props.isTimerActive) {
    showMonitor.value = true;
    startMonitoring();
  }
});

onUnmounted(() => {
  stopMonitoring();
});
</script>

<style scoped>
.timer-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 280px;
  max-width: 400px;
  backdrop-filter: blur(10px);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.monitor-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.expand-btn {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.expand-btn:hover {
  background: rgba(0, 122, 255, 0.1);
}

.monitor-content {
  padding: 16px;
}

.status-section {
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: 600;
}

.status {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.status.normal {
  background: #E8F5E8;
  color: #4CAF50;
}

.status.warning {
  background: #FFF3E0;
  color: #FF9800;
}

.status.error {
  background: #FFEBEE;
  color: #F44336;
}

.status.stopped {
  background: #F5F5F5;
  color: #757575;
}

.stats-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  padding: 12px;
  background: #F8F9FA;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.actions-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.action-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.restart-btn {
  background: #007AFF;
  color: white;
}

.restart-btn:hover:not(:disabled) {
  background: #0056CC;
}

.restart-btn:disabled {
  background: #CCC;
  cursor: not-allowed;
}

.reset-btn {
  background: #FF9500;
  color: white;
}

.reset-btn:hover {
  background: #E6851A;
}

.settings-btn {
  background: #34C759;
  color: white;
}

.settings-btn:hover {
  background: #2DB84C;
}

.health-indicator {
  text-align: center;
}

.health-bar {
  width: 100%;
  height: 6px;
  background: #E0E0E0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.health-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.health-good {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.health-warning {
  background: linear-gradient(90deg, #FF9800, #FFC107);
}

.health-error {
  background: linear-gradient(90deg, #F44336, #FF5722);
}

.health-text {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .timer-monitor {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
</style>