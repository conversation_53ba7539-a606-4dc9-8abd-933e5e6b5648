<template>
  <div class="async-mapbox-container">
    <div v-if="loading" class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>正在加载地图组件...</p>
    </div>
    <component 
      v-else-if="MapboxSearchComponent" 
      :is="MapboxSearchComponent" 
      v-bind="$attrs"
      @update:modelValue="$emit('update:modelValue', $event)"
    />
    <div v-else-if="error" class="error-container">
      <ion-icon name="alert-circle-outline"></ion-icon>
      <p>地图组件加载失败</p>
      <ion-button @click="loadComponent" fill="outline" size="small">
        重试
      </ion-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent } from 'vue'
import { IonSpinner, IonIcon, IonButton } from '@ionic/vue'

const loading = ref(true)
const error = ref(false)
const MapboxSearchComponent = ref<any>(null)

// 异步加载Mapbox组件
const loadComponent = async () => {
  try {
    loading.value = true
    error.value = false
    
    // 动态导入Mapbox组件
    const { default: MapboxSearch } = await import('@/views/navigation/MapboxSearchPage.vue')
    MapboxSearchComponent.value = MapboxSearch
    
    loading.value = false
  } catch (err) {
    console.error('Failed to load Mapbox component:', err)
    error.value = true
    loading.value = false
  }
}

onMounted(() => {
  loadComponent()
})

defineEmits(['update:modelValue'])
</script>

<style scoped>
.async-mapbox-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  text-align: center;
}

.loading-container p,
.error-container p {
  margin: 0;
  color: var(--ion-color-medium);
}

.error-container ion-icon {
  font-size: 48px;
  color: var(--ion-color-warning);
}
</style>
