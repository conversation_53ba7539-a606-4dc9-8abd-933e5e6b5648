<template>
  <ion-app>
    <Suspense>
      <ion-router-outlet />
    </Suspense>
    
    <!-- 蓝牙服务调试面板 (仅在开发模式下显示) -->
    <BluetoothServiceDebugPanel v-if="isDevelopment" />
  </ion-app>
</template>

<script lang="ts" setup>
import { IonApp, IonRouterOutlet } from '@ionic/vue'
import { KeepAwake } from '@capacitor-community/keep-awake'
import { useBleStore } from '@/store/useBleStore'
import { useDisconnectEventBus } from '@/hooks/useDisconnectEventBus'
import { useToast } from '@/hooks/useToast'
import { useSmartBluetoothMessage } from '@/hooks/useSmartBluetoothMessage'
import { useBluetoothLe } from '@/hooks/useBluetooth-le'
import { useDashboardStore } from '@/store/useDashboardStore'
import { onMounted, onUnmounted, computed } from 'vue'
import BluetoothServiceDebugPanel from '@/components/BluetoothServiceDebugPanel.vue'

const { stopSendMessage } = useSmartBluetoothMessage()
const { scan } = useBluetoothLe()
const store = useBleStore()
const { resetDashboard } = useDashboardStore()
const { on } = useDisconnectEventBus()
const toast = useToast()

// 计算属性
const isDevelopment = computed(() => process.env.NODE_ENV === 'development')

on(async () => {
  // 监听设备是否断开
  await toast.presentToast('Bluetooth disconnected')
  store.updateConnectedDevicePairedStatus(false)
  store.clearAvailableDevices()
  await stopSendMessage()
  resetDashboard()
  await scan()
})

onMounted(() => {
  keepAwake()
})

onUnmounted(() => {
  allowSleep()
})

const keepAwake = async () => {
  await KeepAwake.keepAwake()
}

const allowSleep = async () => {
  await KeepAwake.allowSleep()
}
</script>
