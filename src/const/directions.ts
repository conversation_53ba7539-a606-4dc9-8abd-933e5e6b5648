// 🚴‍♂ 自行车骑行专用路径指令（推荐保留的类型）
// 序号路径类型是否保留说明
// 1 左转 ✅ 保留 骑行场景中非常常见
// 2 右转 ✅ 保留 骑行场景中非常常见
// 3 直行 ✅ 保留 骑行路线中常见“继续直行”提示
// 4 调头 ✅ 保留 某些骑行导航中可能需要原路返回
// 5 向左前方行驶 ✅ 保留 通常为“斜左前”骑行道/岔口提示
// 6 向右前方行驶 ✅ 保留 同上，“斜右前”提示
// 7 靠左行驶 ✅ 可选 如果你显示的是多车道岔口或复杂路口
// 8 靠右行驶 ✅ 可选 同上
// 9 到达目的地 ✅ 保留 导航结束提示

import { RouteProgress } from '@/types/RouteProgress.type'

export const Directions = {
  1: '左转',
  2: '右转',
  3: '直行',
  4: '调头',
  5: '向左前方行驶',
  6: '向右前方行驶',
  7: '靠左行驶',
  8: '靠右行驶',
  9: '到达目的地'
}
/**
 * 表示导航过程中的操作类型
 * 对应 iOS SDK 中的 ManeuverType 枚举
 */
export enum ManeuverType {
  /**
   * 步骤要求用户从某个路点出发
   * 如果路点离最近的道路有一定距离，操作方向指示用户到达道路时必须转向的方向
   */
  Depart = 'depart',

  /**
   * 步骤要求用户转弯
   * 操作方向指示用户必须相对于当前行驶方向转弯的方向
   * 出口索引表示从上一个操作到用户必须转弯的交叉路口之间的交叉路口数量
   */
  Turn = 'turn',

  /**
   * 步骤要求用户在转弯后继续行驶
   */
  Continue = 'continue',

  /**
   * 步骤要求用户继续沿当前道路行驶，因为道路名称发生了变化
   * 步骤的名称包含道路的新名称，要获取道路的旧名称，请使用上一步骤的名称
   */
  PassNameChange = 'new name',

  /**
   * 步骤要求用户合并到另一条道路上
   * 操作方向指示另一条道路相对于用户从交叉路口的哪一侧接近
   */
  Merge = 'merge',

  /**
   * 步骤要求用户驶入高速公路的入口匝道
   */
  TakeOnRamp = 'on ramp',

  /**
   * 步骤要求用户驶出高速公路的出口匝道
   * 操作方向指示用户必须从高速公路的哪一侧出口
   * 出口索引表示从上一个操作到用户必须采取的出口之间的高速公路出口数量
   */
  TakeOffRamp = 'off ramp',

  /**
   * 步骤要求用户在 Y 形岔路口选择一个分支
   * 操作方向指示应选择哪个分支
   */
  ReachFork = 'fork',

  /**
   * 步骤要求用户在 T 形三路交叉路口或道路名称也发生变化的急转弯处转弯
   * 此操作类型单独列出，以便用户可以更自信地前进，不用担心错过转弯
   */
  ReachEnd = 'end of road',

  /**
   * 步骤要求用户进入特定车道以便继续沿当前道路行驶
   * 操作方向设为 straightAhead
   * 此操作类型单独列出，以便应用程序可以根据交叉点属性中的第一个元素向用户提供车道引导
   */
  UseLane = 'use lane',

  /**
   * 步骤要求用户进入并穿越环岛（交通圈或转盘）
   * 步骤没有名称，但出口名称是离开环岛要走的道路名称
   * 出口索引表示直到用户必须采取的出口为止的环岛出口数量
   */
  TakeRoundabout = 'roundabout',

  /**
   * 步骤要求用户进入并穿越大型命名环岛（交通圈或转盘）
   * 步骤的名称是环岛的名称，出口名称是离开环岛要走的道路名称
   * 出口索引表示直到用户必须采取的出口为止的转盘出口数量
   */
  TakeRotary = 'rotary',

  /**
   * 步骤要求用户进入并离开一个足够紧凑的环岛，构成一个单一交叉路口
   * 步骤的名称是离开环岛后要走的道路名称
   * 此操作类型单独列出，因为用户可能会将环岛视为中间有岛的普通交叉路口
   */
  TurnAtRoundabout = 'roundabout turn',

  /**
   * 步骤要求用户离开环岛（交通圈或转盘）
   * 此操作类型跟在 takeRoundabout 操作之后，仅在 RouteOptions.includesExitRoundaboutManeuver 设置为 true 时使用
   */
  ExitRoundabout = 'exit roundabout',

  /**
   * 步骤要求用户离开大型命名环岛（交通圈或转盘）
   * 此操作类型跟在 takeRotary 操作之后，仅在 RouteOptions.includesExitRoundaboutManeuver 设置为 true 时使用
   */
  ExitRotary = 'exit rotary',

  /**
   * 步骤要求用户响应行驶条件的变化
   * 例如当驾驶指示要求用户登上渡轮，或骑自行车指示要求用户下车时，可能会发生此操作类型
   */
  HeedWarning = 'notification',

  /**
   * 步骤要求用户到达一个路点
   * 此步骤的距离和预期行驶时间设置为零，表示路线或路线段已完成
   * 操作方向指示可以在道路的哪一侧找到路点（或它是否在正前方）
   */
  Arrive = 'arrive',

  /**
   * 未识别的操作类型被解释为转弯
   */
  Default = 'turn'
}

/**
 * 表示导航操作的方向
 * 对应 iOS SDK 中的 ManeuverDirection 枚举
 */
export enum ManeuverDirection {
  /**
   * 操作需要向右急转
   */
  SharpRight = 'sharp right',

  /**
   * 操作需要右转、向右合并、右侧出口，或目的地在右侧
   */
  Right = 'right',

  /**
   * 操作需要向右轻微转弯
   */
  SlightRight = 'slight right',

  /**
   * 操作不需要明显改变方向，或目的地在正前方
   */
  StraightAhead = 'straight',

  /**
   * 操作需要向左轻微转弯
   */
  SlightLeft = 'slight left',

  /**
   * 操作需要左转、向左合并、左侧出口，或目的地在右侧
   */
  Left = 'left',

  /**
   * 操作需要向左急转
   */
  SharpLeft = 'sharp left',

  /**
   * 操作需要在可能的情况下掉头
   * 使用步骤的初始和最终航向之间的差异来区分向左掉头（在右侧行驶的国家/地区典型）和向右掉头
   */
  UTurn = 'uturn'
}

/**
 * 导航方向协议映射
 * 将 ManeuverDirection 映射到协议定义的数字
 */
export const DirectionProtocolMap: Record<ManeuverDirection, number> = {
  [ManeuverDirection.SharpRight]: 2, // 右转
  [ManeuverDirection.Right]: 2, // 右转
  [ManeuverDirection.SlightRight]: 6, // 向右前方行驶
  [ManeuverDirection.StraightAhead]: 3, // 直行
  [ManeuverDirection.SlightLeft]: 5, // 向左前方行驶
  [ManeuverDirection.Left]: 1, // 左转
  [ManeuverDirection.SharpLeft]: 1, // 左转
  [ManeuverDirection.UTurn]: 4 // 掉头
}

/**
 * 导航步骤信息接口
 * 对应 iOS 导航数据结构
 */
export interface NavigationStep {
  /** 操作类型 */
  maneuverType: ManeuverType

  /** 操作方向 */
  maneuverDirection: ManeuverDirection

  /** 距离下一个操作的距离（米） */
  distance: number

  /** 当前道路名称 */
  roadName: string

  /** 出口索引（适用于环岛等） */
  exitIndex?: number

  /** 原始操作数据 */
  rawManeuver?: any

  /** 语音导航指令 */
  voiceInstruction?: string

  /** 剩余距离文本 */
  distanceText?: string
}

export const mockRouteProgress: RouteProgress = {
  stepIndex: 0,
  currentStepProgress: {
    step: {
      name: '金山路',
      mode: 'cycling',
      speedLimitUnit: 'km/h',
      weight: 137.42,
      duration: 39.291,
      bannerInstructions: [
        {
          distanceAlongGeometry: 152.69,
          drivingSide: 'right',
          primary: {
            modifier: 'slight right',
            type: 'turn',
            text: '运捷路',
            components: [
              {
                type: 'text',
                text: '运捷路'
              }
            ]
          }
        }
      ],
      distance: 152.69,
      driving_side: 'right',
      geometry: 'my_~Dytv~UR~H',
      maneuver: {
        type: 'depart',
        bearing_after: 266,
        instruction: 'Bike west on 金山路.',
        location: [120.543645, 31.297673],
        bearing_before: 0
      },
      speedLimitSign: 'vienna'
    },
    spokenInstructionIndex: 0,
    intersectionIndex: 0,
    userDistanceToManeuverLocation: 152.69,
    intersectionDistances: [0],
    visualInstructionIndex: 0,
    userDistanceToUpcomingIntersection: 152.30389165866342
  },
  distance: 1202.028,
  duration: 285.338
}
