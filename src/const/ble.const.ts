export const ServiceUUID = 0xfff0 // 'fff0';
export const CharacteristicUUID = 0xfff1 // 'fff1';
const WriteData = [
  0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
  0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [11，12，13，14，15，16]
  0x0e //17 截止位
] // [0x3C, 0x05, 0xF5, 0x57, 0x28, 0x95, 0x30, 0x4A, 0x8C, 0x14, 0x00, 0x32, 0x0E];
WriteData[16] =
  WriteData[1] ^
  WriteData[2] ^
  WriteData[3] ^
  WriteData[4] ^
  WriteData[6] ^
  WriteData[7] ^
  WriteData[8] ^
  WriteData[9] ^
  WriteData[10] ^
  WriteData[11] ^
  WriteData[12] ^
  WriteData[13] ^
  WriteData[14] ^
  WriteData[15]
export { WriteData }
