export interface RouteProgress {
  stepIndex: number
  currentStepProgress: CurrentStepProgress
  distance: number
  duration: number
}

export interface CurrentStepProgress {
  step: Step
  spokenInstructionIndex: number
  intersectionIndex: number
  userDistanceToManeuverLocation: number
  intersectionDistances: number[]
  visualInstructionIndex: number
  userDistanceToUpcomingIntersection: number
}

export interface Step {
  name: string
  mode: string
  speedLimitUnit: string
  weight: number
  duration: number
  bannerInstructions: BannerInstruction[]
  distance: number
  driving_side: string
  geometry: string
  maneuver: Maneuver
  speedLimitSign: string
}

export interface BannerInstruction {
  distanceAlongGeometry: number
  drivingSide: string
  primary: Primary
}

export interface Primary {
  modifier: string
  type: string
  text: string
  components: Component[]
}

export interface Component {
  type: string
  text: string
}

export interface Maneuver {
  type: string
  bearing_after: number
  instruction: string
  location: [number, number]
  bearing_before: number
}
