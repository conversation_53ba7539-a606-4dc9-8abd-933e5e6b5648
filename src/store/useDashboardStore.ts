import { defineStore, storeToRefs } from "pinia";
import { useSettingStore } from "@/store/useSettingStore";

export interface IDashboardStore {
  electricQuantity: number; // 电量
  speed: number; // 速度
  gearPosition: number; // 档位
  singleMileage: number; // 单次里程
  singleTime: string; // 单次时间
  totalMileage: number; // 总里程
  lightStatus: boolean; // 灯光状态
  assistance: number; // 助力
  regenative: number; // 反冲电
  undervoltage: number; // 欠压
  reverse: number; // 倒档
  turnRight: number; // 右转
  turnLeft: number; // 左转
  throttle: number; // 转把状态
  cruise: number; // 巡航状态
  brake: number; // 刹车状态
}

export interface IDashboardGetters {
  [key: string]: (state: IDashboardStore) => any;
  getElectricQuantity: (state: IDashboardStore) => number;
  getSpeed: (state: IDashboardStore) => number;
  getGearPosition: (state: IDashboardStore) => number;
  getSingleMileage: (state: IDashboardStore) => string;
  getSingleKM: (state: IDashboardStore) => string;
  getSingleTime: (state: IDashboardStore) => string;
  getTotalMileage: (state: IDashboardStore) => string;
  getTotalKM: (state: IDashboardStore) => string;
  getLightStatus: (state: IDashboardStore) => boolean;
  getAssistance: (state: IDashboardStore) => number;
}

export interface IDashboardActions {
  setGearPosition: (payload: number) => void;
  setSingleMileage: (payload: number) => void;
  setSingleTime: (payload: string) => void;
  setTotalMileage: (payload: number) => void;
  setElectricQuantity: (payload: number) => void;
  setSpeed: (payload: number) => void;
  setLightStatus: (payload: boolean) => void;
  setAssistance: (payload: number) => void;
  setRegenative: (payload: number) => void;
  setUndervoltage: (payload: number) => void;
  setReverse: (payload: number) => void;
  setTurnRight: (payload: number) => void;
  setTurnLeft: (payload: number) => void;
  setThrottle: (payload: number) => void;
  setCruise: (payload: number) => void;
  setBrake: (payload: number) => void;
  resetDashboard: () => void;
}

export const useDashboardStore = defineStore<'dashboard', IDashboardStore, IDashboardGetters, IDashboardActions>("dashboard", {
  state: () => ({
    electricQuantity: 0,
    speed: 0,
    gearPosition: -1,
    singleMileage: 0,
    singleTime: "00:00:00",
    totalMileage: 0,
    lightStatus: false,
    assistance: 0,
    regenative: 0,
    undervoltage: 0,
    reverse: 0,
    turnRight: 0,
    turnLeft: 0,
    throttle: 0,
    cruise: 0,
    brake: 0,
  }),
  getters: {
    getElectricQuantity: (state) => state.electricQuantity,
    getSpeed: (state) => state.speed,
    getGearPosition: (state) => {
      const { getPosition } = storeToRefs(useSettingStore());
      return getPosition.value === 8
        ? state.gearPosition === -1
          ? 5
          : state.gearPosition
        : state.gearPosition;
    },
    getSingleMileage: (state) => {
      return state.singleMileage.toFixed(1);
    },
    getSingleKM: (state) => {
      const value = state.singleMileage * 0.6213712;
      return value.toFixed(1);
    },
    getSingleTime: (state) => state.singleTime,
    getTotalMileage: (state) => {
      return state.totalMileage.toFixed(1);
    },
    getTotalKM: (state) => {
      const value = state.totalMileage * 0.6213712;
      return value.toFixed(1);
    },
    getLightStatus: (state) => state.lightStatus,
    getAssistance: (state) => state.assistance,
  },
  actions: {
    setGearPosition(payload: number) {
      this.gearPosition = payload;
    },
    setSingleMileage(payload: number) {
      this.singleMileage = payload;
    },
    setSingleTime(payload: string) {
      this.singleTime = payload;
    },
    setTotalMileage(payload: number) {
      this.totalMileage = payload;
    },
    setElectricQuantity(payload: number) {
      this.electricQuantity = payload;
    },
    setSpeed(payload: number) {
      this.speed = payload;
      // setInterval(() => {
      //   this.speed = Math.floor(Math.random()*72)
      // }, 1000)
    },
    setLightStatus(payload: boolean) {
      this.lightStatus = payload;
    },
    setAssistance(payload: number) {
      this.assistance = payload;
    },
    setRegenative(payload: number) {
      this.regenative = payload;
    },
    setUndervoltage(payload: number) {
      this.undervoltage = payload;
    },
    setReverse(payload: number) {
      this.reverse = payload;
    },
    setTurnRight(payload: number) {
      this.turnRight = payload;
    },
    setTurnLeft(payload: number) {
      this.turnLeft = payload;
    },
    setThrottle(payload: number) {
      this.throttle = payload;
    },
    setCruise(payload: number) {
      this.cruise = payload;
    },
    setBrake(payload: number) {
      this.brake = payload;
    },
    resetDashboard() {
      this.speed = 0;
      this.throttle = 0;
    },
  },
  persist: {
    pick: ["gearPosition", "displayType", "totalMileage"],
  },
});
