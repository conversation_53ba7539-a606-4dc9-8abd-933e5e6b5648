import { defineStore } from "pinia";
import { DefaultSetting } from "@/const/setting.const";
import { DimensionList, LevelList } from "@/const/bike.const";

export interface ISetting {
  maxSpeed: number;
  dimension: number;
  p1: number;
  p2: number;
  p3: number;
  p4: number;
  p5: number;
  c1: number;
  c2: number;
  c3: number;
  c4: number;
  c5: number;
  c7: number;
  c12: number;
  c13: number;
  c14: number;
  percent: number;
  handlebarMaxSpeed: number;
  candidateParam: number;
  displayType: string;
  dimensionList: { name: string; value: number; dimension: number; }[];
  levelList: { name: string; value: number; }[];
}

export interface ISettingGetters {
  [key: string]: (state: ISetting) => any;
  getMaxSpeed: (state: ISetting) => number;
  getDimension: (state: ISetting) => number;
  getP1: (state: ISetting) => number;
  getP2: (state: ISetting) => number;
  getP3: (state: ISetting) => number;
  getP4: (state: ISetting) => number;
  getP5: (state: ISetting) => number;
  getC1: (state: ISetting) => number;
  getC2: (state: ISetting) => number;
  getPosition: (state: ISetting) => number;
  getC4: (state: ISetting) => number;
  getC5: (state: ISetting) => number;
  getC7: (state: ISetting) => number;
  getC12: (state: ISetting) => number;
  getC13: (state: ISetting) => number;
  getC14: (state: ISetting) => number;
  getPercent: (state: ISetting) => number;
  getHandlebarMaxSpeed: (state: ISetting) => number;
  getCandidateParam: (state: ISetting) => number;
  getDisplayType: (state: ISetting) => string;
  getDisplayUnit: (state: ISetting) => string;
}

export interface ISettingActions {
  setMaxSpeed: (payload: number) => void;
  setDimension: (payload: number) => void;
  setP1: (payload: number) => void;
  setP2: (payload: number) => void;
  setP3: (payload: number) => void;
  setP4: (payload: number) => void;
  setP5: (payload: number) => void;
  setC1: (payload: number) => void;
  setC2: (payload: number) => void;
  setC3: (payload: number) => void;
  setC4: (payload: number) => void;
  setC5: (payload: number) => void;
  setC7: (payload: number) => void;
  setC12: (payload: number) => void;
  setC13: (payload: number) => void;
  setC14: (payload: number) => void;
  setPercent: (payload: number) => void;
  setHandlebarMaxSpeed: (payload: number) => void;
  setCandidateParam: (payload: number) => void;
  setDisplayType: (payload: string) => void;
}

export const useSettingStore = defineStore<"setting", ISetting, ISettingGetters, ISettingActions>("setting", {
  state: () => ({
    maxSpeed: DefaultSetting.maxSpeed,
    dimension: DefaultSetting.dimension,
    p1: DefaultSetting.p1,
    p2: DefaultSetting.p2,
    p3: DefaultSetting.p3,
    p4: DefaultSetting.p4,
    p5: DefaultSetting.p5,
    c1: DefaultSetting.c1,
    c2: DefaultSetting.c2,
    c3: DefaultSetting.c3,
    c4: DefaultSetting.c4,
    c5: DefaultSetting.c5,
    c7: DefaultSetting.c7,
    c12: DefaultSetting.c12,
    c13: DefaultSetting.c13,
    c14: DefaultSetting.c14,
    percent: DefaultSetting.percent,
    handlebarMaxSpeed: DefaultSetting.handlebarMaxSpeed,
    candidateParam: DefaultSetting.candidateParam,
    displayType: DefaultSetting.displayType,
    dimensionList: DimensionList,
    levelList: LevelList,
  }),
  getters: {
    getMaxSpeed: (state) => state.maxSpeed,
    getDimension: (state) => state.dimension,
    getP1: (state) => state.p1,
    getP2: (state) => state.p2,
    getP3: (state) => state.p3,
    getP4: (state) => state.p4,
    getP5: (state) => state.p5,
    getC1: (state) => state.c1,
    getC2: (state) => state.c2,
    getPosition: (state) => {
      return state.c3;
    },
    getC4: (state) => state.c4,
    getC5: (state) => state.c5,
    getC7: (state) => state.c7,
    getC12: (state) => state.c12,
    getC13: (state) => state.c13,
    getC14: (state) => state.c14,
    getPercent: (state) => state.percent,
    getHandlebarMaxSpeed: (state) => state.handlebarMaxSpeed,
    getCandidateParam: (state) => state.candidateParam,
    getDisplayType: (state) => state.displayType,
/*************  ✨ Codeium Command ⭐  *************/
/**
 * Returns the display unit based on the display type.
 * If the display type is "kilometer", it returns "KM/h".
 * Otherwise, it returns "Mil/h".
 * 
 * @param state - The state containing the displayType property.
 * @returns The unit of measurement as a string.
 */

/******  2b1c5a28-6a6e-4b64-bbac-f3f670e43627  *******/
    getDisplayUnit: (state) =>
      state.displayType === "kilometer" ? "KM/h" : "Mil/h",
  },
  actions: {
    setMaxSpeed(payload: number) {
      this.maxSpeed = payload;
    },
    setDimension(payload: number) {
      this.dimension = payload;
    },
    setP1(payload: number) {
      this.p1 = payload;
    },
    setP2(payload: number) {
      this.p2 = payload;
    },
    setP3(payload: number) {
      this.p3 = payload;
    },
    setP4(payload: number) {
      this.p4 = payload;
    },
    setP5(payload: number) {
      this.p5 = payload;
    },
    setC1(payload: number) {
      this.c1 = payload;
    },
    setC2(payload: number) {
      this.c2 = payload;
    },
    setC3(payload: number) {
      this.c3 = payload;
    },
    setC4(payload: number) {
      this.c4 = payload;
    },
    setC5(payload: number) {
      this.c5 = payload;
    },
    setC7(payload: number) {
      this.c7 = payload;
    },
    setC12(payload: number) {
      this.c12 = payload;
    },
    setC13(payload: number) {
      this.c13 = payload;
    },
    setC14(payload: number) {
      this.c14 = payload;
    },
    setPercent(payload: number) {
      this.percent = payload;
    },
    setHandlebarMaxSpeed(payload: number) {
      this.handlebarMaxSpeed = payload;
    },
    setCandidateParam(payload: number) {
      this.candidateParam = payload;
    },
    setDisplayType(payload: string) {
      this.displayType = payload;
    },
  },
  persist: true,
});
