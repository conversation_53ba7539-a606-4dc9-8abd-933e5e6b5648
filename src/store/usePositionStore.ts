// import { defineStore } from "pinia";
// import { Position } from "@capacitor/geolocation";

// export interface Track {
//   id: number;
//   path: [];
//   maxSpeed: string | number;
//   maxAltitude: string | number;
//   averageSpeed: string | number;
//   distance: string | number;
//   time: string;
// }

// export interface IPosition {
//   currentPosition: Position;
//   historyTrack: Track[];
//   locating: boolean;
//   watching: boolean;
// }

// export interface IPositionGetters {
//   [key: string]: (state: IPosition) => any;
//   getHistoryTrackById: (state: IPosition) => (id: number) => Track | undefined;
// }

// export interface IPositionActions {
//   setCurrentPosition: (payload: Position) => void;
//   setLocatingStatus: (payload: boolean) => void;
//   setWatchingStatus: (payload: boolean) => void;
//   addHistoryTrack: (payload: Track) => void;
//   deleteHistory: (payload: number) => void;
//   clearHistory: () => void;
// }

// export const usePositionStore = defineStore<"position", IPosition, IPositionGetters, IPositionActions>("position", {
//   state: () => ({
//     currentPosition: {} as Position,
//     historyTrack: [] as Track[],
//     locating: false,
//     watching: false,
//   }),
//   getters: {
//     getHistoryTrackById: (state) => {
//       return (id: number) =>
//         state.historyTrack.find((history) => history.id === id);
//     },
//   },
//   actions: {
//     setCurrentPosition(payload: Position) {
//       this.currentPosition = payload;
//     },
//     setLocatingStatus(payload: boolean) {
//       this.locating = payload;
//     },
//     setWatchingStatus(payload: boolean) {
//       this.watching = payload;
//     },
//     addHistoryTrack(payload: Track) {
//       this.historyTrack.push(payload);
//     },
//     deleteHistory(payload: number) {
//       this.historyTrack = this.historyTrack.filter(
//         (item) => item.id !== payload
//       );
//     },
//     clearHistory() {
//       this.historyTrack.length = 0;
//     },
//   },
//   persist: true,
// });
