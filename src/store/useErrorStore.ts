import { defineStore } from 'pinia'

export interface IError {
  errorCode: number
}

export interface IErrorGetters {
  [key: string]: (state: IError) => boolean
  hasError: (state: IError) => boolean
  isCurrentError: (state: IError) => boolean
  isThrottleError: (state: IError) => boolean
  isMotorPhaseError: (state: IError) => boolean
  isMotorHallError: (state: IError) => boolean
  isTorqueSensorError: (state: IError) => boolean
  isSpeedSensorError: (state: IError) => boolean
}

export interface IErrorActions {
  setErrorCode: (payload: number) => void
}

export const useErrorStore = defineStore<
  'error',
  IError,
  IErrorGetters,
  IErrorActions
>('error', {
  state: () => ({
    errorCode: -1
  }),
  getters: {
    hasError: state => state.errorCode > 0,
    isCurrentError: state => true, // state.errorCode === 33,
    isThrottleError: state => state.errorCode === 34,
    isMotorPhaseError: state => state.errorCode === 35,
    isMotorHallError: state => state.errorCode === 36,
    isTorqueSensorError: state => state.errorCode === 38,
    isSpeedSensorError: state => state.errorCode === 39
  },
  actions: {
    setErrorCode(payload: number) {
      this.errorCode = payload
    }
  },
  persist: true
})
