import { defineStore } from "pinia";
import { BleDevice } from "@capacitor-community/bluetooth-le";
import exp from "constants";

export interface CharacteristicModule {
  service: string;
  characteristic: string;
  properties: string[];
}

export interface PeripheralModule {
  name: string;
  id: string;
  advertising: object;
  rssi: string;
  isPairing: boolean;
  isPaired: boolean;
  services: string[];
  characteristics: CharacteristicModule[];
}

export interface Device extends BleDevice {
  isPaired: boolean;
  isPairing: boolean;

  [propName: string]: any;
}

export interface IBleState {
  connectedDevice: Device;
  availableDevices: Device[];
}

export interface IBleGetters {
  [key: string]: (state: IBleState) => any;
  getConnectedDevice: (state: IBleState) => Device;
  getAvailableDevices: (state: IBleState) => Device[];
}

export interface IBleActions {
  setConnectedDevice: (payload: Device) => void;
  updateConnectedDevicePairedStatus: (payload: boolean) => void;
  updateConnectedDevicePairingStatus: (payload: boolean) => void;
  removeConnectedDevice: () => void;
  setAvailableDevice: (payload: Device) => void;
  clearAvailableDevices: () => void;  
}

export const useBleStore = defineStore<'ble', IBleState, IBleGetters, IBleActions>("ble", {
  state: () => ({
    connectedDevice: {} as Device,
    availableDevices: [] as Device[],
  }),
  getters: {
    getConnectedDevice: (state) => state.connectedDevice,
    getAvailableDevices: (state) => state.availableDevices,
  },
  actions: {
    setConnectedDevice(payload: Device) {
      this.connectedDevice = payload;
    },
    updateConnectedDevicePairedStatus(payload: boolean) {
      this.connectedDevice.isPaired = payload;
    },
    updateConnectedDevicePairingStatus(payload: boolean) {
      this.connectedDevice.isPairing = payload;
    },
    removeConnectedDevice() {
      this.connectedDevice = {} as Device;
    },
    setAvailableDevice(payload: Device) {
      this.availableDevices.push(payload);
    },
    clearAvailableDevices() {
      this.availableDevices = [];
    },
  },
  persist: {
    pick: ["connectedDevice"],
  },
});
