import { defineStore } from 'pinia'

export interface INavigation {
  start: number[]
  destination: number[]
}

export interface INavigationGetters {
  [key: string]: (state: INavigation) => any
}

export interface INavigationActions {
  setStart: (payload: number[]) => void
  setDestination: (payload: number[]) => void
}

export const useNavigationStore = defineStore<
  'navigation',
  INavigation,
  INavigationGetters,
  INavigationActions
>('navigation', {
  state: () => ({
    start: [],
    destination: []
  }),
  getters: {},
  actions: {
    setStart(payload: number[]) {
      this.start = payload
    },
    setDestination(payload: number[]) {
      this.destination = payload
    }
  }
})
