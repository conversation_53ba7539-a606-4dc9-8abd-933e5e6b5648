<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>History</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-text class="group-header">February 24th</ion-text>
      <ion-card>
        <ion-card-header>
          <ion-card-title>
            <ion-text>19.3</ion-text>
            <ion-text class="display-unit">km</ion-text>
          </ion-card-title>
          <ion-card-subtitle>06:21 PM - 07:32 PM</ion-card-subtitle>
        </ion-card-header>

        <ion-card-content>
          <ion-grid>
            <ion-row>
              <ion-col>
                <ion-text>29:32:06</ion-text>
              </ion-col>
              <ion-col>
                <ion-text>35</ion-text>
                <ion-text>km/h</ion-text>
              </ion-col>
              <ion-col>
                <ion-text>5</ion-text>
                <ion-text>m</ion-text>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col>
                <ion-text>Time</ion-text>
              </ion-col>
              <ion-col>
                <ion-text>Speed</ion-text>
              </ion-col>
              <ion-col>
                <ion-text>Altitude</ion-text>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import { IonContent, IonHeader, IonText, IonPage, IonTitle, IonToolbar } from '@ionic/vue';


</script>

<style scoped lang="scss">
ion-header ion-toolbar:first-of-type {
  --border-width: 0;
  margin-bottom: 12px;
}

ion-text.group-header {
  margin-left: 20px;
}

ion-text.display-unit {
  font-size: 14px;
  margin-left: 4px;
}

ion-card {
  margin-inline-start: 20px;
  margin-inline-end: 20px;
  margin-top: 8px;
  margin-bottom: 8px;
}

ion-card-header {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

ion-card-title {
  display: flex;
  align-items: center;
}
</style>
