<template>
  <ion-page ref="page">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/home"></ion-back-button>
        </ion-buttons>
        <ion-title>Bluetooth</ion-title>
        <ion-buttons v-if="!connectedDevice.isPaired" slot="end">
          <ion-button @click="scan">
            <ion-icon slot="icon-only" :icon="refresh"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="device-container" :fullscreen="true" v-if="!scanning">
      <!-- <ion-card v-if="connectedDevice.isPaired">
        <ion-card-header>
          <ion-card-title class="device-modal__title">
            {{ connectedDevice.name }}
          </ion-card-title>
          <ion-card-subtitle>Device Information</ion-card-subtitle>
        </ion-card-header>
        <ion-card-content>
          Device ID:{{ connectedDevice.deviceId }}
        </ion-card-content>
        <ion-button fill="clear" size="small" @click="presentAlert">Disconnect
        </ion-button>
      </ion-card> -->
      <template v-if="connectedDevice.isPaired">
        <ion-label class="device-label">Paired Devices</ion-label>
        <ion-list :inset="true" lines="full">
          <ion-item class="active" @click="presentAlert">
            <ion-icon slot="start" :icon="bicycle"></ion-icon>
            <ion-label>{{ connectedDevice.name }}</ion-label>
          </ion-item>
        </ion-list>
      </template>
      <template v-if="!connectedDevice.isPaired">
        <ion-label class="device-label">Available Devices</ion-label>
        <ion-list :inset="true" lines="full">
          <ion-item v-for="(item, index) in availableDevices" :key="index" @click="selectDevice(item)">
            <ion-icon slot="start" :icon="bluetooth"></ion-icon>
            <ion-label>{{ item.name }}</ion-label>
          </ion-item>
          <ion-item v-if="availableDevices.length === 0 && !scanning" lines="none">
            <ion-note v-if="isPlatform('ios')">
              No available Bluetooth devices found
            </ion-note>
            <ion-note v-else slot="start">
              No available Bluetooth devices found
            </ion-note>
          </ion-item>
        </ion-list>
      </template>
    </ion-content>
    <ion-content :fullscreen="true" v-else>
      <div class="loading-container">
        <ion-spinner name="crescent"></ion-spinner>
        <ion-label class="loading-container__text">Searching for available devices...</ion-label>
      </div>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  alertController,
  IonBackButton,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonItemDivider,
  IonItemGroup,
  IonLabel,
  IonList,
  IonNote,
  IonPage,
  IonSpinner,
  IonTitle,
  IonToolbar,
  isPlatform,
  loadingController,
} from "@ionic/vue";
import { bicycle, bluetooth, refresh } from "ionicons/icons";
import { Device, useBleStore } from "@/store/useBleStore";
import { useBluetoothLe } from "@/hooks/useBluetooth-le";
import { onMounted, onUnmounted, ref, shallowRef } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { useMessage } from "@/hooks/useMessage";
import { useToast } from "@/hooks/useToast";
import { useDisconnectEventBus } from "@/hooks/useDisconnectEventBus";

const store = useBleStore();
const { connectedDevice, availableDevices } = storeToRefs(store);
const { scan, scanning, connectBle, disConnectBle, onDisconnect } =
  useBluetoothLe();
const { stopSendMessage } = useMessage();
const router = useRouter();
const { on } = useDisconnectEventBus();

let scanIntervalId: any;

onMounted(() => {
  if (connectedDevice.value.isPaired) return;
  scanInterval();
});
on(async () => {
  await scanInterval();
});
onUnmounted(() => {
  clearInterval(scanIntervalId);
});
const toast = useToast();
const scanInterval = async () => {
  await scan();
  scanIntervalId = setInterval(async () => {
    await scan();
  }, 1000 * 15);
};
const selectDevice = async (device: Device) => {
  clearInterval(scanIntervalId);
  await showConnectLoading();
  try {
    await connectBle(device);
    await connectLoading.dismiss();
    router.back();
  } catch (e) {
    console.log("connect error", e);
    await connectLoading.dismiss();
    await toast.presentToast("Unmatched Bluetooth device");
    await scanInterval();
  }
};
const alertButtons = [
  "Cancel",
  {
    text: "Okay",
    handler: async () => {
      alert.value?.dismiss();
      await showDisconnectLoading();
      try {
        await stopSendMessage();
        await disConnectBle(connectedDevice.value);
        disconnectLoading.value?.dismiss();
      } catch (e) {
        disconnectLoading.value?.dismiss();
      }
      setTimeout(async () => {
        await scanInterval();
      }, 1000);
    },
  },
];
const alert = ref<HTMLIonAlertElement>();
const presentAlert = async () => {
  alert.value = await alertController.create({
    header: "Alert",
    subHeader: "Do you want to disconnect the Bluetooth!",
    buttons: alertButtons,
  });

  await alert.value.present();
};
let connectLoading = {} as HTMLIonLoadingElement;
const showConnectLoading = async () => {
  connectLoading = await loadingController.create({
    message: "Connecting to Bluetooth device",
  });
  await connectLoading.present();
};
const disconnectLoading = shallowRef<HTMLIonLoadingElement>();
const showDisconnectLoading = async () => {
  disconnectLoading.value = await loadingController.create({
    message: "Disconnecting Bluetooth device",
  });

  await disconnectLoading.value.present();
};
</script>
<style lang="scss" scoped>
@keyframes refresh {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.icon-refresh--on {
  animation-name: refresh;
  animation-duration: 1s;
  animation-iteration-count: 1;
}

ion-header ion-toolbar:first-of-type {
  --border-width: 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .loading-container__text {
    margin-top: 8px;
    font-size: 14px;
    color: #757575;
    line-height: 20px;
  }
}

.device-container {
  --padding-top: 12px;

  .active {
    --color: var(--ion-color-primary);
  }

  ion-list {
    --background: #191A21;

    &.list-inset {
      margin-inline-start: 20px;
      margin-inline-end: 20px;
      margin-top: 8px;
      margin-bottom: 8px;
    }

    ion-item {
      --padding-start: 20px;
      --padding-end: 20px;
      --inner-padding-top: 6px;
      --inner-padding-bottom: 6px;
      --background: #191A21;
      --ion-item-border-color: #333333;
    }
  }

  .device-label {
    margin-left: 20px;
    font-size: 14px;
    color: #757575;
    line-height: 20px;
  }
}
</style>
