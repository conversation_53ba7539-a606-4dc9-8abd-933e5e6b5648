<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Tab 1</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="toBluetoothPage">
            <ion-icon :icon="bluetooth" class="bluetooth" />
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">Tab 1</ion-title>
        </ion-toolbar>
      </ion-header>
      <ion-button @click="addSpeed">add</ion-button>
      <ion-button @click="reduceSpeed">reduce</ion-button>
      <ExploreContainer name="Tab 1 page" />
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonPage,
  IonTitle,
  IonToolbar,
} from "@ionic/vue";
import { onMounted } from "vue";
import { bluetooth } from "ionicons/icons";
import { useRouter } from "vue-router";
import { useSetting } from "@/hooks/useSetting";
import { storeToRefs } from "pinia";
import { useDashboardStore } from "@/store/useDashboardStore";
import {
  dataViewToNumbers,
  hexStringToDataView,
} from "@capacitor-community/bluetooth-le";
// import {dataViewToNumbers} from "@capacitor-community/bluetooth-le";
// import {hexStringToDataView} from "@capacitor-community/bluetooth-le/dist/esm/conversion";
// import {useError} from "@/hooks/useError";
const dashboardStore = useDashboardStore();
const { gearPosition } = storeToRefs(dashboardStore);
const { changeGearPosition } = useSetting();
const router = useRouter();

const toBluetoothPage = () => {
  router.push({ name: "bluetooth" });
};
const addSpeed = () => {
  if (gearPosition.value >= 5) return;
  const position = gearPosition.value + 1;
  changeGearPosition(position);
  // 档位变化会通过useSetting的回调自动更新native端数据
};
const reduceSpeed = () => {
  if (gearPosition.value <= 1) return;
  const position = gearPosition.value - 1;
  changeGearPosition(position);
  // 档位变化会通过useSetting的回调自动更新native端数据
};
onMounted(() => {
 
});
</script>
