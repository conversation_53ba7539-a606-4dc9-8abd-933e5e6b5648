<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/my"></ion-back-button>
        </ion-buttons>
        <ion-title>Help</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-item
        button
        lines="none"
        v-for="errorType in ErrorTypes"
        :key="errorType.name"
        @click="presentAlert(errorType)"
      >
        <ion-icon
          aria-hidden="true"
          :icon="errorType.icon"
          slot="start"
        ></ion-icon>
        <ion-label>
          {{ errorType.name }}
        </ion-label>
        <ion-icon
          color="danger"
          size="small"
          :icon="alertCircle"
          slot="end"
          v-if="errorType.isError.value"
        ></ion-icon>
      </ion-item>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
  import { useErrorStore } from '@/store/useErrorStore'
  import {
    IonPage,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonContent,
    IonItem,
    IonLabel,
    IonIcon,
    IonButtons,
    IonBackButton,
    alertController
  } from '@ionic/vue'
  import { alertCircle, hammerOutline } from 'ionicons/icons'
  import { storeToRefs } from 'pinia'

  const errorStore = useErrorStore()
  const {
    isCurrentError,
    isThrottleError,
    isMotorPhaseError,
    isMotorHallError,
    isTorqueSensorError,
    isSpeedSensorError
  } = storeToRefs(errorStore)

  const ErrorTypes = [
    {
      name: 'Current',
      icon: hammerOutline,
      isError: isCurrentError,
      description: 'Current Error'
    },
    {
      name: 'Throttle',
      icon: hammerOutline,
      isError: isThrottleError,
      description: 'Throttle Error'
    },
    {
      name: 'MotorPhase',
      icon: hammerOutline,
      isError: isMotorPhaseError,
      description: 'Motor Phase Error'
    },
    {
      name: 'MotorHall',
      icon: hammerOutline,
      isError: isMotorHallError,
      description: 'Motor Hall Error'
    },
    {
      name: 'TorqueSensor',
      icon: hammerOutline,
      isError: isTorqueSensorError,
      description: 'Torque Sensor Error'
    },
    {
      name: 'SpeedSensor',
      icon: hammerOutline,
      isError: isSpeedSensorError,
      description: 'Speed Sensor Error'
    }
  ]

  const presentAlert = async (item: any) => {
    const alert = await alertController.create({
      header: item.name,
      subHeader: item.description,
      buttons: ['OK']
    })

    await alert.present()
  }
</script>

<style scoped lang="scss">
  ion-item {
    --padding-start: 20px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --inner-padding-end: 20px;
  }
</style>
