<template>
  <ion-page>
    <ion-header :translucent="true">
      <ion-toolbar>
        <ion-title>蓝牙后台通讯演示</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">蓝牙后台通讯演示</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="container">
        <!-- 设备信息卡片 -->
        <ion-card v-if="deviceInfo">
          <ion-card-header>
            <ion-card-title>设备信息</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>厂商: {{ deviceInfo.manufacturer }}</h3>
                <p>型号: {{ deviceInfo.model }}</p>
                <p>Android版本: {{ deviceInfo.androidVersion }}</p>
                <p>SDK版本: {{ deviceInfo.sdkVersion }}</p>
              </ion-label>
            </ion-item>
            <ion-item v-if="deviceInfo.hasAggressivePowerManagement">
              <ion-icon :icon="warningOutline" color="warning" slot="start"></ion-icon>
              <ion-label color="warning">
                <h3>电源管理较严格</h3>
                <p>此设备可能需要额外配置以保持后台运行</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 系统状态卡片 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>系统状态</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>后台服务状态</h3>
              </ion-label>
              <ion-chip :color="isServiceRunning ? 'success' : 'medium'" slot="end">
                <ion-icon :icon="isServiceRunning ? checkmarkCircle : stopCircle"></ion-icon>
                <ion-label>{{ isServiceRunning ? '运行中' : '已停止' }}</ion-label>
              </ion-chip>
            </ion-item>
            
            <ion-item>
              <ion-label>
                <h3>Doze模式状态</h3>
                <p>设备是否处于深度休眠模式</p>
              </ion-label>
              <ion-chip :color="isInDozeMode ? 'danger' : 'success'" slot="end">
                <ion-icon :icon="isInDozeMode ? moon : eyeOutline"></ion-icon>
                <ion-label>{{ isInDozeMode ? 'Doze模式' : '正常模式' }}</ion-label>
              </ion-chip>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>蓝牙连接状态</h3>
              </ion-label>
              <ion-chip :color="connectedDevice?.isPaired ? 'success' : 'medium'" slot="end">
                <ion-icon :icon="connectedDevice?.isPaired ? bluetoothOutline : bluetooth"></ion-icon>
                <ion-label>{{ connectedDevice?.isPaired ? '已连接' : '未连接' }}</ion-label>
              </ion-chip>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 操作按钮 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>操作控制</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-button 
              expand="block" 
              :color="isServiceRunning ? 'danger' : 'primary'"
              @click="toggleService"
              :disabled="!connectedDevice?.isPaired"
            >
              <ion-icon :icon="isServiceRunning ? stop : play" slot="start"></ion-icon>
              {{ isServiceRunning ? '停止发送数据' : '开始发送数据' }}
            </ion-button>

            <ion-button 
              expand="block" 
              fill="outline" 
              color="secondary"
              @click="checkDozeStatus"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              检查系统状态
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- 设置指导卡片 -->
        <ion-card v-if="deviceInfo?.hasAggressivePowerManagement || isInDozeMode">
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="settingsOutline" slot="start"></ion-icon>
              设置指导
            </ion-card-title>
            <ion-card-subtitle>为了确保后台蓝牙通讯正常工作，请完成以下设置</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <ion-item button @click="openBatteryOptimizationSettings">
              <ion-icon :icon="batteryHalfOutline" slot="start" color="warning"></ion-icon>
              <ion-label>
                <h3>电池优化设置</h3>
                <p>将应用加入电池优化白名单</p>
              </ion-label>
              <ion-icon :icon="chevronForwardOutline" slot="end"></ion-icon>
            </ion-item>

            <ion-item button @click="openAutoStartSettings" v-if="deviceInfo?.hasAggressivePowerManagement">
              <ion-icon :icon="rocketOutline" slot="start" color="primary"></ion-icon>
              <ion-label>
                <h3>自启动管理</h3>
                <p>允许应用自启动和后台运行</p>
              </ion-label>
              <ion-icon :icon="chevronForwardOutline" slot="end"></ion-icon>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 使用说明 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="informationCircleOutline" slot="start"></ion-icon>
              使用说明
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-text>
              <h3>功能特性：</h3>
              <ul>
                <li>✅ 106ms精确间隔发送蓝牙数据</li>
                <li>✅ 息屏后台保持通讯</li>
                <li>✅ 多重定时器策略保障</li>
                <li>✅ Doze模式检测和对抗</li>
                <li>✅ 前台服务保持运行</li>
                <li>✅ 唤醒锁防止CPU休眠</li>
              </ul>

              <h3>注意事项：</h3>
              <ul>
                <li>⚠️ 首次使用需要手动设置电池优化</li>
                <li>⚠️ 某些厂商设备需要额外的自启动设置</li>
                <li>⚠️ 长时间后台运行会增加电池消耗</li>
                <li>⚠️ 系统更新后可能需要重新设置权限</li>
              </ul>

              <h3>故障排除：</h3>
              <ul>
                <li>🔧 如果息屏后停止发送，请检查电池优化设置</li>
                <li>🔧 如果服务频繁重启，请检查自启动权限</li>
                <li>🔧 如果Doze模式频繁激活，请考虑使用充电器</li>
              </ul>
            </ion-text>
          </ion-card-content>
        </ion-card>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardSubtitle,
  IonCardContent,
  IonButton,
  IonItem,
  IonLabel,
  IonIcon,
  IonChip,
  IonText,
} from '@ionic/vue';
import {
  play,
  stop,
  bluetoothOutline,
  bluetooth,
  checkmarkCircle,
  stopCircle,
  refreshOutline,
  settingsOutline,
  batteryHalfOutline,
  rocketOutline,
  chevronForwardOutline,
  informationCircleOutline,
  warningOutline,
  moon,
  eyeOutline,
} from 'ionicons/icons';
import { useSmartBluetoothMessage } from '@/hooks/useSmartBluetoothMessage';
import { useBackgroundBluetooth } from '@/hooks/useBackgroundBluetooth';
import { useBleStore } from '@/store/useBleStore';
import { storeToRefs } from 'pinia';
import { useToast } from '@/hooks/useToast';

const { sendMessage, stopSendMessage, isServiceRunning } = useSmartBluetoothMessage();
const { 
  isInDozeMode, 
  deviceInfo, 
  checkDozeStatus, 
  openBatteryOptimizationSettings, 
  openAutoStartSettings,
  getDeviceInformation
} = useBackgroundBluetooth();
const { connectedDevice } = storeToRefs(useBleStore());
const { presentToast } = useToast();

const toggleService = async () => {
  try {
    if (isServiceRunning.value) {
      await stopSendMessage();
      await presentToast('已停止蓝牙数据发送');
    } else {
      if (!connectedDevice.value?.isPaired) {
        await presentToast('请先连接蓝牙设备');
        return;
      }
      await sendMessage();
      await presentToast('已开始蓝牙数据发送，请检查通知栏');
    }
  } catch (error) {
    console.error('Toggle service error:', error);
    await presentToast('操作失败，请重试');
  }
};

onMounted(async () => {
  // 获取设备信息
  await getDeviceInformation();
  // 检查Doze状态
  await checkDozeStatus();
});
</script>

<style scoped>
.container {
  padding: 16px;
}

ion-card {
  margin-bottom: 16px;
}

ion-button {
  margin-top: 8px;
}

ul {
  padding-left: 20px;
  margin: 8px 0;
}

li {
  margin: 4px 0;
  line-height: 1.4;
}

h3 {
  margin: 12px 0 8px 0;
  color: var(--ion-color-primary);
}
</style>