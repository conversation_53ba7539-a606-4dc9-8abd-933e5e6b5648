<template>
  <ion-page class="page-setting">
    <ion-header>
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button default-href="/tabs/my"></ion-back-button>
        </ion-buttons>
        <ion-title>Setting</ion-title>
        <ion-buttons slot="end">
          <ion-button fill="solid" @click="saveSettings">Save</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content :fullscreen="true">
      <ion-list>
        <ion-list-header>
          <ion-label>Parameters</ion-label>
        </ion-list-header>

        <ion-item>
          <ion-input v-model="maxSpeed" :max="72" :maxlength="2" :min="10" inputmode="numeric" label="MaxSpeed"
            name="maxSpeed" placeholder="km/h" type="number">
          </ion-input>
        </ion-item>

        <modal-select
          v-model="dimension"
          :list="dimensionList"
          modal-name="dimension"
          label="Bike Dimension"
          placeholder="Select Dimension"
        />

        <ion-item>
          <ion-input v-model="p1" inputmode="numeric" label="MotorSetting (P1)" name="p1" placeholder="P1"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p2" inputmode="numeric" label="SpeedSensor (P2)" name="p2" placeholder="P2"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p3" inputmode="numeric" label="Torque (P3)" name="p3" placeholder="P3"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p4" inputmode="numeric" label="ZeroStart (P4)" name="p4" placeholder="P4"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="p5" inputmode="numeric" label="Battery (P5)" name="p5" placeholder="P5"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c1" inputmode="numeric" label="PAS (C1)" name="c1" placeholder="C1"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c2" inputmode="numeric" label="MotorPhase (C2)" name="c2" placeholder="C2"
            type="number"></ion-input>
        </ion-item>

        <!-- <ion-item>
          <ion-select v-model="c3" :toggle-icon="chevronForwardOutline" :multiple="false" label="InitLevel (C3)"
            name="initLevel" placeholder="Select InitLevel">
            <ion-select-option v-for="(item, index) of levelList" :key="index" :value="item.value">
              {{ item.name }}
            </ion-select-option>
          </ion-select>
        </ion-item> -->
        <modal-select
          v-model="c3"
          :list="levelList"
          modal-name="initLevel"
          label="InitLevel (C3)"
          placeholder="Select InitLevel"
        />
        
        <ion-item>
          <ion-input v-model="c4" inputmode="numeric" label="Throttle (C4)" name="c4" placeholder="C4"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c5" inputmode="numeric" label="Current (C5)" name="c5" placeholder="C5"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c7" inputmode="numeric" label="Cruise (C7)" name="c7" placeholder="C7"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c12" inputmode="numeric" label="UVLO (C12)" name="c12" placeholder="C12"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c13" inputmode="numeric" label="Regenerative (C13)" name="c13" placeholder="C13"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="c14" inputmode="numeric" label="PASPower (C14)" name="c14" placeholder="C14"
            type="number"></ion-input>
        </ion-item>

        <ion-item>
          <ion-input v-model="handlebarMaxSpeed" inputmode="numeric" label="Handlebar Maximum Speed"
            name="handleBarSpeed" placeholder="data.displayType === 'kilometer' ? 'km/h' : 'mile/h'"
            type="number"></ion-input>
          <!--TODO  c4必须等于2，才会起作用        -->
        </ion-item>

        <ion-item>
          <ion-input v-model="percent" inputmode="numeric" label="FirstLevelPercent (Throttle)" name="percent"
            placeholder="percentage" type="number"></ion-input>
          <!--TODO  c4必须等于4，才会起作用        -->
        </ion-item>

        <ion-item>
          <ion-input v-model="candidateParam" inputmode="numeric" label="Candidate" name="candidate"
            placeholder="Signal of PAS" type="number"></ion-input>
        </ion-item>

        <ion-radio-group v-model="displayType" name="displayType">
          <ion-list-header>Kilometer／Mile</ion-list-header>
          <ion-item>
            <ion-radio value="kilometer">Kilometer</ion-radio>
          </ion-item>
          <ion-item>
            <ion-radio value="mile">Mile</ion-radio>
          </ion-item>
        </ion-radio-group>
      </ion-list>
      <ion-grid>
        <ion-row>
          <ion-col class="page-setting__restore">
            <ion-button fill="outline" shape="round" size="small" @click="restore">Restore Settings
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>

    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonButton,
  IonButtons,
  IonBackButton,
  IonCol,
  IonContent,
  IonGrid,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonListHeader,
  IonPage,
  IonRadio,
  IonRadioGroup,
  IonRow,
  IonSelect,
  IonSelectOption,
  IonTitle,
  IonToolbar,
  onIonViewWillEnter,
  onIonViewWillLeave,
} from "@ionic/vue";
import { useSettingStore } from "@/store/useSettingStore";
import { useMessage } from "@/hooks/useMessage";
import { useSetting } from "@/hooks/useSetting";
import { useErrorStore } from "@/store/useErrorStore";
import { useDataValidator } from "@/hooks/useDataValidator";
import { storeToRefs } from "pinia";
import { chevronForwardOutline } from "ionicons/icons";
import ModalSelect from '@/components/ModalSelect.vue'

const settingStore = useSettingStore();
const errorStore = useErrorStore();
const {
  maxSpeed,
  dimension,
  dimensionList,
  p1,
  p2,
  p3,
  p4,
  p5,
  c1,
  c2,
  c3,
  c4,
  c5,
  c7,
  c12,
  c13,
  c14,
  levelList,
  handlebarMaxSpeed,
  percent,
  candidateParam,
  displayType,
} = storeToRefs(settingStore);

const { updateSetting, writeData } = useSetting();
const { monitorDataChanges, arrayToHexString } = useDataValidator();

/**
 * 获取最新蓝牙数据中的导航数据部分
 * @returns Promise<{navigationData: number[], mirrorBit: number}> 导航数据和镜像位
 */
const getLatestNavigationData = async () => {
  try {
    // 动态导入避免循环依赖
    const { bluetoothServiceHelper } = await import("@/utils/bluetoothServiceHelper");
    
    const result = await bluetoothServiceHelper.getCurrentBluetoothSendData();
    if (result.success && result.data && result.data.length >= 18) {
      return {
        navigationData: result.data.slice(12, 16), // 字节12-15
        mirrorBit: result.data[12] & 0x80 // 保存镜像位
      };
    }
  } catch (error) {
    console.warn("获取最新蓝牙数据失败:", error);
  }
  
  // 返回默认值
  return {
    navigationData: [0, 0, 0, 0],
    mirrorBit: 0
  };
};

/**
 * 计算校验位
 * @param data 数据数组
 * @returns {checksum5: number, checksum16: number} 两个校验位的值
 */
const calculateChecksums = (data: number[]) => {
  // 索引5的校验位：对字节1-4和6-11进行异或
  let checksum5 = 0;
  for (let i = 1; i <= 4; i++) {
    checksum5 ^= data[i];
  }
  for (let i = 6; i <= 11; i++) {
    checksum5 ^= data[i];
  }
  
  // 索引16的校验位：对字节1-15进行异或（跳过字节5）
  let checksum16 = 0;
  for (let i = 1; i <= 15; i++) {
    if (i !== 5) {
      checksum16 ^= data[i];
    }
  }
  
  return {
    checksum5: checksum5 & 0xFF,
    checksum16: checksum16 & 0xFF
  };
};

/**
 * 更新蓝牙数据的核心逻辑
 * 在最新蓝牙数据基础上更新1-11字节，保留导航数据，重新计算校验位
 */
const updateBluetoothData = async () => {
  console.log("🔧 开始更新蓝牙数据");
  
  try {
    // 1. 获取最新蓝牙数据中的导航数据
    const { navigationData, mirrorBit } = await getLatestNavigationData();
    console.log("📊 获取到的导航数据:", navigationData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
    console.log("📊 镜像位状态:", mirrorBit ? '开启' : '关闭');
    
    // 2. 更新设置数据到store
    updateSetting();
    
    // 3. 创建完整的蓝牙数据数组
    const completeData = [...writeData.value];
    
    // 4. 合并导航数据（字节12-15）
    for (let i = 0; i < 4; i++) {
      completeData[12 + i] = navigationData[i];
    }
    
    // 5. 恢复镜像位（字节12的第7位）
    if (mirrorBit) {
      completeData[12] = completeData[12] | 0x80;
    } else {
      completeData[12] = completeData[12] & 0x7F;
    }
    
    // 6. 重新计算校验位
    const { checksum5, checksum16 } = calculateChecksums(completeData);
    completeData[5] = checksum5;
    completeData[16] = checksum16;
    
    // 7. 确保结束位正确
    completeData[17] = 0x0E;
    
    console.log("📊 完整的蓝牙数据:", completeData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
    console.log("📊 校验位 - 索引5:", `0x${checksum5.toString(16).padStart(2, '0').toUpperCase()}`, "索引16:", `0x${checksum16.toString(16).padStart(2, '0').toUpperCase()}`);
    
    // 8. 更新所有蓝牙方案的数据
    await updateAllBluetoothSolutions(completeData);
    
    console.log("✅ 蓝牙数据更新完成");
    return completeData;
    
  } catch (error) {
    console.error("❌ 更新蓝牙数据失败:", error);
    throw error;
  }
};

/**
 * 更新当前活跃的蓝牙方案数据 - 根据实际运行状态智能选择
 * @param data 完整的蓝牙数据
 */
const updateAllBluetoothSolutions = async (data: number[]) => {
  try {
    console.log("🔧 开始更新当前活跃的蓝牙方案数据");
    
    // 动态导入所有蓝牙方案hooks
    const [
      { useMessage },
      { useNativeBluetoothMessage },
      { useSmartBluetoothMessage }
    ] = await Promise.all([
      import("@/hooks/useMessage"),
      import("@/hooks/useNativeBluetoothMessage"),
      import("@/hooks/useSmartBluetoothMessage")
    ]);
    
    const messageHook = useMessage();
    const nativeHook = useNativeBluetoothMessage();
    const smartHook = useSmartBluetoothMessage();
    
    // 🔍 检查各方案的服务运行状态
    const serviceStates = {
      traditional: false,
      native: false,
      smart: false
    };
    
    try {
      // 检查传统方案状态
      if (messageHook.isServiceRunning) {
        serviceStates.traditional = await messageHook.isServiceRunning.value;
      }
      
      // 检查原生方案状态
      if (nativeHook.isNativeSending?.value !== undefined) {
        serviceStates.native = nativeHook.isNativeSending.value;
      }
      // 检查智能方案状态
      if (smartHook.isServiceRunning?.value !== undefined) {
        serviceStates.smart = smartHook.isServiceRunning.value;
      }
    } catch (error) {
      console.warn("⚠️ 检查服务状态时出错:", error);
    }
    
    console.log("📊 各蓝牙方案服务状态:", serviceStates);
    
    // 🎯 根据服务状态确定活跃方案并更新
    let activeScheme = null;
    let updateSuccess = false;
    
    // 优先级：智能方案 > 原生方案 > 传统方案
    if (serviceStates.smart) {
      console.log("🔧 检测到智能蓝牙方案正在运行，更新智能方案数据");
      activeScheme = 'smart';
      if (smartHook.updateSmartBluetoothData) {
        await smartHook.updateSmartBluetoothData();
        updateSuccess = true;
        console.log("✅ 智能蓝牙方案数据已更新");
      }
    } else if (serviceStates.native) {
      console.log("🔧 检测到原生蓝牙方案正在运行，更新原生方案数据");
      activeScheme = 'native';
      if (nativeHook.updateNativeBluetoothData) {
        await nativeHook.updateNativeBluetoothData();
        updateSuccess = true;
        console.log("✅ 原生蓝牙方案数据已更新");
      }
    } else if (serviceStates.traditional) {
      console.log("🔧 检测到传统蓝牙方案正在运行，更新传统方案数据");
      activeScheme = 'traditional';
      if (messageHook.updateSendDataCache) {
        await messageHook.updateSendDataCache();
        updateSuccess = true;
        console.log("✅ 传统蓝牙方案数据已更新");
      }
    } else {
      // 🔧 如果没有检测到活跃服务，根据平台选择默认方案
      console.log("⚠️ 未检测到活跃的蓝牙服务，根据平台选择默认方案");
      const isAndroid = /android/i.test(navigator.userAgent);
      
      if (isAndroid) {
        console.log("🔧 Android平台 - 尝试更新原生蓝牙方案");
        activeScheme = 'native-fallback';
        if (nativeHook.updateNativeBluetoothData) {
          await nativeHook.updateNativeBluetoothData();
          updateSuccess = true;
          console.log("✅ Android - 原生蓝牙方案数据已更新（默认方案）");
        }
      } else {
        console.log("🔧 iOS/Web平台 - 尝试更新传统方案");
        activeScheme = 'traditional-fallback';
        if (messageHook.updateSendDataCache) {
          await messageHook.updateSendDataCache();
          updateSuccess = true;
          console.log("✅ iOS/Web - 传统方案数据已更新（默认方案）");
        }
      }
    }
    
    // 发送全局更新事件
    window.dispatchEvent(new CustomEvent('settingDataUpdated', {
      detail: {
        writeData: data,
        timestamp: Date.now(),
        source: 'SettingPage.updateBluetoothData.smart',
        activeScheme,
        serviceStates,
        updateSuccess,
        note: '智能选择活跃方案，避免重复调用'
      }
    }));
    
    if (updateSuccess) {
      console.log(`✅ 蓝牙数据更新完成 - 活跃方案: ${activeScheme}`);
    } else {
      console.warn("⚠️ 未能成功更新任何蓝牙方案的数据");
    }
    
  } catch (error) {
    console.error("❌ 更新活跃蓝牙方案数据失败:", error);
    throw error;
  }
};

onIonViewWillLeave(async () => {
  console.log("🔧 SettingPage - 页面即将离开，更新蓝牙数据");
  
  try {
    await updateBluetoothData();
    console.log("✅ 页面离开时数据更新完成");
  } catch (error) {
    console.error("❌ 页面离开时数据更新失败:", error);
  }
});

const saveSettings = async () => {
  console.log("🔧 SettingPage - 手动保存设置");
  
  try {
    await updateBluetoothData();
    
    // 显示成功提示
    const { useToast } = await import("@/hooks/useToast");
    const { presentToast } = useToast();
    await presentToast("设置已保存并同步到设备");
    
    console.log("✅ 设置保存完成");
    
  } catch (error) {
    console.error("❌ 设置保存失败:", error);
    
    // 显示错误提示
    try {
      const { useToast } = await import("@/hooks/useToast");
      const { presentToast } = useToast();
      await presentToast("设置保存失败，请重试");
    } catch (toastError) {
      console.error("❌ 显示错误提示失败:", toastError);
    }
  }
};

const restore = () => {
  settingStore.$reset();
  errorStore.$reset();
};
</script>
<style lang="scss">
.page-setting {
  ion-list-header {
    --color: var(--ion-color-primary);
  }

  ion-button:not(.button-round) {
    --border-radius: 8px;
    --padding-start: 12px;
    --padding-end: 12px;
  }

  ion-input {
    text-align: right;
  }

  .page-setting__restore {
    display: flex;
    justify-content: center;
  }
}
</style>
