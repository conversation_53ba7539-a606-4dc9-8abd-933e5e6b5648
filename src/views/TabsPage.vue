<template>
  <ion-page>
    <ion-tabs class="tabs" @ionTabsWillChange="handleIonTabsWillChange">
      <ion-router-outlet></ion-router-outlet>
      <ion-tab-bar slot="bottom">
        <ion-tab-button href="/tabs/home" tab="home">
          <ion-icon :icon="bicycle" aria-hidden="true" />
          <ion-label>Home</ion-label>
        </ion-tab-button>
        <!-- <ion-tab-button href="/tabs/navigation" tab="navi">
          <ion-icon :icon="mapOutline" aria-hidden="true" />
          <ion-label>Navigation</ion-label>
        </ion-tab-button> -->
        <ion-tab-button href="/tabs/mapbox" tab="mapbox">
          <ion-icon :icon="mapOutline" aria-hidden="true" />
          <ion-label>MapBox</ion-label>
        </ion-tab-button>
        <ion-tab-button href="/tabs/my" tab="my">
          <ion-icon aria-hidden="true" src="/assets/icon/icon-my.svg" />
          <ion-label>My</ion-label>
        </ion-tab-button>
      </ion-tab-bar>
    </ion-tabs>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonIcon,
  IonLabel,
  IonPage,
  IonRouterOutlet,
  IonTabBar,
  IonTabButton,
  IonTabs
} from '@ionic/vue'
import { bicycle, mapOutline } from 'ionicons/icons'

import { useHaptics } from '@/hooks/useHaptics'

const { hapticsImpactMedium } = useHaptics()

const handleIonTabsWillChange = () => {
  hapticsImpactMedium()
  console.log('handleIonTabButtonClick')
}
</script>
<style lang="scss" scoped>
ion-tab-button {
  &:not(.tab-selected) {
    ion-icon {
      color: #fff;
    }
  }
}

.tabs {
  .tabs__item {
    position: relative;

    .tabs-icon__alert {
      position: absolute;
      font-size: 1rem;
      top: 0;
      right: 20px;

      &.tabs-icon__alert--on {
        display: block;
        animation: twinkle 0.5s infinite alternate;
      }

      &.tabs-icon__alert--off {
        display: none;
      }
    }
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}
</style>
