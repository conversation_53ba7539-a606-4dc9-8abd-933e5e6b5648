<template>
  <ion-page class="my-page">
    <ion-header>
      <ion-toolbar>
        <ion-title>My</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding">
      <div class="my-page__company">
        <ion-icon class="my-page__company-logo" src="/assets/icon/logo.svg"></ion-icon>
        <div class="my-page__company-name">
          <ion-text color="primary">SUZHOU KUNTENG ELECTRONICS CO.,LTD</ion-text>
        </div>

        <div class="my-page__company-url">
          <ion-text color="medium">www.szktdz.com</ion-text>
        </div>

      </div>

      <ion-item class="my-page__link" lines="none" @click="goToHelp">
        <ion-icon slot="start" :icon="warningOutline"></ion-icon>
        <ion-label>Info</ion-label>
      </ion-item>
      <ion-item class="my-page__link" lines="none" @click="goToSetting">
        <ion-icon slot="start" :icon="settingsOutline"></ion-icon>
        <ion-label>Settings</ion-label>
      </ion-item>

      <!-- 开发者工具区域 -->
      <div class="developer-tools">
        <ion-text class="developer-tools__title">
          <h3>开发者工具</h3>
        </ion-text>

        <ion-item class="my-page__link developer-tools__item" lines="none" @click="navigateToComparison">
          <ion-icon slot="start" :icon="analyticsOutline"></ion-icon>
          <ion-label>
            <h3>蓝牙数据对比测试</h3>
            <p>在真实环境下比较重构前后蓝牙数据一致性</p>
          </ion-label>
          <ion-icon slot="end" :icon="chevronForwardOutline"></ion-icon>
        </ion-item>

        <ion-item class="my-page__link developer-tools__item" lines="none" @click="navigateToNavigationControl">
          <ion-icon slot="start" :icon="navigateOutline"></ion-icon>
          <ion-label>
            <h3>导航界面控制</h3>
            <p>手动控制导航Fragment的显示和隐藏</p>
          </ion-label>
          <ion-icon slot="end" :icon="chevronForwardOutline"></ion-icon>
        </ion-item>
      </div>

    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonItem,
  IonIcon,
  IonLabel,
  IonText
} from "@ionic/vue";
import {
  settingsOutline,
  warningOutline,
  analyticsOutline,
  navigateOutline,
  chevronForwardOutline
} from "ionicons/icons";
import { useRouter } from 'vue-router';

const router = useRouter();

const navigateToComparison = () => {
  router.push('/bluetooth-comparison');
};

const navigateToNavigationControl = () => {
  router.push('/navigation-control');
};

const goToSetting = () => {
  router.push({ name: 'setting' });
}

const goToHelp = () => {
  router.push({ name: 'help' });
}
</script>

<style lang="scss" scoped>
.my-page {
  .my-page__company {
    width: 100%;
    height: 160px;
    background: #191A21;
    border-radius: 16px;
    background-image: url(/assets/icon/bicycle.svg);
    background-repeat: no-repeat;
    background-position-x: 100%;

    .my-page__company-logo {
      width: 40px;
      height: 40px;
      margin: 30px 30px 0 30px;
    }

    .my-page__company-name {
      font-size: 12px;
      margin: 24px 0 0 30px;
    }
    .my-page__company-url {
      font-size: 12px;
      margin: 6px 0 0 30px;
    }
  }
  .my-page__link {
    --padding-start: 18px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --inner-padding-end: 20px;
    --background: #191A21;
    --border-radius: 16px;
    margin-top: 16px;
  }

  .developer-tools {
    margin-top: 32px;

    .developer-tools__title {
      margin-left: 18px;
      margin-bottom: 16px;

      h3 {
        color: var(--ion-color-medium);
        font-size: 14px;
        font-weight: 600;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .developer-tools__item {
      ion-label {
        h3 {
          color: var(--ion-color-primary);
          font-size: 16px;
          font-weight: 500;
          margin: 0 0 4px 0;
        }

        p {
          color: var(--ion-color-medium);
          font-size: 12px;
          margin: 0;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
