<template>
  <ion-page>
    <ion-header :translucent="true">
      <ion-toolbar>
        <ion-buttons slot="start">
          <ion-back-button></ion-back-button>
        </ion-buttons>
        <ion-title>蓝牙数据对比测试（真实环境）</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="clearResults">
            <ion-icon :icon="refreshOutline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">蓝牙数据对比测试（真实环境）</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="container">
        <!-- 测试说明 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="informationCircleOutline" style="margin-right: 8px;"></ion-icon>
              真实环境测试说明
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <p>此工具监听真实的导航数据变化，获取Native端实际发送的蓝牙数据，并与useNavigation生成的数据进行对比。</p>
            <p>⚠️ 需要启动导航并开启蓝牙发送服务才能进行真实测试。</p>
          </ion-card-content>
        </ion-card>

        <!-- 测试状态 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>测试状态</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>监听状态</h3>
                <p :style="{ color: isListening ? 'var(--ion-color-success)' : 'var(--ion-color-medium)' }">
                  {{ isListening ? '正在监听导航数据变化' : '未启动监听' }}
                </p>
              </ion-label>
              <ion-button 
                :color="isListening ? 'danger' : 'primary'"
                @click="toggleListener"
                slot="end"
              >
                {{ isListening ? '停止监听' : '开始监听' }}
              </ion-button>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>蓝牙发送状态</h3>
                <p :style="{ color: bluetoothStats.isConnected ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                  {{ bluetoothStats.isConnected ? '蓝牙发送中' : '蓝牙未连接' }}
                </p>
                <p v-if="bluetoothStats.isConnected">
                  已发送: {{ bluetoothStats.totalSent }}, 错误: {{ bluetoothStats.errorCount }}
                </p>
              </ion-label>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>最后收到导航数据</h3>
                <p>{{ lastNavigationData ? formatTime(lastNavigationData.timestamp) : '无' }}</p>
              </ion-label>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>镜像状态</h3>
                <p :style="{ color: mirrorState.enabled ? 'var(--ion-color-success)' : 'var(--ion-color-medium)' }">
                  {{ mirrorState.enabled ? '镜像已开启' : '镜像已关闭' }}
                </p>
                <p v-if="mirrorState.lastUpdate">最后更新: {{ formatTime(mirrorState.lastUpdate) }}</p>
              </ion-label>
              <ion-button 
                @click="refreshMirrorState"
                slot="end"
                fill="clear"
              >
                <ion-icon :icon="refreshOutline"></ion-icon>
              </ion-button>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 手动测试功能 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>手动测试（模拟导航数据）</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-select v-model="testData.type" placeholder="选择操作类型">
                <ion-select-option value="turn">转弯 (turn)</ion-select-option>
                <ion-select-option value="continue">直行 (continue)</ion-select-option>
                <ion-select-option value="arrive">到达 (arrive)</ion-select-option>
                <ion-select-option value="depart">出发 (depart)</ion-select-option>
              </ion-select>
              <ion-label slot="start">操作类型</ion-label>
            </ion-item>

            <ion-item>
              <ion-select v-model="testData.modifier" placeholder="选择方向修饰符">
                <ion-select-option value="left">左转 (left)</ion-select-option>
                <ion-select-option value="right">右转 (right)</ion-select-option>
                <ion-select-option value="straight">直行 (straight)</ion-select-option>
                <ion-select-option value="slight left">稍左转 (slight left)</ion-select-option>
                <ion-select-option value="slight right">稍右转 (slight right)</ion-select-option>
                <ion-select-option value="sharp left">急左转 (sharp left)</ion-select-option>
                <ion-select-option value="sharp right">急右转 (sharp right)</ion-select-option>
                <ion-select-option value="uturn">调头 (uturn)</ion-select-option>
              </ion-select>
              <ion-label slot="start">方向修饰符</ion-label>
            </ion-item>

            <ion-item>
              <ion-input 
                v-model.number="testData.stepDistance" 
                type="number" 
                placeholder="输入单次距离(米)"
              ></ion-input>
              <ion-label slot="start">单次距离</ion-label>
            </ion-item>

            <ion-item>
              <ion-input 
                v-model.number="testData.totalDistance" 
                type="number" 
                placeholder="输入总距离(米)"
              ></ion-input>
              <ion-label slot="start">总距离</ion-label>
            </ion-item>

            <ion-button 
              expand="block" 
              @click="performManualComparison"
              :disabled="!canPerformTest || !bluetoothStats.isConnected"
              class="ion-margin-top"
            >
              <ion-icon :icon="playOutline" slot="start"></ion-icon>
              执行手动对比测试
            </ion-button>
            
            <ion-button 
              expand="block" 
              @click="checkDataConsistency"
              color="secondary"
              class="ion-margin-top"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              检查数据一致性
            </ion-button>
            
            <ion-button 
              expand="block" 
              @click="runValidationTests"
              color="tertiary"
              class="ion-margin-top"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              运行验证测试
            </ion-button>
            
            <ion-button 
              expand="block" 
              @click="performNoNavigationComparison"
              color="warning"
              class="ion-margin-top"
            >
              <ion-icon :icon="playOutline" slot="start"></ion-icon>
              无导航状态对比测试
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- 对比结果 -->
        <ion-card v-if="comparisonResults.length > 0">
          <ion-card-header>
            <ion-card-title>
              <ion-icon 
                :icon="comparisonSummary.allMatch ? checkmarkCircleOutline : closeCircleOutline" 
                :color="comparisonSummary.allMatch ? 'success' : 'danger'"
                style="margin-right: 8px;"
              ></ion-icon>
              对比结果 ({{ comparisonSummary.matchCount }}/{{ comparisonSummary.totalCount }})
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-list v-for="(result, index) in comparisonResults" :key="index">
              <ion-item>
                <ion-icon 
                  :icon="result.isMatch ? checkmarkCircleOutline : closeCircleOutline" 
                  :color="result.isMatch ? 'success' : 'danger'"
                  slot="start"
                ></ion-icon>
                <ion-label>
                  <h3>{{ result.testName }}</h3>
                  <p>{{ result.testType === 'real' ? '真实导航' : '手动测试' }} - {{ result.timestamp }}</p>
                  <p v-if="!result.isMatch" style="color: var(--ion-color-danger);">
                    发现 {{ result.differences.length }} 处数据差异！
                  </p>
                </ion-label>
                <ion-button 
                  fill="clear" 
                  @click="showDetails(result)"
                  slot="end"
                >
                  查看详情
                </ion-button>
              </ion-item>
              
              <!-- 详细对比信息 -->
              <div v-if="result.showDetails" class="comparison-details">
                <ion-item>
                  <ion-label>
                    <h4>测试参数:</h4>
                    <p v-if="result.testType === 'real'">来源: 真实导航数据</p>
                    <p v-else>操作: {{ result.params?.type }}/{{ result.params?.modifier }}</p>
                    <p v-if="result.params">单次距离: {{ result.params.stepDistance }}m</p>
                    <p v-if="result.params">总距离: {{ result.params.totalDistance }}m</p>
                  </ion-label>
                </ion-item>

                <ion-item>
                  <ion-label>
                    <h4>useNavigation 生成的数据:</h4>
                    <div class="data-array">
                      <span 
                        v-for="(byte, i) in result.navigationData" 
                        :key="`nav-${i}`"
                        :class="{'byte-diff': !result.isMatch && result.nativeData[i] !== byte}"
                        class="byte-value"
                      >
                        {{ formatByte(byte) }}
                      </span>
                    </div>
                  </ion-label>
                </ion-item>

                <ion-item>
                  <ion-label>
                    <h4>Native端实际发送的数据:</h4>
                    <div class="data-array">
                      <span 
                        v-for="(byte, i) in result.nativeData" 
                        :key="`native-${i}`"
                        :class="{'byte-diff': !result.isMatch && result.navigationData[i] !== byte}"
                        class="byte-value"
                      >
                        {{ formatByte(byte) }}
                      </span>
                    </div>
                  </ion-label>
                </ion-item>

                <ion-item v-if="!result.isMatch">
                  <ion-label>
                    <h4 style="color: var(--ion-color-danger);">差异字节:</h4>
                    <p v-for="diff in result.differences" :key="diff.index">
                      字节{{ diff.index }}: useNavigation={{ formatByte(diff.navigation) }} vs Native={{ formatByte(diff.native) }}
                    </p>
                  </ion-label>
                </ion-item>
              </div>
            </ion-list>
          </ion-card-content>
        </ion-card>

        <!-- 统计信息 -->
        <ion-card v-if="comparisonResults.length > 0">
          <ion-card-header>
            <ion-card-title>测试统计</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>测试总数: {{ comparisonSummary.totalCount }}</h3>
                <p>匹配数: {{ comparisonSummary.matchCount }}</p>
                <p>不匹配数: {{ comparisonSummary.mismatchCount }}</p>
                <p>匹配率: {{ comparisonSummary.matchRate }}%</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>数据来源统计:</h3>
                <p>真实导航: {{ realTestCount }}次</p>
                <p>手动测试: {{ manualTestCount }}次</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonButton,
  IonButtons,
  IonBackButton,
  IonIcon,
  IonList
} from '@ionic/vue'
import {
  playOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  informationCircleOutline,
  refreshOutline
} from 'ionicons/icons'
import { useNavigation } from '@/hooks/useNavigation'
import { useToast } from '@/hooks/useToast'
import { useSetting } from '@/hooks/useSetting'
import { CapacitorKtService } from 'capacitor-kt-service'
import { isPlatform } from '@ionic/vue'
import { 
  validateBluetoothData, 
  compareBluetoothData, 
  repairBluetoothData,
  generateDiagnosticReport,
  type ValidationResult,
  type ComparisonResult as ValidationComparisonResult
} from '@/utils/dataValidation'
import { runDataValidationTests, testSpecificData } from '@/utils/dataValidationTest'

// 测试数据结构
const testData = reactive({
  type: 'turn',
  modifier: 'left',
  stepDistance: 150,
  totalDistance: 1000
})

// 监听状态
const isListening = ref(false)
const lastNavigationData = ref<any>(null)
const bluetoothStats = ref({
  isConnected: false,
  totalSent: 0,
  errorCount: 0
})

// 镜像状态
const mirrorState = ref({
  enabled: false,
  lastUpdate: null as number | null
})

// 对比结果结构
interface ComparisonResult {
  testName: string
  timestamp: string
  testType: 'real' | 'manual'
  params?: typeof testData
  navigationData: number[]
  nativeData: number[]
  isMatch: boolean
  differences: Array<{
    index: number
    navigation: number
    native: number
  }>
  showDetails: boolean
  // 🔧 新增：详细分析结果
  analysisDetails?: any
}

const comparisonResults = ref<ComparisonResult[]>([])
const { presentToast } = useToast()

// 🔧 获取设置数据以确保数据同步
const setting = useSetting()

// 监听器
let routeProgressListener: any = null

// 检查是否可以执行测试
const canPerformTest = computed(() => {
  return testData.type && 
         testData.modifier && 
         typeof testData.stepDistance === 'number' && 
         typeof testData.totalDistance === 'number'
})

// 统计信息
const comparisonSummary = computed(() => {
  const total = comparisonResults.value.length
  const matches = comparisonResults.value.filter(r => r.isMatch).length
  const mismatches = total - matches
  const matchRate = total > 0 ? Math.round((matches / total) * 100) : 0
  
  return {
    totalCount: total,
    matchCount: matches,
    mismatchCount: mismatches,
    matchRate,
    allMatch: mismatches === 0 && total > 0
  }
})

const realTestCount = computed(() => 
  comparisonResults.value.filter(r => r.testType === 'real').length
)

const manualTestCount = computed(() => 
  comparisonResults.value.filter(r => r.testType === 'manual').length
)

// 开启/关闭导航数据监听
const toggleListener = async () => {
  if (isListening.value) {
    await stopListener()
  } else {
    await startListener()
  }
}

// 开始监听导航数据变化
const startListener = async () => {
  try {
    console.log('🎧 开始监听导航数据变化')
    
    // 监听导航进度变化事件
    routeProgressListener = await CapacitorKtService.addListener('onRouteProgressChange', (data: any) => {
      console.log('📊 收到导航进度数据:', data)
      lastNavigationData.value = {
        ...data,
        timestamp: Date.now()
      }
      
      // 执行真实数据对比
      performRealDataComparison(data)
    })
    
    // 监听镜像状态变化事件
    const mirrorStateListener = await CapacitorKtService.addListener('onScreenMirroringChange', (data: any) => {
      console.log('🔄 镜像状态变化:', data)
      mirrorState.value = {
        enabled: data.enabled,
        lastUpdate: Date.now()
      }
    })
    
    isListening.value = true
    await presentToast('已开始监听导航数据变化')
    
    // 启动蓝牙状态更新定时器
    startBluetoothStatsUpdate()
    
    // 初始获取镜像状态
    await refreshMirrorState()
    
  } catch (error: any) {
    console.error('❌ 启动监听失败:', error)
    await presentToast(`启动监听失败: ${error.message}`)
  }
}

// 停止监听
const stopListener = async () => {
  try {
    if (routeProgressListener) {
      await routeProgressListener.remove()
      routeProgressListener = null
    }
    
    isListening.value = false
    await presentToast('已停止监听数据变化')
    
  } catch (error: any) {
    console.error('❌ 停止监听失败:', error)
    await presentToast(`停止监听失败: ${error.message}`)
  }
}

// 执行真实数据对比 - 使用独立计算避免影响全局状态
const performRealDataComparison = async (navigationData: any) => {
  try {
    console.log('🔍 开始执行真实数据对比（独立计算模式）')
    
    // 🔧 步骤0：同步设置数据并验证导航数据的有效性
    const navigation = useNavigation()
    
    // 🔧 关键修复：在使用navigation.writeData之前，先同步最新的设置数据
    navigation.syncWithSettingData(setting.writeData.value)
    console.log('🔧 已同步设置数据，当前writeData:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    const dataValidation = navigation.validateNavigationData(navigationData)
    
    if (!dataValidation.isValid) {
      console.warn('⚠️ 导航数据无效，尝试使用无导航状态对比:', dataValidation.issues)
      await presentToast(`导航数据无效，转为无导航状态对比: ${dataValidation.issues.slice(0, 2).join(', ')}`)
      
      // 导航数据无效时，执行无导航状态对比
      await performNoNavigationComparison()
      return
    }
    
    console.log('✅ 导航数据有效性验证通过')
    
    // 1. 获取Native端当前发送的数据
    const nativeDataResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
    
    if (!nativeDataResult.success) {
      console.warn('⚠️ 获取Native数据失败:', nativeDataResult.error)
      return
    }
    
    const nativeData = nativeDataResult.data
    
    // 2. 获取Native端镜像状态
    const mirrorStateResult = await (CapacitorKtService as any).getMirrorState()
    const isMirrorEnabled = mirrorStateResult.enabled
    console.log('🔍 Native端镜像状态:', isMirrorEnabled)
    
    // 3. 🔧 使用独立计算函数生成对比数据（不修改全局状态）
    const processedNavigationData = await preprocessNavigationData(navigationData)
    
    // 使用独立计算函数，传入初始数据和镜像状态
    const initialData = createInitialWriteData()
    const calculatedData = navigation.calculateNavigationDataIndependently(
      processedNavigationData, 
      initialData, 
      isMirrorEnabled
    )
    
    console.log('🔍 独立计算完成，未影响全局writeData状态')
    console.log('🔍 全局writeData状态:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    console.log('🔍 独立计算结果:', calculatedData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    // 4. 验证独立计算的数据
    const calculatedValidation = validateBluetoothData(calculatedData)
    if (!calculatedValidation.isValid) {
      console.warn('⚠️ 独立计算的数据无效:', calculatedValidation.errors)
      // 继续进行比对，但会在结果中标记这个问题
    }
    
    // 5. 进行全面的数据比对分析
    const comparisonResult = await performComprehensiveDataComparison(
      calculatedData, 
      nativeData, 
      navigationData,
      isMirrorEnabled
    )
    
    // 6. 生成测试名
    const testName = generateTestName(navigationData)
    
    // 7. 保存结果
    const result: ComparisonResult = {
      testName,
      timestamp: new Date().toLocaleString(),
      testType: 'real',
      navigationData: calculatedData,
      nativeData,
      isMatch: comparisonResult.isMatch,
      differences: comparisonResult.differences,
      showDetails: false,
      // 🔧 新增：详细分析结果
      analysisDetails: comparisonResult.analysisDetails
    }
    
    comparisonResults.value.unshift(result)
    
    // 8. 显示结果和建议
    if (comparisonResult.isMatch) {
      console.log('✅ 真实数据对比成功: 数据完全一致！')
      await presentToast('✅ 数据对比通过')
    } else {
      console.warn(`❌ 真实数据对比发现差异: ${comparisonResult.differences.length} 处不同`)
      await presentToast(`❌ 发现 ${comparisonResult.differences.length} 处数据差异`)
      
      // 🔧 提供修复建议
      if (comparisonResult.fixSuggestions.length > 0) {
        console.log('🔧 修复建议:', comparisonResult.fixSuggestions)
      }
    }
    
  } catch (error: any) {
    console.error('❌ 真实数据对比失败:', error)
    await presentToast(`数据对比失败: ${error.message}`)
  }
}

// 🔧 新增：预处理导航数据，确保与Native端处理逻辑一致
const preprocessNavigationData = async (rawNavigationData: any) => {
  try {
    // 1. 解析原始数据
    const bannerInstructions = typeof rawNavigationData.bannerInstructions === 'string' 
      ? JSON.parse(rawNavigationData.bannerInstructions)
      : rawNavigationData.bannerInstructions

    // 2. 距离处理 - 与useNavigation中的逻辑保持一致
    const rawStepDistance = rawNavigationData.stepDistanceRemaining
    const processedStepDistance = isNaN(rawStepDistance) 
      ? 0 
      : isPlatform('ios') 
        ? rawStepDistance
        : Math.round(rawStepDistance / 5) * 5  // 四舍五入到5的倍数

    // 3. 确保数据结构完整
    const processedData = {
      bannerInstructions: typeof bannerInstructions === 'string' 
        ? bannerInstructions 
        : JSON.stringify(bannerInstructions),
      stepDistanceRemaining: processedStepDistance,
      distanceRemaining: rawNavigationData.distanceRemaining || 0
    }

    console.log('🔧 导航数据预处理完成:', {
      原始单次距离: rawStepDistance,
      处理后单次距离: processedStepDistance,
      总距离: processedData.distanceRemaining,
      指令类型: bannerInstructions?.primary?.type,
      指令修饰符: bannerInstructions?.primary?.modifier
    })

    return processedData
  } catch (error) {
    console.error('❌ 导航数据预处理失败:', error)
    return rawNavigationData // 返回原始数据作为后备
  }
}

// 🔧 新增：增强版校验和验证
const validateChecksum = (data: number[]) => {
  let expectedChecksum = 0
  
  // 按照协议规范计算校验和：对字节1-15进行异或运算（跳过字节5）
  for (let i = 1; i <= 15; i++) {
    if (i !== 5) { // 跳过字节5
      expectedChecksum ^= data[i]
    }
  }
  
  expectedChecksum &= 0xFF
  const actualChecksum = data[16]
  const isChecksumValid = expectedChecksum === actualChecksum
  
  console.log('🔍 校验和验证:', {
    期望校验和: `0x${expectedChecksum.toString(16).toUpperCase()}`,
    实际校验和: `0x${actualChecksum.toString(16).toUpperCase()}`,
    是否正确: isChecksumValid
  })
  
  return {
    isChecksumValid,
    expectedChecksum,
    actualChecksum
  }
}

// 🔧 新增：全面的数据比对分析 - 使用增强版验证工具
const performComprehensiveDataComparison = async (
  vueData: number[], 
  nativeData: number[], 
  navigationData: any,
  isMirrorEnabled: boolean
) => {
  console.log('🔍 开始全面数据比对分析...')
  console.log('🔍 Vue端数据:', vueData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
  console.log('🔍 Native端数据:', nativeData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
  
  // 1. 使用验证工具进行数据有效性检查
  const vueValidation = validateBluetoothData(vueData)
  const nativeValidation = validateBluetoothData(nativeData)
  
  console.log('🔍 Vue端数据验证:', vueValidation)
  console.log('🔍 Native端数据验证:', nativeValidation)
  
  // 2. 使用比对工具进行数据比较
  const comparisonResult = compareBluetoothData(vueData, nativeData)
  
  console.log('🔍 数据比对结果:', comparisonResult)
  
  // 3. 转换为旧格式以保持兼容性
  const differences: Array<{index: number, navigation: number, native: number}> = 
    comparisonResult.differences.map(diff => ({
      index: diff.index,
      navigation: diff.expected,
      native: diff.actual
    }))
  
  // 4. 构建详细分析结果
  const analysisDetails: any = {
    protocolSection: { 
      consistent: comparisonResult.analysis.protocolConsistency === 1, 
      issues: comparisonResult.differences
        .filter(d => d.index <= 11)
        .map(d => `字节${d.index}(${d.field}): 期望0x${d.expected.toString(16).toUpperCase()}, 实际0x${d.actual.toString(16).toUpperCase()}`)
    },
    navigationSection: { 
      consistent: comparisonResult.analysis.navigationConsistency === 1, 
      issues: comparisonResult.differences
        .filter(d => d.index >= 12 && d.index <= 15)
        .map(d => `字节${d.index}(${d.field}): 期望0x${d.expected.toString(16).toUpperCase()}, 实际0x${d.actual.toString(16).toUpperCase()}`)
    },
    checksumSection: { 
      consistent: !comparisonResult.differences.some(d => d.index === 16), 
      issues: comparisonResult.differences
        .filter(d => d.index === 16)
        .map(d => `校验和不一致: 期望0x${d.expected.toString(16).toUpperCase()}, 实际0x${d.actual.toString(16).toUpperCase()}`)
    },
    mirrorBit: { 
      consistent: true, 
      issues: [] 
    }
  }
  
  // 5. 镜像位特殊检查
  const vueMirror = (vueData[12] & 0x80) !== 0
  const nativeMirror = (nativeData[12] & 0x80) !== 0
  
  if (vueMirror !== nativeMirror || vueMirror !== isMirrorEnabled) {
    analysisDetails.mirrorBit.consistent = false
    analysisDetails.mirrorBit.issues.push(
      `镜像位不一致: Vue=${vueMirror}, Native=${nativeMirror}, 期望=${isMirrorEnabled}`
    )
  }
  
  // 6. 增强的修复建议
  const fixSuggestions = [...comparisonResult.recommendations]
  
  // 添加数据验证相关的建议
  if (!vueValidation.isValid) {
    fixSuggestions.push(`Vue端数据无效: ${vueValidation.errors.join(', ')}`)
  }
  
  if (!nativeValidation.isValid) {
    fixSuggestions.push(`Native端数据无效: ${nativeValidation.errors.join(', ')}`)
  }
  
  if (!analysisDetails.mirrorBit.consistent) {
    fixSuggestions.push('镜像状态不同步，建议刷新镜像状态或重新同步')
  }
  
  // 7. 生成详细分析报告
  const analysisReport = {
    总体一致性: comparisonResult.isMatch ? '✅ 完全一致' : `❌ 发现${differences.length}处差异`,
    协议一致性: `${(comparisonResult.analysis.protocolConsistency * 100).toFixed(1)}%`,
    导航一致性: `${(comparisonResult.analysis.navigationConsistency * 100).toFixed(1)}%`,
    整体一致性: `${(comparisonResult.analysis.overallConsistency * 100).toFixed(1)}%`,
    Vue端有效性: vueValidation.isValid ? '✅ 有效' : '❌ 无效',
    Native端有效性: nativeValidation.isValid ? '✅ 有效' : '❌ 无效',
    修复建议数量: fixSuggestions.length
  }
  
  console.table(analysisReport)
  
  // 8. 生成诊断报告
  const diagnosticReport = generateDiagnosticReport(vueData, nativeData, {
    navigationData,
    isMirrorEnabled,
    timestamp: new Date().toISOString(),
    testType: 'comprehensive_comparison'
  })
  
  console.log('📋 诊断报告:\n', diagnosticReport)
  
  return {
    isMatch: comparisonResult.isMatch,
    differences,
    analysisDetails,
    fixSuggestions,
    // 🔧 新增：增强分析结果
    validationResults: { vue: vueValidation, native: nativeValidation },
    comparisonMetrics: comparisonResult.analysis,
    diagnosticReport
  }
}

// 🔧 获取方向名称的辅助函数
const getDirectionName = (direction: number) => {
  const directions: { [key: number]: string } = {
    1: '左转',
    2: '右转', 
    3: '直行',
    4: '调头',
    5: '向左前方行驶',
    6: '向右前方行驶',
    7: '靠左行驶',
    8: '靠右行驶',
    9: '到达目的地'
  }
  return directions[direction] || `未知(${direction})`
}

// 执行手动对比测试
const performManualComparison = async () => {
  try {
    console.log('🔍 开始执行手动对比测试')
    
    // 1. 准备测试数据
    const navigationData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: testData.type,
          text: `${testData.type} ${testData.modifier}`,
          components: [{ text: `${testData.type} ${testData.modifier}`, type: 'text' }],
          modifier: testData.modifier
        },
        distanceAlongGeometry: testData.totalDistance,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: testData.stepDistance,
      distanceRemaining: testData.totalDistance
    }
    
    // 2. 等待Native端数据稳定
    console.log('🔍 等待Native端数据稳定...')
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 3. 获取Native端当前发送的数据
    const nativeDataResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
    
    if (!nativeDataResult.success) {
      await presentToast(`获取Native数据失败: ${nativeDataResult.error}`)
      return
    }
    
    const nativeData = nativeDataResult.data
    
    // 4. 数据有效性检查
    if (!nativeData || nativeData.length === 0) {
      console.warn('⚠️ Native端数据为空')
      await presentToast('Native端数据为空，请确保蓝牙发送已启动')
      return
    }
    
    // 5. 获取Native端镜像状态
    const mirrorStateResult = await (CapacitorKtService as any).getMirrorState()
    const isMirrorEnabled = mirrorStateResult.enabled
    console.log('🔍 Native端镜像状态:', isMirrorEnabled)
    
    // 6. 🔧 同步设置数据并验证手动测试数据的有效性
    const navigation = useNavigation()
    
    // 🔧 关键修复：在使用navigation.writeData之前，先同步最新的设置数据
    navigation.syncWithSettingData(setting.writeData.value)
    console.log('🔧 手动测试已同步设置数据，当前writeData:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    const manualDataValidation = navigation.validateNavigationData(navigationData)
    
    if (!manualDataValidation.isValid) {
      console.warn('⚠️ 手动测试数据无效:', manualDataValidation.issues)
      await presentToast(`测试数据无效: ${manualDataValidation.issues.join(', ')}`)
      return
    }
    
    console.log('✅ 手动测试数据有效性验证通过')
    
    // 7. 🔧 使用独立计算函数生成对比数据（不修改全局状态）
    const processedNavigationData = await preprocessNavigationData(navigationData)
    
    // 使用独立计算函数，传入初始数据和镜像状态
    const initialData = createInitialWriteData()
    const calculatedData = navigation.calculateNavigationDataIndependently(
      processedNavigationData, 
      initialData, 
      isMirrorEnabled
    )
    
    console.log('🔍 手动测试独立计算完成，未影响全局writeData状态')
    console.log('🔍 全局writeData状态:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    console.log('🔍 手动测试计算结果:', calculatedData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    // 8. 验证独立计算的数据
    const calculatedValidation = validateBluetoothData(calculatedData)
    if (!calculatedValidation.isValid) {
      console.warn('⚠️ 手动测试计算的数据无效:', calculatedValidation.errors)
      await presentToast(`计算的数据无效: ${calculatedValidation.errors.join(', ')}`)
      // 继续进行比对，但会在结果中标记这个问题
    }
    
    // 9. 进行全面的数据比对分析
    const comparisonResult = await performComprehensiveDataComparison(
      calculatedData,
      nativeData,
      navigationData,
      isMirrorEnabled
    )
    
    // 10. 保存结果
    const result: ComparisonResult = {
      testName: `${testData.type}/${testData.modifier} - ${testData.stepDistance}m/${testData.totalDistance}m`,
      timestamp: new Date().toLocaleString(),
      testType: 'manual',
      params: { ...testData },
      navigationData: calculatedData,
      nativeData,
      isMatch: comparisonResult.isMatch,
      differences: comparisonResult.differences,
      showDetails: false,
      // 🔧 新增：详细分析结果
      analysisDetails: comparisonResult.analysisDetails
    }
    
    comparisonResults.value.unshift(result)
    
    // 11. 显示结果和修复建议
    if (comparisonResult.isMatch) {
      await presentToast(`✅ 手动测试通过：数据完全一致！`)
    } else {
      await presentToast(`❌ 手动测试失败：发现 ${comparisonResult.differences.length} 处数据差异！`)
      
      // 显示修复建议
      if (comparisonResult.fixSuggestions.length > 0) {
        console.log('🔧 修复建议:')
        comparisonResult.fixSuggestions.forEach((suggestion, index) => {
          console.log(`${index + 1}. ${suggestion}`)
        })
      }
    }
    
  } catch (error: any) {
    console.error('❌ 手动对比测试失败:', error)
    await presentToast(`测试执行失败: ${error.message}`)
  }
}

// 生成测试名称
const generateTestName = (data: any) => {
  try {
    const banner = JSON.parse(data.bannerInstructions)
    const type = banner.primary?.type || 'unknown'
    const modifier = banner.primary?.modifier || 'unknown'
    const stepDistance = data.stepDistanceRemaining || 0
    const totalDistance = data.distanceRemaining || 0
    
    return `${type}/${modifier} - ${stepDistance}m/${totalDistance}m`
  } catch {
    return `导航数据 - ${Date.now()}`
  }
}

// 定时更新蓝牙状态
let bluetoothStatsTimer: any = null

const startBluetoothStatsUpdate = () => {
  bluetoothStatsTimer = setInterval(async () => {
    try {
      const stats = await CapacitorKtService.getBluetoothSendingStats()
      bluetoothStats.value = {
        isConnected: stats.isConnected,
        totalSent: stats.totalSent,
        errorCount: stats.errorCount
      }
    } catch (error) {
      console.warn('获取蓝牙统计失败:', error)
    }
  }, 2000) // 每2秒更新一次
}

const stopBluetoothStatsUpdate = () => {
  if (bluetoothStatsTimer) {
    clearInterval(bluetoothStatsTimer)
    bluetoothStatsTimer = null
  }
}

// 刷新镜像状态
const refreshMirrorState = async () => {
  try {
    const result = await (CapacitorKtService as any).getMirrorState()
    mirrorState.value = {
      enabled: result.enabled,
      lastUpdate: Date.now()
    }
    console.log('🔄 镜像状态已刷新:', result.enabled)
  } catch (error: any) {
    console.error('❌ 刷新镜像状态失败:', error)
    await presentToast(`刷新镜像状态失败: ${error.message}`)
  }
}

// 显示详情
const showDetails = (result: ComparisonResult) => {
  result.showDetails = !result.showDetails
}

// 清除结果
const clearResults = () => {
  comparisonResults.value = []
}

// 检查数据一致性 - 增强版本
const checkDataConsistency = async () => {
  try {
    console.log('🔍 开始增强版数据一致性检查')
    
    // 1. 获取Vue端数据（先同步设置）
    const navigation = useNavigation()
    
    // 🔧 关键修复：在使用navigation.writeData之前，先同步最新的设置数据
    navigation.syncWithSettingData(setting.writeData.value)
    console.log('🔧 数据一致性检查已同步设置数据，当前writeData:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    const vueData = [...navigation.writeData.value]
    
    console.log('🔍 Vue端数据:', vueData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(', '))
    
    // 2. 获取Native端数据
    const nativeResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
    if (!nativeResult.success) {
      console.error('❌ 无法获取Native端数据:', nativeResult.error)
      await presentToast(`无法获取Native端数据: ${nativeResult.error}`)
      return false
    }
    
    const nativeData = nativeResult.data
    console.log('🔍 Native端数据:', nativeData.map((b: number) => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(', '))
    
    // 3. 获取镜像状态
    const mirrorStateResult = await (CapacitorKtService as any).getMirrorState()
    const isMirrorEnabled = mirrorStateResult.enabled
    
    // 4. 进行全面比对分析
    const comparisonResult = await performComprehensiveDataComparison(
      vueData,
      nativeData,
      null, // 没有导航数据时传null
      isMirrorEnabled
    )
    
    // 5. 显示详细分析结果
    if (comparisonResult.isMatch) {
      console.log('✅ 数据一致性检查通过')
      await presentToast('✅ 数据一致性检查通过')
    } else {
      console.warn('⚠️ 数据不一致！')
      console.log('🔍 发现差异:', comparisonResult.differences)
      console.log('🔍 分析详情:', comparisonResult.analysisDetails)
      console.log('🔧 修复建议:', comparisonResult.fixSuggestions)
      
      await presentToast(`发现 ${comparisonResult.differences.length} 处数据差异！`)
      
      // 6. 提供自动修复选项
      const shouldAutoFix = confirm(
        `发现数据不一致，是否尝试自动修复？\n\n` +
        `差异数量: ${comparisonResult.differences.length}\n` +
        `修复建议: ${comparisonResult.fixSuggestions.join(', ')}`
      )
      
      if (shouldAutoFix) {
        await attemptAutoFix(vueData, nativeData, comparisonResult)
      }
    }
    
    return comparisonResult.isMatch
  } catch (error: any) {
    console.error('❌ 数据一致性检查失败:', error)
    await presentToast(`数据一致性检查失败: ${error.message}`)
    return false
  }
}

// 🔧 新增：尝试自动修复数据不一致问题 - 使用增强版修复工具
const attemptAutoFix = async (vueData: number[], nativeData: number[], comparisonResult: any) => {
  try {
    console.log('🔧 开始尝试自动修复数据不一致问题')
    
    // 1. 使用自动修复工具进行初步修复
    const repairOptions = {
      fixChecksum: true,
      fixProtocolHeader: true,
      fixEndByte: true
    }
    
    const { repaired: autoFixedData, changes: autoChanges } = repairBluetoothData(vueData, repairOptions)
    console.log('🔧 自动修复变更:', autoChanges)
    
    // 2. 创建修复后的数据副本
    const fixedData = [...autoFixedData]
    const allChanges = [...autoChanges]
    
    // 3. 根据分析结果进行针对性修复
    if (!comparisonResult.analysisDetails.mirrorBit.consistent) {
      // 修复镜像位
      const mirrorStateResult = await (CapacitorKtService as any).getMirrorState()
      const shouldEnableMirror = mirrorStateResult.enabled
      
      const oldMirrorBit = (fixedData[12] & 0x80) !== 0
      
      if (shouldEnableMirror && !oldMirrorBit) {
        fixedData[12] |= 0x80  // 设置镜像位
        allChanges.push(`设置镜像位: 关闭 -> 开启`)
        console.log('🔧 已设置镜像位')
      } else if (!shouldEnableMirror && oldMirrorBit) {
        fixedData[12] &= 0x7F  // 清除镜像位
        allChanges.push(`清除镜像位: 开启 -> 关闭`)
        console.log('🔧 已清除镜像位')
      }
    }
    
    // 4. 针对特定差异进行修复
    if (comparisonResult.differences && comparisonResult.differences.length > 0) {
      for (const diff of comparisonResult.differences) {
        // 对于导航数据部分，使用Native端的值（认为Native端是正确的）
        if (diff.index >= 12 && diff.index <= 15 && diff.index !== 16) {
          const oldValue = fixedData[diff.index]
          fixedData[diff.index] = diff.native
          allChanges.push(`修复字节${diff.index}: 0x${oldValue.toString(16).toUpperCase()} -> 0x${diff.native.toString(16).toUpperCase()}`)
          console.log(`🔧 已修复字节${diff.index}`)
        }
      }
    }
    // 5. 最后重新计算校验和（确保所有修改后校验和正确）
    const { expectedChecksum: finalChecksum } = validateChecksum(fixedData)
    if (fixedData[16] !== finalChecksum) {
      const oldChecksum = fixedData[16]
      fixedData[16] = finalChecksum
      allChanges.push(`最终校验和修正: 0x${oldChecksum.toString(16).toUpperCase()} -> 0x${finalChecksum.toString(16).toUpperCase()}`)
      console.log('🔧 已重新计算最终校验和')
    }
    
    // 6. 验证修复后的数据
    const fixedValidation = validateBluetoothData(fixedData)
    if (!fixedValidation.isValid) {
      console.warn('⚠️ 修复后的数据仍然无效:', fixedValidation.errors)
      await presentToast(`修复后数据仍有问题: ${fixedValidation.errors.join(', ')}`)
    }
    
    console.log('🔧 所有修复变更:', allChanges)
    console.log('🔧 修复后的数据:', fixedData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    // 7. 发送修复后的数据到Native端
    try {
      await CapacitorKtService.updateBluetoothSendData({ data: fixedData })
      console.log('🔧 已发送修复后的数据到Native端')
      
      // 8. 验证修复效果
      await new Promise(resolve => setTimeout(resolve, 300)) // 增加等待时间确保数据同步
      const verificationResult = await checkDataConsistency()
      
      if (verificationResult) {
        await presentToast(`✅ 自动修复成功！\n应用了${allChanges.length}项修复`)
        
        // 显示修复详情
        if (allChanges.length > 0) {
          console.log('✅ 修复详情:')
          allChanges.forEach((change, index) => {
            console.log(`  ${index + 1}. ${change}`)
          })
        }
      } else {
        await presentToast('⚠️ 自动修复部分成功，建议手动检查剩余问题')
      }
      
    } catch (updateError: any) {
      console.error('❌ 发送修复数据失败:', updateError)
      await presentToast(`自动修复失败: ${updateError.message}`)
    }
    
  } catch (error: any) {
    console.error('❌ 自动修复过程失败:', error)
    await presentToast(`自动修复失败: ${error.message}`)
  }
}

// 格式化字节显示
const formatByte = (byte: number) => {
  return `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 🔧 新增：生成模拟导航数据
const generateMockNavigationData = (params?: {
  type?: string
  modifier?: string
  stepDistance?: number
  totalDistance?: number
}) => {
  const defaultParams = {
    type: 'turn',
    modifier: 'left',
    stepDistance: 150,
    totalDistance: 1000
  }
  
  const finalParams = { ...defaultParams, ...params }
  
  return {
    bannerInstructions: JSON.stringify({
      primary: {
        type: finalParams.type,
        text: `${finalParams.type} ${finalParams.modifier}`,
        components: [{ text: `${finalParams.type} ${finalParams.modifier}`, type: 'text' }],
        modifier: finalParams.modifier
      },
      distanceAlongGeometry: finalParams.totalDistance,
      drivingSide: 'right'
    }),
    stepDistanceRemaining: finalParams.stepDistance,
    distanceRemaining: finalParams.totalDistance
  }
}

// 🔧 新增：无导航状态下的数据对比测试
const performNoNavigationComparison = async () => {
  try {
    console.log('🔍 开始执行无导航状态下的数据对比测试')
    
    // 1. 获取Native端当前发送的数据
    const nativeDataResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
    
    if (!nativeDataResult.success) {
      console.warn('⚠️ 获取Native数据失败:', nativeDataResult.error)
      await presentToast('无法获取Native端数据，请确保蓝牙发送已启动')
      return
    }
    const nativeData = nativeDataResult.data
    console.log('🔍 Native端数据:', nativeData.map((b: number) => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    // 2. 检查Native端数据是否为初始状态（无导航数据）
    const isInitialNavData = nativeData[12] === 0x00 && nativeData[13] === 0x00 && 
                            nativeData[14] === 0x00 && nativeData[15] === 0x00
    
    // 3. 获取镜像状态
    const mirrorStateResult = await (CapacitorKtService as any).getMirrorState()
    const isMirrorEnabled = mirrorStateResult.enabled
    
    // 4. 生成对比数据（先同步设置）
    const navigation = useNavigation()
    
    // 🔧 关键修复：在使用navigation.writeData之前，先同步最新的设置数据
    navigation.syncWithSettingData(setting.writeData.value)
    console.log('🔧 无导航状态对比已同步设置数据，当前writeData:', navigation.writeData.value.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
    
    const initialData = createInitialWriteData()
    
    let calculatedData: number[]
    
    if (isInitialNavData) {
      // Native端是初始状态，直接使用初始数据进行对比
      calculatedData = [...initialData]
      
      // 设置镜像位
      if (isMirrorEnabled) {
        calculatedData[12] |= 0x80
      }
      // 重新计算校验和
      const { expectedChecksum } = validateChecksum(calculatedData)
      calculatedData[16] = expectedChecksum
      
      console.log('🔍 使用初始数据进行对比（无导航状态）')
    } else {
      // Native端有导航数据，生成模拟数据进行对比
      console.log('🔍 Native端有导航数据，生成模拟数据进行反推对比')
      
      // 从Native端数据反推导航参数
      const nativeDirection = nativeData[12] & 0x0F
      const nativeSingleRule = (nativeData[12] >> 4) & 0x03
      const nativeTotalRule = (nativeData[14] >> 4) & 0x03
      
      // 生成对应的模拟导航数据
      const mockNavigationData = generateMockNavigationData({
        type: nativeDirection === 9 ? 'arrive' : 'turn',
        modifier: getModifierFromDirection(nativeDirection),
        stepDistance: calculateDistanceFromRule(nativeData[13], nativeSingleRule),
        totalDistance: calculateDistanceFromRule(nativeData[15], nativeTotalRule)
      })
      
      console.log('🔍 生成的模拟导航数据:', mockNavigationData)
      
      // 使用模拟数据计算
      calculatedData = navigation.calculateNavigationDataIndependently(
        mockNavigationData,
        initialData,
        isMirrorEnabled
      )
    }
    
    // 5. 进行数据比对
    const comparisonResult = await performComprehensiveDataComparison(
      calculatedData,
      nativeData,
      isInitialNavData ? null : '模拟数据',
      isMirrorEnabled
    )
    
    // 6. 保存结果
    const result: ComparisonResult = {
      testName: isInitialNavData ? '无导航状态对比' : '模拟数据反推对比',
      timestamp: new Date().toLocaleString(),
      testType: 'real',
      navigationData: calculatedData,
      nativeData,
      isMatch: comparisonResult.isMatch,
      differences: comparisonResult.differences,
      showDetails: false,
      analysisDetails: comparisonResult.analysisDetails
    }
    
    comparisonResults.value.unshift(result)
    
    // 7. 显示结果
    if (comparisonResult.isMatch) {
      await presentToast('✅ 无导航状态数据对比通过')
    } else {
      await presentToast(`❌ 发现 ${comparisonResult.differences.length} 处数据差异`)
    }
    
  } catch (error: any) {
    console.error('❌ 无导航状态数据对比失败:', error)
    await presentToast(`测试失败: ${error.message}`)
  }
}

// 🔧 辅助函数：从方向代码获取修饰符
const getModifierFromDirection = (direction: number): string => {
  const directionMap: { [key: number]: string } = {
    1: 'left',
    2: 'right',
    3: 'straight',
    4: 'uturn',
    5: 'slight left',
    6: 'slight right',
    7: 'left',
    8: 'right',
    9: 'straight'
  }
  return directionMap[direction] || 'straight'
}

// 🔧 辅助函数：从规则和低位字节计算距离
const calculateDistanceFromRule = (lowByte: number, rule: number): number => {
  const multipliers = [1, 10, 100, 1000]
  return lowByte * (multipliers[rule] || 1)
}

// 🔧 新增：运行验证测试
const runValidationTests = async () => {
  try {
    console.log('🧪 开始运行数据验证测试...')
    await presentToast('开始运行验证测试，请查看控制台输出')
    
    // 运行基础验证测试
    runDataValidationTests()
    
    // 如果有实际数据，也进行测试
    try {
      const navigation = useNavigation()
      const vueData = [...navigation.writeData.value]
      
      const nativeResult = await (CapacitorKtService as any).getCurrentBluetoothSendData()
      if (nativeResult.success && nativeResult.data) {
        console.log('\n🔍 测试当前实际数据:')
        testSpecificData(vueData, nativeResult.data)
      }
    } catch (error) {
      console.warn('⚠️ 无法获取实际数据进行测试:', error)
    }
    
    // 运行无导航状态对比测试
    console.log('\n🔍 运行无导航状态对比测试:')
    await performNoNavigationComparison()
    
    await presentToast('✅ 验证测试完成，请查看控制台详细结果')
    
  } catch (error: any) {
    console.error('❌ 验证测试失败:', error)
    await presentToast(`验证测试失败: ${error.message}`)
  }
}

// 创建初始WriteData
const createInitialWriteData = () => [
  0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
  0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [12，13，14，15，16]
  0x0e //17 截止位
]

// 组件生命周期
onMounted(async () => {
  // 初始获取蓝牙状态
  try {
    const stats = await CapacitorKtService.getBluetoothSendingStats()
    bluetoothStats.value = {
      isConnected: stats.isConnected,
      totalSent: stats.totalSent,
      errorCount: stats.errorCount
    }
  } catch (error) {
    console.warn('初始化蓝牙统计失败:', error)
  }

  // 初始获取镜像状态
  try {
    const mirrorStateResult = await CapacitorKtService.getMirrorState()
    mirrorState.value = {
      enabled: mirrorStateResult.enabled,
      lastUpdate: Date.now()
    }
  } catch (error) {
    console.warn('初始化镜像状态失败:', error)
  }
})

onUnmounted(async () => {
  await stopListener()
  stopBluetoothStatsUpdate()
})
</script>

<style scoped>
.container {
  padding: 16px;
}

.comparison-details {
  background-color: var(--ion-color-light);
  margin: 8px 0;
  border-radius: 8px;
}

.data-array {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  font-family: 'Courier New', monospace;
}

.byte-value {
  padding: 2px 6px;
  background-color: var(--ion-color-medium-tint);
  border-radius: 4px;
  font-size: 12px;
  min-width: 32px;
  text-align: center;
}

.byte-diff {
  background-color: var(--ion-color-danger) !important;
  color: white;
  font-weight: bold;
}

ion-item {
  --padding-start: 16px;
}

ion-card {
  margin: 8px 0;
}

.ion-margin-top {
  margin-top: 16px;
}
</style> 