<template>
  <ion-page>
    <ion-header :translucent="true">
      <ion-toolbar>
        <ion-title>导航界面控制演示</ion-title>
        <ion-buttons slot="start">
          <ion-back-button></ion-back-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content :fullscreen="true">
      <ion-header collapse="condense">
        <ion-toolbar>
          <ion-title size="large">导航界面控制演示</ion-title>
        </ion-toolbar>
      </ion-header>

      <div class="container">
        <!-- 功能说明 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon :icon="informationCircleOutline" style="margin-right: 8px;"></ion-icon>
              功能说明
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <p>此演示可以控制导航Fragment的显示和隐藏，导航逻辑会继续运行。</p>
            <p>⚠️ 需要先启动导航后才能使用控制功能。</p>
          </ion-card-content>
        </ion-card>

        <!-- 导航状态 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>导航状态</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>界面显示状态</h3>
                <p :style="{ color: navigationStatus.isVisible ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                  {{ navigationStatus.isVisible ? '显示中' : '已隐藏' }}
                </p>
              </ion-label>
              <ion-button 
                fill="clear" 
                @click="refreshStatus"
                slot="end"
              >
                <ion-icon :icon="refreshOutline"></ion-icon>
              </ion-button>
            </ion-item>

            <ion-item>
              <ion-label>
                <h3>最后更新时间</h3>
                <p>{{ navigationStatus.lastUpdate || '未更新' }}</p>
              </ion-label>
            </ion-item>

            <ion-item v-if="navigationStatus.error">
              <ion-label>
                <h3 style="color: var(--ion-color-danger);">错误信息</h3>
                <p style="color: var(--ion-color-danger);">{{ navigationStatus.error }}</p>
              </ion-label>
            </ion-item>
          </ion-card-content>
        </ion-card>

        <!-- 控制按钮 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>界面控制</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-row>
              <ion-col size="6">
                <ion-button 
                  expand="block" 
                  color="success"
                  @click="showNavigationInterface"
                  :disabled="isLoading"
                >
                  <ion-icon :icon="eyeOutline" slot="start"></ion-icon>
                  显示界面
                </ion-button>
              </ion-col>
              <ion-col size="6">
                <ion-button 
                  expand="block" 
                  color="warning"
                  @click="hideNavigationInterface"
                  :disabled="isLoading"
                >
                  <ion-icon :icon="eyeOffOutline" slot="start"></ion-icon>
                  隐藏界面
                </ion-button>
              </ion-col>
            </ion-row>

            <ion-button 
              expand="block" 
              fill="outline"
              @click="refreshStatus"
              :disabled="isLoading"
              class="ion-margin-top"
            >
              <ion-icon :icon="refreshOutline" slot="start"></ion-icon>
              {{ isLoading ? '更新中...' : '刷新状态' }}
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- 快速导航启动 -->
        <ion-card>
          <ion-card-header>
            <ion-card-title>快速导航测试</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-item>
              <ion-label>
                <h3>测试坐标</h3>
                <p>起点: 116.4074, 39.9042 (北京天安门)</p>
                <p>终点: 116.4109, 39.9042 (北京故宫)</p>
              </ion-label>
            </ion-item>

            <ion-button 
              expand="block" 
              color="primary"
              @click="startTestNavigation"
              :disabled="isLoading"
            >
              <ion-icon :icon="navigateOutline" slot="start"></ion-icon>
              启动测试导航
            </ion-button>
          </ion-card-content>
        </ion-card>

        <!-- 操作日志 -->
        <ion-card v-if="operationLogs.length > 0">
          <ion-card-header>
            <ion-card-title>
              操作日志
              <ion-button fill="clear" size="small" @click="clearLogs" slot="end">
                <ion-icon :icon="trashOutline"></ion-icon>
              </ion-button>
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <ion-list>
              <ion-item v-for="(log, index) in operationLogs" :key="index">
                <ion-icon 
                  :icon="log.success ? checkmarkCircleOutline : closeCircleOutline" 
                  :color="log.success ? 'success' : 'danger'"
                  slot="start"
                ></ion-icon>
                <ion-label>
                  <h3>{{ log.operation }}</h3>
                  <p>{{ log.timestamp }}</p>
                  <p v-if="log.message" :style="{ color: log.success ? 'var(--ion-color-success)' : 'var(--ion-color-danger)' }">
                    {{ log.message }}
                  </p>
                </ion-label>
              </ion-item>
            </ion-list>
          </ion-card-content>
        </ion-card>
      </div>
    </ion-content>
  </ion-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonButton,
  IonButtons,
  IonBackButton,
  IonIcon,
  IonList,
  IonRow,
  IonCol
} from '@ionic/vue'
import {
  informationCircleOutline,
  refreshOutline,
  eyeOutline,
  eyeOffOutline,
  navigateOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  trashOutline
} from 'ionicons/icons'
import { CapacitorKtService } from 'capacitor-kt-service'
import { useToast } from '@/hooks/useToast'

// 导航状态
const navigationStatus = ref({
  isVisible: false,
  lastUpdate: '',
  error: ''
})

// 加载状态
const isLoading = ref(false)

// 操作日志
interface OperationLog {
  operation: string
  timestamp: string
  success: boolean
  message?: string
}

const operationLogs = ref<OperationLog[]>([])

const { presentToast } = useToast()

// 刷新导航状态
const refreshStatus = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  navigationStatus.value.error = ''
  
  try {
    console.log('🔍 刷新导航界面状态')
    
    const result = await (CapacitorKtService as any).getNavigationFragmentVisibility()
    
    navigationStatus.value.isVisible = result.isVisible
    navigationStatus.value.lastUpdate = new Date().toLocaleTimeString()
    
    addOperationLog('刷新状态', true, `界面${result.isVisible ? '显示' : '隐藏'}中`)
    
    console.log('✅ 状态刷新成功:', result)
    
  } catch (error: any) {
    console.error('❌ 刷新状态失败:', error)
    navigationStatus.value.error = error.message || '刷新状态失败'
    addOperationLog('刷新状态', false, error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 显示导航界面
const showNavigationInterface = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    console.log('👁️ 显示导航界面')
    
    const result = await (CapacitorKtService as any).showNavigationFragment()
    
    if (result.success) {
      await presentToast('导航界面已显示')
      addOperationLog('显示界面', true, '操作成功')
      await refreshStatus()
    } else {
      await presentToast(`显示失败: ${result.error}`)
      addOperationLog('显示界面', false, result.error || '操作失败')
    }
    
    console.log('✅ 显示界面操作完成:', result)
    
  } catch (error: any) {
    console.error('❌ 显示界面失败:', error)
    await presentToast(`显示失败: ${error.message}`)
    addOperationLog('显示界面', false, error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 隐藏导航界面
const hideNavigationInterface = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    console.log('🙈 隐藏导航界面')
    
    const result = await (CapacitorKtService as any).hideNavigationFragment()
    
    if (result.success) {
      await presentToast('导航界面已隐藏')
      addOperationLog('隐藏界面', true, '操作成功')
      await refreshStatus()
    } else {
      await presentToast(`隐藏失败: ${result.error}`)
      addOperationLog('隐藏界面', false, result.error || '操作失败')
    }
    
    console.log('✅ 隐藏界面操作完成:', result)
    
  } catch (error: any) {
    console.error('❌ 隐藏界面失败:', error)
    await presentToast(`隐藏失败: ${error.message}`)
    addOperationLog('隐藏界面', false, error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 启动测试导航
const startTestNavigation = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    console.log('🚀 启动测试导航')
    
    const longitude = 120.5481 // 经度
    const latitude = 31.3012 // 纬度
    const result = await CapacitorKtService.showMapboxNavigation({
      routes: [
        {
          latitude: latitude,
          longitude: longitude
        }
      ],
      simulating: false
    })
    
    if (result.status === 'success') {
      await presentToast('测试导航已启动')
      addOperationLog('启动导航', true, '导航服务已启动')
      // 启动后稍等一下再刷新状态
      setTimeout(refreshStatus, 1000)
    } else {
      await presentToast(`启动失败: ${result.data}`)
      addOperationLog('启动导航', false, result.data || '导航启动失败')
    }
    
    console.log('📊 导航启动结果:', result)
    
  } catch (error: any) {
    console.error('❌ 启动导航失败:', error)
    await presentToast(`启动失败: ${error.message}`)
    addOperationLog('启动导航', false, error.message || '未知错误')
  } finally {
    isLoading.value = false
  }
}

// 添加操作日志
const addOperationLog = (operation: string, success: boolean, message?: string) => {
  const log: OperationLog = {
    operation,
    timestamp: new Date().toLocaleString(),
    success,
    message
  }
  
  operationLogs.value.unshift(log)
  
  // 限制日志数量
  if (operationLogs.value.length > 10) {
    operationLogs.value = operationLogs.value.slice(0, 10)
  }
}

// 清除日志
const clearLogs = () => {
  operationLogs.value = []
}

// 定时刷新状态
let statusTimer: any = null

const startStatusTimer = () => {
  statusTimer = setInterval(async () => {
    if (!isLoading.value) {
      await refreshStatus()
    }
  }, 5000) // 每5秒刷新一次
}

const stopStatusTimer = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 组件生命周期
onMounted(async () => {
  await refreshStatus()
  startStatusTimer()
})

onUnmounted(() => {
  stopStatusTimer()
})
</script>

<style scoped>
.container {
  padding: 16px;
}

ion-item {
  --padding-start: 16px;
}

ion-card {
  margin: 8px 0;
}

.ion-margin-top {
  margin-top: 16px;
}
</style> 