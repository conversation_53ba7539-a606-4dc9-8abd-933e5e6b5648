<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>Mapbox</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content>
    </ion-content>
  </ion-page>
</template>

<script lang="ts" setup>
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonList,
  IonInput,
  IonButton
} from '@ionic/vue'

import { onMounted, reactive, ref } from 'vue'
import { useToast } from '@/hooks/useToast'
// import { CapacitorKtService } from 'capacitor-kt-service'

const { presentToast } = useToast()

const longitude = ref<number>(120.5481) // 经度
const latitude = ref<number>(31.3012) // 纬度



onMounted(() => {

})



const onNavigateButtonClick = () => {
  const long = longitude.value
  const lat = latitude.value
  navigateToAddressWithMapBox({ latitude: lat, longitude: long })
}

const navigateToAddressWithMapBox = async ({
  latitude = 0,
  longitude = 0
}) => {
  if (!isAddressValid({ latitude, longitude })) {
    return
  }

  try {
    await startNavigate({ latitude, longitude })
  } catch (error) {
    handleDeniedLocation(error)
  }
}

const handleDeniedLocation = (error: any) => {
  if (error?.type === 'not_supported') {
    return presentToast('Navigation not supported on web', 'top')
  }
  presentToast(
    'Error in getting location permission, please enable your gps location',
    'top'
  )
}

const startNavigate = async ({ latitude = 0, longitude = 0 }) => {
  // getCurrentPosition().then((result) => {
  //   const position = new google.maps.LatLng(result.coords.latitude, result.coords.longitude)
  //   map?.setCenter(position)
  // })

  // const result = await CapacitorKt.showMapboxNavigation({
  //   routes: [
  //     {
  //       latitude: latitude,
  //       longitude: longitude
  //     }
  //   ],
  //   simulating: false
  // })
  const result = { success: false, message: 'Navigation plugin not available', status: 'failure', type: 'plugin_unavailable' }

  console.log(result)

  if (result?.status === 'failure') {
    switch (result?.type) {
      case 'on_failure':
        presentToast('No routes found', 'top')
        break
      case 'on_cancelled':
        presentToast('Navigation cancelled', 'top')
        break
    }
  }
}

const isAddressValid = ({ latitude = 0, longitude = 0 }) => {
  if (latitude === 0 || longitude === 0) {
    presentToast('Activity Address is not available', 'top')
    return false
  }

  return true
}
</script>

<style scoped lang="scss"></style>
