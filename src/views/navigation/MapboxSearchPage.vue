<template>
    <ion-page>
        <ion-header>
            <ion-toolbar>
                <ion-title>地址搜索</ion-title>
            </ion-toolbar>
        </ion-header>

        <ion-content class="ion-no-padding">
            <!-- 搜索框容器 -->
            <div ref="searchBoxContainer" class="search-box-container"></div>
            <!-- 地图容器 -->
            <div ref="mapContainer" class="map-container"></div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import {
    onMounted,
    onUnmounted,
    ref,
    watch,
    defineProps,
    defineEmits,
    computed,
    PropType
} from 'vue';
import { Feature, MapboxSearchBox } from '@mapbox/search-js-web';
import mapboxgl, { LngLatLike } from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// 定义坐标类型
type Coordinates = [number, number];

// 导入 Mapbox 类型
declare module '@mapbox/search-js-web' {
    export interface Feature {
        place_name: string;
        text: string;
        geometry?: {
            coordinates: number[];
        };
        id?: string;
    }
}

// 定义组件 props
const props = defineProps({
    accessToken: {
        type: String,
        required: true,
        description: 'Mapbox 访问令牌',
        default: 'pk.eyJ1IjoicHd1YnBkam4iLCJhIjoiY21jMGIxb3d1MDBlaTJrczc4cHh3MWFlcCJ9.k7Qk1gP-pVGrHBwAFUuHaA'
    },
    defaultZoom: {
        type: Number,
        default: 12,
        description: '地图默认缩放级别'
    },
    mapStyle: {
        type: String,
        default: 'mapbox://styles/mapbox/streets-v12',
        description: 'Mapbox 地图样式'
    }
});

const emits = defineEmits<{
    (e: 'resultSelected', result: Feature): void;
    (e: 'mapLoaded'): void;
}>();

// 引用和状态
const searchResults = ref<Feature[]>([]);
const mapContainer = ref<HTMLElement | null>(null);
const searchBoxContainer = ref<HTMLElement | null>(null);
let searchBox: MapboxSearchBox | null = null;
let map: mapboxgl.Map | null = null;
let marker: mapboxgl.Marker | null = null;
const searchEventListeners: { event: string; handler: Function }[] = [];
// 初始化地图
const initMap = async () => {
    const coords = {
      longitude: 120.5481,
      latitude: 31.3012
    }
    if (!mapContainer.value) return;
    map = new mapboxgl.Map({
        accessToken: props.accessToken,
        container: mapContainer.value,
        center: [coords.longitude, coords.latitude],
        zoom: props.defaultZoom,
        style: props.mapStyle
    });

    map.on('load', () => {
        console.log('地图加载完成');
        map!.resize();
        emits('mapLoaded');
        initSearchBox([coords.longitude, coords.latitude]);
    });

    map.on('error', (e) => {
        console.error('地图错误:', e);
    });
};

// 初始化搜索框
const initSearchBox = (origin: LngLatLike) => {
    if (!map || !searchBoxContainer.value) return;

    searchBox = new MapboxSearchBox();
    searchBox.accessToken = props.accessToken;

    searchBox.options = {
        language: 'zh',
        country: 'cn',
        navigation_profile: 'cycling',
        origin: origin
    };
    searchBox.placeholder = '请输入地址';
    searchBox.mapboxgl = mapboxgl;
    searchBox.bindMap(map);

    searchBoxContainer.value?.appendChild(searchBox as unknown as HTMLElement);

    const retrieveHandler = (e: any) => {
        const feature = e.detail;
        console.log('搜索结果:', feature);
        searchResults.value = [feature];

        if (map && feature.geometry) {
            const coordinates = feature.geometry.coordinates;
            map.flyTo({
                center: coordinates,
                zoom: 15
            });

            updateMarker(coordinates);
        }
    };

    // 使用类型断言解决事件监听类型问题
    (searchBox as any).addEventListener('retrieve', retrieveHandler);

    // 保存事件监听器以便移除
    searchEventListeners.push({
        event: 'retrieve',
        handler: retrieveHandler
    });
};


// 更新地图标记点
const updateMarker = (coordinates: number[]) => {
    if (marker) {
        marker.remove();
    }

    marker = new mapboxgl.Marker()
        .setLngLat(coordinates as LngLatLike)
        .addTo(map!);
};

// 组件挂载时初始化
onMounted(() => {
    initMap();
});

// 组件卸载时清理资源
onUnmounted(() => {
    // 移除搜索框事件监听器
    searchEventListeners.forEach(listener => {
        (searchBox as any).removeEventListener(listener.event, listener.handler);
    });

    // 清理搜索框
    if (searchBox) {
        searchBox.remove();
        searchBox = null;
    }

    // 清理地图
    if (map) {
        map.remove();
        map = null;
    }

    // 清理标记点
    if (marker) {
        marker.remove();
        marker = null;
    }
});
</script>

<style>
.map-container {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

.search-box-container {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    z-index: 1000;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
}
</style>