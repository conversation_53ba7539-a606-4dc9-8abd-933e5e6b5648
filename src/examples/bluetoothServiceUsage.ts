/**
 * 蓝牙服务助手类使用示例
 * 展示如何在Vue组件中正确使用bluetoothServiceHelper
 */

import { ref, onMounted, onUnmounted } from 'vue';
import { bluetoothServiceHelper, type BluetoothServiceStatus } from '@/utils/bluetoothServiceHelper';
import { useToast } from '@/hooks/useToast';

// =================== 基础使用示例 ===================

export function useBluetoothServiceExample() {
  const isServiceReady = ref(false);
  const serviceStatus = ref<BluetoothServiceStatus | null>(null);
  const currentData = ref<number[] | null>(null);
  const { presentToast } = useToast();

  // 启动蓝牙服务
  const startBluetoothService = async () => {
    try {
      console.log('🚀 启动蓝牙服务...');
      
      // 1. 启动前台服务（会自动等待初始化完成）
      await bluetoothServiceHelper.startBluetoothForegroundService();
      
      // 2. 检查服务状态
      const status = await bluetoothServiceHelper.getServiceInitializationStatus();
      serviceStatus.value = status;
      
      if (status.bluetoothManagerInitialized) {
        isServiceReady.value = true;
        await presentToast('蓝牙服务启动成功', 'top');
        console.log('✅ 蓝牙服务已就绪');
      } else {
        await presentToast('蓝牙服务启动失败', 'top');
        console.error('❌ 蓝牙服务启动失败:', status.error);
      }
    } catch (error) {
      console.error('❌ 启动蓝牙服务异常:', error);
      await presentToast('启动蓝牙服务异常', 'top');
    }
  };

  // 获取当前蓝牙数据
  const getCurrentBluetoothData = async () => {
    try {
      const result = await bluetoothServiceHelper.getCurrentBluetoothSendData();
      
      if (result.success && result.data) {
        currentData.value = result.data;
        console.log('📊 当前蓝牙数据:', result.data);
        return result.data;
      } else {
        console.warn('⚠️ 获取蓝牙数据失败:', result.error);
        await presentToast(`获取数据失败: ${result.error}`, 'top');
        return null;
      }
    } catch (error) {
      console.error('❌ 获取蓝牙数据异常:', error);
      await presentToast('获取数据异常', 'top');
      return null;
    }
  };

  // 检查服务健康状态
  const checkServiceHealth = async () => {
    try {
      const isHealthy = await bluetoothServiceHelper.isServiceHealthy();
      
      if (isHealthy) {
        console.log('✅ 服务健康状态正常');
        await presentToast('服务状态正常', 'top');
      } else {
        console.warn('⚠️ 服务健康状态异常');
        await presentToast('服务状态异常，建议重启', 'top');
      }
      
      return isHealthy;
    } catch (error) {
      console.error('❌ 健康检查异常:', error);
      await presentToast('健康检查失败', 'top');
      return false;
    }
  };

  return {
    isServiceReady,
    serviceStatus,
    currentData,
    startBluetoothService,
    getCurrentBluetoothData,
    checkServiceHealth
  };
}

// =================== 高级使用示例 ===================

export function useAdvancedBluetoothService() {
  const serviceStatus = ref<BluetoothServiceStatus | null>(null);
  const isMonitoring = ref(false);
  let statusInterval: NodeJS.Timeout | null = null;

  // 启动服务状态监控
  const startStatusMonitoring = () => {
    if (isMonitoring.value) return;

    isMonitoring.value = true;
    console.log('🔍 开始监控服务状态...');

    const checkStatus = async () => {
      try {
        const status = await bluetoothServiceHelper.getServiceInitializationStatus();
        serviceStatus.value = status;

        // 检测到服务异常时的处理
        if (status.isServiceRunning && !status.bluetoothManagerInitialized) {
          console.warn('⚠️ 检测到服务异常 - 服务运行但管理器未初始化');
          // 可以在这里添加自动恢复逻辑
        }

        // 在开发模式下输出详细状态
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 服务状态更新:', {
            running: status.isServiceRunning,
            initialized: status.bluetoothManagerInitialized,
            details: status.bluetoothManagerDetails
          });
        }
      } catch (error) {
        console.error('状态检查失败:', error);
      }
    };

    // 立即检查一次
    checkStatus();

    // 每10秒检查一次
    statusInterval = setInterval(checkStatus, 10000);
  };

  // 停止服务状态监控
  const stopStatusMonitoring = () => {
    if (statusInterval) {
      clearInterval(statusInterval);
      statusInterval = null;
    }
    isMonitoring.value = false;
    console.log('🔍 停止监控服务状态');
  };

  // 自动恢复服务
  const autoRecoverService = async () => {
    try {
      console.log('🔧 尝试自动恢复服务...');
      
      // 重置服务状态
      bluetoothServiceHelper.resetInitializationStatus();
      
      // 等待一小段时间
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 重新启动服务
      await bluetoothServiceHelper.startBluetoothForegroundService();
      
      console.log('✅ 服务自动恢复成功');
      return true;
    } catch (error) {
      console.error('❌ 服务自动恢复失败:', error);
      return false;
    }
  };

  // 获取详细的服务统计信息
  const getDetailedServiceStats = async () => {
    try {
      const [status, stats] = await Promise.all([
        bluetoothServiceHelper.getServiceInitializationStatus(),
        bluetoothServiceHelper.getBluetoothSendingStats()
      ]);

      return {
        status,
        stats,
        timestamp: Date.now(),
        isHealthy: await bluetoothServiceHelper.isServiceHealthy()
      };
    } catch (error) {
      console.error('获取服务统计信息失败:', error);
      return null;
    }
  };

  // 清理资源
  const cleanup = () => {
    stopStatusMonitoring();
  };

  return {
    serviceStatus,
    isMonitoring,
    startStatusMonitoring,
    stopStatusMonitoring,
    autoRecoverService,
    getDetailedServiceStats,
    cleanup
  };
}

// =================== 在Vue组件中的使用示例 ===================

/*
// 在Vue组件中使用示例

<template>
  <div class="bluetooth-service-demo">
    <ion-button @click="startService" :disabled="isServiceReady">
      启动蓝牙服务
    </ion-button>
    
    <ion-button @click="getCurrentData" :disabled="!isServiceReady">
      获取当前数据
    </ion-button>
    
    <ion-button @click="checkHealth">
      健康检查
    </ion-button>
    
    <div v-if="serviceStatus" class="status-info">
      <p>服务运行: {{ serviceStatus.isServiceRunning ? '是' : '否' }}</p>
      <p>管理器初始化: {{ serviceStatus.bluetoothManagerInitialized ? '是' : '否' }}</p>
      <p>详细信息: {{ serviceStatus.bluetoothManagerDetails }}</p>
    </div>
    
    <div v-if="currentData" class="data-info">
      <p>当前数据: {{ formatDataArray(currentData) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import { IonButton } from '@ionic/vue';

// 使用基础功能
const {
  isServiceReady,
  serviceStatus,
  currentData,
  startBluetoothService,
  getCurrentBluetoothData,
  checkServiceHealth
} = useBluetoothServiceExample();

// 使用高级功能
const {
  startStatusMonitoring,
  cleanup
} = useAdvancedBluetoothService();

// 方法
const startService = async () => {
  await startBluetoothService();
};

const getCurrentData = async () => {
  await getCurrentBluetoothData();
};

const checkHealth = async () => {
  await checkServiceHealth();
};

const formatDataArray = (data: number[]): string => {
  return data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
};

// 生命周期
onMounted(() => {
  startStatusMonitoring();
});

onUnmounted(() => {
  cleanup();
});
</script>
*/

// =================== 错误处理最佳实践 ===================

export const bluetoothErrorHandler = {
  // 处理服务初始化错误
  handleInitializationError: async (error: any) => {
    console.error('服务初始化错误:', error);
    
    // 可以在这里添加重试逻辑
    const maxRetries = 3;
    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`重试初始化服务 (${i + 1}/${maxRetries})...`);
        await bluetoothServiceHelper.startBluetoothForegroundService();
        console.log('重试成功');
        return true;
      } catch (retryError) {
        console.error(`重试 ${i + 1} 失败:`, retryError);
        if (i === maxRetries - 1) {
          throw new Error(`服务初始化失败，已重试 ${maxRetries} 次`);
        }
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    return false;
  },

  // 处理数据获取错误
  handleDataFetchError: async (error: any) => {
    console.error('数据获取错误:', error);
    
    // 检查服务状态
    const isHealthy = await bluetoothServiceHelper.isServiceHealthy();
    if (!isHealthy) {
      console.warn('服务不健康，尝试重置...');
      bluetoothServiceHelper.resetInitializationStatus();
    }
    
    return null;
  },

  // 通用错误处理
  handleGenericError: (operation: string, error: any) => {
    console.error(`${operation} 错误:`, error);
    
    // 可以在这里添加错误上报逻辑
    if (process.env.NODE_ENV === 'production') {
      // 上报错误到监控系统
      // errorReporter.report(operation, error);
    }
  }
};

export default {
  useBluetoothServiceExample,
  useAdvancedBluetoothService,
  bluetoothErrorHandler
};