import { computed, ref } from 'vue'

export function useError() {
  const errorCode = ref(-1)
  const isCurrentError = ref(false)
  const isThrottleError = ref(false)
  const isMotorPhaseError = ref(false)
  const isMotorHallError = ref(false)
  const isTorqueSensorError = ref(false)
  const isSpeedSensorError = ref(false)
  const hasError = computed(() => {
    return errorCode.value > 0
  })
  const setError = (code: number) => {
    errorCode.value = code
    switch (code) {
      case 33:
        isCurrentError.value = true
        break
      case 34:
        isThrottleError.value = true
        break
      case 35:
        isMotorPhaseError.value = true
        break
      case 36:
        isMotorHallError.value = true
        break
      case 38:
        isTorqueSensorError.value = true
        break
      case 39:
        isSpeedSensorError.value = true
        break
      case 0:
        resetError()
        break
      default:
        break
    }
  }
  const resetError = () => {
    isCurrentError.value = false
    isThrottleError.value = false
    isMotorPhaseError.value = false
    isMotorHallError.value = false
    isTorqueSensorError.value = false
    isSpeedSensorError.value = false
  }

  return {
    setError,
    errorCode,
    isCurrentError,
    isThrottleError,
    isMotorPhaseError,
    isMotorHallError,
    isTorqueSensorError,
    isSpeedSensorError,
    hasError
  }
}
