import { toastController } from "@ionic/vue";

export function useToast() {
  const presentToast = async (
    message: string,
    position: "top" | "middle" | "bottom" = "top",
    duration = 3000
  ) => {
    const toast = await toastController.create({
      message,
      duration,
      position,
      layout: "stacked",
      color: "warning",
    });

    await toast.present();
  };
  return {
    presentToast,
  };
}
