// import { App } from "@capacitor/app";
// import { onMounted, onBeforeUnmount } from "vue";
// import { BackgroundMode } from "@anuradev/capacitor-background-mode";

// export function useBackgroundMode() {
//   let interval: any;
//   onMounted(() => {
//     App.addListener("appStateChange", async (state) => {
//       console.log('appStateChange',state);
//       // App in background
//       if (!state.isActive) {
//         // i want to enable background mode here
//         await BackgroundMode.enable({
//           channelName: "channelName",
//           bigText: true,
//           channelDescription: "channelDescription",
//           closeTitle: "close",
//           text: "text",
//           subText: "subText",
//           title: "title",
//           visibility: "public",
//         });
//       } else {
//         // App in foreground
//         await BackgroundMode.requestNotificationsPermission();
//       }
//     });

//     BackgroundMode.addListener("appInBackground", () => {
//       // i want to run this task every 3 minutes
//       interval = setInterval(
//         async () => {
//           const isEnabled = await BackgroundMode.isEnabled();
//           // const isActive = await BackgroundMode.isActive();
//           const data = `isEnabled : ${isEnabled.enabled}`;
//           console.log(data);
//           console.log(new Date().valueOf());
//         },
//         106,
//       );
//     });
//     BackgroundMode.addListener("appInForeground", () => {
//       clearInterval(interval);
//     })
//   })
// }