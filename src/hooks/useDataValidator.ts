import { ref } from 'vue';

export interface DataComparisonResult {
  isMatch: boolean;
  differences: Array<{
    position: number;
    expected: string;
    actual: string;
    description: string;
  }>;
  summary: string;
}

export function useDataValidator() {
  
  // 数据字段描述
  const dataFieldDescriptions = [
    "P5 - 电池类型",           // 0
    "档位 + 灯光状态",          // 1
    "最大速度 + 轮径",          // 2
    "P1 - 电机设置",           // 3
    "P2+P3+P4+限速+轮径扩展",   // 4
    "校验和(0-4,6-11)",        // 5
    "C1+C2 - PAS+电机相位",    // 6
    "C5+C14 - 电流+PAS功率",   // 7
    "C4+C7+C12 - 油门+巡航+欠压", // 8
    "把手最大速度",             // 9
    "C13 - 再生制动",          // 10
    "油门第一档百分比",         // 11
    "导航方向 + 距离单位",      // 12
    "单次距离低位",            // 13
    "总距离高位 + 单次距离高位", // 14
    "总距离低位",              // 15
    "校验和(1-15)",           // 16
    "截止位"                  // 17
  ];
  
  /**
   * 解析十六进制字符串为数组
   */
  const parseHexString = (hexString: string): number[] => {
    try {
      // 清理输入字符串，移除多余的空格和前缀
      const cleanString = hexString
        .replace(/0x/gi, '') // 移除0x前缀
        .replace(/\s+/g, ' ') // 规范化空格
        .trim();
      
      if (!cleanString) {
        console.warn("空的十六进制字符串");
        return [];
      }
      
      // 按空格分割
      const hexBytes = cleanString.split(' ').filter(byte => byte.length > 0);
      
      return hexBytes.map((hex, index) => {
        // 处理单个字符的情况（如 'f' -> '0f'）
        const normalizedHex = hex.length === 1 ? '0' + hex : hex;
        const value = parseInt(normalizedHex, 16);
        
        if (isNaN(value)) {
          console.warn(`无法解析第${index}位的十六进制值: ${hex}`);
          return 0;
        }
        
        return value;
      });
    } catch (error) {
      console.error("解析十六进制字符串失败:", error);
      return [];
    }
  };
  
  /**
   * 将数组转换为十六进制字符串
   */
  const arrayToHexString = (data: number[]): string => {
    return data.map(byte => `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
  };
  
  /**
   * 比较两组数据
   */
  const compareData = (expected: number[], actual: number[], labels: string[] = ['Expected', 'Actual']): DataComparisonResult => {
    const differences: DataComparisonResult['differences'] = [];
    const maxLength = Math.max(expected.length, actual.length);
    
    for (let i = 0; i < maxLength; i++) {
      const expectedValue = i < expected.length ? expected[i] : undefined;
      const actualValue = i < actual.length ? actual[i] : undefined;
      
      if (expectedValue !== actualValue) {
        differences.push({
          position: i,
          expected: expectedValue !== undefined ? `0x${expectedValue.toString(16).padStart(2, '0').toUpperCase()}` : 'undefined',
          actual: actualValue !== undefined ? `0x${actualValue.toString(16).padStart(2, '0').toUpperCase()}` : 'undefined',
          description: i < dataFieldDescriptions.length ? dataFieldDescriptions[i] : `字节${i}`
        });
      }
    }
    
    const isMatch = differences.length === 0;
    const summary = isMatch 
      ? "✅ 数据完全匹配" 
      : `❌ 发现${differences.length}处差异`;
    
    return {
      isMatch,
      differences,
      summary
    };
  };
  
  /**
   * 验证校验和
   */
  const validateChecksum = (data: number[]): { isValid: boolean; expected: number; actual: number } => {
    if (data.length < 17) {
      return { isValid: false, expected: 0, actual: 0 };
    }
    
    // 计算前面的校验和（字节5）
    let checksum1 = 0;
    for (let i = 1; i <= 4; i++) {
      checksum1 ^= data[i];
    }
    for (let i = 6; i <= 11; i++) {
      checksum1 ^= data[i];
    }
    
    // 计算完整的校验和（字节16）
    let checksum2 = 0;
    for (let i = 1; i <= 15; i++) {
      checksum2 ^= data[i];
    }
    
    const isValid1 = (checksum1 & 0xFF) === data[5];
    const isValid2 = (checksum2 & 0xFF) === data[16];
    
    return {
      isValid: isValid1 && isValid2,
      expected: checksum2 & 0xFF,
      actual: data[16]
    };
  };
  
  /**
   * 分析数据问题
   */
  const analyzeDataIssues = (serialData: string, androidData: string, webData: string) => {
    console.log("🔍 开始数据问题分析");
    
    // 解析数据
    const serial = parseHexString(serialData);
    const android = parseHexString(androidData.replace(/[\[\],]/g, ''));
    const web = parseHexString(webData);
    
    console.log("📊 解析结果:");
    console.log("串口数据:", arrayToHexString(serial));
    console.log("Android数据:", arrayToHexString(android));
    console.log("Web数据:", arrayToHexString(web));
    
    // 比较串口和Android数据
    const serialVsAndroid = compareData(serial, android, ['串口', 'Android']);
    console.log("\n📋 串口 vs Android 比较:");
    console.log(serialVsAndroid.summary);
    if (!serialVsAndroid.isMatch) {
      serialVsAndroid.differences.forEach(diff => {
        console.log(`  位置${diff.position} (${diff.description}): ${diff.expected} → ${diff.actual}`);
      });
    }
    
    // 比较串口和Web数据
    const serialVsWeb = compareData(serial, web, ['串口', 'Web']);
    console.log("\n📋 串口 vs Web 比较:");
    console.log(serialVsWeb.summary);
    if (!serialVsWeb.isMatch) {
      serialVsWeb.differences.forEach(diff => {
        console.log(`  位置${diff.position} (${diff.description}): ${diff.expected} → ${diff.actual}`);
      });
    }
    
    // 比较Android和Web数据
    const androidVsWeb = compareData(android, web, ['Android', 'Web']);
    console.log("\n📋 Android vs Web 比较:");
    console.log(androidVsWeb.summary);
    if (!androidVsWeb.isMatch) {
      androidVsWeb.differences.forEach(diff => {
        console.log(`  位置${diff.position} (${diff.description}): ${diff.expected} → ${diff.actual}`);
      });
    }
    
    // 校验和验证
    console.log("\n🔐 校验和验证:");
    if (serial.length >= 17) {
      const serialChecksum = validateChecksum(serial);
      console.log(`串口数据校验和: ${serialChecksum.isValid ? '✅ 有效' : '❌ 无效'} (期望: 0x${serialChecksum.expected.toString(16).padStart(2, '0').toUpperCase()}, 实际: 0x${serialChecksum.actual.toString(16).padStart(2, '0').toUpperCase()})`);
    }
    
    if (android.length >= 17) {
      const androidChecksum = validateChecksum(android);
      console.log(`Android数据校验和: ${androidChecksum.isValid ? '✅ 有效' : '❌ 无效'} (期望: 0x${androidChecksum.expected.toString(16).padStart(2, '0').toUpperCase()}, 实际: 0x${androidChecksum.actual.toString(16).padStart(2, '0').toUpperCase()})`);
    }
    
    if (web.length >= 17) {
      const webChecksum = validateChecksum(web);
      console.log(`Web数据校验和: ${webChecksum.isValid ? '✅ 有效' : '❌ 无效'} (期望: 0x${webChecksum.expected.toString(16).padStart(2, '0').toUpperCase()}, 实际: 0x${webChecksum.actual.toString(16).padStart(2, '0').toUpperCase()})`);
    }
    
    return {
      serialVsAndroid,
      serialVsWeb,
      androidVsWeb,
      parsedData: {
        serial,
        android,
        web
      }
    };
  };
  
  /**
   * 实时监控数据变化
   */
  const monitorDataChanges = (data: number[], label: string = "数据") => {
    const hexString = arrayToHexString(data);
    const checksum = validateChecksum(data);
    
    console.log(`🔍 ${label}监控:`, {
      hex: hexString,
      length: data.length,
      checksum: checksum.isValid ? '✅ 有效' : '❌ 无效',
      timestamp: new Date().toISOString()
    });
    
    return {
      hex: hexString,
      length: data.length,
      isChecksumValid: checksum.isValid,
      timestamp: Date.now()
    };
  };
  
  return {
    parseHexString,
    arrayToHexString,
    compareData,
    validateChecksum,
    analyzeDataIssues,
    monitorDataChanges,
    dataFieldDescriptions
  };
}