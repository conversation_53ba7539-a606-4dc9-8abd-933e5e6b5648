import { useSetting } from "@/hooks/useSetting";
import { useBluetoothLe } from "@/hooks/useBluetooth-le";
import { useBackgroundBluetooth } from "@/hooks/useBackgroundBluetooth";
import { useIOSBackgroundTimer } from "@/hooks/useIOSBackgroundTimer";
import { CharacteristicUUID, ServiceUUID } from "@/const/ble.const";
import { useDashboardStore } from "@/store/useDashboardStore";
import { useSettingStore } from "@/store/useSettingStore";
import { useBleStore } from "@/store/useBleStore";
import { storeToRefs } from "pinia";
import chalk from "chalk";
import {
  BleClient,
  dataViewToHexString,
  dataViewToNumbers,
  numbersToDataView,
  numberToUUID,
} from "@capacitor-community/bluetooth-le";
import { useToast } from "@/hooks/useToast";
import { DimensionList } from "@/const/bike.const";
import { useErrorStore } from "@/store/useErrorStore";
import { isPlatform } from "@ionic/vue";
import { CapacitorKtService } from 'capacitor-kt-service';
import { bluetoothServiceHelper } from '@/utils/bluetoothServiceHelper';

chalk.level = 1;

export function useMessage() {
  const settingStore = useSettingStore();
  const { getDisplayType } = storeToRefs(settingStore);
  const dashboardStore = useDashboardStore();
  const { writeData, updateSetting } = useSetting();
  const { dimension, candidateParam } = storeToRefs(useSettingStore());
  const { speed, singleMileage, totalMileage } = storeToRefs(dashboardStore);
  const {
    setElectricQuantity,
    setSingleTime,
    setSpeed,
    setSingleMileage,
    setTotalMileage,
    setAssistance,
    setRegenative,
    setUndervoltage,
    setReverse,
    setTurnRight,
    setTurnLeft,
    setThrottle,
    setCruise,
    setBrake,
  } = useDashboardStore();
  const { connectedDevice } = storeToRefs(useBleStore());
  const { write, startNotification, stopNotification, disConnectBle } =
    useBluetoothLe();
  const { presentToast } = useToast();
  const { setErrorCode } = useErrorStore();
  const {
    startBackgroundService,
    stopBackgroundService,
    createReliableTimer,
    isServiceRunning
  } = useBackgroundBluetooth();
  
  const {
    setupIOSBackgroundTimer,
    createUltimateTimer
  } = useIOSBackgroundTimer();
  
  let stopTimer: (() => void) | null = null;
  let singleTimeInterval: any;
  let singleTimeSecond = 0;
  let isTimerActive = false;
  let lastDataSendTime = 0;
  let timerHealthCheck: NodeJS.Timeout | null = null;
  let sendDataFunction: (() => Promise<void>) | null = null;
  let consecutiveFailures = 0;
  let timerRestartCount = 0;
  let lastSuccessfulSendTime = 0;
  let currentSendData: number[] = [...writeData.value]; // 缓存当前要发送的数据
  
  // 更新发送数据缓存的函数
  const updateSendDataCache = async () => {
    try {
      console.log("🔄 更新发送数据缓存...");
      
      // 尝试获取Native端的完整数据（包含导航信息）
      try {
        const nativeDataResult = await bluetoothServiceHelper.getCurrentBluetoothSendData();
        if (nativeDataResult.success && nativeDataResult.data && nativeDataResult.data.length >= 18) {
          currentSendData = [...nativeDataResult.data];
          console.log("✅ 使用Native端完整数据:", currentSendData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
        } else {
          currentSendData = [...writeData.value];
          console.log("⚠️ Native端数据无效，使用Web端数据:", nativeDataResult.error || '未知错误');
          console.log("📊 使用Web端数据:", currentSendData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
        }
      } catch (nativeError) {
        console.warn('获取Native端数据失败，使用Web端数据:', nativeError);
        currentSendData = [...writeData.value];
      }
    } catch (error) {
      console.error('更新发送数据缓存失败:', error);
    }
  };
  
  // 🔧 监听设置数据更新事件
  const handleSettingDataUpdate = async (event: CustomEvent) => {
    try {
      console.log("🔄 收到设置数据更新事件:", event.detail);
      
      // 检查是否为强制更新
      const forceUpdate = event.detail?.forceUpdate === true;
      
      if (isTimerActive || forceUpdate) {
        console.log(`🔄 ${forceUpdate ? '强制更新' : '定时器运行中'}，立即更新发送数据缓存`);
        await updateSendDataCache();
        console.log("✅ 发送数据缓存已更新，下次发送将使用新数据");
        
        // 如果是强制更新，额外记录日志
        if (forceUpdate) {
          console.log("🔧 强制更新完成，数据已立即生效");
        }
      } else {
        console.log("⚠️ 定时器未运行且非强制更新，跳过数据缓存更新");
      }
    } catch (error) {
      console.error("❌ 处理设置数据更新事件失败:", error);
    }
  };
  
  // 添加事件监听器
  window.addEventListener('settingDataUpdated', handleSettingDataUpdate as unknown as EventListener);
  
  // 启动智能定时器健康检查
  const startTimerHealthCheck = () => {
    if (timerHealthCheck) return;
    
    timerHealthCheck = setInterval(() => {
      const now = Date.now();
      const timeSinceLastSend = now - lastDataSendTime;
      const timeSinceLastSuccess = now - lastSuccessfulSendTime;
      
      // 更智能的健康检查逻辑
      if (isTimerActive && timeSinceLastSend > 2000) { // 提高到2秒容忍度
        console.warn(`Timer health check failed: ${timeSinceLastSend}ms since last send`);
        
        // 如果连续多次失败且超过5秒没有成功发送，才考虑重启
        if (timeSinceLastSuccess > 5000) {
          consecutiveFailures++;
          console.warn(`Consecutive failures: ${consecutiveFailures}`);
          
          // 只有在连续失败3次以上才重启定时器
          if (consecutiveFailures >= 3 && timerRestartCount < 5) {
            console.log('Attempting to restart data timer...');
            restartTimer();
          } else if (timerRestartCount >= 5) {
            console.error('Too many timer restarts, manual intervention required');
            // 可以发送通知给用户
            presentToast("数据传输异常，请重新连接设备");
          }
        }
      } else if (isTimerActive && timeSinceLastSend <= 500) {
        // 如果定时器工作正常，重置失败计数
        consecutiveFailures = 0;
      }
    }, 1000); // 每秒检查一次
    
    console.log('Smart timer health check started');
  };
  
  // 重启定时器
  const restartTimer = async () => {
    if (!sendDataFunction) {
      console.error('Cannot restart timer: sendDataFunction not available');
      return;
    }
    
    try {
      timerRestartCount++;
      console.log(`Restarting timer (attempt ${timerRestartCount})`);
      
      // 停止当前定时器
      if (stopTimer) {
        stopTimer();
        stopTimer = null;
      }
      
      // 等待一小段时间让系统稳定
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 重新创建定时器
      if (isPlatform('ios')) {
        stopTimer = createUltimateTimer(sendDataFunction, 106);
      } else {
        stopTimer = createReliableTimer(sendDataFunction, 106);
      }
      
      console.log('Timer restarted successfully');
      consecutiveFailures = 0; // 重置失败计数
      
    } catch (error) {
      console.error('Failed to restart timer:', error);
    }
  };
  
  // 停止定时器健康检查
  const stopTimerHealthCheck = () => {
    if (timerHealthCheck) {
      clearInterval(timerHealthCheck);
      timerHealthCheck = null;
      console.log('Timer health check stopped');
    }
    
    // 重置计数器
    consecutiveFailures = 0;
    timerRestartCount = 0;
  };
  
  const exitApp = async () => {
    await stopSendMessage();
    await disConnectBle(connectedDevice.value);
  };
  
  const sendMessage = async () => {
    if (!connectedDevice.value.deviceId || !connectedDevice.value.isPaired) {
      await presentToast("Please connect your Bluetooth device first");
      return;
    }
    
    // 停止之前的定时器
    if (stopTimer) {
      stopTimer();
      stopTimer = null;
    }
    
    // 启动后台服务
    await startBackgroundService();
    
    // 设置iOS后台定时器监听
    setupIOSBackgroundTimer();
    
    isTimerActive = true;
    lastSuccessfulSendTime = Date.now(); // 初始化成功发送时间
    
    // 初始化发送数据缓存
    await updateSendDataCache();
    
    // 创建发送数据的函数
    sendDataFunction = async () => {
      if (!isTimerActive) return;
      
      // 更新最后发送时间，用于健康检查
      lastDataSendTime = Date.now();
      
      try {
        console.log(
          "start write cached data",
          JSON.stringify(currentSendData)
        );
        
        // 更严格的连接状态检查
        if (!connectedDevice.value.deviceId) {
          console.warn('No device ID, stopping data transmission');
          isTimerActive = false;
          await stopSendMessage();
          return;
        }
        
        // 检查设备连接状态，但给予重试机会
        if (!connectedDevice.value.isPaired) {
          console.warn('Device not paired, but attempting to continue...');
          // 不立即停止，给一些重试机会
          return;
        }
        
        await write(
          connectedDevice.value.deviceId,
          numberToUUID(ServiceUUID),
          numberToUUID(CharacteristicUUID),
          numbersToDataView(currentSendData)
        );
        
        // 发送成功，更新成功时间并重置失败计数
        lastSuccessfulSendTime = Date.now();
        consecutiveFailures = 0;
        
      } catch (error: any) {
        console.log(chalk.red(`send message error: ${JSON.stringify(error)}`));
        
        // 根据错误类型决定是否停止发送
        const errorMessage = error?.message || error?.errorMessage || '';
        
        // 如果是连接超时或设备未找到，尝试继续发送（可能是临时问题）
        if (errorMessage.includes('timeout') || 
            errorMessage.includes('not found') ||
            errorMessage.includes('disconnected')) {
          console.warn('Temporary connection issue, continuing to try...');
          consecutiveFailures++;
          return; // 不停止定时器，继续尝试
        }
        
        // 其他严重错误才停止发送
        console.error('Serious error, stopping data transmission');
        isTimerActive = false;
        await stopSendMessage();
      }
    };
    
    // 使用终极定时器策略 - 确保iOS后台也能保持106ms间隔
    if (isPlatform('ios')) {
      stopTimer = createUltimateTimer(sendDataFunction, 106);
    } else {
      stopTimer = createReliableTimer(sendDataFunction, 106);
    }
    
    // 启动智能定时器健康检查
    startTimerHealthCheck();
    
    await startNotification(
      connectedDevice.value.deviceId,
      numberToUUID(ServiceUUID),
      numberToUUID(CharacteristicUUID),
      onNotification
    );
  };
  
  const stopSendMessage = async () => {
    isTimerActive = false;
    
    // 停止定时器
    if (stopTimer) {
      stopTimer();
      stopTimer = null;
    }
    
    // 清除发送函数引用
    sendDataFunction = null;
    
    // 停止定时器健康检查
    stopTimerHealthCheck();
    
    // 停止后台服务
    await stopBackgroundService();
    
    if (!connectedDevice.value.deviceId || !connectedDevice.value.isPaired)
      return;
    
    await stopNotification(
      connectedDevice.value.deviceId,
      numberToUUID(ServiceUUID),
      numberToUUID(CharacteristicUUID)
    );
  };
  // 校验第一位0x41,不是就跳过
  //
  const onNotification = (value: DataView) => {
    // 欠压、反冲电 message[1]
    // 倒档、转向 message[2]
    const message = dataViewToNumbers(value);
    const isValid = checkIsValidNotification(message);
    if (!isValid) return;
    console.log(chalk.green("onNotification", message));
    getBattery(message);
    getSpeed(message);
    getSingleDistance();
    getAssistance(message);
    checkError(message);
    computedMessageFirst(message);
    computedMessageSecond(message);
    computedMessageSeventh(message);
  };
  const checkIsValidNotification = (value: number[]) => {
    const firstValue = value[0];
    if (firstValue !== 0x41) return false;
    const validate =
      value[1] ^
      value[2] ^
      value[3] ^
      value[4] ^
      value[5] ^
      value[7] ^
      value[8] ^
      value[9] ^
      value[10] ^
      value[11];
    return validate === value[6];
  };
  const computedMessageFirst = (message: number[]) => {
    const command = message[1];
    const undervoltage = command & 1; // 是否是欠压
    const regenative = (command & 2) >> 1; // 是否是反冲电
    setRegenative(regenative);
    setUndervoltage(undervoltage);
  };
  const computedMessageSecond = (message: number[]) => {
    const command = message[2];
    const reverse = (command & 0x40) >> 5; // 是否是倒档
    const turnRight = command & 1; // 是否是右转
    const turnLeft = (command & 2) >> 1; // 是否是左转
    setReverse(reverse);
    setTurnRight(turnRight);
    setTurnLeft(turnLeft);
  };
  const computedMessageSeventh = (message: number[]) => {
    const command = message[7];
    const throttle = command & 3; // 转把状态 1 停止工作，2工作中
    const cruise = (command & 8) >> 3; // 巡航状态 1 代表巡航
    const brake = (command & 32) >> 5; // 刹车状态 1代表刹车
    setThrottle(throttle);
    setCruise(cruise);
    setBrake(brake);
  };
  const setBLEName = async (nickname: string) => {
    const command = `AT+NAME=${nickname}\r\n`;
    const bytes = new TextEncoder().encode(command);
    const dataView = new DataView(bytes.buffer);
    await write(
      connectedDevice.value.deviceId,
      numberToUUID(ServiceUUID),
      numberToUUID(CharacteristicUUID),
      dataView
    );
    await BleClient.startNotifications(
      connectedDevice.value.deviceId,
      numberToUUID(ServiceUUID),
      numberToUUID(CharacteristicUUID),
      (result) => {
        console.log("rename str", dataViewToHexString(result));
        console.log("rename", dataViewToNumbers(result));
      }
    );
  };
  // 读取电量
  const getBattery = (message: number[]) => {
    const electricQuantity = (message[1] & 0x1f) >> 2;
    setElectricQuantity(electricQuantity);
  };
  // 读取速度
  const getSpeed = (message: number[]) => {
    const timeFirst = message[3] << 8;
    const timeLast = message[4];
    const timeSpan = timeFirst + timeLast;
    if (isNaN(timeSpan) || timeSpan > 21008 || timeSpan === 0) {
      return;
    } // 6922是停止时的时间间隔  21008异常时间
    console.log("timeSpan", timeSpan);
    if (timeSpan === 6922) {
      setSpeed(0);
      clearInterval(singleTimeInterval);
      singleTimeInterval = 0;
      return;
    }
    console.log(chalk.yellow("dimension", dimension.value));
    const realDimension =
      DimensionList.find((item) => item.value === dimension.value)?.dimension ||
      26;
    const round = (realDimension * 25.4 * Math.PI) / Math.pow(10, 6);
    const defaultSpeed = (round / timeSpan) * 1000 * 3600;
    const speed = Math.floor(defaultSpeed); // === 1 ? 0 : Math.floor(defaultSpeed);
    console.log("speed", speed);
    const displaySpeed =
      getDisplayType.value === "kilometer"
        ? speed
        : Math.floor(speed * 0.6213712);
    setSpeed(displaySpeed);
    getSingleTime();
  };
  // 获取单次计时
  const getSingleTime = () => {
    if (singleTimeInterval) return;
    singleTimeInterval = setInterval(() => {
      singleTimeSecond++;
      const singleTime = formatSeconds(singleTimeSecond);
      setSingleTime(singleTime);
    }, 1000);
  };
  const formatSeconds = (time: number) => {
    const min = Math.floor(time % 3600);
    return (
      formatBit(Math.floor(time / 3600)) +
      ":" +
      formatBit(Math.floor(min / 60)) +
      ":" +
      formatBit(time % 60)
    );
  };
  const formatBit = (val: number) => {
    val = +val;
    return val > 9 ? val : "0" + val;
  };
  // 计算单次里程
  const getSingleDistance = () => {
    const timeDelay = 1 / 3600; // 0.0000294; // 106ms 转成 hour
    const distance = speed.value * timeDelay;
    const storageMileage = singleMileage || 0;
    const currentMileage = storageMileage.value + distance;
    const storageTotal = totalMileage || 0;
    const total = storageTotal.value + distance;
    setSingleMileage(currentMileage);
    setTotalMileage(total);
  };
  // 计算助力
  const getAssistance = (message: number[]) => {
    const isAssistance = (message[7] >> 4) & 1;
    const candidate = Number(candidateParam.value);
    if (isAssistance) {
      const received = ((message[10] & 0x3f) << 8) + message[11];
      if (received !== 0) {
        const assistance = 60 / ((received * candidate) / 1000);
        setAssistance(Math.floor(assistance));
      } else {
        setAssistance(0);
      }
    } else {
      setAssistance(0);
    }
  };
  // 系统错误
  const checkError = (message: number[]) => {
    const errorCode = message[5];
    setErrorCode(errorCode);
  };

  // 手动重启定时器
  const manualRestartTimer = async () => {
    if (!isTimerActive || !sendDataFunction) {
      console.warn('Cannot manually restart: timer not active or function not available');
      return;
    }
    
    try {
      console.log('Manual timer restart initiated');
      await restartTimer();
      await presentToast("定时器已手动重启");
    } catch (error) {
      console.error('Manual restart failed:', error);
      await presentToast("定时器重启失败，请重新连接设备");
    }
  };
  
  // 获取定时器状态
  const getTimerStatus = () => ({
    isActive: isTimerActive,
    lastSendTime: lastDataSendTime,
    lastSuccessfulSendTime: lastSuccessfulSendTime,
    consecutiveFailures,
    restartCount: timerRestartCount
  });

  // 清理资源的函数
  const cleanup = () => {
    try {
      window.removeEventListener('settingDataUpdated', handleSettingDataUpdate as unknown as EventListener);
      console.log("✅ useMessage 事件监听器已清理");
    } catch (error) {
      console.error("❌ 清理useMessage事件监听器已清理:", error);
    }
  };

  return {
    sendMessage,
    stopSendMessage,
    getSpeed,
    getBattery,
    checkError,
    getAssistance,
    getSingleDistance,
    exitApp,
    setBLEName,
    isServiceRunning,
    manualRestartTimer,
    getTimerStatus,
    updateSendDataCache, // 暴露数据缓存更新函数
    cleanup, // 🔧 新增：清理资源函数
    // 暴露状态用于监控组件
    isTimerActive: () => isTimerActive,
    getLastSendTime: () => lastDataSendTime,
    getLastSuccessTime: () => lastSuccessfulSendTime,
    getFailureCount: () => consecutiveFailures,
    getRestartCount: () => timerRestartCount
  };
}
