import { ref } from 'vue';
import { isPlatform } from '@ionic/vue';
import { App } from '@capacitor/app';

export function useIOSBackgroundTimer() {
  const isBackground = ref(false);
  const isIOSTimerActive = ref(false);
  
  // iOS后台定时器管理
  const setupIOSBackgroundTimer = () => {
    if (!isPlatform('ios')) return;
    
    // 监听应用状态变化
    App.addListener('appStateChange', (state) => {
      isBackground.value = !state.isActive;
      console.log('iOS App state changed:', state.isActive ? 'foreground' : 'background');
    });
    
    // 监听页面可见性变化（额外保障）
    document.addEventListener('visibilitychange', () => {
      const hidden = document.hidden;
      isBackground.value = hidden;
      console.log('iOS Visibility changed:', hidden ? 'hidden' : 'visible');
    });
  };
  
  // 创建强制106ms定时器 - 不管前后台状态
  const createForcedTimer = (callback: () => void, interval: number = 106) => {
    if (!isPlatform('ios')) {
      // 非iOS平台直接使用标准定时器
      const intervalId = setInterval(callback, interval);
      return () => clearInterval(intervalId);
    }
    
    // iOS平台使用多种策略确保106ms间隔
    let mainTimer: number;
    let backupTimer: number;
    let rafTimer: number;
    let isActive = true;
    
    // 策略1: 标准setInterval
    mainTimer = window.setInterval(() => {
      if (isActive) callback();
    }, interval) as unknown as number;
    
    // 策略2: setTimeout递归调用（更精确）
    const recursiveTimeout = () => {
      if (!isActive) return;
      
      const startTime = performance.now();
      callback();
      
      const elapsed = performance.now() - startTime;
      const nextDelay = Math.max(0, interval - elapsed);
      
      backupTimer = setTimeout(recursiveTimeout, nextDelay) as unknown as number;
    };
    
    // 延迟启动备用定时器，避免重复调用
    setTimeout(recursiveTimeout, interval / 2);
    
    // 策略3: requestAnimationFrame高精度定时器
    let lastRAFTime = performance.now();
    const rafTick = (currentTime: number) => {
      if (!isActive) return;
      
      if (currentTime - lastRAFTime >= interval) {
        callback();
        lastRAFTime = currentTime;
      }
      
      rafTimer = requestAnimationFrame(rafTick);
    };
    
    rafTimer = requestAnimationFrame(rafTick);
    
    // 返回清理函数
    return () => {
      isActive = false;
      clearInterval(mainTimer);
      clearTimeout(backupTimer);
      cancelAnimationFrame(rafTimer);
    };
  };
  
  // 创建iOS优化的Web Worker定时器
  const createIOSWorkerTimer = (callback: () => void, interval: number = 106) => {
    const workerCode = `
      let intervalId;
      let timeoutId;
      
      // 双重定时器策略
      const startTimers = (interval) => {
        // 主定时器
        intervalId = setInterval(() => {
          self.postMessage('tick');
        }, interval);
        
        // 备用递归定时器
        const recursiveTick = () => {
          const start = performance.now();
          self.postMessage('tick');
          const elapsed = performance.now() - start;
          const nextDelay = Math.max(0, interval - elapsed);
          timeoutId = setTimeout(recursiveTick, nextDelay);
        };
        
        setTimeout(recursiveTick, interval / 2);
      };
      
      const stopTimers = () => {
        if (intervalId) clearInterval(intervalId);
        if (timeoutId) clearTimeout(timeoutId);
      };
      
      self.onmessage = function(e) {
        const { action, interval } = e.data;
        
        if (action === 'start') {
          stopTimers();
          startTimers(interval);
        } else if (action === 'stop') {
          stopTimers();
        }
      };
    `;
    
    const blob = new Blob([workerCode], { type: 'application/javascript' });
    const worker = new Worker(URL.createObjectURL(blob));
    
    let callCount = 0;
    worker.onmessage = () => {
      callCount++;
      // 过滤重复调用，确保精确间隔
      if (callCount % 2 === 1) {
        callback();
      }
    };
    
    worker.postMessage({ action: 'start', interval });
    
    return () => {
      worker.postMessage({ action: 'stop' });
      worker.terminate();
    };
  };
  
  // 创建终极定时器 - 结合所有策略
  const createUltimateTimer = (callback: () => void, interval: number = 106) => {
    const stopFunctions: Array<() => void> = [];
    let callCount = 0;
    const lastCallTime = { value: 0 };
    let isActive = true;
    
    // 防抖包装器 - 确保不会过度调用
    const debouncedCallback = () => {
      if (!isActive) return;
      
      const now = performance.now();
      if (now - lastCallTime.value >= interval * 0.8) { // 允许20%的误差，提高调用频率
        lastCallTime.value = now;
        callCount++;
        console.log(`Timer tick #${callCount} at ${now.toFixed(2)}ms`);
        callback();
      }
    };
    
    // 策略1: 多重setInterval定时器
    for (let i = 0; i < 3; i++) {
      const timerId = setInterval(() => {
        if (isActive) debouncedCallback();
      }, interval + i * 2); // 略微错开时间，增加命中率
      
      stopFunctions.push(() => clearInterval(timerId));
    }
    
    try {
      // 策略2: iOS Worker定时器
      const stopWorker = createIOSWorkerTimer(debouncedCallback, interval);
      stopFunctions.push(stopWorker);
    } catch (error) {
      console.warn('iOS Worker timer failed:', error);
    }
    
    try {
      // 策略3: 强制定时器
      const stopForced = createForcedTimer(debouncedCallback, interval);
      stopFunctions.push(stopForced);
    } catch (error) {
      console.warn('Forced timer failed:', error);
    }
    
    // 策略4: 高频率检查定时器（用于检测其他定时器是否失效）
    let lastCheckTime = performance.now();
    const checkTimer = setInterval(() => {
      if (!isActive) return;
      
      const now = performance.now();
      const timeSinceLastCall = now - lastCallTime.value;
      const timeSinceLastCheck = now - lastCheckTime;
      
      // 如果超过200ms没有调用，且检查间隔正常，说明主定时器可能失效
      if (timeSinceLastCall > 200 && timeSinceLastCheck < 150) {
        console.warn(`Timer seems stuck, forcing callback. Last call: ${timeSinceLastCall}ms ago`);
        debouncedCallback();
      }
      
      lastCheckTime = now;
    }, 100); // 每100ms检查一次
    
    stopFunctions.push(() => clearInterval(checkTimer));
    
    return () => {
      isActive = false;
      stopFunctions.forEach(stop => {
        try {
          stop();
        } catch (error) {
          console.warn('Error stopping iOS timer:', error);
        }
      });
    };
  };
  
  return {
    isBackground,
    isIOSTimerActive,
    setupIOSBackgroundTimer,
    createForcedTimer,
    createIOSWorkerTimer,
    createUltimateTimer
  };
}