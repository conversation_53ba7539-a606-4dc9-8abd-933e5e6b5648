import { ref } from "vue";
import {
  BleClient,
  dataViewToHexString,
  numberToUUID,
  ScanResult,
} from "@capacitor-community/bluetooth-le";
import { Device, useBleStore } from "@/store/useBleStore";
import { Capacitor } from "@capacitor/core";
import { storeToRefs } from "pinia";
// useToast 将在需要时动态导入
import { ServiceUUID } from "@/const/ble.const";
import { isPlatform } from "@ionic/vue";
import { useDisconnectEventBus } from "@/hooks/useDisconnectEventBus";

export function useBluetoothLe() {
  const bleStore = useBleStore();
  // presentToast 将在需要时动态导入
  const { connectedDevice } = storeToRefs(bleStore);
  const {
    setAvailableDevice,
    clearAvailableDevices,
    setConnectedDevice,
    updateConnectedDevicePairedStatus,
    updateConnectedDevicePairingStatus,
  } = useBleStore();
  const { emit } = useDisconnectEventBus();
  const scanning = ref(false);
  const isNative = Capacitor.isNativePlatform();

  // 🔧 新增：安全的sessionStorage操作辅助函数
  const safeSessionStorage = {
    setItem: (key: string, value: string): boolean => {
      try {
        if (typeof Storage !== 'undefined' && window.sessionStorage) {
          sessionStorage.setItem(key, value);
          return true;
        } else {
          console.warn('⚠️ sessionStorage不可用，使用内存存储');
          // 使用内存备份存储
          (window as any).__bluetoothSessionBackup = (window as any).__bluetoothSessionBackup || {};
          (window as any).__bluetoothSessionBackup[key] = value;
          return true;
        }
      } catch (error) {
        console.error('❌ sessionStorage.setItem失败:', error);
        // 尝试内存备份存储
        try {
          (window as any).__bluetoothSessionBackup = (window as any).__bluetoothSessionBackup || {};
          (window as any).__bluetoothSessionBackup[key] = value;
          return true;
        } catch (backupError) {
          console.error('❌ 内存备份存储也失败:', backupError);
          return false;
        }
      }
    },
    
    getItem: (key: string): string | null => {
      try {
        if (typeof Storage !== 'undefined' && window.sessionStorage) {
          return sessionStorage.getItem(key);
        } else {
          // 尝试从内存备份存储获取
          const backup = (window as any).__bluetoothSessionBackup;
          return backup && backup[key] ? backup[key] : null;
        }
      } catch (error) {
        console.error('❌ sessionStorage.getItem失败:', error);
        // 尝试从内存备份存储获取
        try {
          const backup = (window as any).__bluetoothSessionBackup;
          return backup && backup[key] ? backup[key] : null;
        } catch (backupError) {
          console.error('❌ 内存备份存储读取也失败:', backupError);
          return null;
        }
      }
    },
    
    removeItem: (key: string): boolean => {
      try {
        if (typeof Storage !== 'undefined' && window.sessionStorage) {
          sessionStorage.removeItem(key);
        }
        // 同时清理内存备份
        const backup = (window as any).__bluetoothSessionBackup;
        if (backup && backup[key]) {
          delete backup[key];
        }
        return true;
      } catch (error) {
        console.error('❌ sessionStorage.removeItem失败:', error);
        // 尝试清理内存备份
        try {
          const backup = (window as any).__bluetoothSessionBackup;
          if (backup && backup[key]) {
            delete backup[key];
          }
          return true;
        } catch (backupError) {
          console.error('❌ 内存备份存储清理也失败:', backupError);
          return false;
        }
      }
    }
  };

  async function scan() {
    clearAvailableDevices();
    if (!isNative) {
      testBleDevice();
      return;
    }
    try {
      scanning.value = true;
      await BleClient.initialize();
      const isBtEnabled = await BleClient.isEnabled();
      if (!isBtEnabled) {
        if (isPlatform("android")) {
          await BleClient.openBluetoothSettings();
        }
        if (isPlatform("ios")) {
          await BleClient.openAppSettings();
        }
      }
      await BleClient.requestLEScan(
        {
          services: [numberToUUID(ServiceUUID)], // 电动车蓝牙uuid
        },
        (result) => {
          deviceDetected(result);
          console.log("received new scan result", result);
        }
      );

      setTimeout(async () => {
        await BleClient.stopLEScan();
        scanning.value = false;
        console.log("stopped scanning");
      }, 5000);
    } catch (error) {
      scanning.value = false;
      console.error("scanForBluetoothDevices", error);
    }
  }

  const initialBle = async () => {
    return new Promise(async (resolve, reject) => {
      try {
        await BleClient.initialize();
        const isBtEnabled = await BleClient.isEnabled();
        if (!isBtEnabled) {
          try {
            const { useToast } = await import("@/hooks/useToast");
            const { presentToast } = useToast();
            await presentToast("Please turn on Bluetooth");
          } catch (error) {
            console.warn("Failed to show toast:", error);
          }
          reject();
        } else {
          if (isPlatform("ios")) {
            await BleClient.getDevices([connectedDevice.value.deviceId]);
          }
          await connectBle(connectedDevice.value);
          resolve(true);
        }
      } catch (e) {
        try {
          const { useToast } = await import("@/hooks/useToast");
          const { presentToast } = useToast();
          await presentToast("Please connect your Bluetooth device first");
        } catch (error) {
          console.warn("Failed to show toast:", error);
        }
        reject();
        console.error("Bluetooth Unavailable");
      }
    });
  };
  /*TODO 自动连接失败处理，给提示或者不抛错
  * ⚡️  WebView loaded
    ⚡️  BluetoothLe - Resolve initialize BLE powered on
    ⚡️  TO JS undefined
    ⚡️  To Native ->  BluetoothLe isEnabled 43232727
    ⚡️  TO JS {"value":true}
    ⚡️  To Native ->  App addListener 43232728
    ⚡️  To Native ->  BluetoothLe getDevices 43232729
    ⚡️  To Native ->  Keyboard getResizeMode 43232730
    ⚡️  TO JS {"devices":[{"name":"LanQianTech","deviceId":"A8DF3B4C-A830-8D3D-C2B1-381972D8396B"}]}
    ⚡️  TO JS {"mode":"native"}
    ⚡️  To Native ->  BluetoothLe addListener 43232731
    ⚡️  To Native ->  BluetoothLe connect 43232732
    ⚡️  BluetoothLe - Connecting to peripheral <CBPeripheral: 0x283059ba0, identifier = A8DF3B4C-A830-8D3D-C2B1-381972D8396B, name = LanQianTech, mtu = 0, state = disconnected>
    ⚡️  BluetoothLe - Reject connect Connection timeout
    ERROR MESSAGE:  {"errorMessage":"Connection timeout","message":"Connection timeout"}
    ⚡️  [error] - {"errorMessage":"Connection timeout","message":"Connection timeout"}
    ⚡️  [error] - connectToDevice {"errorMessage":"Connection timeout"}
  * */
  const connectBle = async (device: Device) => {
    return new Promise(async (resolve, reject) => {
      try {
        setConnectedDevice(device);
        updateConnectedDevicePairingStatus(true);
        if (isNative) {
          await BleClient.connect(device.deviceId, (deviceId) =>
            onDisconnect(deviceId)
          );
        }
        updateConnectedDevicePairingStatus(false);
        updateConnectedDevicePairedStatus(true);
        
        // 🔧 修复：连接成功后检查是否需要重新启动数据发送
        const needRestart = safeSessionStorage.getItem('needRestartDataSending');
        const lastDisconnectedDevice = safeSessionStorage.getItem('lastDisconnectedDevice');
        
        if (needRestart === 'true' && (lastDisconnectedDevice === device.deviceId || !lastDisconnectedDevice)) {
          console.log("🔧 检测到设备重新连接，准备自动启动数据发送");
          
          // 🔧 修复：立即清除标志，避免重复执行
          safeSessionStorage.removeItem('needRestartDataSending');
          safeSessionStorage.removeItem('lastDisconnectedDevice');
          
          // 🔧 修复：添加防止快速连接/断开的保护机制
          const connectionId = `${device.deviceId}_${Date.now()}`;
          (window as any).__currentConnectionId = connectionId;
          
          // 🔧 修复：使用Promise确保异步操作正确处理
          const startDataSendingWithDelay = async () => {
            // 延迟确保连接稳定
            await new Promise(resolve => setTimeout(resolve, 1500)); // 增加到1.5秒确保更稳定
            
            // 🔧 修复：检查连接是否仍然有效（防止快速连接/断开）
            if ((window as any).__currentConnectionId !== connectionId) {
              console.warn("⚠️ 连接已被新的连接替换，取消自动启动");
              return;
            }
            
            try {
              console.log("🔧 开始自动启动数据发送...");
              
              // 🔧 修复：再次检查设备连接状态，确保仍然连接
              if (!connectedDevice.value?.isPaired || connectedDevice.value?.deviceId !== device.deviceId) {
                console.warn("⚠️ 设备连接状态已变化，取消自动启动");
                return;
              }
              
              // 尝试获取可用的数据发送方法
              const { useMessage } = await import("@/hooks/useMessage");
              const { useNativeBluetoothMessage } = await import("@/hooks/useNativeBluetoothMessage");
              const { useSmartBluetoothMessage } = await import("@/hooks/useSmartBluetoothMessage");
              
              let startSuccess = false;
              
              // 优先使用智能蓝牙方案
              try {
                const smartHook = useSmartBluetoothMessage();
                if (smartHook.startSmartBluetoothSending) {
                  console.log("🔧 尝试使用智能蓝牙方案重新启动数据发送");
                  await smartHook.startSmartBluetoothSending();
                  startSuccess = true;
                  console.log("✅ 智能蓝牙方案启动成功");
                }
              } catch (smartError) {
                console.warn("⚠️ 智能蓝牙方案启动失败:", smartError);
              }
              
              // 备用方案：使用原生蓝牙
              if (!startSuccess) {
                try {
                  const nativeHook = useNativeBluetoothMessage();
                  if (nativeHook.startNativeBluetoothSending) {
                    console.log("🔧 尝试使用原生蓝牙方案重新启动数据发送");
                    await nativeHook.startNativeBluetoothSending();
                    startSuccess = true;
                    console.log("✅ 原生蓝牙方案启动成功");
                  }
                } catch (nativeError) {
                  console.warn("⚠️ 原生蓝牙方案启动失败:", nativeError);
                }
              }
              
              // 最后备用方案：使用传统方案
              if (!startSuccess) {
                try {
                  const messageHook = useMessage();
                  console.log("🔧 尝试使用传统方案重新启动数据发送");
                  await messageHook.sendMessage();
                  startSuccess = true;
                  console.log("✅ 传统方案启动成功");
                } catch (messageError) {
                  console.warn("⚠️ 传统方案启动失败:", messageError);
                }
              }
              
              if (startSuccess) {
                console.log("✅ 数据发送已自动重新启动");
                // 可选：显示成功提示
                try {
                  const { useToast } = await import("@/hooks/useToast");
                  const { presentToast } = useToast();
                  await presentToast("设备已重连，数据发送已自动启动");
                } catch (toastError) {
                  console.warn("⚠️ 显示成功提示失败:", toastError);
                }
              } else {
                throw new Error("所有蓝牙方案启动失败");
              }
              
            } catch (error) {
              console.error("❌ 自动重新启动数据发送失败:", error);
              // 显示错误提示让用户手动启动
              try {
                const { useToast } = await import("@/hooks/useToast");
                const { presentToast } = useToast();
                await presentToast("设备已连接，请手动启动数据发送");
              } catch (toastError) {
                console.error("❌ 显示错误提示失败:", toastError);
              }
            }
          };
          
          // 🔧 修复：不等待异步操作完成，避免阻塞连接流程
          startDataSendingWithDelay().catch(error => {
            console.error("❌ 延迟启动数据发送失败:", error);
          });
        }
        
        resolve("");
      } catch (error) {
        updateConnectedDevicePairedStatus(false);
        reject();
      }
    });
  };
  // ⚡️  To Native ->  BluetoothLe addListener 86768910
  // ⚡️  To Native ->  BluetoothLe connect 86768911
  // ⚡️  BluetoothLe - Connecting to peripheral <CBPeripheral: 0x280b3b4d0, identifier = A8DF3B4C-A830-8D3D-C2B1-381972D8396B, name = LanQianTech, mtu = 0, state = disconnected>
  // ⚡️  BluetoothLe - Connected to device <CBPeripheral: 0x280b3b4d0, identifier = A8DF3B4C-A830-8D3D-C2B1-381972D8396B, name = LanQianTech, mtu = 23, state = connected>
  // ⚡️  BluetoothLe - Resolve connect|A8DF3B4C-A830-8D3D-C2B1-381972D8396B Successfully connected.
  // ⚡️  BluetoothLe - Connected to peripheral. Waiting for service discovery.
  // ⚡️  BluetoothLe - didDiscoverServices
  // ⚡️  BluetoothLe - didDiscoverCharacteristicsFor 1 1
  // ⚡️  BluetoothLe - Resolve connect Connection successful.

  const disConnectBle = async (device: Device) => {
    return new Promise(async (resolve, reject) => {
      try {
        // 🔧 修复：手动断开连接时也要设置重连标记
        console.log("🔧 手动断开连接，设置重连标记");
        safeSessionStorage.setItem('needRestartDataSending', 'true');
        safeSessionStorage.setItem('lastDisconnectedDevice', device.deviceId);
        
        if (isNative) {
          await BleClient.initialize();
          await BleClient.disconnect(device.deviceId);
        }
        updateConnectedDevicePairedStatus(false); // 断开连接后清空连接状态
        resolve("");
      } catch (error) {
        updateConnectedDevicePairedStatus(false);
        reject();
      }
    });
  };
  const onDisconnect = async (deviceId: string) => {
    emit("onBleDisconnect");
    console.log(`device ${deviceId} disconnected`);
    
    // 🔧 修复：断开连接后标记需要重新启动数据发送
    console.log("🔧 标记设备断开连接，等待重连后自动启动数据发送");
    
    // 设置一个标志，当设备重新连接时自动启动数据发送
    safeSessionStorage.setItem('needRestartDataSending', 'true');
    safeSessionStorage.setItem('lastDisconnectedDevice', deviceId);
  };
  const write = async (
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    value: DataView
  ) => {
    if (!isNative) return;
    await BleClient.write(deviceId, serviceUUID, characteristicUUID, value);
  };

  const startNotification = async (
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string,
    callback: (value: DataView) => void
  ) => {
    if (!isNative) return;
    await BleClient.startNotifications(
      deviceId,
      serviceUUID,
      characteristicUUID,
      (value) => {
        console.log("current notification", dataViewToHexString(value));
        callback(value);
      }
    );
  };

  const stopNotification = async (
    deviceId: string,
    serviceUUID: string,
    characteristicUUID: string
  ) => {
    if (!isNative) return;
    await BleClient.stopNotifications(
      deviceId,
      serviceUUID,
      characteristicUUID
    );
  };

  const testBleDevice = () => {
    const devices = [
      {
        rssi: -53,
        txPower: 0,
        device: {
          deviceId: "A8DF3B4C-A830-8D3D-C2B1-381972D8396B",
          name: "LanQianTech",
        },
        localName: "LanQianTech",
        uuids: ["0000fff0-0000-1000-8000-00805f9b34fb"],
      },
      {
        rssi: -51,
        device: {
          deviceId: "51FD931A-0A66-D005-99B3-C29AE796B21F",
          name: "董思盼的Apple Watch",
        },
        txPower: 8,
        uuids: [],
      },
      {
        uuids: [],
        device: {
          deviceId: "A6542A21-2997-8DB4-D660-EA1011D9442F",
          name: "思盼的AirPods Pro",
        },
        rssi: -56,
        txPower: 127,
      },
      {
        uuids: [],
        device: {
          deviceId: "509D3B9C-FE9A-BF1F-2519-6250EFCE9E07",
          name: "董思盼的MacBook Pro",
        },
        rssi: -40,
        txPower: 127,
      },
      {
        uuids: [],
        device: { deviceId: "8EBA9D81-BEC0-8C12-FE8A-EE11D5F8CE5F" },
        rssi: -73,
        txPower: 127,
      },
      {
        rssi: -79,
        uuids: [],
        device: { deviceId: "DE8A09EE-DDE5-0A3C-5E5C-AFA35877A8E2" },
        txPower: 8,
      },
    ];
    devices.forEach((item) => {
      deviceDetected(item);
    });
  };

  const deviceDetected = (result: ScanResult) => {
    const { device } = result;
    if (!device.deviceId || !device.name) return; // 过滤掉没有deviceId、没有名字的设备
    const availableDevices = {
      ...device,
      isPaired: false,
      isPairing: false,
    };
    setAvailableDevice(availableDevices);
  };

  return {
    initialBle,
    scan,
    scanning,
    connectBle,
    disConnectBle,
    write,
    startNotification,
    stopNotification,
    onDisconnect,
  };
}

/*
⚡️  To Native ->  BluetoothLe write 36263596
⚡️  [log] - [31mstart write 0f 04 f5 58 2e b2 38 ca 84 14 65 32 0e[39m
⚡️  BluetoothLe - Resolve write|0000FFF0-0000-1000-8000-00805F9B34FB|0000FFF1-0000-1000-8000-00805F9B34FB Successfully written value.
⚡️  TO JS undefined
⚡️  To Native ->  BluetoothLe removeListener 36263597
⚡️  To Native ->  BluetoothLe addListener 36263598
⚡️  To Native ->  BluetoothLe startNotifications 36263599
⚡️  BluetoothLe - Set notifications true
⚡️  TO JS {"value":"41 90 80 1b 0a 24 9e 02 00 00 80 39 "}
⚡️  [log] - current notification 41 90 80 1b 0a 24 9e 02 00 00 80 39
⚡️  [log] - [32monNotification 65,144,128,27,10,36,158,2,0,0,128,57[39m
⚡️  [log] - timeSpan 6922
*/
