import { ref, onUnmounted } from 'vue';
import { CapacitorKtService } from 'capacitor-kt-service';
import { bluetoothServiceHelper } from '@/utils/bluetoothServiceHelper';
import { useSetting } from "@/hooks/useSetting";
import { useBluetoothLe } from "@/hooks/useBluetooth-le";
import { useMessage } from "@/hooks/useMessage";
import { CharacteristicUUID, ServiceUUID } from "@/const/ble.const";
import { useDashboardStore } from "@/store/useDashboardStore";
import { useSettingStore } from "@/store/useSettingStore";
import { useBleStore } from "@/store/useBleStore";
import { storeToRefs } from "pinia";
import { useToast } from "@/hooks/useToast";
import { useErrorStore } from "@/store/useErrorStore";
import { isPlatform } from "@ionic/vue";
import { numberToUUID, dataViewToNumbers } from "@capacitor-community/bluetooth-le";
import { useNavigation } from "@/hooks/useNavigation";
import { globalDiagnostics } from '@/utils/runtimeDiagnostics';

export function useNativeBluetoothMessage() {
  const settingStore = useSettingStore();
  const dashboardStore = useDashboardStore();
  const { connectedDevice } = storeToRefs(useBleStore());
  const { startNotification, stopNotification, disConnectBle } = useBluetoothLe();
  const { presentToast } = useToast();
  const { setErrorCode } = useErrorStore();
  const { updateSendDataCache } = useMessage();
  
  // 状态管理
  const isNativeSending = ref(false);
  
  // 🎯 先获取useSetting的引用，稍后传入回调
  let writeDataRef: any;
  let updateSettingRef: any;
  
  // 🔧 获取导航数据引用
  const navigation = useNavigation();
  
  // 🔧 添加导航数据更新事件监听器
  const handleNavigationDataUpdate = async (event: CustomEvent) => {
    try {
      if (isNativeSending.value) {
        console.log("🔄 收到导航数据更新事件:", event.detail);
        await updateNativeBluetoothData();
      }
    } catch (error: any) {
      console.error("❌ 处理导航数据更新事件失败:", error);
    }
  };
  
  // 🔧 添加设置数据更新事件监听器
  const handleSettingDataUpdate = async (event: CustomEvent) => {
    try {
      console.log("🔄 收到设置数据更新事件:", event.detail);
      
      // 检查是否为强制更新
      const forceUpdate = event.detail?.forceUpdate === true;
      
      if (isNativeSending.value || forceUpdate) {
        console.log(`🔄 ${forceUpdate ? '强制更新' : 'Native蓝牙发送中'}，立即更新蓝牙数据`);
        console.log("🔧 Native蓝牙发送方案：立即更新蓝牙数据");
        await updateNativeBluetoothData();
        
        // 如果是强制更新，额外记录日志
        if (forceUpdate) {
          console.log("🔧 Native方案强制更新完成，数据已立即生效");
        }
      } else {
        console.log("⚠️ Native蓝牙未发送且非强制更新，跳过数据更新");
      }
    } catch (error: any) {
      console.error("❌ 处理设置数据更新事件失败:", error);
    }
  };
  
  // 添加事件监听器
  window.addEventListener('navigationDataUpdated', handleNavigationDataUpdate as unknown as EventListener);
  window.addEventListener('settingDataUpdated', handleSettingDataUpdate as unknown as EventListener);
  
  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('navigationDataUpdated', handleNavigationDataUpdate as unknown as EventListener);
    window.removeEventListener('settingDataUpdated', handleSettingDataUpdate as unknown as EventListener);
  });
  
  /**
   * 更新Native端蓝牙数据
   * 正确的数据流：获取当前蓝牙发送数据 → 更新协议部分(0-11) → 重新计算校验和 → 发送到Native端
   */
  const updateNativeBluetoothData = async () => {
    debugger
    try {
      console.log("🔄 开始更新Native端蓝牙数据（基于当前发送数据）");
      
      if (!writeDataRef || !writeDataRef.value || writeDataRef.value.length < 18) {
        console.error("❌ writeDataRef未初始化或长度不足，无法更新Native端数据");
        return;
      }

      // 🔧 步骤1：获取当前Native端正在发送的蓝牙数据
      console.log("📊 步骤1: 获取当前Native端蓝牙发送数据...");
      let currentBluetoothData: number[] | null = null;
      
      try {
        const currentDataResult = await bluetoothServiceHelper.getCurrentBluetoothSendData();
        if (currentDataResult.success && currentDataResult.data && currentDataResult.data.length >= 18) {
          currentBluetoothData = [...currentDataResult.data];
          console.log("✅ 成功获取当前蓝牙发送数据:", currentBluetoothData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
        } else {
          console.warn("⚠️ 无法获取当前蓝牙发送数据，使用Web端数据作为基础:", currentDataResult.error || '未知错误');
        }
      } catch (error) {
        console.warn("⚠️ 获取当前蓝牙发送数据异常:", error);
      }
      
      // 🔧 步骤2：如果无法获取当前数据，使用Web端数据作为基础（仅首次）
      if (!currentBluetoothData) {
        console.log("📊 使用Web端数据作为基础数据");
        currentBluetoothData = [...writeDataRef.value];
      }
      
      // 🔧 步骤3：更新协议部分（字节0-11）- 与控制器通讯的协议
      console.log("📊 步骤2: 更新协议部分（字节0-11）...");
      
      // 保存原有的导航数据部分（字节12-15）和其他部分
      const originalNavigationData = currentBluetoothData.slice(12, 16);
      const originalMirrorBit = currentBluetoothData[12] & 0x80; // 保存镜像位
      
      console.log("📊 原有导航数据:", originalNavigationData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
      console.log("📊 原有镜像位:", originalMirrorBit ? '开启' : '关闭');
      
      // 更新协议部分（字节0-11）
      for (let i = 0; i <= 11; i++) {
        currentBluetoothData[i] = writeDataRef.value[i];
      }
      
      // 恢复导航数据部分
      for (let i = 12; i <= 15; i++) {
        currentBluetoothData[i] = originalNavigationData[i - 12];
      }
      
      // 恢复镜像位（字节12的第7位）
      if (originalMirrorBit) {
        currentBluetoothData[12] = currentBluetoothData[12] | 0x80;
      } else {
        currentBluetoothData[12] = currentBluetoothData[12] & 0x7F;
      }
      
      // 保持结束位（字节17）
      currentBluetoothData[17] = 0x0E;
      
      console.log("📊 更新协议部分后的数据:", currentBluetoothData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
      
      // 🔧 步骤4：重新计算校验和（字节16）
      console.log("📊 步骤3: 重新计算校验和...");
      let checksum = 0;
      
      // 校验和计算：对字节1-15进行异或运算（跳过字节0, 5, 16, 17）
      for (let i = 1; i <= 15; i++) {
        if (i !== 5) { // 跳过字节5
          checksum ^= currentBluetoothData[i];
        }
      }
      
      currentBluetoothData[16] = checksum & 0xFF;
      
      console.log("📊 计算的校验和:", `0x${checksum.toString(16).padStart(2, '0').toUpperCase()}`);
      
      // 🔧 步骤5：显示最终合并的数据详情
      console.log("📊 步骤4: 最终数据详情...");
      console.table({
        "帧头(0)": `0x${currentBluetoothData[0].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节1": `0x${currentBluetoothData[1].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节2": `0x${currentBluetoothData[2].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节3": `0x${currentBluetoothData[3].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节4": `0x${currentBluetoothData[4].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节5": `0x${currentBluetoothData[5].toString(16).padStart(2, '0').toUpperCase()}`,
        "字节6-11": currentBluetoothData.slice(6, 12).map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '),
        "导航数据(12-15)": currentBluetoothData.slice(12, 16).map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '),
        "校验和(16)": `0x${currentBluetoothData[16].toString(16).padStart(2, '0').toUpperCase()}`,
        "截止位(17)": `0x${currentBluetoothData[17].toString(16).padStart(2, '0').toUpperCase()}`
      });
      
      // 🔧 完整数据的十六进制表示
      const fullHexString = currentBluetoothData.map(byte => `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
      console.log("📝 完整数据十六进制:", fullHexString);
      
      // 🔍 记录合并后的数据到诊断日志
      if (globalDiagnostics.isMonitoring.value) {
        globalDiagnostics.logDiagnostic('useNativeBluetoothMessage', 'updateNativeBluetoothData', currentBluetoothData, '更新协议部分+重新计算校验和后');
      }
      
      // 🔧 步骤6：发送更新后的数据到Native端
      console.log("📊 步骤5: 发送更新后的数据到Native端...");
      try {
        await CapacitorKtService.updateBluetoothSendData({ data: currentBluetoothData });
        console.log("✅ 原生蓝牙发送数据已更新（基于当前发送数据+更新协议部分）");
        
        // 🔧 验证更新是否成功 - 增加重试机制
        let verificationSuccess = false;
        let retryCount = 0;
        const maxRetries = 3;
        
        while (!verificationSuccess && retryCount < maxRetries) {
          try {
            // 添加小延迟确保数据已更新
            await new Promise(resolve => setTimeout(resolve, 50));
            
            const verificationResult = await bluetoothServiceHelper.getCurrentBluetoothSendData();
            if (verificationResult.success && verificationResult.data) {
              const verifyHex = verificationResult.data.map((byte: number) => `0x${byte.toString(16).padStart(2, '0').toUpperCase()}`).join(' ');
              console.log(`📝 验证Native端数据(尝试${retryCount + 1}):`, verifyHex);
              
              // 比较数据是否一致
              const isDataMatch = JSON.stringify(currentBluetoothData) === JSON.stringify(verificationResult.data);
              if (isDataMatch) {
                console.log("✅ 数据同步验证成功");
                verificationSuccess = true;
              } else {
                console.warn(`⚠️ 数据同步验证失败(尝试${retryCount + 1})，存在差异`);
                console.warn("期望数据:", currentBluetoothData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
                console.warn("实际数据:", verificationResult.data.map((b: number) => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
                
                // 如果是最后一次尝试，强制重新发送
                if (retryCount === maxRetries - 1) {
                  console.warn("🔄 最后一次尝试，强制重新发送数据");
                  await CapacitorKtService.updateBluetoothSendData({ data: currentBluetoothData });
                }
              }
            } else {
              console.warn(`⚠️ 无法获取Native端数据进行验证(尝试${retryCount + 1}):`, verificationResult.error);
            }
          } catch (verifyError) {
            console.warn(`⚠️ 数据同步验证异常(尝试${retryCount + 1}):`, verifyError);
          }
          
          retryCount++;
        }
        
        if (!verificationSuccess) {
          console.error("❌ 数据同步验证最终失败，可能存在数据传输问题");
        } else {
          console.log("✅ Native端蓝牙数据更新完成，更新发送缓存");
          // 数据更新成功后，更新定时器的发送数据缓存
          try {
            await updateSendDataCache();
          } catch (cacheError) {
            console.warn("⚠️ 更新发送数据缓存失败:", cacheError);
          }
        }
        
      } catch (updateError) {
        console.error("❌ 更新Native端数据失败:", updateError);
        throw updateError;
      }
      
    } catch (error: any) {
      console.error("❌ 更新原生蓝牙数据失败:", error);
      console.error("❌ 错误详情:", {
        message: error.message,
        stack: error.stack,
        writeDataRef: writeDataRef?.value,
        navigationData: navigation.writeData.value
      });
    }
  };
  
  // 🎯 现在安全地调用useSetting，传入updateNativeBluetoothData作为回调
  const settingResult = useSetting(updateNativeBluetoothData);
  writeDataRef = settingResult.writeData;
  updateSettingRef = settingResult.updateSetting;
  
  // 为了保持API兼容性，解构出来
  const { writeData, updateSetting } = settingResult;
  const sendingStats = ref({
    totalSent: 0,
    successCount: 0,
    errorCount: 0,
    averageInterval: 0,
    lastError: null as string | null,
    isConnected: false
  });
  
  let notificationListener: any = null;
  let stateListener: any = null;
  let dataListener: any = null;
  let errorListener: any = null;
  
  // 启动原生蓝牙发送
  const startNativeBluetoothSending = async () => {
    try {
      if (!connectedDevice.value.deviceId || !connectedDevice.value.isPaired) {
        await presentToast("请先连接蓝牙设备");
        return false;
      }
      
      // 更新设置数据
      updateSetting();
      
      console.log("🚀 启动原生蓝牙发送服务");
      console.log("设备ID:", connectedDevice.value.deviceId);
      console.log("Vue端writeData:", writeData.value);
      console.log("writeData长度:", writeData.value?.length);
      
      // 验证writeData是否有效
      if (!writeData.value || writeData.value.length === 0) {
        console.error("❌ writeData为空或无效");
        await presentToast("蓝牙数据无效，无法启动发送");
        return false;
      }
      
              // 使用助手类安全启动前台服务
        await bluetoothServiceHelper.startBluetoothForegroundService();
      
      // 设置监听器
      await setupListeners();
      
      // 获取当前Native端的数据（如果有的话）
      let initialData = writeData.value;
      try {
        const currentDataResult = await bluetoothServiceHelper.getCurrentBluetoothSendData();
        if (currentDataResult.success && currentDataResult.data) {
          // �� 修复数据合并逻辑：确保数据长度一致，包含导航数据
          const nativeData = currentDataResult.data;
          console.log("📊 Native端数据:", nativeData);
          console.log("📊 Native端数据长度:", nativeData.length);
          console.log("📊 Vue端数据长度:", writeData.value.length);
          console.log("📊 导航数据:", navigation.writeData.value);
          
          // 合并设置数据和导航数据
          initialData = [...writeData.value];
          
          // 将导航数据（字节12-15）合并到设置数据中
          initialData[12] = navigation.writeData.value[12]; // 方向 + 距离单位
          initialData[13] = navigation.writeData.value[13]; // 单次距离低位
          initialData[14] = navigation.writeData.value[14]; // 总距离高位 + 单次距离高位
          initialData[15] = navigation.writeData.value[15]; // 总距离低位
          
          // 重新计算校验和（字节16）
          let checksum = 0;
          for (let i = 1; i <= 15; i++) {
            checksum ^= initialData[i];
          }
          initialData[16] = checksum & 0xFF;
          
          console.log("📊 合并设置+导航数据完成:", {
            "设置数据(0-11)": initialData.slice(0, 12),
            "导航数据(12-15)": initialData.slice(12, 16),
            "校验和(16)": initialData[16],
            "截止位(17)": initialData[17]
          });
        }
      } catch (error) {
        console.warn("⚠️ 启动时无法获取当前Native端数据，使用Vue端数据:", error);
        
        // 即使无法获取Native端数据，也要确保包含导航数据
        initialData = [...writeData.value];
        initialData[12] = navigation.writeData.value[12];
        initialData[13] = navigation.writeData.value[13];
        initialData[14] = navigation.writeData.value[14];
        initialData[15] = navigation.writeData.value[15];
        
        // 重新计算校验和
        let checksum = 0;
        for (let i = 1; i <= 15; i++) {
          checksum ^= initialData[i];
        }
        initialData[16] = checksum & 0xFF;
      }
      
      // 数据验证和清理：确保没有null或undefined值
      initialData = initialData.map((value, index) => {
        if (value === null || value === undefined || isNaN(value)) {
          console.warn(`⚠️ 数据第${index}位为无效值(${value})，使用默认值0`);
          return 0;
        }
        // 确保值在0-255范围内
        const validValue = Math.max(0, Math.min(255, Math.floor(Number(value))));
        if (validValue !== value) {
          console.warn(`⚠️ 数据第${index}位值(${value})超出范围，调整为${validValue}`);
        }
        return validValue;
      });
      
      console.log("发送数据:", initialData);
      console.log("数据验证完成，长度:", initialData.length);
      
      // 启动原生蓝牙发送
      await CapacitorKtService.startNativeBluetoothSending({
        deviceId: connectedDevice.value.deviceId,
        serviceUUID: numberToUUID(ServiceUUID),
        characteristicUUID: numberToUUID(CharacteristicUUID),
        sendInterval: 106, // 严格的106ms间隔
        data: initialData
      });
      
      // 启动通知监听（用于接收数据）
      await startNotification(
        connectedDevice.value.deviceId,
        numberToUUID(ServiceUUID),
        numberToUUID(CharacteristicUUID),
        onNotification
      );
      
      isNativeSending.value = true;
      
      await presentToast("原生蓝牙发送已启动");
      console.log("✅ 原生蓝牙发送服务启动成功");
      
      return true;
      
    } catch (error: any) {
      console.error("❌ 启动原生蓝牙发送失败:", error);
      await presentToast(`启动失败: ${error.message || error}`);
      return false;
    }
  };
  
  // 停止原生蓝牙发送
  const stopNativeBluetoothSending = async () => {
    try {
      console.log("🛑 停止原生蓝牙发送服务");
      
      // 停止原生蓝牙发送
      await CapacitorKtService.stopNativeBluetoothSending();
      
      // 停止通知监听
      if (connectedDevice.value.deviceId && connectedDevice.value.isPaired) {
        await stopNotification(
          connectedDevice.value.deviceId,
          numberToUUID(ServiceUUID),
          numberToUUID(CharacteristicUUID)
        );
      }
      
      // 清理监听器
      await cleanupListeners();
      
      // 停止前台服务
      await CapacitorKtService.stopBluetoothForegroundService();
      
      isNativeSending.value = false;
      
      await presentToast("原生蓝牙发送已停止");
      console.log("✅ 原生蓝牙发送服务停止成功");
      
    } catch (error: any) {
      console.error("❌ 停止原生蓝牙发送失败:", error);
      await presentToast(`停止失败: ${error.message || error}`);
    }
  };
  
  // 原updateNativeBluetoothData函数已移至上方，避免循环依赖
  
  // 重新连接蓝牙设备
  const reconnectNativeBluetoothDevice = async () => {
    try {
      console.log("🔄 重新连接原生蓝牙设备");
      
      await CapacitorKtService.reconnectBluetoothDevice();
      await presentToast("正在重新连接蓝牙设备...");
      
    } catch (error: any) {
      console.error("❌ 重新连接失败:", error);
      await presentToast(`重新连接失败: ${error.message || error}`);
    }
  };
  
  // 获取发送统计信息
  const updateSendingStats = async () => {
    try {
              const stats = await bluetoothServiceHelper.getBluetoothSendingStats();
      sendingStats.value = {
        totalSent: stats.totalSent || 0,
        successCount: stats.successCount || 0,
        errorCount: stats.errorCount || 0,
        averageInterval: stats.averageInterval || 0,
        lastError: stats.lastError || null,
        isConnected: stats.isConnected || false
      };
    } catch (error) {
      console.error("获取发送统计失败:", error);
    }
  };
  
  // 设置监听器
  const setupListeners = async () => {
    try {
      // 蓝牙发送状态变化监听
      stateListener = await CapacitorKtService.addListener('bluetoothSendingStateChanged', (data: any) => {
        console.log("📊 蓝牙发送状态变化:", data);
        
        // 更新统计信息
        updateSendingStats();
        
        // 更新通知内容
        CapacitorKtService.updateBluetoothNotification({
          title: "智能单车原生蓝牙发送",
          message: `已发送: ${data.sendCount}, 错误: ${data.errorCount}`
        });
      });
      
      // 蓝牙数据接收监听
      dataListener = await CapacitorKtService.addListener('bluetoothDataReceived', (data: any) => {
        console.log("�� 原生蓝牙数据接收:", data);
        
        // 处理接收到的数据（使用现有的处理逻辑）
        if (data.data && Array.isArray(data.data)) {
          onNotification(new DataView(new Uint8Array(data.data).buffer));
        }
      });
      
      // 蓝牙错误监听
      errorListener = await CapacitorKtService.addListener('bluetoothError', (data: any) => {
        console.error("❌ 原生蓝牙错误:", data);
        presentToast(`蓝牙错误: ${data.error}`);
      });
      
      console.log("✅ 原生蓝牙监听器设置完成");
      
    } catch (error) {
      console.error("❌ 设置监听器失败:", error);
    }
  };
  
  // 清理监听器
  const cleanupListeners = async () => {
    try {
      if (stateListener) {
        await stateListener.remove();
        stateListener = null;
      }
      
      if (dataListener) {
        await dataListener.remove();
        dataListener = null;
      }
      
      if (errorListener) {
        await errorListener.remove();
        errorListener = null;
      }
      
      console.log("✅ 原生蓝牙监听器清理完成");
      
    } catch (error) {
      console.error("❌ 清理监听器失败:", error);
    }
  };
  
  // 处理接收到的蓝牙数据（复用现有逻辑）
  const onNotification = (value: DataView) => {
    const message = dataViewToNumbers(value);
    const isValid = checkIsValidNotification(message);
    if (!isValid) return;
    
    console.log("📡 原生蓝牙接收数据:", message);
    
    // 复用现有的数据处理逻辑
    getBattery(message);
    getSpeed(message);
    getSingleDistance();
    getAssistance(message);
    checkError(message);
    computedMessageFirst(message);
    computedMessageSecond(message);
    computedMessageSeventh(message);
  };
  
  // 数据验证和处理方法（复用现有逻辑）
  const checkIsValidNotification = (value: number[]) => {
    const firstValue = value[0];
    if (firstValue !== 0x41) return false;
    const validate =
      value[1] ^
      value[2] ^
      value[3] ^
      value[4] ^
      value[5] ^
      value[7] ^
      value[8] ^
      value[9] ^
      value[10] ^
      value[11];
    return validate === value[6];
  };
  
  // 以下方法复用现有的数据处理逻辑
  const {
    setElectricQuantity,
    setSingleTime,
    setSpeed,
    setSingleMileage,
    setTotalMileage,
    setAssistance,
    setRegenative,
    setUndervoltage,
    setReverse,
    setTurnRight,
    setTurnLeft,
    setThrottle,
    setCruise,
    setBrake,
  } = useDashboardStore();
  
  const { speed, singleMileage, totalMileage } = storeToRefs(dashboardStore);
  const { candidateParam } = storeToRefs(useSettingStore());
  
  let singleTimeInterval: any;
  let singleTimeSecond = 0;
  
  const getBattery = (message: number[]) => {
    const electricQuantity = (message[1] & 0x1f) >> 2;
    setElectricQuantity(electricQuantity);
  };
  
  const getSpeed = async (message: number[]) => {
    const timeFirst = message[3] << 8;
    const timeLast = message[4];
    const timeSpan = timeFirst + timeLast;
    if (isNaN(timeSpan) || timeSpan > 21008 || timeSpan === 0) {
      return;
    }
    
    console.log("timeSpan", timeSpan);
    if (timeSpan === 6922) {
      setSpeed(0);
      clearInterval(singleTimeInterval);
      singleTimeInterval = 0;
      return;
    }
    
    const { dimension } = storeToRefs(useSettingStore());
    const { getDisplayType } = storeToRefs(settingStore);
    
    // 动态导入DimensionList
    let DimensionList: any[] = [];
    try {
      const bikeConst = await import("@/const/bike.const");
      DimensionList = bikeConst.DimensionList || [];
    } catch (error) {
      console.error("导入DimensionList失败:", error);
      DimensionList = [{ value: 26, dimension: 26 }]; // 默认值
    }
    
    const realDimension =
      DimensionList.find((item) => item.value === dimension.value)?.dimension || 26;
    const round = (realDimension * 25.4 * Math.PI) / Math.pow(10, 6);
    const defaultSpeed = (round / timeSpan) * 1000 * 3600;
    const speed = Math.floor(defaultSpeed);
    
    console.log("speed", speed);
    const displaySpeed =
      getDisplayType.value === "kilometer"
        ? speed
        : Math.floor(speed * 0.6213712);
    setSpeed(displaySpeed);
    getSingleTime();
  };
  
  const getSingleTime = () => {
    if (singleTimeInterval) return;
    singleTimeInterval = setInterval(() => {
      singleTimeSecond++;
      const singleTime = formatSeconds(singleTimeSecond);
      setSingleTime(singleTime);
    }, 1000);
  };
  
  const formatSeconds = (time: number) => {
    const min = Math.floor(time % 3600);
    return (
      formatBit(Math.floor(time / 3600)) +
      ":" +
      formatBit(Math.floor(min / 60)) +
      ":" +
      formatBit(time % 60)
    );
  };
  
  const formatBit = (val: number) => {
    val = +val;
    return val > 9 ? val : "0" + val;
  };
  
  const getSingleDistance = () => {
    const timeDelay = 1 / 3600;
    const distance = speed.value * timeDelay;
    const storageMileage = singleMileage || 0;
    const currentMileage = storageMileage.value + distance;
    const storageTotal = totalMileage || 0;
    const total = storageTotal.value + distance;
    setSingleMileage(currentMileage);
    setTotalMileage(total);
  };
  
  const getAssistance = (message: number[]) => {
    const isAssistance = (message[7] >> 4) & 1;
    const candidate = Number(candidateParam.value);
    if (isAssistance) {
      const received = ((message[10] & 0x3f) << 8) + message[11];
      if (received !== 0) {
        const assistance = 60 / ((received * candidate) / 1000);
        setAssistance(Math.floor(assistance));
      } else {
        setAssistance(0);
      }
    } else {
      setAssistance(0);
    }
  };
  
  const checkError = (message: number[]) => {
    const errorCode = message[5];
    setErrorCode(errorCode);
  };
  
  const computedMessageFirst = (message: number[]) => {
    const command = message[1];
    const undervoltage = command & 1;
    const regenative = (command & 2) >> 1;
    setRegenative(regenative);
    setUndervoltage(undervoltage);
  };
  
  const computedMessageSecond = (message: number[]) => {
    const command = message[2];
    const reverse = (command & 0x40) >> 5;
    const turnRight = command & 1;
    const turnLeft = (command & 2) >> 1;
    setReverse(reverse);
    setTurnRight(turnRight);
    setTurnLeft(turnLeft);
  };
  
  const computedMessageSeventh = (message: number[]) => {
    const command = message[7];
    const throttle = command & 3;
    const cruise = (command & 8) >> 3;
    const brake = (command & 32) >> 5;
    setThrottle(throttle);
    setCruise(cruise);
    setBrake(brake);
  };
  
  // 退出应用
  const exitApp = async () => {
    await stopNativeBluetoothSending();
    await disConnectBle(connectedDevice.value);
  };
  
  // 检查原生发送状态
  const checkNativeSendingStatus = async () => {
    try {
      const status = await CapacitorKtService.isNativeBluetoothSending();
      return status;
    } catch (error: any) {
      console.error("❌ 检查原生蓝牙发送状态失败:", error);
      return false;
    }
  };

  // 🔧 新增：手动触发导航数据更新
  const updateNavigationData = async () => {
    try {
      if (isNativeSending.value) {
        console.log("🔄 手动触发导航数据更新");
        await updateNativeBluetoothData();
      }
    } catch (error: any) {
      console.error("❌ 手动更新导航数据失败:", error);
    }
  };
  
  // 组件卸载时清理
  onUnmounted(async () => {
    try {
      await cleanupListeners();
      if (isNativeSending.value) {
        await stopNativeBluetoothSending();
      }
    } catch (error) {
      console.error("清理原生蓝牙资源失败:", error);
    }
  });
  
  return {
    // 状态
    isNativeSending,
    sendingStats,
    
    // 主要功能
    startNativeBluetoothSending,
    stopNativeBluetoothSending,
    reconnectNativeBluetoothDevice,
    updateSendingStats,
    
    // 数据更新
    updateNativeBluetoothData,
    updateNavigationData, // 🔧 新增：手动更新导航数据
    
    // 状态检查
    checkNativeSendingStatus,
    
    // 清理
    exitApp,
    
    // 兼容性
    writeData,
    updateSetting
  };
}