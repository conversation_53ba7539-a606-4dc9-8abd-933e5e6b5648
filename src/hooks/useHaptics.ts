import { Haptics, ImpactStyle } from '@capacitor/haptics';

// 触觉 API 通过触控或振动为用户提供物理反馈。
const hapticsImpactMedium = async () => {
  await Haptics.impact({ style: ImpactStyle.Medium });
};

const hapticsImpactLight = async () => {
  await Haptics.impact({ style: ImpactStyle.Light });
  console.log('hapticsImpactLight');
};

const hapticsVibrate = async () => {
  await Haptics.vibrate();
};

const hapticsSelectionStart = async () => {
  await Haptics.selectionStart();
};

const hapticsSelectionChanged = async () => {
  await Haptics.selectionChanged();
};

const hapticsSelectionEnd = async () => {
  await Haptics.selectionEnd();
};

export function useHaptics() {
  return {
    hapticsImpactMedium,
    hapticsImpactLight,
    hapticsVibrate,
    hapticsSelectionStart,
    hapticsSelectionChanged,
    hapticsSelectionEnd,
  };
}