import { isPlatform } from '@ionic/vue';
import { computed, ref } from 'vue';
import { useMessage } from './useMessage';
import { useNativeBluetoothMessage } from './useNativeBluetoothMessage';

/**
 * 智能蓝牙消息Hook
 * 根据平台自动选择最佳的发送方案：
 * - Android: 使用原生蓝牙发送 (不受WebView限制)
 * - iOS/Web: 使用WebView方案
 */
export function useSmartBluetoothMessage() {
  // 检测当前平台
  const isAndroid = isPlatform('android');
  
  // 根据平台选择对应的Hook
  const webViewHook = useMessage();
  const nativeHook = useNativeBluetoothMessage();
  
  // 当前使用的方案
  const currentMethod = computed(() => isAndroid ? 'native' : 'webview');
  
  console.log(`🎯 智能蓝牙消息: 当前平台使用 ${currentMethod.value} 方案`);
  
  // 统一的服务状态管理（确保返回ref对象保持一致性）
  const unifiedIsServiceRunning = computed(() => {
    if (isAndroid) {
      // 原生Hook返回的是ref对象
      return nativeHook.isNativeSending?.value ?? false;
    } else {
      // WebView Hook返回的是ref对象
      return webViewHook.isServiceRunning?.value ?? false;
    }
  });
  
  // 统一的API接口，根据平台自动路由到对应实现
  return {
    // === 主要发送控制方法 ===
    sendMessage: isAndroid ? nativeHook.startNativeBluetoothSending : webViewHook.sendMessage,
    stopSendMessage: isAndroid ? nativeHook.stopNativeBluetoothSending : webViewHook.stopSendMessage,
    
    // 🔧 新增：统一的启动方法（别名）
    startSmartBluetoothSending: isAndroid ? nativeHook.startNativeBluetoothSending : webViewHook.sendMessage,
    
    // 🔧 新增：统一的数据更新方法
    updateSmartBluetoothData: async () => {
      console.log("🔧 智能蓝牙方案：更新数据");
      if (isAndroid) {
        // Android使用原生方案的数据更新
        if (nativeHook.updateNativeBluetoothData) {
          await nativeHook.updateNativeBluetoothData();
        }
      } else {
        // iOS/Web使用WebView方案的数据缓存更新
        if (webViewHook.updateSendDataCache) {
          await webViewHook.updateSendDataCache();
        }
      }
      console.log("✅ 智能蓝牙方案：数据更新完成");
    },
    
    // === 服务状态检查 ===
    isServiceRunning: unifiedIsServiceRunning,
    
    // === 数据处理方法 (主要来自WebView方案) ===
    getSpeed: webViewHook.getSpeed,
    getBattery: webViewHook.getBattery,
    checkError: webViewHook.checkError,
    getAssistance: webViewHook.getAssistance,
    getSingleDistance: webViewHook.getSingleDistance,
    setBLEName: webViewHook.setBLEName,
    
    // === 应用控制 ===
    exitApp: isAndroid ? nativeHook.exitApp : webViewHook.exitApp,
    
    // === WebView特有的高级控制方法 ===
    ...(isAndroid ? {} : {
      manualRestartTimer: webViewHook.manualRestartTimer,
      getTimerStatus: webViewHook.getTimerStatus,
      isTimerActive: webViewHook.isTimerActive,
      getLastSendTime: webViewHook.getLastSendTime,
      getLastSuccessTime: webViewHook.getLastSuccessTime,
      getFailureCount: webViewHook.getFailureCount,
      getRestartCount: webViewHook.getRestartCount,
      updateSendDataCache: webViewHook.updateSendDataCache, // 🔧 新增：暴露数据缓存更新方法
    }),
    
    // === 原生方案特有的方法 ===
    ...(isAndroid ? {
      isNativeSending: nativeHook.isNativeSending,
      sendingStats: nativeHook.sendingStats,
      updateSendingStats: nativeHook.updateSendingStats,
      updateNativeBluetoothData: nativeHook.updateNativeBluetoothData,
      reconnectNativeBluetoothDevice: nativeHook.reconnectNativeBluetoothDevice,
      checkNativeSendingStatus: nativeHook.checkNativeSendingStatus,
    } : {}),
    
    // === 平台信息 ===
    currentMethod,
    isAndroidNative: isAndroid,
    isWebViewMode: !isAndroid,
    
    // === 调试和监控方法 ===
    getPlatformInfo: () => ({
      platform: isPlatform('android') ? 'Android' : isPlatform('ios') ? 'iOS' : 'Web',
      method: currentMethod.value,
      isNativeSupported: isAndroid,
      capabilities: {
        backgroundSending: isAndroid, // 只有Android原生支持真正的后台发送
        preciseTimer: isAndroid,      // 原生方案有更精确的定时器
        systemIntegration: isAndroid, // 原生方案与系统集成更好
      }
    }),
    
    // === 方案切换提示 (仅用于调试) ===
    getSolutionRecommendation: () => {
      if (isAndroid) {
        return {
          current: '原生蓝牙发送方案',
          benefits: [
            '✅ 熄屏后持续发送 (解决5分钟限制)',
            '✅ 精确106ms间隔 (±5ms误差)',
            '✅ 不受WebView限制',
            '✅ 系统级后台保活',
            '✅ 自动重连机制'
          ],
          status: '推荐使用 - 最佳性能'
        };
      } else {
        return {
          current: 'WebView蓝牙发送方案',
          benefits: [
            '✅ 跨平台兼容性好',
            '✅ 开发调试方便',
            '✅ 现有功能完整'
          ],
          limitations: [
            '⚠️  熄屏5分钟后可能停止发送',
            '⚠️  受WebView系统限制影响',
            '⚠️  定时器精度相对较低'
          ],
          status: '当前平台最佳选择'
        };
      }
    }
  };
} 