import { WriteData } from '@/const/ble.const'
import {
  DirectionProtocolMap,
  Directions,
  ManeuverDirection,
  ManeuverType
} from '@/const/directions'
import { BannerInstruction } from '@/types/RouteProgress.type'
import { isPlatform } from '@ionic/core'
import { ref } from 'vue'

export function useNavigation() {
  const writeData = ref([...WriteData]) // 🔧 使用深拷贝避免引用问题
  // const isMirrorOpen = ref(false)
  const MIRROR_BIT = 1 << 7 // 镜像位掩码 (第7位) 128
  
  // 🔧 新增：同步设置数据的方法
  const syncWithSettingData = (settingWriteData: number[]) => {
    if (!settingWriteData || settingWriteData.length < 18) {
      console.warn('⚠️ syncWithSettingData: 无效的设置数据')
      return
    }
    
    // 保存当前的导航数据（字节12-15）
    const currentNavigationData = {
      byte12: writeData.value[12],
      byte13: writeData.value[13],
      byte14: writeData.value[14],
      byte15: writeData.value[15]
    }
    
    // 复制设置数据到writeData（前12字节的协议部分和设置相关的字节）
    for (let i = 0; i < 12; i++) {
      writeData.value[i] = settingWriteData[i]
    }
    
    // 恢复导航数据
    writeData.value[12] = currentNavigationData.byte12
    writeData.value[13] = currentNavigationData.byte13
    writeData.value[14] = currentNavigationData.byte14
    writeData.value[15] = currentNavigationData.byte15
    
    // 重新计算校验和
    validateSixteen()
    
    console.log('🔧 已同步设置数据到useNavigation')
  }
  const SINGLE_HIGH_SHIFT = 6 // 单次距离高位左移位数
  const TOTAL_RULE_SHIFT = 4 // 总距离单位左移位数
  const SINGLE_RULE_SHIFT = 4 // 单次距离单位左移位数

  const RoundingIncrement = 5 // 距离四舍五入到最接近的5的倍数

  // 🔧 新增：重置数据到初始状态
  const resetWriteData = () => {
    writeData.value = [...WriteData]
    console.log('🔧 writeData已重置到初始状态')
  }

  // 🔧 增强版：确保数据一致性的导航进度发送
  const sendNavigationProgress = (data: any) => {
    try {
      console.log('🚀 开始处理导航进度数据:', data)
      
      const bannerJsonString = data.bannerInstructions
      if (!bannerJsonString) {
        console.warn('⚠️ 缺少导航指令数据')
        return
      }
      
      const banner = JSON.parse(bannerJsonString)
      console.log('🔍 解析后的导航指令:', banner)
      
      // 获取当前导航的方向
      const direction = getDirectionByBannerInstruction(banner)
      console.log('🔍 计算出的方向:', direction, `(${getDirectionName(direction)})`)
      
      // 🔧 增强版距离处理：确保与Native端逻辑完全一致
      const rawStepDistance = data.stepDistanceRemaining
      const distance = processDistance(rawStepDistance)
      console.log('🔍 距离处理:', { 原始: rawStepDistance, 处理后: distance })
      
      // 距离终点的距离
      const totalDistance = data.distanceRemaining || 0
      console.log('🔍 总距离:', totalDistance)
      
      // 🔧 增强版距离规则计算
      const singleDistanceRule = mapDistanceToRule(distance)
      const totalDistanceRule = mapDistanceToRule(totalDistance)
      
      console.log('🔍 距离规则:', {
        单次距离规则: singleDistanceRule,
        总距离规则: totalDistanceRule
      })
      
      // 🔧 严格按照协议计算高位和低位
      const singleHigh = Math.floor(singleDistanceRule.effectiveNumber / 256)
      const singleLow = singleDistanceRule.effectiveNumber % 256
      const totalHigh = Math.floor(totalDistanceRule.effectiveNumber / 256)
      const totalLow = totalDistanceRule.effectiveNumber % 256
      
      console.log('🔍 高低位计算:', {
        单次高位: singleHigh,
        单次低位: singleLow,
        总距离高位: totalHigh,
        总距离低位: totalLow
      })
      
      // 🔧 按顺序更新数据，确保每一步都正确
      // 1. 先清除导航相关的字节（保留镜像位）
      const currentMirrorBit = writeData.value[12] & MIRROR_BIT
      writeData.value[12] = currentMirrorBit // 只保留镜像位，清除其他位
      writeData.value[13] = 0
      writeData.value[14] = 0
      writeData.value[15] = 0
      
      // 2. 计算并设置字节12（方向 + 单次距离规则 + 镜像位）
      compute12Data(singleDistanceRule, direction)
      
      // 3. 设置字节13（单次距离低位）
      writeData.value[13] = singleLow
      
      // 4. 计算并设置字节14（距离高位 + 规则）
      compute14Data(totalDistanceRule, singleHigh, totalHigh)
      
      // 5. 设置字节15（总距离低位）
      writeData.value[15] = totalLow
      
      // 6. 最后计算校验和
      validateSixteen()
      
      // 🔧 数据完整性验证
      const finalData = writeData.value
      console.log('🔍 最终生成的数据:', finalData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
      
      // 验证关键字节
      const finalDirection = finalData[12] & 0x0F
      const finalMirror = (finalData[12] & 0x80) !== 0
      const finalSingleRule = (finalData[12] >> 4) & 0x03
      
      console.log('🔍 最终数据验证:', {
        方向: `${finalDirection} (${getDirectionName(finalDirection)})`,
        镜像位: finalMirror,
        单次距离规则: finalSingleRule,
        单次距离低位: finalData[13],
        字节14: `0x${finalData[14].toString(16).toUpperCase()}`,
        总距离低位: finalData[15],
        校验和: `0x${finalData[16].toString(16).toUpperCase()}`
      })
      
      // 🔧 触发数据更新事件
      const event = new CustomEvent('navigationDataUpdated', {
        detail: {
          navigationData: writeData.value.slice(12, 16),
          fullData: [...writeData.value],
          metadata: {
            direction,
            distance,
            totalDistance,
            singleDistanceRule,
            totalDistanceRule
          }
        }
      });
      window.dispatchEvent(event);
      
      console.log('✅ 导航进度处理完成')
      
    } catch (error) {
      console.error('❌ 导航进度处理失败:', error)
      throw error
    }
  }

  // 🔧 增强版距离处理函数
  const processDistance = (rawDistance: number): number => {
    if (isNaN(rawDistance) || rawDistance < 0) {
      console.warn('⚠️ 无效的距离值:', rawDistance)
      return 0
    }
    
    // iOS平台使用原始值，其他平台四舍五入到5的倍数
    const processedDistance = isPlatform('ios') 
      ? rawDistance 
      : Math.round(rawDistance / RoundingIncrement) * RoundingIncrement
    
    console.log('🔧 距离处理详情:', {
      平台: isPlatform('ios') ? 'iOS' : '其他',
      原始距离: rawDistance,
      处理后距离: processedDistance,
      四舍五入增量: RoundingIncrement
    })
    
    return processedDistance
  }

  const getDirectionName = (direction: number) => {
    return Directions[direction as keyof typeof Directions] || `未知方向(${direction})`
  }

  // 🔧 增强版字节12计算
  const compute12Data = (
    singleDistanceRule: { rule: number },
    direction: number
  ) => {
    // 保留当前的镜像位
    const currentMirrorBit = writeData.value[12] & MIRROR_BIT
    
    // 计算新的字节12值：镜像位 + 单次距离规则 + 方向
    const newByte12 = currentMirrorBit | 
                     (singleDistanceRule.rule << SINGLE_RULE_SHIFT) | 
                     direction
    
    writeData.value[12] = newByte12
    
    console.log('🔧 字节12计算详情:', {
      镜像位: currentMirrorBit ? '开启(0x80)' : '关闭(0x00)',
      单次距离规则: `${singleDistanceRule.rule} << ${SINGLE_RULE_SHIFT} = 0x${(singleDistanceRule.rule << SINGLE_RULE_SHIFT).toString(16).toUpperCase()}`,
      方向: `${direction} (${getDirectionName(direction)})`,
      最终字节12: `0x${newByte12.toString(16).toUpperCase()}`
    })
  }

  // 🔧 增强版字节14计算
  const compute14Data = (
    totalDistanceRule: { rule: number },
    singleHigh: number,
    totalHigh: number
  ) => {
    // 字节14 = 单次距离高位[7,6] + 总距离单位[5,4] + 保留位[3,2] + 总距离高位[1,0]
    const newByte14 = (singleHigh << SINGLE_HIGH_SHIFT) | 
                     (totalDistanceRule.rule << TOTAL_RULE_SHIFT) | 
                     totalHigh
    
    writeData.value[14] = newByte14
    
    console.log('🔧 字节14计算详情:', {
      单次距离高位: `${singleHigh} << ${SINGLE_HIGH_SHIFT} = 0x${(singleHigh << SINGLE_HIGH_SHIFT).toString(16).toUpperCase()}`,
      总距离单位: `${totalDistanceRule.rule} << ${TOTAL_RULE_SHIFT} = 0x${(totalDistanceRule.rule << TOTAL_RULE_SHIFT).toString(16).toUpperCase()}`,
      总距离高位: `${totalHigh} = 0x${totalHigh.toString(16).toUpperCase()}`,
      最终字节14: `0x${newByte14.toString(16).toUpperCase()}`
    })
  }

  // 🔧 增强版镜像控制
  const openMirror = () => {
    console.log('🔧 开启镜像模式')
    const oldValue = writeData.value[12]
    writeData.value[12] |= MIRROR_BIT
    const newValue = writeData.value[12]
    
    console.log('🔧 镜像位设置:', {
      旧值: `0x${oldValue.toString(16).toUpperCase()}`,
      新值: `0x${newValue.toString(16).toUpperCase()}`,
      镜像位: '已开启'
    })
    
    validateSixteen()
  }

  const closeMirror = () => {
    console.log('🔧 关闭镜像模式')
    const oldValue = writeData.value[12]
    writeData.value[12] &= ~MIRROR_BIT // 使用按位非运算清除镜像位
    const newValue = writeData.value[12]
    
    console.log('🔧 镜像位清除:', {
      旧值: `0x${oldValue.toString(16).toUpperCase()}`,
      新值: `0x${newValue.toString(16).toUpperCase()}`,
      镜像位: '已关闭'
    })
    
    validateSixteen()
  }

  // 🔧 增强版校验和计算 - 严格按照协议规范
  const validateSixteen = () => {
    let checksum = 0
    
    // 按照协议规范：对字节1-15进行异或运算（跳过字节0和字节5）
    const bytesToCheck = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    
    for (const byteIndex of bytesToCheck) {
      checksum ^= writeData.value[byteIndex]
    }
    
    checksum &= 0xFF // 确保结果在0-255范围内
    writeData.value[16] = checksum
    
    console.log('🔧 校验和计算详情:', {
      参与计算的字节: bytesToCheck.map(i => `${i}: 0x${writeData.value[i].toString(16).toUpperCase()}`).join(', '),
      计算结果: `0x${checksum.toString(16).toUpperCase()}`,
      字节16更新为: `0x${writeData.value[16].toString(16).toUpperCase()}`
    })
  }

  /**
   * 🔧 增强版距离规则映射 - 添加详细日志和边界检查
   */
  const mapDistanceToRule = (x: number) => {
    if (isNaN(x) || x < 0) {
      console.warn('⚠️ 无效的距离值:', x)
      return { rule: 0, effectiveNumber: 0, multiplier: 1 }
    }

    // 规则对应表，按除数从小到大排列
    const rules = [
      { rule: 0, divisor: 1, name: '个位' },
      { rule: 1, divisor: 10, name: '十位' },
      { rule: 2, divisor: 100, name: '百位' },
      { rule: 3, divisor: 1000, name: '千位' }
    ]

    console.log('🔧 距离规则映射:', { 输入距离: x })

    // 从最小的除数开始尝试
    for (const { rule, divisor, name } of rules) {
      const effectiveNumber = Math.floor(x / divisor)

      console.log(`🔧 尝试${name}显示(rule=${rule}, divisor=${divisor}):`, {
        有效数字: effectiveNumber,
        是否符合: effectiveNumber <= 999
      })

      // 检查有效数字是否小于等于999
      if (effectiveNumber <= 999) {
        const result = {
          rule,
          effectiveNumber,
          multiplier: divisor
        }
        
        console.log('✅ 距离规则映射结果:', {
          规则: `${rule} (${name})`,
          有效数字: effectiveNumber,
          乘数: divisor,
          验证: `${effectiveNumber} * ${divisor} = ${effectiveNumber * divisor}`
        })
        
        return result
      }
    }

    // 如果都不满足（理论上不会出现），默认返回最大规则
    console.warn('⚠️ 距离过大，使用千位显示')
    return {
      rule: 3,
      effectiveNumber: Math.floor(x / 1000),
      multiplier: 1000
    }
  }

  // 🔧 增强版停止导航
  const stopSendNavigationProgress = () => {
    console.log('🛑 停止发送导航进度')
    
    // 保留镜像位，清除导航数据
    const currentMirrorBit = writeData.value[12] & MIRROR_BIT
    writeData.value[12] = currentMirrorBit
    writeData.value[13] = 0x00
    writeData.value[14] = 0x00
    writeData.value[15] = 0x00
    
    validateSixteen()
    
    console.log('🔧 导航数据已清除，镜像位保持:', currentMirrorBit ? '开启' : '关闭')
  }

  /**
   * 🔧 增强版方向获取 - 添加详细的错误处理和日志
   */
  const getDirectionByBannerInstruction = (
    bannerInstruction: BannerInstruction
  ) => {
    try {
      console.log('🔧 解析导航指令:', bannerInstruction)
      
      const { primary } = bannerInstruction
      if (!primary) {
        throw new Error('缺少primary指令')
      }
      
      const { type, modifier } = primary
      console.log('🔧 指令详情:', { type, modifier })
      
      // 使用类型守卫函数进行类型检查
      const isManeuverType = (value: string): value is ManeuverType => {
        return Object.values(ManeuverType).includes(value as ManeuverType)
      }

      const isManeuverDirection = (value: string): value is ManeuverDirection => {
        return Object.values(ManeuverDirection).includes(
          value as ManeuverDirection
        )
      }

      if (typeof type !== 'string' || !isManeuverType(type)) {
        throw new Error(`无效的操作类型: ${type}`)
      }

      if (!isManeuverDirection(modifier)) {
        throw new Error(`无效的操作修饰符: ${modifier}`)
      }

      const protocolDirection = getProtocolDirection(type, modifier)
      
      console.log('🔧 方向解析结果:', {
        操作类型: type,
        修饰符: modifier,
        协议方向: protocolDirection,
        方向名称: getDirectionName(protocolDirection)
      })
      
      return protocolDirection
    } catch (error) {
      console.error('❌ 方向解析失败:', error)
      throw error
    }
  }

  /**
   * 根据操作类型获取默认协议方向
   */
  const getProtocolDirection = (
    maneuverType: ManeuverType,
    modifier: ManeuverDirection
  ): number => {
    // 特殊类型处理
    if (maneuverType === ManeuverType.Arrive) {
      console.log('🔧 到达目的地，返回方向代码9')
      return 9
    }
    
    if (maneuverType === ManeuverType.Depart) {
      console.log('🔧 出发指令，返回方向代码3(直行)')
      return 3
    }
    
    // 其他类型根据修饰符处理
    const direction = getProtocolDirectionByModifier(modifier)
    console.log('🔧 根据修饰符获取方向:', { modifier, direction })
    return direction
  }

  /**
   * 根据操作修饰符获取协议方向
   */
  const getProtocolDirectionByModifier = (
    modifier: ManeuverDirection
  ): number => {
    const direction = DirectionProtocolMap[modifier]
    if (direction === undefined) {
      console.warn('⚠️ 未知的方向修饰符:', modifier)
      return 3 // 默认直行
    }
    return direction
  }

  // 🔧 新增：获取当前数据状态的调试信息
  const getDebugInfo = () => {
    const data = writeData.value
    return {
      完整数据: data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '),
      协议部分: data.slice(0, 12).map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '),
      导航部分: data.slice(12, 16).map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '),
      方向: data[12] & 0x0F,
      方向名称: getDirectionName(data[12] & 0x0F),
      镜像位: (data[12] & 0x80) !== 0,
      单次距离规则: (data[12] >> 4) & 0x03,
      单次距离低位: data[13],
      字节14详情: `0x${data[14].toString(16).toUpperCase()}`,
      总距离低位: data[15],
      校验和: `0x${data[16].toString(16).toUpperCase()}`,
      截止位: `0x${data[17].toString(16).toUpperCase()}`
    }
  }

  // 🔧 新增：独立的数据计算函数，不修改writeData.value，专门用于数据对比
  const calculateNavigationDataIndependently = (data: any, initialData?: number[], mirrorEnabled: boolean = false) => {
    try {
      console.log('🧮 开始独立计算导航数据（不影响全局状态）')
      
      // 使用传入的初始数据或默认初始数据
      const independentData = initialData ? [...initialData] : [...WriteData]
      
      console.log('🧮 使用的初始数据:', independentData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
      
      const bannerJsonString = data.bannerInstructions
      if (!bannerJsonString) {
        console.warn('⚠️ 缺少导航指令数据，返回初始数据')
        return independentData
      }
      
      const banner = JSON.parse(bannerJsonString)
      console.log('🧮 解析后的导航指令:', banner)
      
      // 获取当前导航的方向
      const direction = getDirectionByBannerInstruction(banner)
      console.log('🧮 计算出的方向:', direction, `(${getDirectionName(direction)})`)
      
      // 🔧 增强版距离处理：确保与Native端逻辑完全一致
      const rawStepDistance = data.stepDistanceRemaining
      const distance = processDistance(rawStepDistance)
      console.log('🧮 距离处理:', { 原始: rawStepDistance, 处理后: distance })
      
      // 距离终点的距离
      const totalDistance = data.distanceRemaining || 0
      console.log('🧮 总距离:', totalDistance)
      
      // 🔧 增强版距离规则计算
      const singleDistanceRule = mapDistanceToRule(distance)
      const totalDistanceRule = mapDistanceToRule(totalDistance)
      
      console.log('🧮 距离规则:', {
        单次距离规则: singleDistanceRule,
        总距离规则: totalDistanceRule
      })
      
      // 🔧 严格按照协议计算高位和低位
      const singleHigh = Math.floor(singleDistanceRule.effectiveNumber / 256)
      const singleLow = singleDistanceRule.effectiveNumber % 256
      const totalHigh = Math.floor(totalDistanceRule.effectiveNumber / 256)
      const totalLow = totalDistanceRule.effectiveNumber % 256
      
      console.log('🧮 高低位计算:', {
        单次高位: singleHigh,
        单次低位: singleLow,
        总距离高位: totalHigh,
        总距离低位: totalLow
      })
      
      // 🔧 按顺序更新数据，确保每一步都正确
      // 1. 先清除导航相关的字节（保留或设置镜像位）
      const mirrorBit = mirrorEnabled ? MIRROR_BIT : 0
      independentData[12] = mirrorBit // 只设置镜像位，清除其他位
      independentData[13] = 0
      independentData[14] = 0
      independentData[15] = 0
      
      // 2. 计算并设置字节12（方向 + 单次距离规则 + 镜像位）
      const newByte12 = mirrorBit | 
                       (singleDistanceRule.rule << SINGLE_RULE_SHIFT) | 
                       direction
      independentData[12] = newByte12
      
      // 3. 设置字节13（单次距离低位）
      independentData[13] = singleLow
      
      // 4. 计算并设置字节14（距离高位 + 规则）
      const newByte14 = (singleHigh << SINGLE_HIGH_SHIFT) | 
                       (totalDistanceRule.rule << TOTAL_RULE_SHIFT) | 
                       totalHigh
      independentData[14] = newByte14
      
      // 5. 设置字节15（总距离低位）
      independentData[15] = totalLow
      
      // 6. 最后计算校验和
      let checksum = 0
      const bytesToCheck = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
      for (const byteIndex of bytesToCheck) {
        checksum ^= independentData[byteIndex]
      }
      checksum &= 0xFF
      independentData[16] = checksum
      
      // 🔧 数据完整性验证
      console.log('🧮 独立计算的最终数据:', independentData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '))
      
      // 验证关键字节
      const finalDirection = independentData[12] & 0x0F
      const finalMirror = (independentData[12] & 0x80) !== 0
      const finalSingleRule = (independentData[12] >> 4) & 0x03
      
      console.log('🧮 独立计算数据验证:', {
        方向: `${finalDirection} (${getDirectionName(finalDirection)})`,
        镜像位: finalMirror,
        单次距离规则: finalSingleRule,
        单次距离低位: independentData[13],
        字节14: `0x${independentData[14].toString(16).toUpperCase()}`,
        总距离低位: independentData[15],
        校验和: `0x${independentData[16].toString(16).toUpperCase()}`
      })
      
      console.log('✅ 独立导航数据计算完成')
      
      return independentData
      
    } catch (error) {
      console.error('❌ 独立导航数据计算失败:', error)
      // 返回初始数据作为后备
      return initialData ? [...initialData] : [...WriteData]
    }
  }

  // 🔧 新增：验证导航数据的有效性
  const validateNavigationData = (data: any): { isValid: boolean, issues: string[] } => {
    const issues: string[] = []
    
    try {
      // 1. 检查基本结构
      if (!data) {
        issues.push('导航数据为空')
        return { isValid: false, issues }
      }
      
      // 2. 检查bannerInstructions
      if (!data.bannerInstructions) {
        issues.push('缺少bannerInstructions字段')
      } else {
        try {
          const banner = JSON.parse(data.bannerInstructions)
          if (!banner.primary) {
            issues.push('bannerInstructions缺少primary字段')
          } else {
            if (!banner.primary.type) {
              issues.push('bannerInstructions.primary缺少type字段')
            }
            if (!banner.primary.modifier) {
              issues.push('bannerInstructions.primary缺少modifier字段')
            }
          }
        } catch (parseError) {
          issues.push('bannerInstructions不是有效的JSON格式')
        }
      }
      
      // 3. 检查距离数据
      if (data.stepDistanceRemaining === undefined || data.stepDistanceRemaining === null) {
        issues.push('缺少stepDistanceRemaining字段')
      } else if (typeof data.stepDistanceRemaining !== 'number' || isNaN(data.stepDistanceRemaining)) {
        issues.push('stepDistanceRemaining不是有效的数字')
      } else if (data.stepDistanceRemaining < 0) {
        issues.push('stepDistanceRemaining不能为负数')
      }
      
      if (data.distanceRemaining === undefined || data.distanceRemaining === null) {
        issues.push('缺少distanceRemaining字段')
      } else if (typeof data.distanceRemaining !== 'number' || isNaN(data.distanceRemaining)) {
        issues.push('distanceRemaining不是有效的数字')
      } else if (data.distanceRemaining < 0) {
        issues.push('distanceRemaining不能为负数')
      }
      
      // 4. 检查数据合理性
      if (data.stepDistanceRemaining > data.distanceRemaining) {
        issues.push('单次距离不应大于总距离')
      }
      
      const isValid = issues.length === 0
      
      console.log('🔍 导航数据有效性检查:', {
        有效: isValid,
        问题数量: issues.length,
        问题列表: issues
      })
      
      return { isValid, issues }
      
    } catch (error: unknown) {
      console.error('❌ 导航数据验证失败:', error)
      issues.push(`验证过程出错: ${error instanceof Error ? error.message : String(error)}`)
      return { isValid: false, issues }
    }
  }

  return {
    sendNavigationProgress,
    stopSendNavigationProgress,
    openMirror,
    closeMirror,
    writeData,
    // 🔧 新增的增强功能
    resetWriteData,
    processDistance,
    getDebugInfo,
    // 🔧 新增：独立计算和验证功能
    calculateNavigationDataIndependently,
    validateNavigationData,
    // 🔧 新增：同步设置数据的功能
    syncWithSettingData
  }
}
