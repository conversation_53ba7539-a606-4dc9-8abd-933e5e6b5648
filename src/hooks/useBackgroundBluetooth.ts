import { ref, onUnmounted, watch } from 'vue';
import { Capacitor } from '@capacitor/core';
import { isPlatform } from '@ionic/vue';
import { KeepAwake } from '@capacitor-community/keep-awake';
import { CapacitorKtService } from 'capacitor-kt-service';
import { bluetoothServiceHelper, type BluetoothServiceStatus } from '@/utils/bluetoothServiceHelper';

export function useBackgroundBluetooth() {
  const isServiceRunning = ref(false);
  const isInDozeMode = ref(false);
  const deviceInfo = ref<any>(null);
  const serviceStatus = ref<BluetoothServiceStatus | null>(null);
  const isNative = Capacitor.isNativePlatform();
  let worker: Worker | null = null;
  let nativeInterval: number | null = null;
  let serviceListener: any = null;
  let dozeCheckInterval: NodeJS.Timeout | null = null;
  let keepAliveInterval: NodeJS.Timeout | null = null;
  let statusCheckInterval: NodeJS.Timeout | null = null;
  
  // 启动后台服务
  const startBackgroundService = async () => {
    try {
      if (!isNative) return;
      
      console.log('🚀 开始启动后台蓝牙服务...');
      
      // 获取设备信息
      await getDeviceInformation();
      
      // 保持屏幕唤醒
      await KeepAwake.keepAwake();
      
      if (isPlatform('android')) {
        // 检查Doze模式状态
        await checkDozeStatus();
        
        // 请求电池优化豁免
        await CapacitorKtService.requestIgnoreBatteryOptimizations();
        
        // 使用助手类安全启动前台服务
        await bluetoothServiceHelper.startBluetoothForegroundService();
        
        // 启动保活机制
        startKeepAliveTimer();
        
        // 启动Doze模式监控
        startDozeMonitoring();
        
        // 启动服务状态监控
        startServiceStatusMonitoring();
      }
      
      // 监听服务状态变化
      if (!serviceListener) {
        serviceListener = await CapacitorKtService.addListener('bluetoothServiceStateChanged', (data: any) => {
          isServiceRunning.value = data.isRunning;
          console.log('📡 Service state changed:', data.isRunning);
          
          // 如果服务停止，重置助手类状态
          if (!data.isRunning) {
            bluetoothServiceHelper.resetInitializationStatus();
          }
        });
      }
      
      // 检查服务状态
      const { isRunning } = await CapacitorKtService.isBluetoothServiceRunning();
      isServiceRunning.value = isRunning;
      
      console.log('✅ 后台蓝牙服务启动完成');
      
    } catch (error) {
      console.error('❌ Failed to start background service:', error);
      throw error;
    }
  };
  
  // 停止后台服务
  const stopBackgroundService = async () => {
    try {
      if (!isNative) return;
      
      // 停止保活和监控定时器
      stopKeepAliveTimer();
      stopDozeMonitoring();
      stopServiceStatusMonitoring(); // 停止服务状态监控
      
      // 允许屏幕休眠
      await KeepAwake.allowSleep();
      
      if (isPlatform('android')) {
        await CapacitorKtService.stopBluetoothForegroundService();
      }
      
      isServiceRunning.value = false;
      
    } catch (error) {
      console.error('Failed to stop background service:', error);
    }
  };
  
  // 获取设备信息
  const getDeviceInformation = async () => {
    try {
      if (isPlatform('android')) {
        const info = await CapacitorKtService.getDeviceInfo();
        deviceInfo.value = info;
        console.log('Device info:', info);
        
        // 如果是激进电源管理的设备，给出警告
        if (info.hasAggressivePowerManagement) {
          console.warn('Device has aggressive power management. May need manual settings adjustment.');
        }
      }
    } catch (error) {
      console.error('Failed to get device info:', error);
    }
  };
  
  // 检查Doze模式状态
  const checkDozeStatus = async () => {
    try {
      if (isPlatform('android')) {
        const status = await CapacitorKtService.checkDozeMode();
        isInDozeMode.value = status.isInDozeMode;
        
        console.log('Doze status:', status);
        
        if (status.isInDozeMode) {
          console.warn('Device is in Doze mode - may affect background operation');
        }
        
        if (!status.isIgnoringBatteryOptimizations) {
          console.warn('App is not exempt from battery optimization');
        }
        
        return status;
      }
    } catch (error) {
      console.error('Failed to check Doze status:', error);
    }
  };
  
  // 启动保活定时器
  const startKeepAliveTimer = () => {
    if (keepAliveInterval) return;
    
    keepAliveInterval = setInterval(async () => {
      try {
        await CapacitorKtService.sendKeepAlive();
        console.log('Keep alive signal sent');
      } catch (error) {
        console.error('Failed to send keep alive:', error);
      }
    }, 15000); // 每15秒发送一次保活信号，与后端保持一致
    
    console.log('Keep alive timer started');
  };
  
  // 停止保活定时器
  const stopKeepAliveTimer = () => {
    if (keepAliveInterval) {
      clearInterval(keepAliveInterval);
      keepAliveInterval = null;
      console.log('Keep alive timer stopped');
    }
  };
  
  // 启动Doze模式监控
  const startDozeMonitoring = () => {
    if (dozeCheckInterval) return;
    
    dozeCheckInterval = setInterval(async () => {
      await checkDozeStatus();
    }, 60000); // 每分钟检查一次Doze状态
    
    console.log('Doze monitoring started');
  };
  
  // 停止Doze模式监控
  const stopDozeMonitoring = () => {
    if (dozeCheckInterval) {
      clearInterval(dozeCheckInterval);
      dozeCheckInterval = null;
      console.log('Doze monitoring stopped');
    }
  };
  
  // 启动服务状态监控
  const startServiceStatusMonitoring = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
    }

    // 每10秒检查一次服务状态
    statusCheckInterval = setInterval(async () => {
      try {
        const status = await bluetoothServiceHelper.getServiceInitializationStatus();
        serviceStatus.value = status;
        
        // 如果检测到服务异常，记录日志
        if (!status.bluetoothManagerInitialized && status.isServiceRunning) {
          console.warn('⚠️ 服务运行中但蓝牙管理器未初始化:', status);
        }
        
        // 在开发模式下输出详细状态
        if (process.env.NODE_ENV === 'development') {
          console.log('📊 Service Status Check:', {
            running: status.isServiceRunning,
            initialized: status.bluetoothManagerInitialized,
            details: status.bluetoothManagerDetails
          });
        }
      } catch (error) {
        console.error('检查服务状态失败:', error);
      }
    }, 10000);
  };

  // 停止服务状态监控
  const stopServiceStatusMonitoring = () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
      statusCheckInterval = null;
    }
  };

  // 获取当前服务状态
  const getCurrentServiceStatus = async (): Promise<BluetoothServiceStatus> => {
    try {
      const status = await bluetoothServiceHelper.getServiceInitializationStatus();
      serviceStatus.value = status;
      return status;
    } catch (error) {
      console.error('获取服务状态失败:', error);
      const errorStatus: BluetoothServiceStatus = {
        isServiceRunning: false,
        bluetoothManagerInitialized: false,
        lastSendingStatusCached: false,
        lastSendingStatsCached: false,
        bluetoothManagerDetails: '获取状态失败',
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : String(error)
      };
      serviceStatus.value = errorStatus;
      return errorStatus;
    }
  };

  // 检查服务是否健康
  const isServiceHealthy = async (): Promise<boolean> => {
    return await bluetoothServiceHelper.isServiceHealthy();
  };
  
  // 打开电池优化设置
  const openBatteryOptimizationSettings = async () => {
    try {
      if (isPlatform('android')) {
        await CapacitorKtService.openBatteryOptimizationSettings();
      }
    } catch (error) {
      console.error('Failed to open battery optimization settings:', error);
    }
  };
  
  // 打开自启动设置
  const openAutoStartSettings = async () => {
    try {
      if (isPlatform('android')) {
        await CapacitorKtService.openAutoStartSettings();
      }
    } catch (error) {
      console.error('Failed to open auto start settings:', error);
    }
  };
  
  // 更新通知
  const updateNotification = async (title?: string, message?: string) => {
    try {
      if (isPlatform('android')) {
        await CapacitorKtService.updateBluetoothNotification({ title, message });
      }
    } catch (error) {
      console.error('Failed to update notification:', error);
    }
  };
  
  // 创建高精度定时器 - 始终保持106ms间隔
  const createPrecisionTimer = (callback: () => void, interval: number = 106) => {
    if (isPlatform('hybrid')) {
      // 原生平台优先使用Web Worker
      return createWorkerTimer(callback, interval);
    } else {
      // Web平台使用高精度RAF定时器
      return createRAFTimer(callback, interval);
    }
  };
  
  // 创建Web Worker定时器（用于原生平台）
  const createWorkerTimer = (callback: () => void, interval: number) => {
    const workerCode = `
      let intervalId;
      self.onmessage = function(e) {
        const { action, interval } = e.data;
        
        if (action === 'start') {
          intervalId = setInterval(() => {
            self.postMessage('tick');
          }, interval);
        } else if (action === 'stop') {
          clearInterval(intervalId);
        }
      };
    `;
    
    const blob = new Blob([workerCode], { type: 'application/javascript' });
    worker = new Worker(URL.createObjectURL(blob));
    
    worker.onmessage = () => {
      callback();
    };
    
    worker.postMessage({ action: 'start', interval });
    
    return () => {
      if (worker) {
        worker.postMessage({ action: 'stop' });
        worker.terminate();
        worker = null;
      }
    };
  };
  
  // 创建高精度RAF定时器（用于Web平台和备用方案）
  const createRAFTimer = (callback: () => void, interval: number) => {
    let lastTime = performance.now();
    let targetTime = lastTime + interval;
    
    const tick = () => {
      const currentTime = performance.now();
      
      if (currentTime >= targetTime) {
        callback();
        lastTime = currentTime;
        targetTime = lastTime + interval;
      }
      
      nativeInterval = requestAnimationFrame(tick);
    };
    
    nativeInterval = requestAnimationFrame(tick);
    
    return () => {
      if (nativeInterval) {
        cancelAnimationFrame(nativeInterval);
        nativeInterval = null;
      }
    };
  };
  
  // 创建原生setInterval定时器（备用方案）
  const createNativeTimer = (callback: () => void, interval: number) => {
    const intervalId = setInterval(callback, interval);
    
    return () => {
      clearInterval(intervalId);
    };
  };
  
  // 创建多重定时器策略确保106ms间隔
  const createReliableTimer = (callback: () => void, interval: number = 106) => {
    const stopFunctions: Array<() => void> = [];
    
    // 策略1: Web Worker定时器（主要策略）
    if (isPlatform('hybrid')) {
      try {
        const stopWorker = createWorkerTimer(callback, interval);
        stopFunctions.push(stopWorker);
      } catch (error) {
        console.warn('Web Worker timer failed, falling back to RAF:', error);
      }
    }
    
    // 策略2: RAF定时器（高精度备用）
    try {
      const stopRAF = createRAFTimer(() => {
        // 只在Worker失败时执行
        if (stopFunctions.length === 0) {
          callback();
        }
      }, interval);
      stopFunctions.push(stopRAF);
    } catch (error) {
      console.warn('RAF timer failed, falling back to setInterval:', error);
    }
    
    // 策略3: 原生setInterval（最后备用）
    if (stopFunctions.length === 0) {
      const stopNative = createNativeTimer(callback, interval);
      stopFunctions.push(stopNative);
    }
    
    // 返回统一的停止函数
    return () => {
      stopFunctions.forEach(stop => {
        try {
          stop();
        } catch (error) {
          console.warn('Error stopping timer:', error);
        }
      });
    };
  };
  
  // 监听Doze模式变化，自动调整策略
  watch(isInDozeMode, (inDoze) => {
    if (inDoze) {
      console.warn('Entered Doze mode - background operation may be limited');
      // 可以在这里实施额外的对策
    } else {
      console.log('Exited Doze mode - normal operation resumed');
    }
  });
  
  // 清理资源
  onUnmounted(async () => {
    await stopBackgroundService();
    
    if (serviceListener) {
      await serviceListener.remove();
      serviceListener = null;
    }
    
    if (worker) {
      worker.terminate();
      worker = null;
    }
    
    if (nativeInterval) {
      cancelAnimationFrame(nativeInterval);
      nativeInterval = null;
    }
  });
  
  return {
    isServiceRunning,
    isInDozeMode,
    deviceInfo,
    startBackgroundService,
    stopBackgroundService,
    updateNotification,
    checkDozeStatus,
    openBatteryOptimizationSettings,
    openAutoStartSettings,
    getDeviceInformation,
    createPrecisionTimer,
    createReliableTimer,
    createWorkerTimer,
    createRAFTimer,
    createNativeTimer,
    getCurrentServiceStatus,
    isServiceHealthy
  };
}