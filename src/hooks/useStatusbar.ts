import { onMounted, onUnmounted } from 'vue'
import { StatusBar, Style } from '@capacitor/status-bar'
import { isPlatform } from '@ionic/vue'

export const useStatusbar = () => {
  const makeTransparent = async () => {
    try {
      // 基础设置
      await StatusBar.setStyle({ style: Style.Dark })
      await StatusBar.setOverlaysWebView({ overlay: true })

      // Android 透明背景设置
      if (isPlatform('android')) {
        await StatusBar.setBackgroundColor({ color: '#00000000' })
      }
    } catch (error) {
      console.error('透明状态栏设置失败:', error)
    }
  }

  const restoreDefault = async () => {
    try {
      await StatusBar.setStyle({ style: Style.Default })
      await StatusBar.setOverlaysWebView({ overlay: false })

      // 恢复默认背景色
      await StatusBar.setBackgroundColor({ color: '#000000' })
    } catch (error) {
      console.error('恢复默认状态栏失败:', error)
    }
  }

  onMounted(async () => {
    await makeTransparent()
  })

  onUnmounted(async () => {
    await restoreDefault()
  })

  return {
    makeTransparent,
    restoreDefault
  }
}
