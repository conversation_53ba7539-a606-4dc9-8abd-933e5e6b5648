// 蓝牙数据更新测试脚本
// 用于验证updateBluetoothSendData是否正确更新发送数据

const testBluetoothDataUpdate = {
  // 原始数据（问题中提到的接收到的数据）
  originalData: [15, 5, 245, 88, 41, 212, 56, 202, 132, 20, 5, 50, 0, 0, 0, 0, 212, 14],
  
  // 期望的串口数据（16进制格式）
  expectedSerialData: "0F 05 F5 58 29 D4 38 CA 84 14 05 32 00 00 00 00 D4 0E",
  
  // 测试数据更新功能
  async testDataUpdate() {
    console.log('🔍 开始测试蓝牙数据更新功能...');
    
    try {
      // 1. 记录当前时间戳
      const updateTimestamp = Date.now();
      console.log(`🕐 测试开始时间戳: ${updateTimestamp}`);
      
      // 2. 准备测试数据
      const testData = this.originalData;
      console.log('📝 准备更新的数据:', testData);
      console.log('📝 数据长度:', testData.length);
      
      // 3. 转换为16进制字符串用于比较
      const hexString = testData.map(val => val.toString(16).padStart(2, '0').toUpperCase()).join(' ');
      console.log('📝 16进制格式:', hexString);
      
      // 4. 检查数据是否与期望的串口数据匹配
      if (hexString === this.expectedSerialData) {
        console.log('✅ 测试数据与期望的串口数据匹配');
      } else {
        console.log('❌ 测试数据与期望的串口数据不匹配');
        console.log('期望:', this.expectedSerialData);
        console.log('实际:', hexString);
      }
      
      // 5. 调用数据更新方法（需要在实际环境中运行）
      if (typeof CapacitorKtService !== 'undefined') {
        console.log('📤 调用updateBluetoothSendData...');
        await CapacitorKtService.updateBluetoothSendData({ data: testData });
        console.log('✅ updateBluetoothSendData调用成功');
        
        // 6. 等待一段时间让数据更新生效
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 7. 验证数据是否被正确更新（如果有获取当前数据的方法）
        if (typeof CapacitorKtService.getCurrentBluetoothSendData !== 'undefined') {
          try {
            const currentData = await CapacitorKtService.getCurrentBluetoothSendData();
            console.log('📋 当前发送数据:', currentData);
            
            if (JSON.stringify(currentData.data) === JSON.stringify(testData)) {
              console.log('✅ 数据更新验证成功');
            } else {
              console.log('❌ 数据更新验证失败');
              console.log('期望:', testData);
              console.log('实际:', currentData.data);
            }
          } catch (error) {
            console.log('⚠️ 无法获取当前发送数据:', error.message);
          }
        }
        
      } else {
        console.log('⚠️ CapacitorKtService不可用，无法执行实际测试');
        console.log('💡 请在实际应用环境中运行此测试');
      }
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }
  },
  
  // 分析数据差异
  analyzeDataDifference() {
    console.log('🔍 分析数据差异...');
    
    const receivedData = this.originalData;
    const expectedHex = this.expectedSerialData.split(' ');
    
    console.log('📊 数据对比分析:');
    console.log('索引 | 接收值 | 16进制 | 期望16进制 | 匹配');
    console.log('-----|--------|--------|----------|------');
    
    let hasDiscrepancy = false;
    
    for (let i = 0; i < receivedData.length; i++) {
      const receivedVal = receivedData[i];
      const receivedHex = receivedVal.toString(16).padStart(2, '0').toUpperCase();
      const expectedHex_i = expectedHex[i] || 'N/A';
      const matches = receivedHex === expectedHex_i;
      
      if (!matches) hasDiscrepancy = true;
      
      console.log(`${i.toString().padStart(4)} | ${receivedVal.toString().padStart(6)} | ${receivedHex.padStart(6)} | ${expectedHex_i.padStart(8)} | ${matches ? '✅' : '❌'}`);
    }
    
    if (!hasDiscrepancy) {
      console.log('✅ 所有数据都匹配期望值');
    } else {
      console.log('❌ 发现数据不匹配，这可能是问题的根源');
    }
  },
  
  // 生成调试日志过滤器
  generateLogFilter() {
    console.log('📋 生成Android日志过滤命令:');
    console.log('adb logcat | grep -E "(updateBluetoothSendData|UPDATE_BLUETOOTH_DATA|📝|📤|🔄|🌐|🔧)"');
    console.log('');
    console.log('📋 或者使用标签过滤:');
    console.log('adb logcat CapacitorKtService:D BluetoothForegroundService:D NativeBluetoothManager:D *:S');
  }
};

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testBluetoothDataUpdate;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.testBluetoothDataUpdate = testBluetoothDataUpdate;
}

// 立即执行分析
console.log('🚀 蓝牙数据更新测试工具已加载');
testBluetoothDataUpdate.analyzeDataDifference();
testBluetoothDataUpdate.generateLogFilter();

// 如果在支持的环境中，运行完整测试
if (typeof CapacitorKtService !== 'undefined') {
  testBluetoothDataUpdate.testDataUpdate();
}