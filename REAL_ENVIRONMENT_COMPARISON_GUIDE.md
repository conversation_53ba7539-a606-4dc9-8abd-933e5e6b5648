# 真实环境蓝牙数据对比测试指南

## 📱 访问测试工具

在真实设备或模拟器中运行应用后，可以通过以下方式访问数据对比工具：

### 方式一：通过设置页面
1. 打开应用
2. 导航到"设置"页面 
3. 滚动到底部的"开发者工具"区域
4. 点击"蓝牙数据对比测试"

### 方式二：直接访问
在浏览器中访问: `http://localhost:8100/bluetooth-comparison`

## 🧪 测试功能说明

### 1. 导航数据输入
可以手动配置测试参数：
- **操作类型**: turn, continue, arrive, depart
- **方向修饰符**: left, right, straight, slight left, slight right, sharp left, sharp right, uturn
- **单次距离**: 输入米数（如：150）
- **总距离**: 输入米数（如：1000）

### 2. 快速测试按钮
提供预设的测试场景：
- **左转测试**: turn/left, 200m/1500m
- **右转测试**: turn/right, 300m/2000m  
- **直行测试**: continue/straight, 500m/3000m
- **到达测试**: arrive/straight, 0m/0m

### 3. 实时对比结果
- ✅ **数据一致**: 绿色勾号，表示两种方式生成的数据完全相同
- ❌ **数据不一致**: 红色X号，会显示具体的差异字节
- 📊 **详细信息**: 点击"查看详情"可以看到完整的字节数组对比

## 🔍 测试原理

### 数据生成对比
1. **重构前方式**: 使用`useNavigation`的`sendNavigationProgress`方法
2. **重构后方式**: 模拟`useNativeBluetoothMessage`的数据生成逻辑
3. **实时比较**: 逐字节比较生成的18字节writeData数组

### 验证的数据结构
```
字节 0-11:  固定协议部分（与控制器通讯协议）
字节 12:    [7]镜像位 [6]保留 [5,4]单次距离单位 [3,2,1,0]方向
字节 13:    单次距离低位
字节 14:    [7,6]单次距离高位 [5,4]总距离单位 [3,2]保留 [1,0]总距离高位  
字节 15:    总距离低位
字节 16:    XOR校验值
字节 17:    截止位 (0x0e)
```

## 📋 测试用例建议

### 基础功能测试
1. **所有方向类型**
   - 左转、右转、直行、调头
   - 稍左转、稍右转、急左转、急右转
   - 到达目的地

2. **距离规则边界测试**
   - 小距离: 50m (个位显示)
   - 中等距离: 1000m (十位显示)  
   - 大距离: 10000m (百位显示)
   - 极大距离: 100000m (千位显示)

3. **特殊情况测试**
   - 零距离: 0m/0m (到达目的地)
   - 最大距离: 999999m
   - 非5倍数距离: 123m (测试四舍五入)

### 实际导航场景测试
1. **城市导航**
   - 短距离转弯: turn/left, 80m/500m
   - 长直路: continue/straight, 800m/5000m

2. **高速公路导航**  
   - 长距离直行: continue/straight, 2000m/50000m
   - 出口转弯: turn/right, 500m/1200m

3. **复杂路口**
   - 稍微转弯: turn/slight right, 300m/2000m
   - 急转弯: turn/sharp left, 150m/800m

## 📊 结果分析

### 成功指标
- ✅ 所有测试用例数据匹配率100%
- ✅ 字节12-17的导航数据部分完全一致
- ✅ XOR校验值计算正确

### 失败分析
如果出现数据不匹配：

1. **检查字节12（方向字节）**
   - 方向代码是否正确映射
   - 距离单位规则是否正确
   - 镜像位是否按预期设置

2. **检查字节13-15（距离字节）**
   - 距离值的高低位拆分是否正确
   - 距离规则映射是否准确
   - 有效数字计算是否正确

3. **检查字节16（校验字节）**
   - XOR校验算法是否一致
   - 参与校验的字节范围是否正确

## 🛠️ 调试技巧

### 1. 使用浏览器开发者工具
- 打开Console查看详细的调试信息
- 使用Network面板监控数据传输（如果适用）

### 2. 逐步测试
- 从简单的测试用例开始（如左转150m/1000m）
- 逐步增加复杂度（距离规则、特殊方向等）
- 记录每次测试的结果用于分析

### 3. 数据格式检查
- 确保所有字节都在0-255范围内
- 验证字节数组长度为18
- 检查固定协议部分（0-11字节）是否不变

## 🚀 测试流程建议

### 完整测试流程
1. **启动应用**: 确保在真实设备或模拟器上运行
2. **访问工具**: 通过设置页面进入数据对比工具
3. **快速验证**: 使用快速测试按钮进行基本验证
4. **详细测试**: 手动输入各种测试参数
5. **边界测试**: 测试极端情况和边界值
6. **结果分析**: 查看详细的对比结果和统计信息
7. **问题定位**: 如有不匹配，分析具体的差异字节

### 测试报告
工具会自动生成测试统计：
- 测试总数
- 匹配数量和比例
- 详细的差异信息
- 时间戳记录

## 💡 注意事项

1. **平台差异**: iOS和Android平台的距离四舍五入处理可能不同
2. **数据精度**: 浮点数计算可能存在精度差异
3. **实时性**: 在真实环境中测试能更好地反映实际使用情况
4. **内存管理**: 大量测试后建议清除结果以释放内存

---

*此工具专为验证重构后系统的数据一致性而设计，确保在实际环境中的可靠性。* 