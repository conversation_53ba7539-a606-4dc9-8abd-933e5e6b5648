# 蓝牙数据架构重构 - 基于原始数据的导航修改

## 🎯 **重构目标**

将导航数据发送从"创建全新数组"模式改为"基于原始数据修改"模式，确保保留设备发送的原始信息（速度、电池、其他控制器数据）。

## 🔄 **架构对比**

### ❌ **重构前的错误架构**
```
导航数据变化 → NavigationDataBuilder.buildNavigationBluetoothData()
                     ↓
                 创建全新18字节数组 → 发送
                     ↓
              丢失原始蓝牙数据信息
```

**问题**：
- 丢失了速度、电池状态等重要信息
- 只有导航数据，缺少设备状态
- 校验和基于不完整数据计算

### ✅ **重构后的正确架构**
```
设备 → 发送原始蓝牙数据(18字节) → 存储为最新接收数据
                                    ↓
导航变化 → 获取最新接收数据 → 修改导航字节(12-16) → 重算校验和 → 发送
```

**优势**：
- 保留原始设备信息（速度、电池等）
- 只修改导航相关字节
- 正确的校验和计算
- 完整的18字节数据结构

## 🛠️ **核心修改**

### 1. **CapacitorKtService.java** - 添加最新接收数据存储

```java
// 新增字段
private static int[] latestReceivedBluetoothData = null;
private static final Object bluetoothDataLock = new Object();

// 新增方法：更新最新接收数据
public void updateLatestReceivedBluetoothData(int[] data) {
    synchronized (bluetoothDataLock) {
        latestReceivedBluetoothData = data != null ? data.clone() : null;
        Log.d("CapacitorKtService", "更新最新接收的蓝牙数据，长度: " + 
            (data != null ? data.length : "null"));
    }
}

// 新增方法：获取最新接收数据
public int[] getLatestReceivedBluetoothData() {
    synchronized (bluetoothDataLock) {
        return latestReceivedBluetoothData != null ? 
               latestReceivedBluetoothData.clone() : null;
    }
}
```

### 2. **NavigationDataBuilder.java** - 基于原始数据的修改方法

```java
/**
 * 基于原始蓝牙数据修改导航字节（核心新方法）
 * @param baseData 接收到的原始蓝牙数据数组
 * @param routeProgress 导航路线进度
 * @return 修改后的蓝牙数据数组
 */
public int[] modifyNavigationData(int[] baseData, RouteProgress routeProgress) {
    if (baseData == null || baseData.length < 18) {
        baseData = DEFAULT_BASE_DATA.clone(); // 使用默认基础数据
    }
    
    // 复制原始数据，避免修改传入的数组
    int[] modifiedData = baseData.clone();
    
    // 计算导航相关数据
    int direction = getDirectionFromBannerInstructions(routeProgress.getBannerInstructions());
    DistanceRule singleRule = mapDistanceToRule(singleDistance);
    DistanceRule totalRule = mapDistanceToRule(totalDistance);
    
    // 只修改导航相关的字节（12-15），保留原始数据的其他信息
    int mirrorBit = modifiedData[12] & 0x80; // 保留镜像位
    modifiedData[12] = mirrorBit | (singleRule.rule << 4) | direction;
    modifiedData[13] = singleRule.effectiveNumber % 256;
    modifiedData[14] = (singleHigh << 6) | (totalRule.rule << 4) | totalHigh;
    modifiedData[15] = totalRule.effectiveNumber % 256;
    
    // 重新计算校验和
    modifiedData[16] = calculateChecksum(modifiedData);
    
    return modifiedData;
}

/**
 * 基于原始蓝牙数据清空导航字节
 */
public int[] clearNavigationData(int[] baseData) {
    int[] clearedData = baseData.clone();
    
    // 清空导航字节，但保留镜像位和其他原始数据
    int mirrorBit = clearedData[12] & 0x80;
    clearedData[12] = mirrorBit;
    clearedData[13] = 0x00;
    clearedData[14] = 0x00;
    clearedData[15] = 0x00;
    
    // 重新计算校验和
    clearedData[16] = calculateChecksum(clearedData);
    
    return clearedData;
}
```

### 3. **NavigationManager.kt** - 使用新的基于原始数据的方法

```kotlin
private fun sendNavigationDataToBluetooth(routeProgress: RouteProgress) {
    if (!isMirrorEnabled) return
    
    try {
        val ktService = CapacitorKtService.getInstance()
        if (ktService != null) {
            // 获取最新接收到的蓝牙数据作为基础
            val baseData = ktService.getLatestReceivedBluetoothData()
            
            // 基于原始数据修改导航字节
            val navigationData = if (baseData != null) {
                dataBuilder.modifyNavigationData(baseData, routeProgress)
            } else {
                // 备选方案：使用默认数据
                Log.w(tag, "未找到原始蓝牙数据，使用默认基础数据")
                dataBuilder.buildNavigationBluetoothData(routeProgress)
            }
            
            // 发送修改后的数据
            ktService.updateBluetoothSendDataInternal(navigationData)
            
            Log.d(tag, "已发送导航数据(基于原始数据): ${navigationData.joinToString { "0x%02X".format(it) }}")
        }
    } catch (e: Exception) {
        Log.e(tag, "发送导航数据失败", e)
    }
}

fun clearNavigationBluetoothData() {
    try {
        val ktService = CapacitorKtService.getInstance()
        if (ktService != null) {
            // 获取最新接收到的蓝牙数据作为基础
            val baseData = ktService.getLatestReceivedBluetoothData()
            
            // 基于原始数据清空导航字节
            val clearData = if (baseData != null) {
                dataBuilder.clearNavigationData(baseData)
            } else {
                dataBuilder.clearNavigationBluetoothData() // 兼容性方法
            }
            
            ktService.updateBluetoothSendDataInternal(clearData)
            Log.d(tag, "已清空导航蓝牙数据(基于原始数据)")
        }
    } catch (e: Exception) {
        Log.e(tag, "清空导航蓝牙数据失败", e)
    }
}
```

### 4. **BluetoothForegroundService.java** - 在接收数据时更新最新数据

```java
private void notifyBluetoothDataReceived(int[] data) {
    try {
        // 更新CapacitorKtService中的最新接收数据，用于导航数据修改的基础
        CapacitorKtService ktService = CapacitorKtService.getInstance();
        if (ktService != null) {
            ktService.updateLatestReceivedBluetoothData(data);
        }
        
        // 通过插件通知前端
        BluetoothBackgroundPlugin plugin = BluetoothBackgroundPlugin.getInstance();
        if (plugin != null) {
            plugin.notifyBluetoothDataReceived(data, currentDeviceId);
        }
        
        Log.d(TAG, "蓝牙数据接收已通知，数据长度: " + data.length + " bytes");
    } catch (Exception e) {
        Log.e(TAG, "通知蓝牙数据接收失败", e);
    }
}
```

## 📊 **数据流向图**

```
┌─────────────┐    接收原始数据     ┌─────────────────────┐
│    设备     │ ─────────────→    │  NativeBluetoothManager │
└─────────────┘                   └─────────────────────┘
                                            │
                                            ▼
                                  notifyBluetoothDataReceived
                                            │
                                            ▼
                          ┌─────────────────────────────────┐
                          │    CapacitorKtService          │
                          │  updateLatestReceivedBluetoothData │
                          └─────────────────────────────────┘
                                            │
                          存储最新接收数据    ▼
                          ┌─────────────────────────────────┐
                          │   latestReceivedBluetoothData  │
                          └─────────────────────────────────┘
                                            │
            导航变化触发                      ▼ 获取基础数据
┌─────────────┐           ┌─────────────────────────────────┐
│ RouteProgress│ ────────→ │      NavigationManager        │
└─────────────┘           │   sendNavigationDataToBluetooth │
                          └─────────────────────────────────┘
                                            │
                                            ▼
                          ┌─────────────────────────────────┐
                          │    NavigationDataBuilder       │
                          │    modifyNavigationData         │
                          │  (基于原始数据修改12-16字节)      │
                          └─────────────────────────────────┘
                                            │
                                            ▼
                          ┌─────────────────────────────────┐
                          │         发送修改后数据           │
                          │   (保留原始速度、电池等信息)      │
                          └─────────────────────────────────┘
```

## 🎯 **字节修改策略**

### 18字节数组结构
```
索引  │ 内容         │ 修改策略
─────┼─────────────┼──────────────────
0-11  │ 控制器协议   │ 保持原值（来自设备）
12    │ 导航方向+镜像│ 修改方向位，保留镜像位
13    │ 单次距离低位 │ 修改为导航计算值
14    │ 距离规则+高位│ 修改为导航计算值
15    │ 总距离低位   │ 修改为导航计算值
16    │ 校验和       │ 重新计算
17    │ 截止位       │ 保持原值
```

### 关键修改逻辑
```java
// 保留镜像位，修改导航位
int mirrorBit = modifiedData[12] & 0x80; // 保留第7位镜像
modifiedData[12] = mirrorBit | (rule << 4) | direction; // 修改其他位

// 重新计算校验和
modifiedData[16] = calculateChecksum(modifiedData);
```

## ✅ **重构优势**

### 1. **数据完整性**
- ✅ 保留设备发送的完整信息
- ✅ 速度、电池状态、其他控制器数据完整
- ✅ 只修改导航相关字节

### 2. **架构正确性**
- ✅ 符合实际硬件通信协议
- ✅ 正确的校验和计算
- ✅ 维持原始数据结构

### 3. **向后兼容性**
- ✅ 保留原有方法作为备选方案
- ✅ 当没有原始数据时自动降级
- ✅ 不破坏现有功能

### 4. **可维护性**
- ✅ 清晰的职责分离
- ✅ 详细的日志记录
- ✅ 完善的错误处理

## 🧪 **测试验证**

### 验证要点
1. **数据保留**: 确认原始设备数据（速度、电池）在导航时保持不变
2. **导航功能**: 确认导航指令正确修改到字节12-15
3. **校验和**: 确认校验和基于完整修改后数据计算
4. **兼容性**: 确认在没有原始数据时的降级处理

### 测试方法
1. 使用蓝牙数据对比测试页面验证数据一致性
2. 检查控制台日志确认数据流向正确
3. 验证导航功能在实际设备上工作正常

## 🚀 **使用方式**

### 自动模式（推荐）
系统会自动：
1. 接收设备发送的原始蓝牙数据
2. 存储为最新基础数据
3. 导航变化时基于原始数据修改
4. 发送完整的修改后数据

### 手动测试
```javascript
// Web端可以验证数据对比
const nativeData = await CapacitorKtService.getCurrentBluetoothSendData();
const calculatedData = useNavigation().writeData.value;
// 对比两者是否一致
```

---

**🎊 基于原始蓝牙数据的导航架构重构完成！**

现在导航数据会基于设备实际发送的蓝牙数据进行修改，而不是创建全新数组，确保了数据的完整性和正确性。这正是你需要的架构改进！ 