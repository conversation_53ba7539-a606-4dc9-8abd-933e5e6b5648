# 导航数据修复总结报告

## 问题描述

**原始问题**：
- **发送到蓝牙的数据（Vue端）**: `0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x81, 0x1C, 0xA1, 0x22, 0xAD, 0x0E`
- **串口收到的数据**: `0F 03 F6 58 29 D1 38 CA 84 14 05 32 00 00 00 00 D1 0E`
- **Web端收到的数据**: `0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x81, 0x1C, 0xA1, 0x22, 0xAD, 0x0E`

**核心问题**：串口收到的导航数据字节（12-15位）为 `05 32 00 00 00 00`，导航数据被清空了，而Vue端和Web端的数据包含正确的导航信息。

## 根本原因分析

问题的根本原因在于Native端的数据处理逻辑存在循环依赖：

1. **数据源问题**：`NavigationManager.kt` 中的 `sendNavigationDataToBluetooth` 方法依赖 `getLatestReceivedBluetoothData()` 作为基础数据
2. **循环问题**：接收到的蓝牙数据本身就没有导航信息（因为是从串口接收的），用这样的数据作为基础必然导致导航信息丢失
3. **设置覆盖**：Vue端的设置更新可能意外覆盖导航数据

## 修复方案

### 1. 修改NavigationManager.kt中的数据源优先级

**文件**: `capacitor-kt-service/android/src/main/java/com/kunteng/plugins/kt/NavigationManager.kt`

**核心修改**：
- **修复前**：总是使用 `getLatestReceivedBluetoothData()` 作为基础数据
- **修复后**：优先使用Vue端的完整数据 `getCurrentBluetoothSendDataSync()` 作为基础数据

```kotlin
// 修复前
val baseBluetoothData = ktService.getLatestReceivedBluetoothData()

// 修复后
var baseBluetoothData: IntArray? = null
try {
    val currentDataResult = ktService.getCurrentBluetoothSendDataSync()
    if (currentDataResult != null && currentDataResult.isNotEmpty()) {
        baseBluetoothData = currentDataResult
        Log.d(tag, "使用Vue端当前发送数据作为基础")
    }
} catch (e: Exception) {
    Log.w(tag, "无法获取Vue端当前发送数据: ${e.message}")
}

// 如果无法获取Vue端数据，才尝试使用接收数据
if (baseBluetoothData == null) {
    val receivedData = ktService.getLatestReceivedBluetoothData()
    if (receivedData != null && receivedData.size >= 18) {
        val hasValidSettings = receivedData.sliceArray(0..11).any { it != 0 }
        if (hasValidSettings) {
            baseBluetoothData = receivedData
        }
    }
}
```

### 2. 新增同步数据获取方法

**文件**: `capacitor-kt-service/android/src/main/java/com/kunteng/plugins/kt/CapacitorKtService.java`

**新增方法**：
```java
/**
 * 同步方法：获取当前蓝牙发送数据
 * 用于NavigationManager直接调用，避免异步回调的复杂性
 */
public int[] getCurrentBluetoothSendDataSync() {
    try {
        JSObject result = BluetoothForegroundService.getCurrentBluetoothSendData();
        if (result != null && result.has("success") && result.getBool("success")) {
            JSArray dataArray = result.getJSArray("data");
            if (dataArray != null && dataArray.length() > 0) {
                int[] data = new int[dataArray.length()];
                for (int i = 0; i < dataArray.length(); i++) {
                    data[i] = dataArray.optInt(i);
                }
                return data;
            }
        }
        return null;
    } catch (Exception e) {
        Log.e(TAG, "同步获取当前蓝牙发送数据异常: " + e.getMessage(), e);
        return null;
    }
}
```

### 3. 修改清空导航数据的逻辑

**同样的优先级调整**应用到 `clearNavigationBluetoothData` 方法，确保清空导航数据时也使用Vue端的完整数据作为基础。

### 4. 保护Vue端设置更新时的导航数据

**文件**: `src/hooks/useSetting.ts`

**核心修改**：在设置更新时保存和恢复导航数据
```typescript
const updateSetting = () => {
    // 🔧 保存当前的导航数据（字节12-15），避免在设置更新时被覆盖
    const currentNavigationData = {
        byte12: writeData.value[12],
        byte13: writeData.value[13],
        byte14: writeData.value[14],
        byte15: writeData.value[15],
        byte16: writeData.value[16]
    };
    
    // ... 执行设置更新 ...
    
    // 🔧 恢复导航数据，确保设置更新不会清除导航信息
    const hasValidNavigationData = currentNavigationData.byte12 !== 0 || 
                                   currentNavigationData.byte13 !== 0 || 
                                   currentNavigationData.byte14 !== 0 || 
                                   currentNavigationData.byte15 !== 0;
    
    if (hasValidNavigationData) {
        writeData.value[12] = currentNavigationData.byte12;
        writeData.value[13] = currentNavigationData.byte13;
        writeData.value[14] = currentNavigationData.byte14;
        writeData.value[15] = currentNavigationData.byte15;
    }
    
    // 验证数据完整性（重新计算校验和）
    validateWriteData();
}
```

## 修复效果

### 修复前的数据流
```
Vue端数据 → Native端 → 基于接收数据构建 → 串口发送
                ↑
        接收数据（无导航信息）
```

### 修复后的数据流
```
Vue端数据 → Native端 → 基于Vue端数据构建 → 串口发送
     ↓                         ↑
   完整数据              优先使用Vue端数据
```

## 关键改进点

1. **数据源优先级**：优先使用Vue端的完整数据，而不是可能不完整的接收数据
2. **数据验证**：在使用接收数据前验证其是否包含有效的设置信息
3. **导航数据保护**：在设置更新时保护导航数据不被意外覆盖
4. **同步访问**：提供同步方法避免异步回调的复杂性

## 预期结果

修复后，系统应该能够：

✅ **确保导航数据正确发送到串口**
- 串口接收的数据应该包含完整的导航信息
- 导航数据字节（12-15位）不再为全零

✅ **在设置更新时保留导航信息**
- 用户修改设置时，导航数据不会被意外清除
- 设置和导航数据能够正确共存

✅ **正确处理镜像开关状态**
- 镜像开启/关闭时，导航数据的其他部分保持正确
- 镜像位（字节12的第7位）正确反映状态

✅ **避免循环依赖问题**
- 不再依赖可能不完整的接收数据作为基础
- 数据流向更加清晰和可靠

## 测试建议

1. **功能测试**：
   - 启动导航并检查串口接收的数据是否包含导航信息
   - 在导航过程中修改设置，验证导航数据是否保留
   - 测试镜像开关功能

2. **日志监控**：
   - 查看Android日志中的数据发送记录
   - 确认使用的数据源（Vue端 vs 接收数据）

3. **数据对比**：
   - 对比修复前后的数据传输
   - 验证导航数据的完整性和正确性

通过以上修复，应该能够彻底解决串口收到的数据不包含导航信息的问题。