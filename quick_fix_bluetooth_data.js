/**
 * 蓝牙数据问题快速修复脚本
 * 在浏览器控制台中运行此脚本来诊断和修复数据问题
 */

(async function quickFixBluetoothData() {
  console.log('🔧 开始蓝牙数据问题快速修复...');
  
  try {
    // 检查必要的服务是否可用
    if (typeof CapacitorKtService === 'undefined') {
      console.error('❌ CapacitorKtService不可用');
      return;
    }
    
    console.log('✅ CapacitorKtService可用');
    
    // 1. 获取当前Native端数据
    console.log('📊 步骤1: 获取Native端当前数据...');
    const nativeResult = await CapacitorKtService.getCurrentBluetoothSendData();
    
    if (!nativeResult.success) {
      console.error('❌ 无法获取Native端数据:', nativeResult.error);
      return;
    }
    
    const nativeData = nativeResult.data;
    console.log('📊 Native端当前数据:', nativeData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
    
    // 2. 检查数据是否为固定值（问题症状）
    const expectedFixedData = [0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x81, 0x2B, 0xA1, 0x22, 0x9A, 0x0E];
    const isFixedData = JSON.stringify(nativeData) === JSON.stringify(expectedFixedData);
    
    if (isFixedData) {
      console.warn('⚠️ 检测到数据固定问题！数据始终为:', expectedFixedData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
    }
    
    // 3. 检查Vue端数据（如果可用）
    console.log('📊 步骤2: 检查Vue端数据...');
    let vueData = null;
    
    // 尝试获取Vue端数据（需要根据实际情况调整）
    if (typeof window !== 'undefined' && window.Vue) {
      console.log('✅ Vue环境可用');
      // 这里需要根据实际的Vue应用结构来获取数据
      // 暂时跳过Vue端数据获取
    }
    
    // 4. 生成测试数据
    console.log('📊 步骤3: 生成测试数据...');
    const testData = [
      0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA,  // 0-7: 基础协议
      0x84, 0x14, 0x65, 0x32,                           // 8-11: 通信协议
      0x01, 0x02, 0x03, 0x04,                           // 12-15: 测试导航数据
      0x00,                                             // 16: 校验和（将被计算）
      0x0E                                              // 17: 结束位
    ];
    
    // 计算校验和
    let checksum = 0;
    for (let i = 1; i <= 15; i++) {
      if (i !== 5) { // 跳过字节5
        checksum ^= testData[i];
      }
    }
    testData[16] = checksum & 0xFF;
    
    console.log('📊 生成的测试数据:', testData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
    console.log('📊 计算的校验和:', `0x${checksum.toString(16).padStart(2, '0').toUpperCase()}`);
    
    // 5. 尝试更新数据
    console.log('📊 步骤4: 尝试更新Native端数据...');
    try {
      await CapacitorKtService.updateBluetoothSendData({ data: testData });
      console.log('✅ 测试数据发送成功');
      
      // 验证更新
      await new Promise(resolve => setTimeout(resolve, 200)); // 等待200ms
      const verifyResult = await CapacitorKtService.getCurrentBluetoothSendData();
      
      if (verifyResult.success) {
        const verifyData = verifyResult.data;
        const isUpdated = JSON.stringify(testData) === JSON.stringify(verifyData);
        
        if (isUpdated) {
          console.log('✅ 数据更新验证成功！');
          console.log('📊 验证数据:', verifyData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
        } else {
          console.warn('⚠️ 数据更新验证失败');
          console.warn('期望数据:', testData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
          console.warn('实际数据:', verifyData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
        }
      } else {
        console.error('❌ 无法验证数据更新:', verifyResult.error);
      }
      
    } catch (updateError) {
      console.error('❌ 数据更新失败:', updateError);
    }
    
    // 6. 检查蓝牙发送状态
    console.log('📊 步骤5: 检查蓝牙发送状态...');
    try {
      const bluetoothStats = await CapacitorKtService.getBluetoothSendingStats();
      console.log('📊 蓝牙发送统计:', {
        isConnected: bluetoothStats.isConnected,
        totalSent: bluetoothStats.totalSent,
        errorCount: bluetoothStats.errorCount
      });
      
      if (!bluetoothStats.isConnected) {
        console.warn('⚠️ 蓝牙未连接，这可能是数据不更新的原因');
      }
      
    } catch (statsError) {
      console.warn('⚠️ 无法获取蓝牙统计信息:', statsError);
    }
    
    // 7. 生成修复建议
    console.log('📊 步骤6: 生成修复建议...');
    
    const suggestions = [];
    
    if (isFixedData) {
      suggestions.push('🔧 数据固定问题：检查Vue端数据是否正确更新到Native端');
      suggestions.push('🔧 尝试重启应用或重新连接蓝牙设备');
    }
    
    if (nativeData[12] === 0 && nativeData[13] === 0 && nativeData[14] === 0 && nativeData[15] === 0) {
      suggestions.push('🔧 导航数据为空：检查导航是否已启动，镜像是否已开启');
    }
    
    suggestions.push('🔧 使用BluetoothDataComparisonPage.vue页面进行实时数据对比');
    suggestions.push('🔧 查看Android日志：adb logcat | grep -E "(NavigationManager|BluetoothForegroundService)"');
    
    console.log('💡 修复建议:');
    suggestions.forEach(suggestion => console.log(suggestion));
    
    console.log('✅ 快速修复脚本执行完成');
    
    return {
      nativeData,
      testData,
      suggestions,
      isFixedData
    };
    
  } catch (error) {
    console.error('❌ 快速修复脚本执行失败:', error);
    return null;
  }
})();

// 导出一些有用的调试函数
window.bluetoothDebugUtils = {
  // 强制同步数据
  async forceSyncData(data) {
    try {
      await CapacitorKtService.updateBluetoothSendData({ data: data });
      console.log('✅ 强制同步完成');
      
      // 验证
      const result = await CapacitorKtService.getCurrentBluetoothSendData();
      if (result.success) {
        console.log('📊 同步后数据:', result.data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
      }
    } catch (error) {
      console.error('❌ 强制同步失败:', error);
    }
  },
  
  // 生成随机测试数据
  generateTestData() {
    const data = [
      0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA,
      0x84, 0x14, 0x65, 0x32,
      Math.floor(Math.random() * 256),  // 随机导航数据
      Math.floor(Math.random() * 256),
      Math.floor(Math.random() * 256),
      Math.floor(Math.random() * 256),
      0x00, 0x0E
    ];
    
    // 计算校验和
    let checksum = 0;
    for (let i = 1; i <= 15; i++) {
      if (i !== 5) checksum ^= data[i];
    }
    data[16] = checksum & 0xFF;
    
    return data;
  },
  
  // 检查数据一致性
  async checkConsistency() {
    const result = await CapacitorKtService.getCurrentBluetoothSendData();
    if (result.success) {
      console.log('📊 当前Native端数据:', result.data.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(' '));
      return result.data;
    } else {
      console.error('❌ 无法获取数据:', result.error);
      return null;
    }
  }
};

console.log('💡 调试工具已加载到 window.bluetoothDebugUtils');
console.log('💡 可用方法:');
console.log('  - bluetoothDebugUtils.forceSyncData(data)');  
console.log('  - bluetoothDebugUtils.generateTestData()');
console.log('  - bluetoothDebugUtils.checkConsistency()');