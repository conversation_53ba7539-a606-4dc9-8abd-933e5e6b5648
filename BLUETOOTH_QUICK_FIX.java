// 🔧 蓝牙写入失败快速修复方案
// 复制这些修改到 NativeBluetoothManager.java

// 1. 增加发送间隔（从106ms改为150ms）
private int sendInterval = 150; // 原来是106，改为150ms

// 2. 添加写入状态锁，防止并发写入
private AtomicBoolean isWriting = new AtomicBoolean(false);

// 3. 修改发送逻辑，添加更严格的状态检查
sendTask = sendExecutor.scheduleWithFixedDelay(() -> {
    // 严格的状态检查
    if (!isSending.get() || !isConnected.get() || 
        bluetoothGatt == null || targetCharacteristic == null) {
        return;
    }
    
    // 检查是否正在写入，避免并发
    if (!isWriting.compareAndSet(false, true)) {
        Log.w(TAG, "⚠️ 上一个写入操作未完成，跳过此次发送");
        return;
    }
    
    try {
        // 检查特征属性
        int properties = targetCharacteristic.getProperties();
        if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 && 
            (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
            Log.e(TAG, "❌ 特征不支持写入，属性: " + properties);
            isWriting.set(false);
            return;
        }
        
        // 准备数据
        if (sendData == null || sendData.length == 0) {
            Log.w(TAG, "⚠️ 发送数据为空");
            isWriting.set(false);
            return;
        }
        
        byte[] dataToSend = new byte[sendData.length];
        for (int i = 0; i < sendData.length; i++) {
            dataToSend[i] = (byte) (sendData[i] & 0xFF);
        }
        
        // 检查数据长度
        if (dataToSend.length > 20) {
            Log.w(TAG, "⚠️ 数据长度 " + dataToSend.length + " 超过BLE建议值20字节");
        }
        
        // 写入特征
        targetCharacteristic.setValue(dataToSend);
        boolean writeResult = bluetoothGatt.writeCharacteristic(targetCharacteristic);
        
        if (!writeResult) {
            errorCount.incrementAndGet();
            isWriting.set(false); // 立即释放锁
            
            lastError = String.format("写入失败 - 连接: %s, GATT: %s, 数据长度: %d", 
                isConnected.get() ? "正常" : "异常",
                bluetoothGatt != null ? "正常" : "null",
                dataToSend.length);
            Log.e(TAG, "❌ " + lastError);
            
            // 如果错误率过高，增加发送间隔
            if (errorCount.get() % 10 == 0) {
                sendInterval = Math.min(sendInterval + 20, 300);
                Log.w(TAG, "🔧 错误率过高，调整发送间隔至: " + sendInterval + "ms");
            }
        }
        
    } catch (Exception e) {
        errorCount.incrementAndGet();
        isWriting.set(false);
        lastError = "写入异常: " + e.getMessage();
        Log.e(TAG, "❌ " + lastError, e);
    }
}, 0, sendInterval, TimeUnit.MILLISECONDS);

// 4. 修改 onCharacteristicWrite 回调
@Override
public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
    isWriting.set(false); // 🔧 释放写入锁
    
    if (status == BluetoothGatt.GATT_SUCCESS) {
        successCount.incrementAndGet();
        lastSendTime.set(System.currentTimeMillis());
        
        // 如果连续成功，可以稍微减少发送间隔
        if (successCount.get() % 50 == 0 && sendInterval > 106) {
            sendInterval = Math.max(sendInterval - 10, 106);
            Log.d(TAG, "✅ 连续成功，优化发送间隔至: " + sendInterval + "ms");
        }
    } else {
        errorCount.incrementAndGet();
        
        String statusDesc = parseGattStatus(status);
        lastError = "写入回调失败: " + statusDesc;
        
        Log.e(TAG, String.format("❌ 写入失败 - %s (错误率: %.1f%%)", 
            statusDesc, 
            (double) errorCount.get() / (totalSent.get() + 1) * 100));
        
        // 针对特定错误的处理
        if (status == BluetoothGatt.GATT_CONNECTION_CONGESTED) {
            sendInterval = Math.min(sendInterval + 50, 500);
            Log.w(TAG, "🔧 连接拥塞，增加发送间隔至: " + sendInterval + "ms");
        }
    }
    
    totalSent.incrementAndGet();
}

// 5. 添加状态码解析方法
private String parseGattStatus(int status) {
    switch (status) {
        case BluetoothGatt.GATT_SUCCESS: return "成功";
        case BluetoothGatt.GATT_WRITE_NOT_PERMITTED: return "写入不被允许(3)";
        case BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION: return "认证不足(5)";
        case BluetoothGatt.GATT_CONNECTION_CONGESTED: return "连接拥塞(143)";
        case BluetoothGatt.GATT_FAILURE: return "通用失败(257)";
        default: return "未知错误(" + status + ")";
    }
} 