import { describe, it, expect } from 'vitest'
import { useNavigation } from '@/hooks/useNavigation'

describe('字节12修复验证', () => {
  it('应该正确处理字节12的各个位', () => {
    const navigation = useNavigation()
    
    // 创建测试数据：左转，150米，1000米
    const testData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: 'turn',
          text: 'Turn left',
          components: [{ text: 'Turn left', type: 'text' }],
          modifier: 'left'
        },
        distanceAlongGeometry: 1000,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: 150,
      distanceRemaining: 1000
    }

    // 重置数据
    navigation.writeData.value = [
      0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
      0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [12，13，14，15，16]
      0x0e //17 截止位
    ]

    // 发送导航数据
    navigation.sendNavigationProgress(testData)

    const byte12 = navigation.writeData.value[12]
    
    // 验证方向位 [3,2,1,0] - 左转应该是1
    expect(byte12 & 0x0f).toBe(1)
    
    // 验证单次距离单位位 [5,4] - 150 < 999，使用个位显示 (rule=0)
    expect((byte12 >> 4) & 0x03).toBe(0)
    
    // 验证第6位是保留位，应该为0
    expect((byte12 >> 6) & 0x01).toBe(0)
    
    // 验证第7位镜像位，初始应该为0
    expect((byte12 >> 7) & 0x01).toBe(0)
    
    // 验证完整的字节12值
    // 方向=1, 距离单位=0, 保留位=0, 镜像位=0
    // 二进制: 0000 0001 = 0x01
    expect(byte12).toBe(0x01)
  })

  it('应该正确处理镜像位设置', () => {
    const navigation = useNavigation()
    
    // 重置数据
    navigation.writeData.value = [
      0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
      0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [12，13，14，15，16]
      0x0e //17 截止位
    ]

    // 先打开镜像
    navigation.openMirror()
    
    // 验证镜像位被设置
    expect((navigation.writeData.value[12] >> 7) & 0x01).toBe(1)
    expect(navigation.writeData.value[12]).toBe(0x80) // 只有镜像位被设置

    // 再发送导航数据
    const testData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: 'turn',
          text: 'Turn right',
          components: [{ text: 'Turn right', type: 'text' }],
          modifier: 'right'
        },
        distanceAlongGeometry: 1000,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: 150,
      distanceRemaining: 1000
    }

    navigation.sendNavigationProgress(testData)

    const byte12 = navigation.writeData.value[12]
    
    // 验证方向位 [3,2,1,0] - 右转应该是2
    expect(byte12 & 0x0f).toBe(2)
    
    // 验证单次距离单位位 [5,4] - 150 < 999，使用个位显示 (rule=0)
    expect((byte12 >> 4) & 0x03).toBe(0)
    
    // 验证第6位是保留位，应该为0
    expect((byte12 >> 6) & 0x01).toBe(0)
    
    // 验证第7位镜像位，应该保持为1
    expect((byte12 >> 7) & 0x01).toBe(1)
    
    // 验证完整的字节12值
    // 方向=2, 距离单位=0, 保留位=0, 镜像位=1
    // 二进制: 1000 0010 = 0x82
    expect(byte12).toBe(0x82)
  })

  it('应该正确处理保留位', () => {
    const navigation = useNavigation()
    
    // 重置数据，并设置保留位
    navigation.writeData.value = [
      0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
      0x40, 0x00, 0x00, 0x00, 0x00, //12-16  设置保留位 [12，13，14，15，16]
      0x0e //17 截止位
    ]

    // 发送导航数据
    const testData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: 'turn',
          text: 'Turn left',
          components: [{ text: 'Turn left', type: 'text' }],
          modifier: 'left'
        },
        distanceAlongGeometry: 1000,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: 150,
      distanceRemaining: 1000
    }

    navigation.sendNavigationProgress(testData)

    const byte12 = navigation.writeData.value[12]
    
    // 验证方向位 [3,2,1,0] - 左转应该是1
    expect(byte12 & 0x0f).toBe(1)
    
    // 验证单次距离单位位 [5,4] - 150 < 999，使用个位显示 (rule=0)
    expect((byte12 >> 4) & 0x03).toBe(0)
    
    // 验证第6位保留位，应该保持为1
    expect((byte12 >> 6) & 0x01).toBe(1)
    
    // 验证第7位镜像位，应该为0
    expect((byte12 >> 7) & 0x01).toBe(0)
    
    // 验证完整的字节12值
    // 方向=1, 距离单位=0, 保留位=1, 镜像位=0
    // 二进制: 0100 0001 = 0x41
    expect(byte12).toBe(0x41)
  })
}) 