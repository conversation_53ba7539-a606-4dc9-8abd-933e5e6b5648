import { describe, it, expect, vi, beforeEach, afterEach, beforeAll } from 'vitest'
import { useNavigation } from '../../src/hooks/useNavigation'

// Mock @ionic/core
vi.mock('@ionic/core', () => ({
  isPlatform: vi.fn()
}))

describe('useNavigation', () => {
  let navigation: ReturnType<typeof useNavigation>
  let consoleSpy: any

  // 定义测试用的初始WriteData
  const createInitialWriteData = () => [
    0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
    0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [11，12，13，14，15，16]
    0x0e //17 截止位
  ]

  beforeAll(() => {
    // 确保测试环境正确初始化
    console.log('开始运行 useNavigation 测试套件')
  })

  beforeEach(() => {
    navigation = useNavigation()
    // 重置writeData到初始状态，避免测试间状态污染
    navigation.writeData.value = createInitialWriteData()
    consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('sendNavigationProgress', () => {
    it('应该正确处理左转导航指令', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 29048.186,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 150,
        distanceRemaining: 29048.186
      }

      navigation.sendNavigationProgress(mockData)

      // 验证方向是左转 (1)
      expect(navigation.writeData.value[12] & 0x0f).toBe(1)

      // 验证距离数据
      expect(navigation.writeData.value[13]).toBe(150) // 单次距离低位
      expect(navigation.writeData.value[15]).toBe(34) // 总距离低位 (290 % 256)

      // 验证距离单位规则
      const singleDistanceRule = 0 // 150 < 999，使用个位显示
      const totalDistanceRule = 2 // 29048 < 99900，使用百位显示
      expect((navigation.writeData.value[12] >> 4) & 0x03).toBe(
        singleDistanceRule
      )
      expect((navigation.writeData.value[14] >> 4) & 0x03).toBe(
        totalDistanceRule
      )
    })

    it('应该正确处理右转导航指令', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn right',
            components: [{ text: 'Turn right', type: 'text' }],
            modifier: 'right'
          },
          distanceAlongGeometry: 1000,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 500,
        distanceRemaining: 5000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证方向是右转 (2)
      expect(navigation.writeData.value[12] & 0x0f).toBe(2)

      // 验证距离数据
      expect(navigation.writeData.value[13]).toBe(244) // 单次距离低位 (500 % 256)
      expect(navigation.writeData.value[15]).toBe(244) // 总距离低位 (500 % 256)
    })

    it('应该正确处理直行导航指令', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'continue',
            text: 'Continue straight',
            components: [{ text: 'Continue straight', type: 'text' }],
            modifier: 'straight'
          },
          distanceAlongGeometry: 2000,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 800,
        distanceRemaining: 8000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证方向是直行 (3)
      expect(navigation.writeData.value[12] & 0x0f).toBe(3)
    })

    it('应该正确处理到达目的地指令', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'arrive',
            text: 'You have arrived',
            components: [{ text: 'You have arrived', type: 'text' }],
            modifier: 'straight'
          },
          distanceAlongGeometry: 0,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 0,
        distanceRemaining: 0
      }

      navigation.sendNavigationProgress(mockData)

      // 验证方向是到达目的地 (9)
      expect(navigation.writeData.value[12] & 0x0f).toBe(9)
    })

    it('应该正确处理出发指令', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'depart',
            text: 'Depart',
            components: [{ text: 'Depart', type: 'text' }],
            modifier: 'straight'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 50,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证方向是直行 (3) - depart 默认直行
      expect(navigation.writeData.value[12] & 0x0f).toBe(3)
    })

    it('应该正确处理大距离值的距离规则映射', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100000,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 50000,
        distanceRemaining: 100000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证距离规则
      // 50000 < 99900，使用百位显示 (rule=2)
      expect((navigation.writeData.value[12] >> 4) & 0x03).toBe(2)
      // 100000 >= 99900，使用千位显示 (rule=3)
      expect((navigation.writeData.value[14] >> 4) & 0x03).toBe(3)
    })

    it('应该正确处理iOS平台的原始距离值', async () => {
      const { isPlatform } = await import('@ionic/core')
      vi.mocked(isPlatform).mockReturnValue(true)

      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 23, // 非5的倍数
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // iOS平台应该使用原始值，不进行四舍五入
      expect(navigation.writeData.value[13]).toBe(23)
    })

    it('应该正确处理非iOS平台的距离四舍五入', async () => {
      const { isPlatform } = await import('@ionic/core')
      vi.mocked(isPlatform).mockReturnValue(false)

      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 23, // 应该四舍五入到25
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // 非iOS平台应该四舍五入到最接近的5的倍数
      expect(navigation.writeData.value[13]).toBe(25)
    })

    it('应该正确处理NaN距离值', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: NaN,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // NaN应该被转换为0
      expect(navigation.writeData.value[13]).toBe(0)
    })

    it('应该正确处理缺少bannerInstructions的情况', () => {
      const mockData = {
        bannerInstructions: null,
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      // 不应该抛出错误
      expect(() => {
        navigation.sendNavigationProgress(mockData)
      }).not.toThrow()

      // 数据应该保持初始状态
      expect(navigation.writeData.value[12]).toBe(0)
      expect(navigation.writeData.value[13]).toBe(0)
      expect(navigation.writeData.value[14]).toBe(0)
      expect(navigation.writeData.value[15]).toBe(0)
    })

    it('应该正确处理无效的操作类型', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'invalid_type',
            text: 'Invalid',
            components: [{ text: 'Invalid', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      // 应该抛出错误
      expect(() => {
        navigation.sendNavigationProgress(mockData)
      }).toThrow('无效的操作类型: invalid_type')
    })

    it('应该正确处理无效的操作修饰符', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn',
            components: [{ text: 'Turn', type: 'text' }],
            modifier: 'invalid_modifier'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      // 应该抛出错误
      expect(() => {
        navigation.sendNavigationProgress(mockData)
      }).toThrow('无效的操作修饰符: invalid_modifier')
    })

    it('应该正确计算校验值', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证校验值计算正确
      const initialData = createInitialWriteData()
      const expectedChecksum =
        initialData[1] ^
        initialData[2] ^
        initialData[3] ^
        initialData[4] ^
        initialData[6] ^
        initialData[7] ^
        initialData[8] ^
        initialData[9] ^
        initialData[10] ^
        initialData[11] ^
        navigation.writeData.value[12] ^
        navigation.writeData.value[13] ^
        navigation.writeData.value[14] ^
        navigation.writeData.value[15]

      expect(navigation.writeData.value[16]).toBe(expectedChecksum)
    })

    it('应该正确输出调试信息', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      expect(consoleSpy).toHaveBeenCalledWith(
        '方向: 左转',
        '单次距离: 100',
        '总距离: 1000',
        expect.stringContaining('单次距离规则'),
        expect.stringContaining('总距离规则')
      )
    })

    it('应该正确设置字节12的各个位', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 150,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      const byte12 = navigation.writeData.value[12]
      
      // 验证方向位 [3,2,1,0] - 左转应该是1
      expect(byte12 & 0x0f).toBe(1)
      
      // 验证单次距离单位位 [5,4] - 150 < 999，使用个位显示 (rule=0)
      expect((byte12 >> 4) & 0x03).toBe(0)
      
      // 验证第6位是保留位，应该为0
      expect((byte12 >> 6) & 0x01).toBe(0)
      
      // 验证第7位镜像位，初始应该为0
      expect((byte12 >> 7) & 0x01).toBe(0)
    })

    it('应该正确设置字节13（单次距离低位）', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn right',
            components: [{ text: 'Turn right', type: 'text' }],
            modifier: 'right'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 520, // 测试超过256的值
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      const byte13 = navigation.writeData.value[13]
      
      // 520经过mapDistanceToRule后，effectiveNumber=520，singleLow=520%256=8
      expect(byte13).toBe(8)
      
      // 验证字节13是8位无符号整数
      expect(byte13).toBeGreaterThanOrEqual(0)
      expect(byte13).toBeLessThan(256)
    })

    it('应该正确设置字节14的各个位', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 520, // 单次距离高位测试
        distanceRemaining: 50000 // 总距离高位测试
      }

      navigation.sendNavigationProgress(mockData)

      const byte14 = navigation.writeData.value[14]
      
      // 验证单次距离高位 [7,6] - 520/256=2
      expect((byte14 >> 6) & 0x03).toBe(2)
      
      // 验证总距离单位位 [5,4] - 50000 < 99900，使用百位显示 (rule=2)
      expect((byte14 >> 4) & 0x03).toBe(2)
      
      // 验证保留位 [3,2] 应该为0
      expect((byte14 >> 2) & 0x03).toBe(0)
      
      // 验证总距离高位 [1,0] - 500/256=1
      expect(byte14 & 0x03).toBe(1)
    })

    it('应该正确设置字节15（总距离低位）', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 50000 // 测试大距离值
      }

      navigation.sendNavigationProgress(mockData)

      const byte15 = navigation.writeData.value[15]
      
      // 50000经过mapDistanceToRule后，effectiveNumber=500，totalLow=500%256=244
      expect(byte15).toBe(244)
      
      // 验证字节15是8位无符号整数
      expect(byte15).toBeGreaterThanOrEqual(0)
      expect(byte15).toBeLessThan(256)
    })

    it('应该正确处理字节12的镜像位设置', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      // 先打开镜像
      navigation.openMirror()
      
      // 再发送导航数据
      navigation.sendNavigationProgress(mockData)

      const byte12 = navigation.writeData.value[12]
      
      // 验证镜像位 [7] 被设置
      expect((byte12 >> 7) & 0x01).toBe(1)
      
      // 验证其他位仍然正确
      expect(byte12 & 0x0f).toBe(1) // 方向位
      expect((byte12 >> 4) & 0x03).toBe(0) // 单次距离单位位
    })

    it('应该验证字节12-15的完整数据流', () => {
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn right',
            components: [{ text: 'Turn right', type: 'text' }],
            modifier: 'right'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 1234, // 测试中等距离
        distanceRemaining: 56789 // 测试大距离
      }

      navigation.sendNavigationProgress(mockData)

      // 验证完整的字节12-15
      const byte12 = navigation.writeData.value[12]
      const byte13 = navigation.writeData.value[13]
      const byte14 = navigation.writeData.value[14]
      const byte15 = navigation.writeData.value[15]

      // 字节12: [7]镜像位=0, [6]保留位=0, [5,4]单次距离单位=1, [3,2,1,0]方向=2
      expect(byte12).toBe(0x12) // 0001 0010

      // 字节13: 单次距离低位 (1234经过mapDistanceToRule后effectiveNumber=123, 123%256=123)
      expect(byte13).toBe(123)

      // 字节14: [7,6]单次距离高位=0, [5,4]总距离单位=2, [3,2]保留位=0, [1,0]总距离高位=2
      // 123经过mapDistanceToRule后effectiveNumber=123, 123/256=0; 567经过mapDistanceToRule后effectiveNumber=567, 567/256=2
      expect(byte14).toBe(34) // 0010 0010

      // 字节15: 总距离低位 (56789经过mapDistanceToRule后effectiveNumber=567, 567%256=55)
      expect(byte15).toBe(55)
    })
  })

  describe('stopSendNavigationProgress', () => {
    it('应该清除导航数据并关闭镜像', () => {
      // 先设置一些导航数据
      const mockData = {
        bannerInstructions: JSON.stringify({
          primary: {
            type: 'turn',
            text: 'Turn left',
            components: [{ text: 'Turn left', type: 'text' }],
            modifier: 'left'
          },
          distanceAlongGeometry: 100,
          drivingSide: 'right'
        }),
        stepDistanceRemaining: 100,
        distanceRemaining: 1000
      }

      navigation.sendNavigationProgress(mockData)

      // 验证数据已设置
      expect(navigation.writeData.value[12]).not.toBe(0)
      expect(navigation.writeData.value[13]).not.toBe(0)
      expect(navigation.writeData.value[14]).not.toBe(0)
      expect(navigation.writeData.value[15]).not.toBe(0)

      // 停止导航
      navigation.stopSendNavigationProgress()

      // 验证数据已清除
      expect(navigation.writeData.value[12]).toBe(0)
      expect(navigation.writeData.value[13]).toBe(0)
      expect(navigation.writeData.value[14]).toBe(0)
      expect(navigation.writeData.value[15]).toBe(0)
    })
  })

  describe('镜像控制', () => {
    it('应该正确打开镜像', () => {
      navigation.openMirror()

      // 验证第7位被设置 (0x80)
      expect(navigation.writeData.value[12] & 0x80).toBe(0x80)
    })

    it('应该正确关闭镜像', () => {
      // 先打开镜像
      navigation.openMirror()
      expect(navigation.writeData.value[12] & 0x80).toBe(0x80)

      // 关闭镜像
      navigation.closeMirror()

      // 验证第7位被清除
      expect(navigation.writeData.value[12] & 0x80).toBe(0)
    })
  })
})
