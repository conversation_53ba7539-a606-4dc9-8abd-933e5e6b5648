import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useNavigation } from '@/hooks/useNavigation'

// Mock CapacitorKtService
vi.mock('capacitor-kt-service', () => ({
  CapacitorKtService: {
    getMirrorState: vi.fn(),
    getCurrentBluetoothSendData: vi.fn()
  }
}))

describe('镜像状态修复验证', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确处理镜像开启状态', async () => {
    const { CapacitorKtService } = await import('capacitor-kt-service')
    
    // Mock Native端镜像开启状态
    vi.mocked(CapacitorKtService.getMirrorState).mockResolvedValue({
      enabled: true
    })
    
    // Mock Native端蓝牙数据（镜像位已设置 + 左转方向1 + 距离单位0）
    // 字节12: 0x80(镜像位) | 0x01(左转方向) | 0x00(距离单位) = 0x81
    vi.mocked(CapacitorKtService.getCurrentBluetoothSendData).mockResolvedValue({
      success: true,
      data: [
        0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11
        0x81, 0x00, 0x00, 0x00, 0x00, // 12-16 (字节12: 镜像位+左转方向)
        0x0e // 17
      ]
    })

    const navigation = useNavigation()
    
    // 创建测试数据
    const testData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: 'turn',
          text: 'Turn left',
          components: [{ text: 'Turn left', type: 'text' }],
          modifier: 'left'
        },
        distanceAlongGeometry: 1000,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: 150,
      distanceRemaining: 1000
    }

    // 重置数据
    navigation.writeData.value = [
      0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11
      0x00, 0x00, 0x00, 0x00, 0x00, // 12-16
      0x0e // 17
    ]

    // 获取Native端镜像状态
    const mirrorState = await CapacitorKtService.getMirrorState()
    
    // 根据Native端镜像状态设置Web端镜像位
    if (mirrorState.enabled) {
      navigation.openMirror()
    }
    
    // 发送导航数据
    navigation.sendNavigationProgress(testData)
    
    // 验证字节12的镜像位已正确设置
    expect(navigation.writeData.value[12] & 0x80).toBe(0x80)
    
    // 获取Native端数据进行比较
    const nativeDataResult = await CapacitorKtService.getCurrentBluetoothSendData()
    
    // 验证数据一致性
    expect(navigation.writeData.value[12]).toBe(nativeDataResult.data[12])
  })

  it('应该正确处理镜像关闭状态', async () => {
    const { CapacitorKtService } = await import('capacitor-kt-service')
    
    // Mock Native端镜像关闭状态
    vi.mocked(CapacitorKtService.getMirrorState).mockResolvedValue({
      enabled: false
    })
    
    // Mock Native端蓝牙数据（镜像位未设置 + 右转方向2 + 距离单位0）
    // 字节12: 0x00(镜像位) | 0x02(右转方向) | 0x00(距离单位) = 0x02
    vi.mocked(CapacitorKtService.getCurrentBluetoothSendData).mockResolvedValue({
      success: true,
      data: [
        0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11
        0x02, 0x00, 0x00, 0x00, 0x00, // 12-16 (字节12: 右转方向)
        0x0e // 17
      ]
    })

    const navigation = useNavigation()
    
    // 创建测试数据
    const testData = {
      bannerInstructions: JSON.stringify({
        primary: {
          type: 'turn',
          text: 'Turn right',
          components: [{ text: 'Turn right', type: 'text' }],
          modifier: 'right'
        },
        distanceAlongGeometry: 500,
        drivingSide: 'right'
      }),
      stepDistanceRemaining: 100,
      distanceRemaining: 500
    }

    // 重置数据
    navigation.writeData.value = [
      0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11
      0x00, 0x00, 0x00, 0x00, 0x00, // 12-16
      0x0e // 17
    ]

    // 获取Native端镜像状态
    const mirrorState = await CapacitorKtService.getMirrorState()
    
    // 根据Native端镜像状态设置Web端镜像位
    if (mirrorState.enabled) {
      navigation.openMirror()
    }
    
    // 发送导航数据
    navigation.sendNavigationProgress(testData)
    
    // 验证字节12的镜像位未设置
    expect(navigation.writeData.value[12] & 0x80).toBe(0x00)
    
    // 获取Native端数据进行比较
    const nativeDataResult = await CapacitorKtService.getCurrentBluetoothSendData()
    
    // 验证数据一致性
    expect(navigation.writeData.value[12]).toBe(nativeDataResult.data[12])
  })
}) 