import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest'
import { useNavigation } from '../../src/hooks/useNavigation'
import { useNativeBluetoothMessage } from '../../src/hooks/useNativeBluetoothMessage'
import { useSetting } from '../../src/hooks/useSetting'

// Mock dependencies
vi.mock('@ionic/core', () => ({
  isPlatform: vi.fn().mockReturnValue(false)
}))

vi.mock('@ionic/vue', () => ({
  isPlatform: vi.fn().mockReturnValue(false)
}))

vi.mock('capacitor-kt-service', () => ({
  CapacitorKtService: {
    startBluetoothForegroundService: vi.fn(),
    stopBluetoothForegroundService: vi.fn(),
    startNativeBluetoothSending: vi.fn(),
    stopNativeBluetoothSending: vi.fn(),
    updateBluetoothSendData: vi.fn(),
    reconnectBluetoothDevice: vi.fn(),
    getBluetoothSendingStats: vi.fn().mockResolvedValue({
      totalSent: 0,
      successCount: 0,
      errorCount: 0,
      averageInterval: 0,
      lastError: null,
      isConnected: false
    }),
    isNativeBluetoothSending: vi.fn().mockResolvedValue({
      isActive: false,
      lastSendTime: 0,
      sendCount: 0,
      errorCount: 0
    }),
    addListener: vi.fn().mockResolvedValue({ remove: vi.fn() }),
    updateBluetoothNotification: vi.fn()
  }
}))

vi.mock('@capacitor-community/bluetooth-le', () => ({
  numberToUUID: vi.fn().mockImplementation((num) => `0000${num.toString(16)}-0000-1000-8000-00805f9b34fb`),
  dataViewToNumbers: vi.fn().mockImplementation((dataView) => Array.from(new Uint8Array(dataView.buffer)))
}))

// Mock stores
vi.mock('../../src/store/useDashboardStore', () => ({
  useDashboardStore: vi.fn(() => ({
    setElectricQuantity: vi.fn(),
    setSingleTime: vi.fn(),
    setSpeed: vi.fn(),
    setSingleMileage: vi.fn(),
    setTotalMileage: vi.fn(),
    setAssistance: vi.fn(),
    setRegenative: vi.fn(),
    setUndervoltage: vi.fn(),
    setReverse: vi.fn(),
    setTurnRight: vi.fn(),
    setTurnLeft: vi.fn(),
    setThrottle: vi.fn(),
    setCruise: vi.fn(),
    setBrake: vi.fn()
  }))
}))

vi.mock('../../src/store/useSettingStore', () => ({
  useSettingStore: vi.fn(() => ({
    candidateParam: { value: 1000 },
    getDisplayType: { value: 'kilometer' },
    dimension: { value: 26 }
  }))
}))

vi.mock('../../src/store/useBleStore', () => ({
  useBleStore: vi.fn(() => ({
    connectedDevice: {
      deviceId: 'test-device-id',
      isPaired: true
    }
  }))
}))

vi.mock('../../src/store/useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    setErrorCode: vi.fn()
  }))
}))

vi.mock('../../src/hooks/useToast', () => ({
  useToast: vi.fn(() => ({
    presentToast: vi.fn()
  }))
}))

vi.mock('../../src/hooks/useBluetooth-le', () => ({
  useBluetoothLe: vi.fn(() => ({
    startNotification: vi.fn(),
    stopNotification: vi.fn(),
    disConnectBle: vi.fn()
  }))
}))

vi.mock('../../src/hooks/useSetting', () => ({
  useSetting: vi.fn(() => ({
    writeData: { value: [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e] },
    updateSetting: vi.fn()
  }))
}))

vi.mock('pinia', () => ({
  storeToRefs: vi.fn().mockImplementation((store) => {
    if (typeof store === 'function') {
      const storeInstance = store()
      return Object.keys(storeInstance).reduce((refs, key) => {
        refs[key] = { value: storeInstance[key] }
        return refs
      }, {} as any)
    }
    return store
  })
}))

describe('蓝牙数据比对测试', () => {
  let navigation: ReturnType<typeof useNavigation>
  let nativeBluetoothMessage: ReturnType<typeof useNativeBluetoothMessage>
  
  // 定义测试用的初始WriteData
  const createInitialWriteData = () => [
    0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32, // 0-11 与控制器通讯的协议
    0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [12，13，14，15，16]
    0x0e //17 截止位
  ]

  // 测试用的导航数据
  const createTestNavigationData = (
    type: string = 'turn',
    modifier: string = 'left',
    stepDistance: number = 150,
    totalDistance: number = 1000
  ) => ({
    bannerInstructions: JSON.stringify({
      primary: {
        type,
        text: `${type} ${modifier}`,
        components: [{ text: `${type} ${modifier}`, type: 'text' }],
        modifier
      },
      distanceAlongGeometry: totalDistance,
      drivingSide: 'right'
    }),
    stepDistanceRemaining: stepDistance,
    distanceRemaining: totalDistance
  })

  beforeAll(() => {
    console.log('开始运行蓝牙数据比对测试套件')
  })

  beforeEach(() => {
    vi.clearAllMocks()
    navigation = useNavigation()
    nativeBluetoothMessage = useNativeBluetoothMessage()
    
    // 重置writeData到初始状态
    navigation.writeData.value = createInitialWriteData()
  })

  describe('数据一致性比对', () => {
    it('应该确保useNavigation生成的writeData可以被native蓝牙服务正确使用', () => {
      // 1. 使用useNavigation处理导航数据
      const testData = createTestNavigationData('turn', 'left', 150, 1000)
      navigation.sendNavigationProgress(testData)
      
      // 2. 获取useNavigation生成的writeData
      const navigationWriteData = [...navigation.writeData.value]
      
      // 3. 验证关键字节的数据结构
      // 字节12: [7]镜像位, [6]保留位, [5,4]单次距离单位, [3,2,1,0]方向
      const byte12 = navigationWriteData[12]
      const direction = byte12 & 0x0f // [3,2,1,0]
      const singleDistanceUnit = (byte12 >> 4) & 0x03 // [5,4]
      const reserved = (byte12 >> 6) & 0x01 // [6]
      const mirror = (byte12 >> 7) & 0x01 // [7]
      
      expect(direction).toBe(1) // 左转
      expect(singleDistanceUnit).toBe(0) // 150 < 999，使用个位显示
      expect(reserved).toBe(0) // 保留位应该为0
      expect(mirror).toBe(0) // 镜像位初始为0
      
      // 字节13: 单次距离低位
      expect(navigationWriteData[13]).toBe(150) // 150 % 256 = 150
      
      // 字节14: [7,6]单次距离高位, [5,4]总距离单位, [3,2]保留位, [1,0]总距离高位
      const byte14 = navigationWriteData[14]
      const singleHigh = (byte14 >> 6) & 0x03 // [7,6]
      const totalDistanceUnit = (byte14 >> 4) & 0x03 // [5,4]
      const reserved14 = (byte14 >> 2) & 0x03 // [3,2]
      const totalHigh = byte14 & 0x03 // [1,0]
      
      expect(singleHigh).toBe(0) // 150 / 256 = 0
      expect(totalDistanceUnit).toBe(1) // 1000 需要十位显示
      expect(reserved14).toBe(0) // 保留位应该为0
      expect(totalHigh).toBe(0) // 100 / 256 = 0
      
      // 字节15: 总距离低位
      expect(navigationWriteData[15]).toBe(100) // 100 % 256 = 100
      
      // 字节16: 校验值
      const expectedChecksum = 
        navigationWriteData[1] ^
        navigationWriteData[2] ^
        navigationWriteData[3] ^
        navigationWriteData[4] ^
        navigationWriteData[6] ^
        navigationWriteData[7] ^
        navigationWriteData[8] ^
        navigationWriteData[9] ^
        navigationWriteData[10] ^
        navigationWriteData[11] ^
        navigationWriteData[12] ^
        navigationWriteData[13] ^
        navigationWriteData[14] ^
        navigationWriteData[15]
      
      expect(navigationWriteData[16]).toBe(expectedChecksum)
      
      console.log('✅ useNavigation生成的writeData结构验证通过')
      console.log('导航数据字节:', {
        byte12: `0x${byte12.toString(16).padStart(2, '0')}`,
        byte13: navigationWriteData[13],
        byte14: `0x${byte14.toString(16).padStart(2, '0')}`,
        byte15: navigationWriteData[15],
        byte16: navigationWriteData[16]
      })
    })

    it('应该验证不同导航指令的数据生成一致性', () => {
      const testCases = [
        { type: 'turn', modifier: 'left', expected: 1, name: '左转' },
        { type: 'turn', modifier: 'right', expected: 2, name: '右转' },
        { type: 'continue', modifier: 'straight', expected: 3, name: '直行' },
        { type: 'turn', modifier: 'uturn', expected: 4, name: '调头' },
        { type: 'turn', modifier: 'slight left', expected: 5, name: '稍左转' },
        { type: 'turn', modifier: 'slight right', expected: 6, name: '稍右转' },
        { type: 'turn', modifier: 'sharp left', expected: 1, name: '急左转' },
        { type: 'turn', modifier: 'sharp right', expected: 2, name: '急右转' },
        { type: 'arrive', modifier: 'straight', expected: 9, name: '到达目的地' }
      ]

      testCases.forEach(({ type, modifier, expected, name }) => {
        // 重置writeData
        navigation.writeData.value = createInitialWriteData()
        
        const testData = createTestNavigationData(type, modifier, 100, 500)
        navigation.sendNavigationProgress(testData)
        
        const direction = navigation.writeData.value[12] & 0x0f
        expect(direction).toBe(expected)
        
        console.log(`✅ ${name} (${type}/${modifier}) -> 方向代码: ${expected}`)
      })
    })

    it('应该验证距离规则映射的正确性', () => {
      const distanceTestCases = [
        { distance: 50, expectedRule: 0, expectedValue: 50, name: '小距离-个位显示' },
        { distance: 995, expectedRule: 0, expectedValue: 995, name: '个位显示边界' },
        { distance: 1000, expectedRule: 1, expectedValue: 100, name: '十位显示起始' },
        { distance: 9990, expectedRule: 1, expectedValue: 999, name: '十位显示边界' },
        { distance: 10000, expectedRule: 2, expectedValue: 100, name: '百位显示起始' },
        { distance: 99900, expectedRule: 2, expectedValue: 999, name: '百位显示边界' },
        { distance: 100000, expectedRule: 3, expectedValue: 100, name: '千位显示起始' }
      ]

      distanceTestCases.forEach(({ distance, expectedRule, expectedValue, name }) => {
        // 重置writeData
        navigation.writeData.value = createInitialWriteData()
        
        const testData = createTestNavigationData('turn', 'left', distance, distance * 2)
        navigation.sendNavigationProgress(testData)
        
        // 验证单次距离规则
        const singleDistanceRule = (navigation.writeData.value[12] >> 4) & 0x03
        const singleHigh = (navigation.writeData.value[14] >> 6) & 0x03
        const singleLow = navigation.writeData.value[13]
        const actualValue = singleHigh * 256 + singleLow
        
        expect(singleDistanceRule).toBe(expectedRule)
        expect(actualValue).toBe(expectedValue)
        
        console.log(`✅ ${name}: 距离${distance} -> 规则${expectedRule}, 值${expectedValue}`)
      })
    })

    it('应该验证镜像位控制的正确性', () => {
      const testData = createTestNavigationData('turn', 'left', 100, 500)
      
      // 初始状态，镜像位应该为0
      navigation.sendNavigationProgress(testData)
      expect((navigation.writeData.value[12] >> 7) & 0x01).toBe(0)
      
      // 打开镜像
      navigation.openMirror()
      expect((navigation.writeData.value[12] >> 7) & 0x01).toBe(1)
      
      // 发送导航数据后，镜像位应该保持
      navigation.sendNavigationProgress(testData)
      expect((navigation.writeData.value[12] >> 7) & 0x01).toBe(1)
      
      // 关闭镜像
      navigation.closeMirror()
      expect((navigation.writeData.value[12] >> 7) & 0x01).toBe(0)
      
      console.log('✅ 镜像位控制验证通过')
    })

    it('应该验证校验值计算的正确性', () => {
      const testData = createTestNavigationData('turn', 'right', 520, 12345)
      navigation.sendNavigationProgress(testData)
      
      // 手动计算校验值
      const manualChecksum = 
        navigation.writeData.value[1] ^
        navigation.writeData.value[2] ^
        navigation.writeData.value[3] ^
        navigation.writeData.value[4] ^
        navigation.writeData.value[6] ^
        navigation.writeData.value[7] ^
        navigation.writeData.value[8] ^
        navigation.writeData.value[9] ^
        navigation.writeData.value[10] ^
        navigation.writeData.value[11] ^
        navigation.writeData.value[12] ^
        navigation.writeData.value[13] ^
        navigation.writeData.value[14] ^
        navigation.writeData.value[15]
      
      expect(navigation.writeData.value[16]).toBe(manualChecksum)
      
      console.log('✅ 校验值计算验证通过')
      console.log('计算得到的校验值:', manualChecksum)
    })

    it('应该验证完整的数据包结构', () => {
      const testData = createTestNavigationData('turn', 'left', 1234, 56789)
      navigation.sendNavigationProgress(testData)
      
      const writeData = navigation.writeData.value
      
      // 验证数据包长度
      expect(writeData.length).toBe(18)
      
      // 验证固定协议部分（0-11字节）不变
      const expectedProtocol = [0xf, 0x5, 0xf5, 0x58, 0x29, 0xD4, 0x38, 0xca, 0x84, 0x14, 0x05, 0x32]
      for (let i = 0; i < 12; i++) {
        expect(writeData[i]).toBe(expectedProtocol[i])
      }
      
      // 验证导航数据部分（12-16字节）
      expect(writeData[12]).toBeGreaterThan(0) // 包含方向和距离单位信息
      expect(writeData[13]).toBeGreaterThanOrEqual(0) // 单次距离低位
      expect(writeData[14]).toBeGreaterThanOrEqual(0) // 距离高位和单位信息
      expect(writeData[15]).toBeGreaterThanOrEqual(0) // 总距离低位
      expect(writeData[16]).toBeGreaterThanOrEqual(0) // 校验值
      
      // 验证截止位（17字节）不变
      expect(writeData[17]).toBe(0x0e)
      
      console.log('✅ 完整数据包结构验证通过')
      console.log('完整writeData:', writeData.map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '))
    })
  })

  describe('边界情况测试', () => {
    it('应该正确处理最大距离值', () => {
      const maxDistance = 999999 // 接近最大可能的距离
      const testData = createTestNavigationData('turn', 'left', maxDistance, maxDistance)
      
      expect(() => {
        navigation.sendNavigationProgress(testData)
      }).not.toThrow()
      
      // 验证使用千位显示规则
      const rule = (navigation.writeData.value[12] >> 4) & 0x03
      expect(rule).toBe(3) // 千位显示
      
      console.log('✅ 最大距离值处理验证通过')
    })

    it('应该正确处理零距离值', () => {
      const testData = createTestNavigationData('arrive', 'straight', 0, 0)
      
      navigation.sendNavigationProgress(testData)
      
      // 验证到达目的地指令
      expect(navigation.writeData.value[12] & 0x0f).toBe(9)
      expect(navigation.writeData.value[13]).toBe(0)
      expect(navigation.writeData.value[15]).toBe(0)
      
      console.log('✅ 零距离值处理验证通过')
    })

    it('应该正确处理无效的导航数据', () => {
      const invalidData = {
        bannerInstructions: null,
        stepDistanceRemaining: 100,
        distanceRemaining: 500
      }
      
      // 应该不抛出错误，并保持原始状态
      expect(() => {
        navigation.sendNavigationProgress(invalidData)
      }).not.toThrow()
      
      // 验证导航数据部分保持为0
      expect(navigation.writeData.value[12]).toBe(0)
      expect(navigation.writeData.value[13]).toBe(0)
      expect(navigation.writeData.value[14]).toBe(0)
      expect(navigation.writeData.value[15]).toBe(0)
      
      console.log('✅ 无效导航数据处理验证通过')
    })
  })

  describe('Native蓝牙兼容性', () => {
    it('应该确保生成的writeData格式符合Native蓝牙服务期望', () => {
      const testData = createTestNavigationData('turn', 'right', 500, 2500)
      navigation.sendNavigationProgress(testData)
      
      const writeData = navigation.writeData.value
      
      // 验证数据类型：所有元素都应该是0-255范围内的整数
      writeData.forEach((byte, index) => {
        expect(byte).toBeGreaterThanOrEqual(0)
        expect(byte).toBeLessThanOrEqual(255)
        expect(Number.isInteger(byte)).toBe(true)
      })
      
      // 验证关键字节的位结构
      const byte12 = writeData[12]
      const byte14 = writeData[14]
      
      // 字节12的各位都应该在有效范围内
      expect(byte12 & 0x0f).toBeLessThanOrEqual(15) // 方向位 [3,2,1,0]
      expect((byte12 >> 4) & 0x03).toBeLessThanOrEqual(3) // 距离单位位 [5,4]
      
      // 字节14的各位都应该在有效范围内
      expect((byte14 >> 6) & 0x03).toBeLessThanOrEqual(3) // 单次距离高位 [7,6]
      expect((byte14 >> 4) & 0x03).toBeLessThanOrEqual(3) // 总距离单位 [5,4]
      expect(byte14 & 0x03).toBeLessThanOrEqual(3) // 总距离高位 [1,0]
      
      console.log('✅ Native蓝牙服务兼容性验证通过')
      console.log('writeData字节格式检查完成，所有字节都在0-255范围内')
    })

    it('应该验证校验和的正确性确保数据完整性', () => {
      const testData = createTestNavigationData('turn', 'left', 777, 8888)
      navigation.sendNavigationProgress(testData)
      
      const writeData = navigation.writeData.value
      
      // 重新计算校验和
      let calculatedChecksum = 0
      const checksumBytes = [1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
      
      checksumBytes.forEach(index => {
        calculatedChecksum ^= writeData[index]
      })
      
      expect(writeData[16]).toBe(calculatedChecksum)  
      
      // 模拟数据传输损坏的情况
      const corruptedData = [...writeData]
      corruptedData[13] = corruptedData[13] ^ 0x01 // 翻转一个位
      
      // 重新计算损坏数据的校验和
      let corruptedChecksum = 0
      checksumBytes.forEach(index => {
        corruptedChecksum ^= corruptedData[index]
      })
      
      // 损坏的数据应该产生不同的校验和
      expect(corruptedChecksum).not.toBe(writeData[16])
      
      console.log('✅ 校验和完整性验证通过')
      console.log('原始校验和:', writeData[16])
      console.log('损坏数据校验和:', corruptedChecksum)
    })
  })
}) 