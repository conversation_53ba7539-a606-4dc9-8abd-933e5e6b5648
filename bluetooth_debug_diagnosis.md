# 蓝牙数据更新问题诊断指南

## 问题描述
- **接收到的数据**: `[15, 5, 245, 88, 41, 212, 56, 202, 132, 20, 5, 50, 0, 0, 0, 0, 212, 14]`
- **串口实际数据**: `0F 05 F6 58 29 D7 38 CA 84 14 05 32 00 00 00 00 D7 0E`
- **问题现象**: updateBluetoothSendData更新数据失败，发送的仍是旧数据

## 数据分析结果
经过分析，接收到的数据与期望的串口数据**完全匹配**，这表明问题不在数据本身。

## 可能的问题原因

### 1. 线程同步问题
- `updateSendData`方法虽然添加了`synchronized`，但可能存在其他同步问题
- 定时发送任务可能在数据更新的同时正在读取数据

### 2. 数据更新时机问题
- 数据更新请求可能在蓝牙连接断开时发送
- 服务可能还未完全初始化就收到数据更新请求

### 3. Intent传递问题
- Intent中的数据可能在传递过程中丢失或损坏
- 服务可能没有正确处理Intent

### 4. 服务状态问题
- BluetoothForegroundService可能未正常运行
- NativeBluetoothManager可能为null

## 诊断步骤

### 第一步：检查日志流程
使用以下命令监控完整的数据更新流程：

```bash
# 清除现有日志
adb logcat -c

# 监控相关日志
adb logcat | grep -E "(🌐|🔄|📝|📤|🔧|updateBluetoothSendData|UPDATE_BLUETOOTH_DATA)"
```

### 第二步：验证数据传递链路
数据更新的完整链路应该是：
1. **JS端调用** → `CapacitorKtService.updateBluetoothSendData`
2. **Plugin接收** → 记录 "🌐 从JS端接收到数据更新"
3. **Intent发送** → 记录 "✅ 数据更新Intent已发送"
4. **Service接收** → 记录 "🔄 收到数据更新请求"
5. **Manager更新** → 记录 "📝 蓝牙数据已更新"
6. **发送使用** → 记录 "📤 发送数据转换"

### 第三步：检查时间戳一致性
确保以下时间戳在合理范围内（几毫秒内）：
- Plugin收到JS请求时间戳
- Service收到更新请求时间戳
- 数据更新时间戳
- 发送时间戳

### 第四步：验证数据一致性
检查日志中的数据是否在整个链路中保持一致：
- JS端发送的数据
- Plugin接收的数据
- Service接收的数据
- Manager更新的数据
- 实际发送的数据

## 修复建议

### 已实施的修复
1. ✅ 添加了`synchronized`关键字到`updateSendData`方法
2. ✅ 在发送任务中使用同步块获取当前数据
3. ✅ 增强了所有关键点的调试日志
4. ✅ 添加了时间戳跟踪

### 待验证的修复
1. **数据更新后强制刷新**: 在数据更新后立即触发一次发送
2. **添加数据版本号**: 给每次数据更新添加版本号，确保使用最新数据
3. **增加更新确认机制**: 数据更新后返回确认消息

## 测试方法

### 1. 实时监控测试
```javascript
// 在浏览器控制台运行
await CapacitorKtService.updateBluetoothSendData({ 
  data: [15, 5, 245, 88, 41, 212, 56, 202, 132, 20, 5, 50, 0, 0, 0, 0, 212, 14] 
});
```

### 2. 数据验证测试
```javascript
// 更新数据后立即获取当前数据
await CapacitorKtService.updateBluetoothSendData({ data: testData });
setTimeout(async () => {
  const current = await CapacitorKtService.getCurrentBluetoothSendData();
  console.log('当前数据:', current);
}, 1000);
```

### 3. 连续更新测试
```javascript
// 连续更新不同数据，观察是否都能正确更新
const testSets = [
  [1, 2, 3, 4, 5],
  [10, 20, 30, 40, 50],
  [100, 200, 255, 0, 128]
];

for (let i = 0; i < testSets.length; i++) {
  console.log(`测试数据集 ${i + 1}:`, testSets[i]);
  await CapacitorKtService.updateBluetoothSendData({ data: testSets[i] });
  await new Promise(resolve => setTimeout(resolve, 2000));
}
```

## 预期结果
如果修复成功，应该看到：
1. 完整的日志链路，所有步骤都有对应日志
2. 时间戳连续，没有异常延迟
3. 数据在整个链路中保持一致
4. 串口接收到的数据与updateBluetoothSendData发送的数据一致

## 故障排除

### 如果仍然发送旧数据
1. 检查是否有多个蓝牙发送任务在运行
2. 确认服务是否正确重启
3. 验证数据是否真的被更新到sendData变量

### 如果日志不完整
1. 确认应用的日志级别设置
2. 检查是否有日志被过滤
3. 确认所有相关的TAG都在日志输出中

### 如果数据传递中断
1. 检查Intent是否正确创建和发送
2. 确认服务是否正在运行
3. 验证权限和配置是否正确