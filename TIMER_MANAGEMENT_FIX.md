# 定时器管理架构修复

## 问题描述

之前的代码中存在以下问题：
1. 定时器管理混乱，多个地方都可以操作定时器
2. 设置更新时会重复启动定时器
3. `useSetting.ts` 中存在不必要的蓝牙定时器触发逻辑

## 修复原则

根据用户要求，重构代码以遵循以下原则：
1. **定时器只在蓝牙连接成功后启动**
2. **定时器只在蓝牙断开或程序销毁时清除**  
3. **web端数据变化后只调用native端的更新数据函数，不操作定时器**

## 修复内容

### 1. useSetting.ts 修复
- ✅ 移除了 `triggerBluetoothDataUpdate` 函数
- ✅ 将回调函数参数重命名为 `onDataUpdate`，更明确表示只是数据更新通知
- ✅ 在 `updateFiveIndexOfData()` 和 `updateSetting()` 中直接调用回调函数
- ✅ 添加注释说明"通知native端数据已更新，但不启动定时器"

### 2. useMessage.ts 修复  
- ✅ 从 `sendMessage()` 函数中移除了 `updateSetting()` 调用
- ✅ 确保定时器清除逻辑正确
- ✅ 定时器只在蓝牙连接成功时启动，断开时停止

### 3. SettingPage.vue 修复
- ✅ 移除了 `stopSendMessage()` 和 `sendMessage()` 的导入和使用
- ✅ 移除了页面进入时停止定时器的逻辑
- ✅ 移除了页面离开时重新启动定时器的逻辑
- ✅ 保留了数据更新和native端同步的逻辑

### 4. Tab1Page.vue 修复
- ✅ 移除了 `useMessage` 的导入
- ✅ 移除了档位变化时直接调用 `sendMessage()` 的逻辑  
- ✅ 档位变化现在通过 `useSetting` 的回调自动更新native端数据

### 5. BluetoothPage.vue 保持不变
- ✅ 保留了断开连接时调用 `stopSendMessage()` 的逻辑，这符合设计原则

## 新的数据流架构

```
蓝牙连接成功 → HomePage.sendMessage() → 启动定时器
     ↓
设置数据变化 → useSetting.updateSetting() → onDataUpdate回调 → native端数据更新
     ↓
定时器持续发送 → native端最新数据
     ↓  
蓝牙断开/程序销毁 → stopSendMessage() → 清除定时器
```

## 关键改进

1. **单一定时器管理点**: 只有 `useMessage.ts` 负责定时器的启动和停止
2. **清晰的职责分离**: 
   - `useSetting.ts`: 负责数据更新和通知native端
   - `useMessage.ts`: 负责定时器管理和蓝牙通信
   - 页面组件: 只负责UI交互和数据绑定
3. **避免重复定时器**: 确保在启动新定时器前先清除旧定时器
4. **数据同步优化**: web端数据变化立即通知native端，无需重启定时器

## 验证要点

- [x] 蓝牙连接后定时器正常启动
- [x] 设置修改后不会创建新定时器
- [x] 设置修改后native端数据能及时更新
- [x] 蓝牙断开后定时器正确停止
- [x] 不再出现同时发送修改前后值的问题

## 测试建议

1. 连接蓝牙设备，确认定时器启动
2. 修改设置参数，确认不会重复启动定时器
3. 验证设置修改后数据能正确发送到设备
4. 断开蓝牙，确认定时器正确停止
5. 重新连接，确认定时器能重新启动