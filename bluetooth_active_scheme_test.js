// 蓝牙活跃方案智能选择测试工具

/**
 * 蓝牙方案状态模拟器
 * 用于测试和验证活跃方案选择逻辑
 */
class BluetoothSchemeSimulator {
  constructor() {
    this.schemes = {
      traditional: { 
        active: false, 
        name: '传统WebView方案',
        priority: 1,
        updateMethod: 'updateSendDataCache'
      },
      native: { 
        active: false, 
        name: '原生蓝牙方案',
        priority: 2,
        updateMethod: 'updateNativeBluetoothData'
      },
      smart: { 
        active: false, 
        name: '智能蓝牙方案',
        priority: 3,
        updateMethod: 'updateSmartBluetoothData'
      }
    };
    
    this.callHistory = [];
    this.testScenarios = [];
  }

  /**
   * 设置方案状态
   */
  setSchemeState(scheme, active) {
    if (this.schemes[scheme]) {
      this.schemes[scheme].active = active;
      console.log(`📝 设置 ${this.schemes[scheme].name} 状态: ${active ? '活跃' : '非活跃'}`);
    }
  }

  /**
   * 获取当前活跃方案
   */
  getActiveScheme() {
    // 按优先级排序：智能 > 原生 > 传统
    const sortedSchemes = Object.entries(this.schemes)
      .filter(([_, config]) => config.active)
      .sort(([_, a], [__, b]) => b.priority - a.priority);
    
    return sortedSchemes.length > 0 ? sortedSchemes[0] : null;
  }

  /**
   * 模拟智能选择逻辑
   */
  simulateSmartSelection() {
    const activeScheme = this.getActiveScheme();
    const timestamp = Date.now();
    
    const result = {
      timestamp,
      activeScheme: activeScheme ? activeScheme[0] : null,
      activeSchemeInfo: activeScheme ? activeScheme[1] : null,
      allStates: { ...this.schemes },
      updateMethod: activeScheme ? activeScheme[1].updateMethod : null,
      shouldUpdate: !!activeScheme
    };
    
    this.callHistory.push(result);
    
    console.log('🔍 智能选择结果:', {
      活跃方案: result.activeScheme || '无',
      方案名称: result.activeSchemeInfo?.name || '无',
      更新方法: result.updateMethod || '无',
      是否更新: result.shouldUpdate ? '是' : '否'
    });
    
    return result;
  }

  /**
   * 创建测试场景
   */
  createTestScenarios() {
    this.testScenarios = [
      {
        name: '场景1：只有传统方案活跃',
        setup: () => {
          this.setSchemeState('traditional', true);
          this.setSchemeState('native', false);
          this.setSchemeState('smart', false);
        },
        expected: 'traditional'
      },
      {
        name: '场景2：只有原生方案活跃',
        setup: () => {
          this.setSchemeState('traditional', false);
          this.setSchemeState('native', true);
          this.setSchemeState('smart', false);
        },
        expected: 'native'
      },
      {
        name: '场景3：只有智能方案活跃',
        setup: () => {
          this.setSchemeState('traditional', false);
          this.setSchemeState('native', false);
          this.setSchemeState('smart', true);
        },
        expected: 'smart'
      },
      {
        name: '场景4：传统+原生方案同时活跃（应选原生）',
        setup: () => {
          this.setSchemeState('traditional', true);
          this.setSchemeState('native', true);
          this.setSchemeState('smart', false);
        },
        expected: 'native'
      },
      {
        name: '场景5：原生+智能方案同时活跃（应选智能）',
        setup: () => {
          this.setSchemeState('traditional', false);
          this.setSchemeState('native', true);
          this.setSchemeState('smart', true);
        },
        expected: 'smart'
      },
      {
        name: '场景6：所有方案都活跃（应选智能）',
        setup: () => {
          this.setSchemeState('traditional', true);
          this.setSchemeState('native', true);
          this.setSchemeState('smart', true);
        },
        expected: 'smart'
      },
      {
        name: '场景7：所有方案都不活跃',
        setup: () => {
          this.setSchemeState('traditional', false);
          this.setSchemeState('native', false);
          this.setSchemeState('smart', false);
        },
        expected: null
      }
    ];
  }

  /**
   * 运行所有测试场景
   */
  runAllTests() {
    console.log('🧪 开始运行蓝牙方案智能选择测试');
    console.log('='.repeat(50));
    
    this.createTestScenarios();
    const results = [];
    
    this.testScenarios.forEach((scenario, index) => {
      console.log(`\n📋 ${scenario.name}`);
      
      // 重置状态
      Object.keys(this.schemes).forEach(key => {
        this.schemes[key].active = false;
      });
      
      // 设置测试场景
      scenario.setup();
      
      // 执行智能选择
      const result = this.simulateSmartSelection();
      
      // 验证结果
      const passed = result.activeScheme === scenario.expected;
      const testResult = {
        scenario: scenario.name,
        expected: scenario.expected,
        actual: result.activeScheme,
        passed,
        result
      };
      
      results.push(testResult);
      
      console.log(`${passed ? '✅' : '❌'} 测试${passed ? '通过' : '失败'}`);
      if (!passed) {
        console.log(`   期望: ${scenario.expected || '无'}`);
        console.log(`   实际: ${result.activeScheme || '无'}`);
      }
    });
    
    // 输出测试总结
    const passedCount = results.filter(r => r.passed).length;
    const totalCount = results.length;
    
    console.log('\n' + '='.repeat(50));
    console.log(`📊 测试总结: ${passedCount}/${totalCount} 通过`);
    
    if (passedCount === totalCount) {
      console.log('🎉 所有测试都通过了！智能选择逻辑工作正常。');
    } else {
      console.log('⚠️ 部分测试失败，需要检查智能选择逻辑。');
    }
    
    return results;
  }

  /**
   * 模拟实际的updateBluetoothSendData调用计数
   */
  simulateUpdateCalls() {
    console.log('\n🔢 模拟 updateBluetoothSendData 调用计数测试');
    console.log('-'.repeat(40));
    
    const scenarios = [
      {
        name: '修复前：所有方案都尝试更新',
        calls: ['traditional.updateSendDataCache', 'native.updateNativeBluetoothData', 'smart.updateSmartBluetoothData'],
        actualUpdateCalls: 3 // 假设都会调用updateBluetoothSendData
      },
      {
        name: '修复后：只有活跃方案更新',
        calls: ['smart.updateSmartBluetoothData'], // 假设智能方案活跃
        actualUpdateCalls: 1
      }
    ];
    
    scenarios.forEach(scenario => {
      console.log(`\n📋 ${scenario.name}`);
      console.log(`   方法调用: ${scenario.calls.join(', ')}`);
      console.log(`   updateBluetoothSendData 调用次数: ${scenario.actualUpdateCalls}`);
      console.log(`   性能提升: ${scenario.actualUpdateCalls === 1 ? '✅ 最优' : '⚠️ 可优化'}`);
    });
  }

  /**
   * 生成调试命令
   */
  generateDebugCommands() {
    console.log('\n🛠️ 调试命令生成');
    console.log('-'.repeat(40));
    
    const commands = [
      '// 在浏览器控制台中运行以下命令来测试',
      '',
      '// 1. 创建测试实例',
      'const simulator = new BluetoothSchemeSimulator();',
      '',
      '// 2. 运行所有测试',
      'simulator.runAllTests();',
      '',
      '// 3. 模拟调用计数',
      'simulator.simulateUpdateCalls();',
      '',
      '// 4. 手动测试特定场景',
      'simulator.setSchemeState("smart", true);',
      'simulator.simulateSmartSelection();',
      '',
      '// 5. 查看调用历史',
      'console.table(simulator.callHistory);'
    ];
    
    commands.forEach(cmd => console.log(cmd));
  }

  /**
   * 获取调用历史统计
   */
  getCallStats() {
    const stats = {
      totalCalls: this.callHistory.length,
      successfulUpdates: this.callHistory.filter(call => call.shouldUpdate).length,
      failedUpdates: this.callHistory.filter(call => !call.shouldUpdate).length,
      schemeUsage: {}
    };
    
    this.callHistory.forEach(call => {
      if (call.activeScheme) {
        stats.schemeUsage[call.activeScheme] = (stats.schemeUsage[call.activeScheme] || 0) + 1;
      }
    });
    
    return stats;
  }
}

/**
 * 实际环境中的方案检测工具
 */
const createRealEnvironmentDetector = () => {
  return {
    async detectActiveScheme() {
      console.log('🔍 检测实际环境中的活跃蓝牙方案...');
      
      try {
        // 检查是否有CapacitorKtService（原生方案的标志）
        const hasNativeService = typeof CapacitorKtService !== 'undefined';
        
        // 检查平台
        const isAndroid = /android/i.test(navigator.userAgent);
        const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);
        
        // 检查Capacitor环境
        const hasCapacitor = typeof window.Capacitor !== 'undefined';
        
        const detection = {
          platform: isAndroid ? 'Android' : (isIOS ? 'iOS' : 'Web'),
          hasCapacitor,
          hasNativeService,
          timestamp: Date.now()
        };
        
        console.log('📊 环境检测结果:', detection);
        
        // 推荐方案
        let recommendedScheme = 'traditional';
        if (hasCapacitor && hasNativeService) {
          recommendedScheme = isAndroid ? 'smart' : 'traditional';
        }
        
        console.log(`💡 推荐使用方案: ${recommendedScheme}`);
        
        return { ...detection, recommendedScheme };
        
      } catch (error) {
        console.error('❌ 环境检测失败:', error);
        return { error: error.message };
      }
    }
  };
};

// 导出工具
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    BluetoothSchemeSimulator,
    createRealEnvironmentDetector
  };
}

// 在浏览器环境中添加到全局对象
if (typeof window !== 'undefined') {
  window.BluetoothSchemeTest = {
    BluetoothSchemeSimulator,
    createRealEnvironmentDetector
  };
}

// 立即运行演示
console.log('🚀 蓝牙活跃方案智能选择测试工具已加载');
console.log('使用方法：');
console.log('1. const simulator = new BluetoothSchemeSimulator();');
console.log('2. simulator.runAllTests();');
console.log('3. simulator.simulateUpdateCalls();');

// 如果在Node.js环境中，立即运行测试
if (typeof window === 'undefined') {
  const simulator = new BluetoothSchemeSimulator();
  simulator.runAllTests();
  simulator.simulateUpdateCalls();
  simulator.generateDebugCommands();
}