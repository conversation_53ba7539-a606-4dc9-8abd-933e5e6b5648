# 导航数据发送修复

## 问题描述

测试发现修改设置参数后，串口收到的数据中没有导航相关的数据：

1. **Web端控制台打印**: `start write origin data [0F, 05, F6, 58, 29, D7, 38, CA ,84, 14, 05, 32, 00, 00 ,00 ,00, D7 ,0E]`
2. **串口收到的数据**: 与Web端打印的数据一致（缺少导航数据）
3. **Native端数据**: 通过 `CapacitorKtService.getCurrentBluetoothSendData` 可以获取到包含导航的完整数据

## 根本原因

定时器发送的是Web端的 `writeData.value`，而不是Native端包含导航信息的完整数据。

```
问题流程：
设置更新 → useSetting.updateSetting() → 更新Web端writeData → 
定时器发送Web端数据（缺少导航） → 串口收到不完整数据
```

## 修复方案

### 1. 数据缓存机制

在 `useMessage.ts` 中引入数据缓存机制：
- 添加 `currentSendData` 变量缓存当前要发送的数据
- 创建 `updateSendDataCache()` 函数更新缓存
- 定时器发送缓存的数据，而不是直接发送Web端数据

### 2. 数据获取优化

`updateSendDataCache()` 函数的逻辑：
```typescript
// 尝试获取Native端完整数据（包含导航）
const nativeDataResult = await CapacitorKtService.getCurrentBluetoothSendData();
if (nativeDataResult.success && nativeDataResult.data) {
  currentSendData = [...nativeDataResult.data]; // 使用Native端完整数据
} else {
  currentSendData = [...writeData.value]; // 降级使用Web端数据
}
```

### 3. 数据同步触发

- **定时器启动时**: 初始化数据缓存
- **设置更新后**: Native端数据更新成功后，触发缓存更新
- **导航数据变化**: 通过事件监听触发缓存更新

## 修复的关键文件

### 1. useMessage.ts
```typescript
// ✅ 添加数据缓存变量
let currentSendData: number[] = [...writeData.value];

// ✅ 添加缓存更新函数
const updateSendDataCache = async () => {
  // 获取Native端完整数据逻辑
};

// ✅ 定时器发送缓存数据
sendDataFunction = async () => {
  await write(..., numbersToDataView(currentSendData));
};

// ✅ 暴露缓存更新函数
return { updateSendDataCache, ... };
```

### 2. useNativeBluetoothMessage.ts
```typescript
// ✅ 导入缓存更新函数
const { updateSendDataCache } = useMessage();

// ✅ 数据更新成功后触发缓存更新
if (!verificationSuccess) {
  // 处理失败
} else {
  console.log("✅ Native端蓝牙数据更新完成，更新发送缓存");
  await updateSendDataCache();
}
```

## 新的数据流架构

```
设置更新 → useSetting.updateSetting() → onDataUpdate回调 → 
useNativeBluetoothMessage.updateNativeBluetoothData() → 
Native端数据更新 → updateSendDataCache() → 
更新currentSendData缓存 → 定时器发送完整数据 → 串口收到完整数据
```

## 性能优化

1. **避免高频API调用**: 不在每次定时器执行时调用 `getCurrentBluetoothSendData`
2. **缓存机制**: 只在数据更新时才刷新缓存
3. **降级策略**: Native端数据获取失败时使用Web端数据

## 验证要点

- [x] 定时器发送的数据包含导航信息
- [x] 设置修改后串口能收到完整的数据
- [x] 导航数据变化后能及时反映到发送数据中
- [x] 性能不受影响（避免高频API调用）
- [x] 有降级机制保证系统稳定性

## 测试建议

1. 连接蓝牙设备，启动定时器
2. 查看控制台打印的发送数据是否包含导航信息
3. 修改设置参数，确认串口收到的数据包含最新设置
4. 触发导航数据更新，确认串口收到包含导航的数据
5. 测试Native端数据获取失败的降级情况

## 预期效果

修复后，串口应该收到类似这样的完整数据：
```
发送前: [0F, 05, F6, 58, 29, D7, 38, CA, 84, 14, 05, 32, 00, 00, 00, 00, D7, 0E]
发送后: [0F, 05, F6, 58, 29, D7, 38, CA, 84, 14, 05, 32, 12, 34, 56, 78, XX, XX]
                                                            ↑ 导航数据  ↑ 更新的校验和
```