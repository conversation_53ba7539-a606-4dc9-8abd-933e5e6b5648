# 蓝牙数据传输问题诊断与修复指南

## 问题概述

**现象1：数据不一致**
- 串口收到：`0F 05 F5 58 29 D4 38 CA 84 14 05 32 00 00 00 00 D4 0E`
- 控制台显示：`0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA, 0x84, 0x14, 0x65, 0x32, 0x81, 0x2B, 0xA1, 0x22, 0x9A, 0x0E`

**现象2：设置更改无效**
- 无论如何修改设置，发送的数据始终不变

## 诊断步骤

### 第一步：检查数据流向

1. **检查Vue端数据生成**
   - 打开浏览器开发者工具
   - 查看`useSetting.ts`中的`writeData`值
   - 确认`updateSetting()`是否正确执行

2. **检查Native端数据接收**
   - 查看Android日志：`adb logcat | grep -E "(NavigationManager|BluetoothForegroundService|CapacitorKtService)"`
   - 确认`updateBluetoothSendDataInternal`是否被调用
   - 确认`getCurrentBluetoothSendData`返回的数据

3. **检查蓝牙管理器状态**
   - 查看`NativeBluetoothManager`的`sendData`字段
   - 确认数据是否正确传递到蓝牙发送层

### 第二步：数据源优先级检查

根据`NAVIGATION_DATA_FIX_SUMMARY.md`，系统应该优先使用Vue端数据。检查：

1. **NavigationManager.kt第187行**
   ```kotlin
   val currentDataResult = ktService.getCurrentBluetoothSendDataSync()
   ```
   确认此方法是否返回正确的Vue端数据

2. **数据回退逻辑**
   如果Vue端数据获取失败，系统会使用串口接收数据，但这可能导致导航信息丢失

### 第三步：镜像状态检查

镜像状态影响字节12的第7位：
- 镜像开启：字节12 |= 0x80
- 镜像关闭：字节12 &= 0x7F

检查`getMirrorState()`返回值是否正确。

## 修复方案

### 方案1：强制使用Vue端数据

修改`NavigationManager.kt`，确保优先使用Vue端数据：

```kotlin
private fun sendNavigationDataToBluetooth(routeProgress: RouteProgress) {
    if (!isMirrorEnabled) return
    
    try {
        val ktService = CapacitorKtService.getInstance() ?: return
        
        // 🔧 强制优先使用Vue端数据
        var baseBluetoothData: IntArray? = null
        
        // 首先尝试获取Vue端数据
        try {
            val vueData = ktService.getCurrentBluetoothSendDataSync()
            if (vueData != null && vueData.size >= 18) {
                baseBluetoothData = vueData
                Log.d(tag, "✅ 使用Vue端数据作为基础")
            }
        } catch (e: Exception) {
            Log.e(tag, "❌ 获取Vue端数据失败: ${e.message}")
        }
        
        // 如果Vue端数据不可用，使用默认数据而不是串口数据
        if (baseBluetoothData == null) {
            Log.w(tag, "⚠️ Vue端数据不可用，使用默认基础数据")
            baseBluetoothData = intArrayOf(
                0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA,
                0x84, 0x14, 0x65, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E
            )
        }
        
        // 基于基础数据构建导航数据
        val navigationData = dataBuilder.modifyNavigationData(baseBluetoothData, routeProgress)
        
        // 设置镜像位
        if (isMirrorEnabled) {
            navigationData[12] = navigationData[12] or 0x80
        } else {
            navigationData[12] = navigationData[12] and 0x7F
        }
        
        // 重新计算校验和
        navigationData[16] = dataBuilder.calculateChecksum(navigationData)
        
        // 发送数据
        ktService.updateBluetoothSendDataInternal(navigationData)
        
        Log.d(tag, "🚀 发送导航数据: ${navigationData.joinToString { "0x%02X".format(it) }}")
        
    } catch (e: Exception) {
        Log.e(tag, "❌ 发送导航数据失败: ${e.message}", e)
    }
}
```

### 方案2：确保Vue端数据正确更新

在`useNativeBluetoothMessage.ts`中添加数据验证：

```typescript
const updateNativeBluetoothData = async () => {
  try {
    // 确保writeData是最新的
    if (!writeData.value || writeData.value.length < 18) {
      console.warn('⚠️ writeData无效，重新初始化')
      // 重新初始化writeData
      return
    }
    
    // 验证数据完整性
    const dataToSend = [...writeData.value]
    
    // 记录发送前的数据
    console.log('🚀 准备发送数据到Native:', dataToSend.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(', '))
    
    // 发送到Native端
    await CapacitorKtService.updateBluetoothSendData(dataToSend)
    
    // 验证发送结果
    const verificationResult = await CapacitorKtService.getCurrentBluetoothSendData()
    if (verificationResult.success) {
      console.log('✅ Native端数据验证成功:', verificationResult.data)
    } else {
      console.error('❌ Native端数据验证失败:', verificationResult.error)
    }
    
  } catch (error) {
    console.error('❌ 更新Native蓝牙数据失败:', error)
  }
}
```

### 方案3：添加数据同步检查

创建一个数据同步检查机制：

```typescript
// 在BluetoothDataComparisonPage.vue中添加
const checkDataConsistency = async () => {
  try {
    // 1. 获取Vue端数据
    const navigation = useNavigation()
    const vueData = [...navigation.writeData.value]
    
    // 2. 获取Native端数据
    const nativeResult = await CapacitorKtService.getCurrentBluetoothSendData()
    if (!nativeResult.success) {
      console.error('❌ 无法获取Native端数据:', nativeResult.error)
      return false
    }
    
    const nativeData = nativeResult.data
    
    // 3. 比较数据
    const isConsistent = JSON.stringify(vueData) === JSON.stringify(nativeData)
    
    if (!isConsistent) {
      console.warn('⚠️ 数据不一致！')
      console.log('Vue端:', vueData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(', '))
      console.log('Native端:', nativeData.map(b => `0x${b.toString(16).padStart(2, '0').toUpperCase()}`).join(', '))
      
      // 强制同步数据
      await CapacitorKtService.updateBluetoothSendData(vueData)
      console.log('🔄 已强制同步数据')
    }
    
    return isConsistent
  } catch (error) {
    console.error('❌ 数据一致性检查失败:', error)
    return false
  }
}
```

## 调试建议

### 1. 实时监控
在Android Studio中查看实时日志：
```bash
adb logcat | grep -E "(NavigationManager|BluetoothForegroundService|NativeBluetoothManager)" --color=always
```

### 2. 数据对比
使用`BluetoothDataComparisonPage.vue`页面进行实时数据对比测试

### 3. 强制刷新
如果数据卡住不更新，尝试：
1. 重启蓝牙发送服务
2. 重新连接蓝牙设备
3. 清除应用缓存

## 预期结果

修复后应该实现：
1. ✅ 串口收到的数据与控制台显示的数据完全一致
2. ✅ 设置更改后，发送的数据立即更新
3. ✅ 导航数据正确包含在发送的数据包中
4. ✅ 镜像开关状态正确反映在数据中

## 验证步骤

1. **设置更改测试**：修改任意设置参数，检查发送数据是否变化
2. **导航数据测试**：启动导航，检查字节12-15是否包含导航信息  
3. **镜像状态测试**：切换镜像开关，检查字节12的第7位是否变化
4. **数据一致性测试**：对比Vue端、Native端、串口三方数据是否一致