# Build Fast 开发版本配置

## 🎯 目标
为 `build:fast` 命令添加环境变量，使其打包成开发版本，提高构建速度。

## 🔧 修改内容

### 1. package.json 修改
```json
{
  "scripts": {
    "build:fast": "NODE_ENV=development vite build --mode development"
  }
}
```

**变更说明**：
- 添加 `NODE_ENV=development` 环境变量
- 添加 `--mode development` 参数，使用开发模式配置

### 2. vite.config.ts 优化
针对开发模式进行了以下优化：

#### 代码分割优化
```typescript
manualChunks: process.env.NODE_ENV === 'development' ? undefined : {
  // 开发模式不分割chunk，加快构建速度
  // 生产模式才进行代码分割优化
}
```

#### 压缩优化
```typescript
// 开发模式禁用压缩，加快构建速度
minify: process.env.NODE_ENV === 'development' ? false : 'terser'
```

#### 源码映射
```typescript
// 开发模式启用源码映射，便于调试
sourcemap: process.env.NODE_ENV === 'development'
```

### 3. 环境变量配置

#### .env.development
```bash
NODE_ENV=development
VITE_APP_TITLE=KT Smart (Dev)
VITE_APP_VERSION=dev
VITE_DEBUG=true
VITE_CONSOLE_LOG=true
VITE_BLUETOOTH_DEBUG=true
VITE_SOURCE_MAP=true
```

#### .env.production
```bash
NODE_ENV=production
VITE_APP_TITLE=KT Smart
VITE_APP_VERSION=1.0.0
VITE_DEBUG=false
VITE_CONSOLE_LOG=false
VITE_BLUETOOTH_DEBUG=false
VITE_SOURCE_MAP=false
```

### 4. .gitignore 更新
添加了环境变量文件的忽略规则：
```
.env.local
.env.*.local
```

## 🚀 使用方法

### 开发版本快速构建
```bash
npm run build:fast
```

### 生产版本构建
```bash
npm run build
```

### 调试命令（已更新）
```bash
# iOS 调试（使用开发版本）
npm run debug:ios

# Android 调试（使用开发版本）
npm run debug:android
```

## 📊 性能对比

### 开发模式 (build:fast)
- ✅ **无代码分割** - 减少构建时间
- ✅ **无压缩** - 大幅提升构建速度
- ✅ **保留 console** - 便于调试
- ✅ **启用源码映射** - 便于调试
- ✅ **跳过类型检查** - 更快的构建

### 生产模式 (build)
- 🔧 **代码分割** - 优化加载性能
- 🔧 **Terser 压缩** - 减小包体积
- 🔧 **移除 console** - 清理生产代码
- 🔧 **禁用源码映射** - 保护源码
- 🔧 **TypeScript 检查** - 确保代码质量

## 🎯 适用场景

### build:fast (开发版本)
- 🔧 **本地调试** - 快速构建用于调试
- 🔧 **功能测试** - 快速验证功能
- 🔧 **开发迭代** - 频繁构建测试
- 🔧 **设备调试** - debug:ios / debug:android

### build (生产版本)
- 🚀 **正式发布** - 完整优化的生产包
- 🚀 **性能测试** - 测试最终性能
- 🚀 **质量检查** - 完整的类型检查

## 🔍 环境变量使用

在代码中可以通过以下方式使用环境变量：

```typescript
// 检查是否为开发模式
const isDev = import.meta.env.DEV;

// 获取应用标题
const appTitle = import.meta.env.VITE_APP_TITLE;

// 检查是否启用调试
const isDebugEnabled = import.meta.env.VITE_DEBUG === 'true';

// 检查是否启用蓝牙调试
const isBluetoothDebugEnabled = import.meta.env.VITE_BLUETOOTH_DEBUG === 'true';
```

## ✅ 验证方法

### 1. 构建速度验证
```bash
# 测试开发版本构建速度
time npm run build:fast

# 对比生产版本构建速度
time npm run build
```

### 2. 输出文件验证
```bash
# 开发版本 - 查看输出文件
npm run build:fast && ls -la dist/

# 生产版本 - 查看输出文件
npm run build && ls -la dist/
```

### 3. 功能验证
```bash
# 使用开发版本进行设备调试
npm run debug:android
npm run debug:ios
```

## 📝 注意事项

1. **开发版本文件较大** - 未压缩，包含调试信息
2. **生产版本更优化** - 压缩后体积更小，性能更好
3. **环境变量区分** - 不同模式使用不同的配置
4. **调试信息保留** - 开发版本保留 console 和源码映射

这个配置让您可以在开发过程中使用 `build:fast` 快速构建，同时保持生产构建的完整优化。
