# CapacitorKtService

KunTeng Electronics Native Services - 昆腾电子原生服务，集成蓝牙后台通信、Mapbox导航和搜索功能。

## 安装

```bash
npm install capacitor-kt-service
npx cap sync
```

## 配置

### Android

在 `android/app/src/main/java/.../MainActivity.java` 中注册插件：

```java
import com.kunteng.plugins.kt.CapacitorKtService;

public class MainActivity extends BridgeActivity {
  @Override
  public void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    
    // 注册插件
    this.init(savedInstanceState, new ArrayList<Class<? extends Plugin>>() {{
      add(CapacitorKtService.class);
    }});
  }
}
```

### iOS

iOS平台会自动注册插件。

## Mapbox配置

1. 获取Mapbox Access Token
   - 登录 [Mapbox官网](https://account.mapbox.com/access-tokens/)
   - 创建或复制你的public access token (用于API访问)
   - 创建或复制你的secret downloads token (用于下载SDK)

2. 在项目中配置token
   
   **Android配置:**
   ```properties
   # gradle.properties - 用于下载SDK的secret token
   MAPBOX_DOWNLOADS_TOKEN=your_mapbox_downloads_token_here
   ```
   
        ```xml
     <!-- android/app/src/main/res/values/mapbox_access_token.xml - 用于API访问的public token -->
     <resources>
         <string name="mapbox_access_token">your_mapbox_public_access_token_here</string>
     </resources>
     ```

     **iOS配置:**
     ```xml
     <!-- Info.plist - 用于API访问的public token -->
     <key>MBXAccessToken</key>
     <string>your_mapbox_public_access_token_here</string>
     ```

## 使用方法

### 导入插件

```typescript
import { CapacitorKt } from 'capacitor-kt-service';
```

### 蓝牙后台通信

```typescript
// 启动蓝牙前台服务
await CapacitorKt.startBluetoothForegroundService();

// 启动原生蓝牙发送
await CapacitorKt.startNativeBluetoothSending({
  deviceId: 'your-device-id',
  serviceUUID: 'your-service-uuid',
  characteristicUUID: 'your-characteristic-uuid',
  sendInterval: 106, // 毫秒
  data: [0x01, 0x02, 0x03] // 数据数组
});

// 检查服务状态
const { isRunning } = await CapacitorKt.isBluetoothServiceRunning();

// 获取发送统计
const stats = await CapacitorKt.getBluetoothSendingStats();

// 停止服务
await CapacitorKt.stopBluetoothForegroundService();
```

### Mapbox导航

```typescript
// 显示导航
await CapacitorKt.showMapboxNavigation({
  routes: [
    { latitude: 37.7749, longitude: -122.4194 },
    { latitude: 37.7849, longitude: -122.4094 }
  ],
  simulating: false
});

// 请求权限
await CapacitorKt.requestNavigationPermissions();

// 检查权限
const permissions = await CapacitorKt.checkNavigationPermissions();
```

### Mapbox搜索

```typescript
// 搜索地点
const searchResult = await CapacitorKt.searchMapboxPlaces({
  query: '星巴克'
});
console.log('搜索结果:', searchResult.results);

// 获取地点建议
const suggestions = await CapacitorKt.getMapboxPlaceSuggestions({
  query: '北京'
});
console.log('地点建议:', suggestions.suggestions);

// 反向地理编码
const geocodeResult = await CapacitorKt.reverseGeocodeMapbox({
  latitude: 39.9042,
  longitude: 116.4074
});
console.log('地理编码结果:', geocodeResult.results);

// 按类别搜索
const categoryResult = await CapacitorKt.searchMapboxByCategory({
  category: 'restaurant',
  latitude: 37.7749,
  longitude: -122.4194
});
console.log('类别搜索结果:', categoryResult.results);

// 验证Mapbox令牌
const tokenStatus = await CapacitorKt.validateMapboxToken();
console.log('令牌有效性:', tokenStatus.isValid);

// 打开地图界面
await CapacitorKt.openMapboxMap({
  location: {
    latitude: 37.7749,
    longitude: -122.4194
  }
});

// 打开搜索框界面
await CapacitorKt.openMapboxSearchBox();

// 打开自动完成界面
await CapacitorKt.openMapboxAutocomplete();
```

### 事件监听

```typescript
// 监听蓝牙服务状态变化
CapacitorKt.addListener('bluetoothServiceStateChanged', (data) => {
  console.log('蓝牙服务状态:', data.isRunning);
});

// 监听导航进度
CapacitorKt.addListener('onRouteProgressChange', (data) => {
  console.log('导航进度:', data);
});

// 监听导航完成
CapacitorKt.addListener('onNavigationComplete', () => {
  console.log('导航完成');
});
```

## API参考

### 蓝牙后台通信方法

| 方法 | 描述 |
|------|------|
| `startBluetoothForegroundService()` | 启动蓝牙前台服务 |
| `stopBluetoothForegroundService()` | 停止蓝牙前台服务 |
| `isBluetoothServiceRunning()` | 检查服务运行状态 |
| `startNativeBluetoothSending(config)` | 启动原生蓝牙发送 |
| `stopNativeBluetoothSending()` | 停止原生蓝牙发送 |
| `updateBluetoothSendData(data)` | 更新发送数据 |
| `getBluetoothSendingStats()` | 获取发送统计 |
| `requestIgnoreBatteryOptimizations()` | 请求电池优化豁免 |
| `getDeviceInfo()` | 获取设备信息 |

### Mapbox导航方法

| 方法 | 描述 |
|------|------|
| `showMapboxNavigation(options)` | 显示导航界面 |
| `getNavigationHistory()` | 获取导航历史 |
| `requestNavigationPermissions()` | 请求导航权限 |
| `checkNavigationPermissions()` | 检查导航权限 |

### Mapbox搜索方法

| 方法 | 描述 |
|------|------|
| `searchMapboxPlaces(options)` | 搜索地点 |
| `getMapboxPlaceSuggestions(options)` | 获取地点建议 |
| `reverseGeocodeMapbox(options)` | 反向地理编码 |
| `searchMapboxByCategory(options)` | 按类别搜索 |
| `validateMapboxToken()` | 验证Mapbox访问令牌 |
| `openMapboxMap(options)` | 打开地图界面 |
| `openMapboxSearchBox()` | 打开搜索框界面 |
| `openMapboxAutocomplete()` | 打开自动完成界面 |

## 权限要求

### Android权限

```xml
<!-- 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

<!-- 位置权限 -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 前台服务权限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### iOS权限

在 `Info.plist` 中添加：

```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>App需要蓝牙权限进行设备通信</string>
<key>NSLocationWhenInUseUsageDescription</key>
<string>App需要位置权限进行导航</string>
```

## 兼容性

- **Capacitor**: >= 7.0.0
- **Android**: API Level 23+
- **iOS**: 14.0+
- **Node.js**: >= 16.0.0

## 迁移指南

### 从单独插件迁移

如果您之前使用的是单独的插件，请按以下步骤迁移：

1. 卸载旧插件：
```bash
npm uninstall capacitor-bluetooth-background
npm uninstall capacitor-mapbox-navigation-plugin
npm uninstall capacitor-mapbox-search-plugin
```

2. 安装新插件：
```bash
npm install capacitor-kt-plugin
```

3. 更新代码中的导入：
```typescript
// 旧的导入
import { BluetoothBackground } from 'capacitor-bluetooth-background';
import { CapacitorMapboxNavigation } from 'capacitor-mapbox-navigation-plugin';

// 新的导入
import { CapacitorKt } from 'capacitor-kt-plugin';
```

4. 更新方法调用（方法名已重命名以提高语义清晰度）：
```typescript
// 旧的调用
BluetoothBackground.startForegroundService();

// 新的调用
CapacitorKt.startBluetoothForegroundService();
```

## 故障排除

### 常见问题

1. **蓝牙连接失败**
   - 确保设备支持蓝牙并已开启
   - 检查权限是否已授予
   - 验证UUID格式是否正确

2. **Mapbox导航无法启动**
   - 确保已配置有效的Access Token
   - 检查网络连接
   - 验证位置权限

3. **Android电池优化**
   - 引导用户手动设置电池优化豁免
   - 不同厂商设置页面可能不同

## 更新日志

### v1.0.0
- 🎉 首次发布
- 🔵 集成蓝牙后台通信功能
- 🗺️ 集成Mapbox导航功能
- 🔍 集成Mapbox搜索功能
- 📱 支持Android和iOS平台
- 🔧 统一API接口

## 许可证

MIT License

## 支持

如有问题或建议，请提交Issue或联系昆腾电子技术支持团队。
