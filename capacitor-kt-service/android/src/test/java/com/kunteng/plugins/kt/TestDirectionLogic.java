package com.kunteng.plugins.kt;

/**
 * 简单的方向逻辑测试程序
 * 可以直接运行来验证方向获取逻辑
 */
public class TestDirectionLogic {
    
    public static void main(String[] args) {
        NavigationDataBuilder dataBuilder = new NavigationDataBuilder();
        
        System.out.println("=== 测试方向获取逻辑 ===");
        
        // 测试基本方向
        testDirection(dataBuilder, "turn", "left", 1, "左转");
        testDirection(dataBuilder, "turn", "right", 2, "右转");
        testDirection(dataBuilder, "turn", "straight", 3, "直行");
        
        // 测试轻微转向
        testDirection(dataBuilder, "turn", "slight left", 5, "向左前方行驶");
        testDirection(dataBuilder, "turn", "slight right", 6, "向右前方行驶");
        
        // 测试急转
        testDirection(dataBuilder, "turn", "sharp left", 1, "急左转");
        testDirection(dataBuilder, "turn", "sharp right", 2, "急右转");
        
        // 测试掉头
        testDirection(dataBuilder, "turn", "uturn", 4, "掉头");
        
        // 测试特殊类型
        testDirection(dataBuilder, "arrive", "straight", 9, "到达目的地");
        testDirection(dataBuilder, "depart", "straight", 3, "出发");
        
        // 测试边界情况
        testDirection(dataBuilder, "turn", "unknown", 3, "未知修饰符");
        testDirection(dataBuilder, "turn", null, 3, "null修饰符");
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void testDirection(NavigationDataBuilder dataBuilder, String type, String modifier, int expected, String description) {
        int result = dataBuilder.getDirectionFromBannerInstructions(type, modifier);
        String status = (result == expected) ? "✓" : "✗";
        System.out.printf("%s %s (%s, %s) -> %d (期望: %d)\n", 
                         status, description, type, modifier, result, expected);
    }
} 