package com.kunteng.plugins.kt;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 简单的导航测试
 * 验证方向获取逻辑是否正确
 */
public class SimpleNavigationTest {

    private NavigationDataBuilder dataBuilder = new NavigationDataBuilder();

    @Test
    public void testGetDirectionFromBannerInstructions_AllDirections() {
        // 测试所有方向代码 - 与TypeScript版本中的DirectionProtocolMap保持一致
        assertEquals(1, dataBuilder.getDirectionFromBannerInstructions("turn", "left"));
        assertEquals(2, dataBuilder.getDirectionFromBannerInstructions("turn", "right"));
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", "straight"));
        assertEquals(5, dataBuilder.getDirectionFromBannerInstructions("turn", "slight left"));
        assertEquals(6, dataBuilder.getDirectionFromBannerInstructions("turn", "slight right"));
        assertEquals(1, dataBuilder.getDirectionFromBannerInstructions("turn", "sharp left"));
        assertEquals(2, dataBuilder.getDirectionFromBannerInstructions("turn", "sharp right"));
        assertEquals(4, dataBuilder.getDirectionFromBannerInstructions("turn", "uturn"));
        assertEquals(9, dataBuilder.getDirectionFromBannerInstructions("arrive", "straight"));
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("depart", "straight"));
    }

    @Test
    public void testGetDirectionFromBannerInstructions_DefaultCase() {
        // 测试未知修饰符的默认情况
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", "unknown"));
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", null));
    }

    @Test
    public void testGetDirectionFromBannerInstructions_RealWorldExample() {
        // 测试你提供的真实数据示例
        // BannerInstructions{unrecognized=null, distanceAlongGeometry=539.1234741210938, 
        // primary=BannerText{unrecognized=null, text=左转, components=[...], type=turn, modifier=left, ...}
        
        // 对于左转的情况
        assertEquals(1, dataBuilder.getDirectionFromBannerInstructions("turn", "left"));
        
        // 对于右转的情况
        assertEquals(2, dataBuilder.getDirectionFromBannerInstructions("turn", "right"));
        
        // 对于直行的情况
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", "straight"));
    }
}