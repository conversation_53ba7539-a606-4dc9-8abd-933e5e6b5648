package com.kunteng.plugins.kt;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.mapbox.navigation.base.trip.model.RouteProgress;
import com.mapbox.navigation.base.trip.model.RouteLegProgress;
import com.mapbox.navigation.base.trip.model.RouteStepProgress;
import com.mapbox.navigation.base.trip.model.BannerInstructions;
import com.mapbox.navigation.base.trip.model.BannerComponents;

/**
 * 导航数据构建器单元测试
 * 验证从Web端迁移到Native端的导航数据处理逻辑
 */
@RunWith(MockitoJUnitRunner.class)
public class NavigationDataBuilderTest {

    @Mock
    private RouteProgress mockRouteProgress;
    @Mock
    private RouteLegProgress mockLegProgress;
    @Mock
    private RouteStepProgress mockStepProgress;
    @Mock
    private BannerInstructions mockBannerInstructions;
    @Mock
    private BannerComponents mockPrimaryBanner;

    private NavigationDataBuilder dataBuilder;

    // 定义测试用的初始WriteData (对应useNavigation.ts中的WriteData)
    private int[] createInitialWriteData() {
        return new int[]{
            0xf, 0x5, 0xf5, 0x58, 0x2e, 0x0, 0x38, 0xca, 0x84, 0x14, 0x65, 0x32, // 0-11 与控制器通讯的协议
            0x00, 0x00, 0x00, 0x00, 0x00, //12-16  [11，12，13，14，15，16]
            0x0e //17 截止位
        };
    }

    @Before
    public void setUp() {
        dataBuilder = new NavigationDataBuilder();
    }

    @Test
    public void testBuildNavigationBluetoothData_LeftTurn() {
        // 模拟左转导航指令
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(29048.186);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(150.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("left");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证数组长度
        assertEquals(17, result.length);

        // 验证方向是左转 (1)
        assertEquals(1, result[12] & 0x0f);

        // 验证距离数据
        assertEquals(150, result[13]); // 单次距离低位
        assertEquals(34, result[15]);  // 总距离低位 (290 % 256)

        // 验证距离单位规则
        int singleDistanceRule = 0; // 150 < 999，使用个位显示
        int totalDistanceRule = 2;  // 29048 < 99900，使用百位显示
        assertEquals(singleDistanceRule, (result[12] >> 4) & 0x03);
        assertEquals(totalDistanceRule, (result[14] >> 4) & 0x03);
    }

    @Test
    public void testBuildNavigationBluetoothData_RightTurn() {
        // 模拟右转导航指令
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(5000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(500.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("right");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证方向是右转 (2)
        assertEquals(2, result[12] & 0x0f);

        // 验证距离数据
        assertEquals(244, result[13]); // 单次距离低位 (500 % 256)
        assertEquals(244, result[15]); // 总距离低位 (500 % 256)
    }

    @Test
    public void testBuildNavigationBluetoothData_Straight() {
        // 模拟直行导航指令
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(8000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(800.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("continue");
        when(mockPrimaryBanner.modifier()).thenReturn("straight");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证方向是直行 (3)
        assertEquals(3, result[12] & 0x0f);
    }

    @Test
    public void testBuildNavigationBluetoothData_Arrive() {
        // 模拟到达目的地指令
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(0.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(0.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("arrive");
        when(mockPrimaryBanner.modifier()).thenReturn("straight");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证方向是到达目的地 (9)
        assertEquals(9, result[12] & 0x0f);
    }

    @Test
    public void testBuildNavigationBluetoothData_Depart() {
        // 模拟出发指令
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(1000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(50.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("depart");
        when(mockPrimaryBanner.modifier()).thenReturn("straight");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证方向是直行 (3) - depart 默认直行
        assertEquals(3, result[12] & 0x0f);
    }

    @Test
    public void testBuildNavigationBluetoothData_LargeDistances() {
        // 模拟大距离值的距离规则映射
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(100000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(50000.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("left");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证距离规则
        // 50000 < 99900，使用百位显示 (rule=2)
        assertEquals(2, (result[12] >> 4) & 0x03);
        // 100000 >= 99900，使用千位显示 (rule=3)
        assertEquals(3, (result[14] >> 4) & 0x03);
    }

    @Test
    public void testBuildNavigationBluetoothData_RoundingToNearestFive() {
        // 模拟距离四舍五入到最接近的5的倍数
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(1000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(23.0); // 应该四舍五入到25
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("left");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证四舍五入到最接近的5的倍数
        assertEquals(25, result[13]);
    }

    @Test
    public void testBuildNavigationBluetoothData_NullBannerInstructions() {
        // 模拟缺少bannerInstructions的情况
        when(mockRouteProgress.getBannerInstructions()).thenReturn(null);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(1000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(100.0);

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证不会抛出错误，且返回基础数据
        assertNotNull(result);
        assertEquals(17, result.length);
        
        // 验证基础协议数据设置正确
        assertEquals(0xAA, result[0]);
        assertEquals(0x55, result[1]);
        assertEquals(0x44, result[2]);
        assertEquals(0x33, result[3]);
        assertEquals(0x22, result[4]);
        assertEquals(0x11, result[5]);
    }

    @Test
    public void testBuildNavigationBluetoothData_NullStepProgress() {
        // 模拟stepProgress为null的情况
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(1000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(null);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("left");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证使用默认值0.0
        assertEquals(0, result[13]); // 单次距离低位应该是0
    }

    @Test
    public void testCalculateChecksum() {
        // 测试校验值计算
        int[] testData = createInitialWriteData();
        testData[12] = 0x12; // 设置一些测试数据
        testData[13] = 123;
        testData[14] = 34;
        testData[15] = 55;

        int expectedChecksum = testData[1] ^ testData[2] ^ testData[3] ^ testData[4] ^
                              testData[6] ^ testData[7] ^ testData[8] ^ testData[9] ^
                              testData[10] ^ testData[11] ^ testData[12] ^ testData[13] ^
                              testData[14] ^ testData[15];

        int actualChecksum = dataBuilder.calculateChecksum(testData);
        assertEquals(expectedChecksum, actualChecksum);
    }

    @Test
    public void testMapDistanceToRule_IndividualDigits() {
        // 测试个位显示规则 (距离 < 999)
        NavigationDataBuilder.DistanceRule rule = dataBuilder.mapDistanceToRule(150.0);
        assertEquals(0, rule.rule);
        assertEquals(150, rule.effectiveNumber);
        assertEquals(1, rule.multiplier);
    }

    @Test
    public void testMapDistanceToRule_TensDigits() {
        // 测试十位显示规则 (999 <= 距离 < 9990)
        NavigationDataBuilder.DistanceRule rule = dataBuilder.mapDistanceToRule(5000.0);
        assertEquals(1, rule.rule);
        assertEquals(500, rule.effectiveNumber);
        assertEquals(10, rule.multiplier);
    }

    @Test
    public void testMapDistanceToRule_HundredsDigits() {
        // 测试百位显示规则 (9990 <= 距离 < 99900)
        NavigationDataBuilder.DistanceRule rule = dataBuilder.mapDistanceToRule(50000.0);
        assertEquals(2, rule.rule);
        assertEquals(500, rule.effectiveNumber);
        assertEquals(100, rule.multiplier);
    }

    @Test
    public void testMapDistanceToRule_ThousandsDigits() {
        // 测试千位显示规则 (距离 >= 99900)
        NavigationDataBuilder.DistanceRule rule = dataBuilder.mapDistanceToRule(100000.0);
        assertEquals(3, rule.rule);
        assertEquals(100, rule.effectiveNumber);
        assertEquals(1000, rule.multiplier);
    }

    @Test
    public void testGetDirectionFromBannerInstructions_AllDirections() {
        // 测试所有方向代码 - 与TypeScript版本中的DirectionProtocolMap保持一致
        assertEquals(1, dataBuilder.getDirectionFromBannerInstructions("turn", "left"));
        assertEquals(2, dataBuilder.getDirectionFromBannerInstructions("turn", "right"));
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", "straight"));
        assertEquals(5, dataBuilder.getDirectionFromBannerInstructions("turn", "slight left"));
        assertEquals(6, dataBuilder.getDirectionFromBannerInstructions("turn", "slight right"));
        assertEquals(1, dataBuilder.getDirectionFromBannerInstructions("turn", "sharp left"));
        assertEquals(2, dataBuilder.getDirectionFromBannerInstructions("turn", "sharp right"));
        assertEquals(4, dataBuilder.getDirectionFromBannerInstructions("turn", "uturn"));
        assertEquals(9, dataBuilder.getDirectionFromBannerInstructions("arrive", "straight"));
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("depart", "straight"));
    }

    @Test
    public void testGetDirectionFromBannerInstructions_DefaultCase() {
        // 测试未知修饰符的默认情况
        assertEquals(3, dataBuilder.getDirectionFromBannerInstructions("turn", "unknown"));
    }

    @Test
    public void testBuildNavigationBluetoothData_CompleteDataFlow() {
        // 测试完整的字节12-15数据流
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(56789.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(1234.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("right");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        // 验证完整的字节12-15
        int byte12 = result[12];
        int byte13 = result[13];
        int byte14 = result[14];
        int byte15 = result[15];

        // 字节12: [7]镜像位=0, [6]保留位=0, [5,4]单次距离单位=1, [3,2,1,0]方向=2
        assertEquals(0x12, byte12); // 0001 0010

        // 字节13: 单次距离低位 (1235经过mapDistanceToRule后effectiveNumber=123, 123%256=123)
        assertEquals(123, byte13);

        // 字节14: [7,6]单次距离高位=0, [5,4]总距离单位=2, [3,2]保留位=0, [1,0]总距离高位=2
        assertEquals(34, byte14); // 0010 0010

        // 字节15: 总距离低位 (56789经过mapDistanceToRule后effectiveNumber=567, 567%256=55)
        assertEquals(55, byte15);
    }

    @Test
    public void testBuildNavigationBluetoothData_BytePositions() {
        // 测试字节位置设置
        when(mockRouteProgress.getBannerInstructions()).thenReturn(mockBannerInstructions);
        when(mockRouteProgress.getCurrentLegProgress()).thenReturn(mockLegProgress);
        when(mockRouteProgress.getDistanceRemaining()).thenReturn(50000.0);
        
        when(mockLegProgress.getCurrentStepProgress()).thenReturn(mockStepProgress);
        when(mockStepProgress.getDistanceRemaining()).thenReturn(520.0);
        
        when(mockBannerInstructions.primary()).thenReturn(mockPrimaryBanner);
        when(mockPrimaryBanner.type()).thenReturn("turn");
        when(mockPrimaryBanner.modifier()).thenReturn("left");

        int[] result = dataBuilder.buildNavigationBluetoothData(mockRouteProgress);

        int byte12 = result[12];
        int byte13 = result[13];
        int byte14 = result[14];
        int byte15 = result[15];

        // 验证字节12的各个位
        assertEquals(1, byte12 & 0x0f); // 方向位 [3,2,1,0] - 左转
        assertEquals(0, (byte12 >> 4) & 0x03); // 单次距离单位位 [5,4] - 520 < 999，使用个位显示
        assertEquals(0, (byte12 >> 6) & 0x01); // 第6位保留位
        assertEquals(0, (byte12 >> 7) & 0x01); // 第7位镜像位

        // 验证字节13（单次距离低位）
        assertEquals(8, byte13); // 520经过mapDistanceToRule后，effectiveNumber=520，singleLow=520%256=8

        // 验证字节14的各个位
        assertEquals(2, (byte14 >> 6) & 0x03); // 单次距离高位 [7,6] - 520/256=2
        assertEquals(2, (byte14 >> 4) & 0x03); // 总距离单位位 [5,4] - 50000 < 99900，使用百位显示
        assertEquals(0, (byte14 >> 2) & 0x03); // 保留位 [3,2]
        assertEquals(1, byte14 & 0x03); // 总距离高位 [1,0] - 500/256=1

        // 验证字节15（总距离低位）
        assertEquals(244, byte15); // 50000经过mapDistanceToRule后，effectiveNumber=500，totalLow=500%256=244
    }

    @Test
    public void testClearNavigationBluetoothData() {
        // 测试清空导航数据
        int[] result = dataBuilder.clearNavigationBluetoothData();

        // 验证数组长度
        assertEquals(17, result.length);

        // 验证基础协议数据
        assertEquals(0xAA, result[0]);
        assertEquals(0x55, result[1]);
        assertEquals(0x44, result[2]);
        assertEquals(0x33, result[3]);
        assertEquals(0x22, result[4]);
        assertEquals(0x11, result[5]);

        // 验证导航相关字节被清空
        assertEquals(0x00, result[12]);
        assertEquals(0x00, result[13]);
        assertEquals(0x00, result[14]);
        assertEquals(0x00, result[15]);

        // 验证校验值正确计算
        int expectedChecksum = result[1] ^ result[2] ^ result[3] ^ result[4] ^
                              result[6] ^ result[7] ^ result[8] ^ result[9] ^
                              result[10] ^ result[11] ^ result[12] ^ result[13] ^
                              result[14] ^ result[15];
        assertEquals(expectedChecksum, result[16]);
    }
}