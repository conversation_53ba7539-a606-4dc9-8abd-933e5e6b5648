package com.kunteng.plugins.kt;

import android.util.Log;

import com.mapbox.api.directions.v5.models.BannerInstructions;
import com.mapbox.navigation.base.trip.model.RouteProgress;
// TODO: 这些类在新版本的Mapbox Navigation SDK中已更改或不存在
// import com.mapbox.navigation.base.trip.model.BannerInstructions;
// import com.mapbox.navigation.base.trip.model.BannerComponents;

/**
 * 导航数据构建器
 * 负责将Mapbox导航数据转换为蓝牙通信协议数据
 * 基于接收到的原始蓝牙数据进行导航字节修改，而不是创建全新数组
 */
public class NavigationDataBuilder {

    private static final String TAG = "NavigationDataBuilder";
    private static final int ROUNDING_INCREMENT = 5; // 距离四舍五入到最接近的5的倍数
    
    /**
     * 距离映射到规则的数据类
     */
    public static class DistanceRule {
        public final int rule;
        public final int effectiveNumber;
        public final int multiplier;

        public DistanceRule(int rule, int effectiveNumber, int multiplier) {
            this.rule = rule;
            this.effectiveNumber = effectiveNumber;
            this.multiplier = multiplier;
        }
    }

    /**
     * 将距离映射到显示规则
     */
    public DistanceRule mapDistanceToRule(double distance) {
        // 规则对应表，按除数从小到大排列
        int[][] rules = {
            {0, 1},     // 个位
            {1, 10},    // 十位  
            {2, 100},   // 百位
            {3, 1000}   // 千位
        };
        
        for (int[] rule : rules) {
            int ruleNumber = rule[0];
            int divisor = rule[1];
            int effectiveNumber = (int) (distance / divisor);
            
            if (effectiveNumber <= 999) {
                return new DistanceRule(ruleNumber, effectiveNumber, divisor);
            }
        }
        
        // 默认返回最大规则
        return new DistanceRule(3, (int) (distance / 1000), 1000);
    }

    // 默认的基础数据数组（当没有接收到原始数据时使用）
    private static final int[] DEFAULT_BASE_DATA = {
        0x0f, 0x05, 0xf5, 0x58, 0x2e, 0x00, 0x38, 0xca, 
        0x84, 0x14, 0x65, 0x32, // 0-11 与控制器通讯的协议
        0x00, 0x00, 0x00, 0x00, 0x00, // 12-16 导航数据
        0x0e // 17 截止位
    };

    /**
     * 基于原始蓝牙数据修改导航字节
     * 这是新的核心方法，基于接收到的原始数据进行修改而不是创建全新数组
     * @param baseData 接收到的原始蓝牙数据数组
     * @param routeProgress 导航路线进度
     * @return 修改后的蓝牙数据数组
     */
    public int[] modifyNavigationData(int[] baseData, RouteProgress routeProgress) {
        if (baseData == null || baseData.length < 18) {
            Log.w(TAG, "基础数据无效，使用默认数据: " + (baseData != null ? baseData.length : "null"));
            baseData = DEFAULT_BASE_DATA.clone();
        }
        
        // 复制原始数据，避免修改传入的数组
        int[] modifiedData = baseData.clone();
        
        try {
            // 获取导航指令信息
            BannerInstructions bannerInstructions = routeProgress.getBannerInstructions();
            double stepDistanceRemaining = 0.0;
            if (routeProgress.getCurrentLegProgress() != null && 
                routeProgress.getCurrentLegProgress().getCurrentStepProgress() != null) {
                stepDistanceRemaining = routeProgress.getCurrentLegProgress()
                    .getCurrentStepProgress().getDistanceRemaining();
            }
            double totalDistanceRemaining = routeProgress.getDistanceRemaining();
            
            // 获取方向信息
            int direction = getDirectionFromBannerInstructions(bannerInstructions);
            
            // 处理距离 (四舍五入到最接近的5的倍数)
            double singleDistance = Math.round(stepDistanceRemaining / ROUNDING_INCREMENT) * ROUNDING_INCREMENT;
            double totalDistance = totalDistanceRemaining;
            
            // 计算距离规则
            DistanceRule singleDistanceRule = mapDistanceToRule(singleDistance);
            DistanceRule totalDistanceRule = mapDistanceToRule(totalDistance);
            
            // 计算高位和低位
            int singleHigh = singleDistanceRule.effectiveNumber / 256;
            int singleLow = singleDistanceRule.effectiveNumber % 256;
            int totalHigh = totalDistanceRule.effectiveNumber / 256;
            int totalLow = totalDistanceRule.effectiveNumber % 256;
            
            // 只修改导航相关的字节（12-15），保留原始数据的其他信息
            // 字节12: [7]镜像位(保持原值) [6]保留位(保持原值) [5,4]单次距离单位 [3,2,1,0]方向
            // 先保留镜像位和保留位，然后设置方向和距离单位位
            int mirrorBit = modifiedData[12] & 0x80; // 保留镜像位
            int reservedBit = modifiedData[12] & 0x40; // 保留第6位保留位
            modifiedData[12] = mirrorBit | reservedBit | (singleDistanceRule.rule << 4) | direction;
            
            // 字节13: 单次距离低位
            modifiedData[13] = singleLow;
            
            // 字节14: [7,6]单次距离高位 [5,4]总距离单位 [1,0]总距离高位
            modifiedData[14] = (singleHigh << 6) | (totalDistanceRule.rule << 4) | totalHigh;
            
            // 字节15: 总距离低位
            modifiedData[15] = totalLow;
            
            // 重新计算校验和（字节16）
            modifiedData[16] = calculateChecksum(modifiedData);
            
            Log.d(TAG, String.format("导航数据修改完成 - 方向: %d, 单次距离: %.0f(规则%d), 总距离: %.0f(规则%d)", 
                direction, singleDistance, singleDistanceRule.rule, totalDistance, totalDistanceRule.rule));
            Log.d(TAG, "修改后数据: " + java.util.Arrays.toString(modifiedData));
            
            return modifiedData;
            
        } catch (Exception e) {
            Log.e(TAG, "修改导航数据失败", e);
            return modifiedData; // 返回未修改的原始数据
        }
    }

    /**
     * 基于原始蓝牙数据清空导航字节
     * @param baseData 接收到的原始蓝牙数据数组
     * @return 清空导航数据后的蓝牙数据数组
     */
    public int[] clearNavigationData(int[] baseData) {
        if (baseData == null || baseData.length < 18) {
            Log.w(TAG, "基础数据无效，使用默认数据进行清空");
            baseData = DEFAULT_BASE_DATA.clone();
        }
        
        // 复制原始数据
        int[] clearedData = baseData.clone();
        
        // 清空导航字节，但保留镜像位和保留位
        int mirrorBit = clearedData[12] & 0x80; // 保留镜像位
        int reservedBit = clearedData[12] & 0x40; // 保留第6位保留位
        clearedData[12] = mirrorBit | reservedBit; // 只保留镜像位和保留位，其他位清零
        clearedData[13] = 0x00;
        clearedData[14] = 0x00;
        clearedData[15] = 0x00;
        
        // 重新计算校验和
        clearedData[16] = calculateChecksum(clearedData);
        
        Log.d(TAG, "导航数据已清空(基于原始数据)");
        return clearedData;
    }

    /**
     * 计算校验和（XOR校验）
     * @param data 数据数组
     * @return 校验和值
     */
    public int calculateChecksum(int[] data) {
        int checksum = 0;
        // XOR校验：严格按照useNavigation.ts的逻辑进行校验
        // 包括字节：1, 2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
        checksum ^= data[1];
        checksum ^= data[2];
        checksum ^= data[3];
        checksum ^= data[4];
        checksum ^= data[6];
        checksum ^= data[7];
        checksum ^= data[8];
        checksum ^= data[9];
        checksum ^= data[10];
        checksum ^= data[11];
        checksum ^= data[12];
        checksum ^= data[13];
        checksum ^= data[14];
        checksum ^= data[15];
        return checksum & 0xFF;
    }

    /**
     * 兼容性方法：构建导航蓝牙数据（使用默认基础数据）
     * @deprecated 建议使用 modifyNavigationData 方法
     */
    @Deprecated
    public int[] buildNavigationBluetoothData(RouteProgress routeProgress) {
        return modifyNavigationData(DEFAULT_BASE_DATA, routeProgress);
    }

    /**
     * 兼容性方法：清空导航蓝牙数据（使用默认基础数据）
     * @deprecated 建议使用 clearNavigationData 方法
     */
    @Deprecated
    public int[] clearNavigationBluetoothData() {
        return clearNavigationData(DEFAULT_BASE_DATA);
    }

    /**
     * 从导航指令中获取方向代码
     * 适配新版本的Mapbox Navigation SDK API
     */
    public int getDirectionFromBannerInstructions(Object bannerInstructions) {
        try {
            if (bannerInstructions == null) {
                Log.w(TAG, "BannerInstructions为null，返回默认直行");
                return 3; // 默认直行
            }
            
            // 使用反射获取BannerInstructions的属性，适配新版本SDK
            Class<?> bannerClass = bannerInstructions.getClass();
            
            // 获取primary方法
            java.lang.reflect.Method primaryMethod = null;
            try {
                primaryMethod = bannerClass.getMethod("primary");
            } catch (NoSuchMethodException e) {
                Log.w(TAG, "未找到primary方法，尝试其他方法名");
                // 尝试其他可能的方法名
                for (java.lang.reflect.Method method : bannerClass.getMethods()) {
                    if (method.getName().toLowerCase().contains("primary") && 
                        method.getParameterCount() == 0) {
                        primaryMethod = method;
                        break;
                    }
                }
            }
            
            if (primaryMethod == null) {
                Log.w(TAG, "无法找到获取primary的方法，返回默认直行");
                return 3; // 默认直行
            }
            
            Object primary = primaryMethod.invoke(bannerInstructions);
            if (primary == null) {
                Log.w(TAG, "Primary为null，返回默认直行");
                return 3; // 默认直行
            }
            
            Class<?> primaryClass = primary.getClass();
            
            // 获取type和modifier方法
            java.lang.reflect.Method typeMethod = null;
            java.lang.reflect.Method modifierMethod = null;
            
            try {
                typeMethod = primaryClass.getMethod("type");
                modifierMethod = primaryClass.getMethod("modifier");
            } catch (NoSuchMethodException e) {
                Log.w(TAG, "未找到type或modifier方法，尝试其他方法名");
                // 尝试其他可能的方法名
                for (java.lang.reflect.Method method : primaryClass.getMethods()) {
                    String methodName = method.getName().toLowerCase();
                    if (methodName.contains("type") && method.getParameterCount() == 0) {
                        typeMethod = method;
                    } else if (methodName.contains("modifier") && method.getParameterCount() == 0) {
                        modifierMethod = method;
                    }
                }
            }
            
            if (typeMethod == null || modifierMethod == null) {
                Log.w(TAG, "无法找到获取type或modifier的方法，返回默认直行");
                return 3; // 默认直行
            }
            
            String type = (String) typeMethod.invoke(primary);
            String modifier = (String) modifierMethod.invoke(primary);
            
            Log.d(TAG, "获取到导航指令 - type: " + type + ", modifier: " + modifier);
            
            return getDirectionFromBannerInstructions(type, modifier);
            
        } catch (Exception e) {
            Log.e(TAG, "获取方向失败", e);
            return 3; // 默认直行
        }
    }

    /**
     * 根据类型和修饰符获取方向代码
     * 与TypeScript版本中的DirectionProtocolMap保持一致
     */
    public int getDirectionFromBannerInstructions(String type, String modifier) {
        // 根据操作类型获取默认协议方向
        if ("arrive".equals(type)) {
            return 9; // 到达目的地
        }
        if ("depart".equals(type)) {
            return 3; // 出发时默认直行
        }
        
        // 根据操作修饰符获取协议方向
        return getProtocolDirectionByModifier(modifier);
    }
    
    /**
     * 根据操作修饰符获取协议方向
     * 对应TypeScript版本中的DirectionProtocolMap
     */
    private int getProtocolDirectionByModifier(String modifier) {
        if (modifier == null) {
            return 3; // 默认直行
        }
        
        switch (modifier) {
            case "sharp right":
                return 2; // 右转
            case "right":
                return 2; // 右转
            case "slight right":
                return 6; // 向右前方行驶
            case "straight":
                return 3; // 直行
            case "slight left":
                return 5; // 向左前方行驶
            case "left":
                return 1; // 左转
            case "sharp left":
                return 1; // 左转
            case "uturn":
                return 4; // 掉头
            default:
                Log.w(TAG, "未知的操作修饰符: " + modifier + "，返回默认直行");
                return 3; // 默认直行
        }
    }
}