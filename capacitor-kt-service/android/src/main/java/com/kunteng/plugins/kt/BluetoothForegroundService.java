package com.kunteng.plugins.kt;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.app.AlarmManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.BroadcastReceiver;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.IBinder;
import android.os.PowerManager;
import android.os.SystemClock;
import android.util.Log;
import android.net.Uri;
import android.provider.Settings;
import android.os.Handler;
import android.os.Looper;

import androidx.core.app.NotificationCompat;

import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;

public class BluetoothForegroundService extends Service {
    private static final String TAG = "BluetoothForegroundService";
    private static final String CHANNEL_ID = "BluetoothServiceChannel";
    private static final int NOTIFICATION_ID = 1001;
    private static final String ALARM_ACTION = "com.smartbicycle.bluetoothbackground.KEEP_ALIVE_ALARM";
    private static final int ALARM_REQUEST_CODE = 1002;
    
    private static boolean isServiceRunning = false;
    private PowerManager.WakeLock wakeLock;
    private PowerManager.WakeLock screenWakeLock;
    private PowerManager.WakeLock fullWakeLock; // 新增全功能唤醒锁
    private NotificationManager notificationManager;
    private PowerManager powerManager;
    private AlarmManager alarmManager;
    private PendingIntent alarmPendingIntent;
    private Handler keepAliveHandler;
    private Runnable keepAliveRunnable;
    private BroadcastReceiver dozeReceiver;
    private BroadcastReceiver screenReceiver;
    private BroadcastReceiver alarmReceiver;
    private int consecutiveDozeDetections = 0; // 连续Doze模式检测计数
    private long lastKeepAliveTime = 0; // 最后保活时间
    private Handler dozeCounterHandler; // Doze模式对抗处理器
    
    private String currentTitle = "智能单车蓝牙连接";
    private String currentMessage = "正在保持蓝牙连接...";

    // =================== 新增原生蓝牙发送功能 ===================
    private static NativeBluetoothManager bluetoothManager;
    private static NativeBluetoothManager.BluetoothSendingStatus lastSendingStatus;
    private static NativeBluetoothManager.BluetoothSendingStats lastSendingStats;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service onCreate");
        
        createNotificationChannel();
        
        // 获取PowerManager
        powerManager = (PowerManager) getSystemService(POWER_SERVICE);
        
        // 获取AlarmManager
        alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        
        // 获取强力唤醒锁以保持CPU运行
        if (powerManager != null) {
            // PARTIAL_WAKE_LOCK - 保持CPU运行，允许屏幕和键盘背光关闭
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK, 
                "SmartBicycle:BluetoothService"
            );
            wakeLock.acquire(30*60*1000L /*30 minutes*/);
            Log.d(TAG, "PARTIAL_WAKE_LOCK acquired for 30 minutes");
            
            // 额外的屏幕唤醒锁，用于对抗深度Doze模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                screenWakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    "SmartBicycle:ScreenWake"
                );
                
                // 新增全功能唤醒锁，用于极端情况下的Doze模式对抗
                fullWakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_BRIGHT_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    "SmartBicycle:FullWake"
                );
            }
        }
        
        // 初始化Doze模式对抗处理器
        dozeCounterHandler = new Handler(Looper.getMainLooper());
        
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        
        // 注册Doze模式和屏幕状态广播接收器
        registerBroadcastReceivers();
        
        // 启动保活机制
        startKeepAliveTimer();
        
        // 启动AlarmManager备用保活机制
        startAlarmKeepAlive();

        // 安全初始化蓝牙管理器（增加异常处理和重试机制）
        safeInitializeBluetoothManager();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "Service onStartCommand");
        
        String action = intent != null ? intent.getStringExtra("action") : null;
        
        if ("STOP".equals(action)) {
            Log.d(TAG, "Stopping service");
            stopKeepAliveTimer();
            stopForeground(true);
            stopSelf();
            isServiceRunning = false;
            return START_NOT_STICKY;
        }
        
        if ("UPDATE_NOTIFICATION".equals(action)) {
            String title = intent.getStringExtra("title");
            String message = intent.getStringExtra("message");
            if (title != null) currentTitle = title;
            if (message != null) currentMessage = message;
            updateNotification();
            return START_STICKY;
        }
        
        if ("KEEP_ALIVE".equals(action)) {
            Log.d(TAG, "Keep alive ping received");
            handleKeepAlive();
            return START_STICKY;
        }

        // =================== 处理原生蓝牙发送指令 ===================
        if ("START_BLUETOOTH_SENDING".equals(action)) {
            handleStartBluetoothSending(intent);
            return START_STICKY;
        }

        if ("STOP_BLUETOOTH_SENDING".equals(action)) {
            handleStopBluetoothSending();
            return START_STICKY;
        }

        if ("UPDATE_BLUETOOTH_DATA".equals(action)) {
            handleUpdateBluetoothData(intent);
            return START_STICKY;
        }

        if ("RECONNECT_BLUETOOTH".equals(action)) {
            handleReconnectBluetooth();
            return START_STICKY;
        }

        // 启动前台服务
        startForegroundService();
        isServiceRunning = true;
        
        // 检查并请求电池优化豁免
        checkBatteryOptimization();
        
        Log.d(TAG, "Service started successfully");
        return START_STICKY; // 服务被杀死后自动重启
    }
    
    private void startForegroundService() {
        Notification notification = createNotification();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE);
        } else {
            startForeground(NOTIFICATION_ID, notification);
        }
        
        Log.d(TAG, "Foreground service started with notification");
    }
    
    private void startKeepAliveTimer() {
        if (keepAliveHandler == null) {
            keepAliveHandler = new Handler(Looper.getMainLooper());
        }
        
        keepAliveRunnable = new Runnable() {
            @Override
            public void run() {
                handleKeepAlive();
                
                // 根据Doze模式状态动态调整保活频率
                long nextDelay = getAdaptiveKeepAliveDelay();
                keepAliveHandler.postDelayed(this, nextDelay);
            }
        };
        
        keepAliveHandler.post(keepAliveRunnable);
        Log.d(TAG, "Adaptive keep alive timer started");
    }
    
    // 获取自适应保活延迟时间
    private long getAdaptiveKeepAliveDelay() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager != null) {
            boolean isInDozeMode = powerManager.isDeviceIdleMode();
            
            if (isInDozeMode) {
                // Doze模式下更频繁的保活
                return consecutiveDozeDetections > 5 ? 5000 : 10000; // 5-10秒
            } else {
                // 正常模式下标准保活
                return 15000; // 15秒
            }
        }
        return 15000;
    }
    
    private void stopKeepAliveTimer() {
        if (keepAliveHandler != null && keepAliveRunnable != null) {
            keepAliveHandler.removeCallbacks(keepAliveRunnable);
            Log.d(TAG, "Keep alive timer stopped");
        }
    }
    
    private void handleKeepAlive() {
        try {
            lastKeepAliveTime = System.currentTimeMillis();
            
            // 检查Doze模式状态
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager != null) {
                boolean isInDozeMode = powerManager.isDeviceIdleMode();
                boolean isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(getPackageName());
                
                Log.d(TAG, "Doze mode: " + isInDozeMode + ", Battery optimization ignored: " + isIgnoringBatteryOptimizations);
                
                if (isInDozeMode) {
                    consecutiveDozeDetections++;
                    Log.w(TAG, "Device is in Doze mode (consecutive: " + consecutiveDozeDetections + "), implementing enhanced countermeasures");
                    
                    // 启动增强的Doze模式对抗策略
                    startEnhancedDozeCountermeasures();
                } else {
                    // 退出Doze模式，重置计数
                    if (consecutiveDozeDetections > 0) {
                        Log.d(TAG, "Exited Doze mode after " + consecutiveDozeDetections + " consecutive detections");
                        consecutiveDozeDetections = 0;
                    }
                }
            }
            
            // 确保唤醒锁仍然有效 - 延长超时时间并更频繁地重新获取
            refreshWakeLocks();
            
            // 更新通知以保持前台服务活跃
            updateNotification();
            
        } catch (Exception e) {
            Log.e(TAG, "Error in keep alive handler: " + e.getMessage());
        }
    }
    
    // 启动增强的Doze模式对抗措施
    private void startEnhancedDozeCountermeasures() {
        try {
            // 策略1: 渐进式屏幕唤醒
            if (screenWakeLock != null) {
                try {
                    if (screenWakeLock.isHeld()) {
                        screenWakeLock.release();
                    }
                    
                    // 根据连续检测次数增加唤醒时间
                    long wakeTime = Math.min(5000, 1000 + consecutiveDozeDetections * 500);
                    screenWakeLock.acquire(wakeTime);
                    Log.d(TAG, "Screen wake lock acquired for " + wakeTime + "ms to counter Doze mode");
                    
                    // 延迟释放
                    dozeCounterHandler.postDelayed(() -> {
                        try {
                            if (screenWakeLock != null && screenWakeLock.isHeld()) {
                                screenWakeLock.release();
                                Log.d(TAG, "Screen wake lock released after Doze counter");
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error releasing screen wake lock: " + e.getMessage());
                        }
                    }, wakeTime - 500);
                    
                } catch (Exception e) {
                    Log.e(TAG, "Failed to acquire screen wake lock: " + e.getMessage());
                }
            }
            
            // 策略2: 极端情况下使用全功能唤醒锁
            if (consecutiveDozeDetections > 10 && fullWakeLock != null) {
                try {
                    if (!fullWakeLock.isHeld()) {
                        fullWakeLock.acquire(3000); // 3秒全功能唤醒
                        Log.w(TAG, "Full wake lock acquired for extreme Doze mode counter");
                        
                        dozeCounterHandler.postDelayed(() -> {
                            try {
                                if (fullWakeLock != null && fullWakeLock.isHeld()) {
                                    fullWakeLock.release();
                                    Log.d(TAG, "Full wake lock released");
                                }
                            } catch (Exception e) {
                                Log.e(TAG, "Error releasing full wake lock: " + e.getMessage());
                            }
                        }, 2500);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Failed to acquire full wake lock: " + e.getMessage());
                }
            }
            
            // 策略3: 强制重新获取CPU唤醒锁
            refreshWakeLocks();
            
        } catch (Exception e) {
            Log.e(TAG, "Error in enhanced Doze countermeasures: " + e.getMessage());
        }
    }
    
    // 刷新所有唤醒锁
    private void refreshWakeLocks() {
        try {
            if (wakeLock != null) {
                if (!wakeLock.isHeld()) {
                    wakeLock.acquire(30*60*1000L); // 延长到30分钟
                    Log.d(TAG, "Wake lock re-acquired for 30 minutes");
                } else {
                    // 即使Wake Lock还在持有，也重新获取以重置超时时间
                    try {
                        wakeLock.release();
                        wakeLock.acquire(30*60*1000L); // 延长到30分钟
                        Log.d(TAG, "Wake lock refreshed for 30 minutes");
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to refresh wake lock: " + e.getMessage());
                        // 如果刷新失败，尝试重新创建
                        try {
                            wakeLock = powerManager.newWakeLock(
                                PowerManager.PARTIAL_WAKE_LOCK, 
                                "SmartBicycle:BluetoothService"
                            );
                            wakeLock.acquire(30*60*1000L);
                            Log.d(TAG, "Wake lock recreated");
                        } catch (Exception e2) {
                            Log.e(TAG, "Failed to recreate wake lock: " + e2.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error refreshing wake locks: " + e.getMessage());
        }
    }
    
    private void checkBatteryOptimization() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager != null) {
            boolean isIgnoring = powerManager.isIgnoringBatteryOptimizations(getPackageName());
            Log.d(TAG, "Battery optimization ignored: " + isIgnoring);
            
            if (!isIgnoring) {
                Log.w(TAG, "App is not exempt from battery optimization");
                // 注意：这里不直接打开设置页面，而是记录日志
                // 应用应该在适当的时机通过插件方法请求用户设置
            }
        }
    }
    
    private Notification createNotification() {
        // 创建主Activity的PendingIntent
        Intent notificationIntent = new Intent();
        notificationIntent.setClassName(getPackageName(), getPackageName() + ".MainActivity");
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 
            0, 
            notificationIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // 创建停止服务的PendingIntent
        Intent stopIntent = new Intent(this, BluetoothForegroundService.class);
        stopIntent.putExtra("action", "STOP");
        PendingIntent stopPendingIntent = PendingIntent.getService(
            this, 
            0, 
            stopIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // 添加Doze模式状态信息
        String notificationText = currentMessage;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager != null) {
            boolean isInDozeMode = powerManager.isDeviceIdleMode();
            if (isInDozeMode) {
                notificationText += " (Doze模式活跃)";
            }
        }

        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle(currentTitle)
                .setContentText(notificationText)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentIntent(pendingIntent)
                .addAction(
                    android.R.drawable.ic_menu_close_clear_cancel, 
                    "断开连接", 
                    stopPendingIntent
                )
                .setOngoing(true)
                .setPriority(NotificationCompat.PRIORITY_HIGH) // 提高通知优先级
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setAutoCancel(false)
                .build();
    }
    
    void updateNotification() {
        if (isServiceRunning && notificationManager != null) {
            Notification notification = createNotification();
            notificationManager.notify(NOTIFICATION_ID, notification);
            Log.d(TAG, "Notification updated");
        }
    }



    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    @Override
    public void onTaskRemoved(Intent rootIntent) {
        Log.d(TAG, "Task removed, but service continues running");
        // 不调用stopSelf()，让服务继续运行
        // 重新启动服务以确保持续运行
        Intent restartServiceIntent = new Intent(getApplicationContext(), this.getClass());
        restartServiceIntent.setPackage(getPackageName());
        startService(restartServiceIntent);
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "蓝牙服务通知",
                    NotificationManager.IMPORTANCE_HIGH // 提高重要性等级
            );
            serviceChannel.setDescription("保持蓝牙连接的前台服务");
            serviceChannel.setShowBadge(false);
            serviceChannel.setSound(null, null);
            serviceChannel.enableVibration(false);
            serviceChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
                Log.d(TAG, "Notification channel created");
            }
        }
    }
    
    public static boolean isServiceRunning() {
        return isServiceRunning;
    }
    
    // 注册广播接收器
    private void registerBroadcastReceivers() {
        try {
            // Doze模式广播接收器
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                dozeReceiver = new BroadcastReceiver() {
                    @Override
                    public void onReceive(Context context, Intent intent) {
                        String action = intent.getAction();
                        Log.d(TAG, "Doze broadcast received: " + action);
                        
                        if (PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED.equals(action)) {
                            handleDozeStateChanged();
                        }
                    }
                };
                
                IntentFilter dozeFilter = new IntentFilter();
                dozeFilter.addAction(PowerManager.ACTION_DEVICE_IDLE_MODE_CHANGED);
                registerReceiver(dozeReceiver, dozeFilter);
                Log.d(TAG, "Doze mode receiver registered");
            }
            
            // 屏幕状态广播接收器
            screenReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    Log.d(TAG, "Screen broadcast received: " + action);
                    
                    if (Intent.ACTION_SCREEN_ON.equals(action)) {
                        handleScreenOn();
                    } else if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                        handleScreenOff();
                    }
                }
            };
            
            IntentFilter screenFilter = new IntentFilter();
            screenFilter.addAction(Intent.ACTION_SCREEN_ON);
            screenFilter.addAction(Intent.ACTION_SCREEN_OFF);
            registerReceiver(screenReceiver, screenFilter);
            Log.d(TAG, "Screen state receiver registered");
            
            // AlarmManager保活广播接收器
            alarmReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (ALARM_ACTION.equals(intent.getAction())) {
                        Log.d(TAG, "Alarm keep alive triggered");
                        handleKeepAlive();
                        
                        // 重新设置下一次闹钟
                        scheduleNextAlarm();
                    }
                }
            };
            
            IntentFilter alarmFilter = new IntentFilter();
            alarmFilter.addAction(ALARM_ACTION);
            registerReceiver(alarmReceiver, alarmFilter);
            Log.d(TAG, "Alarm receiver registered");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to register broadcast receivers: " + e.getMessage());
        }
    }
    
    // 取消注册广播接收器
    private void unregisterBroadcastReceivers() {
        try {
            if (dozeReceiver != null) {
                unregisterReceiver(dozeReceiver);
                dozeReceiver = null;
                Log.d(TAG, "Doze mode receiver unregistered");
            }
            
            if (screenReceiver != null) {
                unregisterReceiver(screenReceiver);
                screenReceiver = null;
                Log.d(TAG, "Screen state receiver unregistered");
            }
            
            if (alarmReceiver != null) {
                unregisterReceiver(alarmReceiver);
                alarmReceiver = null;
                Log.d(TAG, "Alarm receiver unregistered");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to unregister broadcast receivers: " + e.getMessage());
        }
    }
    
    // 处理Doze模式状态变化
    private void handleDozeStateChanged() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && powerManager != null) {
                boolean isInDozeMode = powerManager.isDeviceIdleMode();
                Log.d(TAG, "Doze mode state changed: " + isInDozeMode);
                
                if (isInDozeMode) {
                    Log.w(TAG, "Entered Doze mode - implementing countermeasures");
                    
                    // 立即执行保活操作
                    handleKeepAlive();
                    
                    // 增加保活频率 - 在Doze模式下每10秒保活一次
                    if (keepAliveHandler != null && keepAliveRunnable != null) {
                        keepAliveHandler.removeCallbacks(keepAliveRunnable);
                        keepAliveHandler.postDelayed(keepAliveRunnable, 10000);
                        Log.d(TAG, "Increased keep alive frequency for Doze mode");
                    }
                } else {
                    Log.d(TAG, "Exited Doze mode - resuming normal operation");
                    
                    // 恢复正常保活频率
                    if (keepAliveHandler != null && keepAliveRunnable != null) {
                        keepAliveHandler.removeCallbacks(keepAliveRunnable);
                        keepAliveHandler.postDelayed(keepAliveRunnable, 15000);
                        Log.d(TAG, "Restored normal keep alive frequency");
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling Doze state change: " + e.getMessage());
        }
    }
    
    // 处理屏幕点亮
    private void handleScreenOn() {
        Log.d(TAG, "Screen turned ON - refreshing wake locks");
        try {
            // 屏幕点亮时立即刷新Wake Lock
            if (wakeLock != null) {
                if (wakeLock.isHeld()) {
                    wakeLock.release();
                }
                wakeLock.acquire(30*60*1000L);
                Log.d(TAG, "Wake lock refreshed on screen ON");
            }
            
            // 立即执行一次保活
            handleKeepAlive();
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling screen ON: " + e.getMessage());
        }
    }
    
    // 处理屏幕关闭
    private void handleScreenOff() {
        Log.d(TAG, "Screen turned OFF - preparing for background operation");
        try {
            // 屏幕关闭时确保Wake Lock有效
            if (wakeLock != null && !wakeLock.isHeld()) {
                wakeLock.acquire(30*60*1000L);
                Log.d(TAG, "Wake lock acquired on screen OFF");
            }
            
            // 立即执行一次保活
            handleKeepAlive();
            
        } catch (Exception e) {
            Log.e(TAG, "Error handling screen OFF: " + e.getMessage());
        }
    }
    
    // 启动AlarmManager保活机制
    private void startAlarmKeepAlive() {
        try {
            if (alarmManager == null) return;
            
            Intent alarmIntent = new Intent(ALARM_ACTION);
            alarmPendingIntent = PendingIntent.getBroadcast(
                this, 
                ALARM_REQUEST_CODE, 
                alarmIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            
            scheduleNextAlarm();
            Log.d(TAG, "AlarmManager keep alive started");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start AlarmManager keep alive: " + e.getMessage());
        }
    }
    
    // 停止AlarmManager保活机制
    private void stopAlarmKeepAlive() {
        try {
            if (alarmManager != null && alarmPendingIntent != null) {
                alarmManager.cancel(alarmPendingIntent);
                alarmPendingIntent = null;
                Log.d(TAG, "AlarmManager keep alive stopped");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop AlarmManager keep alive: " + e.getMessage());
        }
    }
    
    // 安排下一次闹钟
    private void scheduleNextAlarm() {
        try {
            if (alarmManager == null || alarmPendingIntent == null) return;
            
            long triggerTime = SystemClock.elapsedRealtime() + 20000; // 20秒后触发
            
            // 使用setExactAndAllowWhileIdle确保在Doze模式下也能执行
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    triggerTime,
                    alarmPendingIntent
                );
            } else {
                alarmManager.setExact(
                    AlarmManager.ELAPSED_REALTIME_WAKEUP,
                    triggerTime,
                    alarmPendingIntent
                );
            }

            Log.d(TAG, "Next alarm scheduled in 20 seconds");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to schedule next alarm: " + e.getMessage());
        }
    }

    // =================== 原生蓝牙发送处理方法 ===================

    // 初始化蓝牙管理器
    private void initializeBluetoothManager() {
        if (bluetoothManager == null) {
            bluetoothManager = new NativeBluetoothManager(this);
            bluetoothManager.setCallback(new NativeBluetoothManager.BluetoothCallback() {
                @Override
                public void onConnectionStateChanged(boolean connected) {
                    Log.d(TAG, "Bluetooth connection state changed: " + connected);
                    updateNotificationForBluetooth(connected);
                }

                @Override
                public void onDataReceived(int[] data) {
                    Log.d(TAG, "Bluetooth data received, length: " + data.length);
                    // 通过插件通知前端
                    notifyBluetoothDataReceived(data);
                }

                @Override
                public void onSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
                    Log.d(TAG, "Bluetooth sending state changed - Active: " + active + 
                              ", Sent: " + sendCount + ", Errors: " + errorCount);
                    
                    // 更新缓存状态
                    lastSendingStatus = new NativeBluetoothManager.BluetoothSendingStatus(
                        active, lastSendTime, sendCount, errorCount);
                    
                    // 通过插件通知前端
                    notifyBluetoothSendingStateChanged(active, lastSendTime, sendCount, errorCount);
                    
                    // 更新通知显示发送状态
                    if (active) {
                        currentMessage = "正在发送蓝牙数据 (已发送: " + sendCount + ")";
                    } else {
                        currentMessage = "蓝牙发送已停止";
                    }
                    updateNotification();
                }

                @Override
                public void onError(String error, int errorCode) {
                    Log.e(TAG, "Bluetooth error: " + error + " (code: " + errorCode + ")");
                    // 通过插件通知前端
                    notifyBluetoothError(error, errorCode);
                }
            });
            
            Log.d(TAG, "Native bluetooth manager initialized");
        }
    }

    // 处理启动蓝牙发送
    private void handleStartBluetoothSending(Intent intent) {
        try {
            String deviceId = intent.getStringExtra("deviceId");
            String serviceUUID = intent.getStringExtra("serviceUUID");
            String characteristicUUID = intent.getStringExtra("characteristicUUID");
            int sendInterval = intent.getIntExtra("sendInterval", 106);
            int[] data = intent.getIntArrayExtra("data");

            if (bluetoothManager != null && deviceId != null && serviceUUID != null && 
                characteristicUUID != null && data != null) {
                
                Log.d(TAG, "Starting native bluetooth sending - Device: " + deviceId + 
                          ", Interval: " + sendInterval + "ms, Data length: " + data.length);
                
                boolean success = bluetoothManager.startBluetoothSending(
                    deviceId, serviceUUID, characteristicUUID, sendInterval, data);
                
                if (success) {
                    currentTitle = "智能单车蓝牙发送";
                    currentMessage = "正在启动蓝牙发送服务...";
                    updateNotification();
                } else {
                    Log.e(TAG, "Failed to start bluetooth sending");
                }
            } else {
                Log.e(TAG, "Invalid parameters for bluetooth sending");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling start bluetooth sending: " + e.getMessage());
        }
    }

    // 处理停止蓝牙发送
    private void handleStopBluetoothSending() {
        try {
            if (bluetoothManager != null) {
                Log.d(TAG, "Stopping native bluetooth sending");
                bluetoothManager.stopBluetoothSending();
                
                currentTitle = "智能单车蓝牙连接";
                currentMessage = "蓝牙发送已停止";
                updateNotification();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling stop bluetooth sending: " + e.getMessage());
        }
    }

    // 处理更新蓝牙数据
    private void handleUpdateBluetoothData(Intent intent) {
        try {
            int[] data = intent.getIntArrayExtra("data");
            if (bluetoothManager != null && data != null) {
                // 🔧 增强调试：记录接收到的数据更新请求
                StringBuilder dataLog = new StringBuilder("🔄 收到数据更新请求: [");
                for (int i = 0; i < Math.min(data.length, 10); i++) {
                    dataLog.append(data[i]);
                    if (i < Math.min(data.length, 10) - 1) dataLog.append(", ");
                }
                if (data.length > 10) dataLog.append("...");
                dataLog.append("] 长度: ").append(data.length);
                
                Log.d(TAG, dataLog.toString());
                Log.d(TAG, "🕐 Service收到更新请求时间戳: " + System.currentTimeMillis());
                
                bluetoothManager.updateSendData(data);
                
                Log.d(TAG, "✅ 数据更新请求已转发给NativeBluetoothManager");
            } else {
                if (bluetoothManager == null) {
                    Log.w(TAG, "⚠️ 无法更新数据：bluetoothManager为null");
                }
                if (data == null) {
                    Log.w(TAG, "⚠️ 无法更新数据：接收到的数据为null");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ 处理数据更新请求时发生错误: " + e.getMessage(), e);
        }
    }

    // 处理重新连接蓝牙
    private void handleReconnectBluetooth() {
        try {
            if (bluetoothManager != null) {
                Log.d(TAG, "Reconnecting bluetooth device");
                bluetoothManager.reconnectDevice();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling reconnect bluetooth: " + e.getMessage());
        }
    }

    // 更新蓝牙相关通知
    private void updateNotificationForBluetooth(boolean connected) {
        if (connected) {
            currentMessage = "蓝牙设备已连接，准备发送数据";
        } else {
            currentMessage = "蓝牙设备连接断开";
        }
        updateNotification();
    }

    // 获取蓝牙发送状态（静态方法供插件调用）
    public static JSObject getBluetoothSendingStatus() {
        JSObject result = new JSObject();
        
        try {
            if (lastSendingStatus != null) {
                result.put("isActive", lastSendingStatus.isActive);
                result.put("lastSendTime", lastSendingStatus.lastSendTime);
                result.put("sendCount", lastSendingStatus.sendCount);
                result.put("errorCount", lastSendingStatus.errorCount);
                result.put("success", true);
            } else if (bluetoothManager != null) {
                NativeBluetoothManager.BluetoothSendingStatus status = bluetoothManager.getSendingStatus();
                if (status != null) {
                    result.put("isActive", status.isActive);
                    result.put("lastSendTime", status.lastSendTime);
                    result.put("sendCount", status.sendCount);
                    result.put("errorCount", status.errorCount);
                    result.put("success", true);
                } else {
                    result.put("isActive", false);
                    result.put("lastSendTime", 0);
                    result.put("sendCount", 0);
                    result.put("errorCount", 0);
                    result.put("success", false);
                    result.put("error", "蓝牙发送状态为空");
                }
            } else {
                result.put("isActive", false);
                result.put("lastSendTime", 0);
                result.put("sendCount", 0);
                result.put("errorCount", 0);
                result.put("success", false);
                result.put("error", "蓝牙管理器未初始化，请确保蓝牙前台服务已启动");
                Log.w(TAG, "getBluetoothSendingStatus called but bluetoothManager is null");
            }
        } catch (Exception e) {
            result.put("isActive", false);
            result.put("lastSendTime", 0);
            result.put("sendCount", 0);
            result.put("errorCount", 0);
            result.put("success", false);
            result.put("error", "获取蓝牙发送状态异常: " + e.getMessage());
            Log.e(TAG, "Exception in getBluetoothSendingStatus: " + e.getMessage(), e);
        }
        
        return result;
    }

    // 获取蓝牙发送统计（静态方法供插件调用）
    public static JSObject getBluetoothSendingStats() {
        JSObject result = new JSObject();
        
        try {
            if (lastSendingStats != null) {
                result.put("totalSent", lastSendingStats.totalSent);
                result.put("successCount", lastSendingStats.successCount);
                result.put("errorCount", lastSendingStats.errorCount);
                result.put("averageInterval", lastSendingStats.averageInterval);
                result.put("lastError", lastSendingStats.lastError);
                result.put("isConnected", lastSendingStats.isConnected);
                result.put("success", true);
            } else if (bluetoothManager != null) {
                NativeBluetoothManager.BluetoothSendingStats stats = bluetoothManager.getSendingStats();
                if (stats != null) {
                    result.put("totalSent", stats.totalSent);
                    result.put("successCount", stats.successCount);
                    result.put("errorCount", stats.errorCount);
                    result.put("averageInterval", stats.averageInterval);
                    result.put("lastError", stats.lastError);
                    result.put("isConnected", stats.isConnected);
                    result.put("success", true);
                    
                    // 更新缓存
                    lastSendingStats = stats;
                } else {
                    result.put("totalSent", 0);
                    result.put("successCount", 0);
                    result.put("errorCount", 0);
                    result.put("averageInterval", 0.0);
                    result.put("lastError", null);
                    result.put("isConnected", false);
                    result.put("success", false);
                    result.put("error", "蓝牙发送统计为空");
                }
            } else {
                result.put("totalSent", 0);
                result.put("successCount", 0);
                result.put("errorCount", 0);
                result.put("averageInterval", 0.0);
                result.put("lastError", null);
                result.put("isConnected", false);
                result.put("success", false);
                result.put("error", "蓝牙管理器未初始化，请确保蓝牙前台服务已启动");
                Log.w(TAG, "getBluetoothSendingStats called but bluetoothManager is null");
            }
        } catch (Exception e) {
            result.put("totalSent", 0);
            result.put("successCount", 0);
            result.put("errorCount", 0);
            result.put("averageInterval", 0.0);
            result.put("lastError", null);
            result.put("isConnected", false);
            result.put("success", false);
            result.put("error", "获取蓝牙发送统计异常: " + e.getMessage());
            Log.e(TAG, "Exception in getBluetoothSendingStats: " + e.getMessage(), e);
        }
        
        return result;
    }

    // 获取当前发送的蓝牙数据（静态方法供插件调用）
    public static JSObject getCurrentBluetoothSendData() {
        JSObject result = new JSObject();
        
        try {
            // 添加null检查，防止在服务未完全初始化时调用导致崩溃
            if (bluetoothManager != null) {
                int[] currentData = bluetoothManager.getCurrentSendData();
                if (currentData != null) {
                    JSArray dataArray = new JSArray();
                    for (int data : currentData) {
                        dataArray.put(data);
                    }
                    result.put("data", dataArray);
                    result.put("length", currentData.length);
                    result.put("success", true);
                    result.put("timestamp", System.currentTimeMillis());
                } else {
                    result.put("data", new JSArray());
                    result.put("length", 0);
                    result.put("success", false);
                    result.put("error", "当前发送数据为空");
                    result.put("timestamp", System.currentTimeMillis());
                }
            } else {
                result.put("data", new JSArray());
                result.put("length", 0);
                result.put("success", false);
                result.put("error", "蓝牙管理器未初始化，请确保蓝牙前台服务已启动");
                result.put("timestamp", System.currentTimeMillis());
                Log.w(TAG, "getCurrentBluetoothSendData called but bluetoothManager is null - service may not be initialized yet");
            }
        } catch (Exception e) {
            result.put("data", new JSArray());
            result.put("length", 0);
            result.put("success", false);
            result.put("error", "获取蓝牙发送数据异常: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            Log.e(TAG, "Exception in getCurrentBluetoothSendData: " + e.getMessage(), e);
        }
        
        return result;
    }

    // 通知方法（需要通过插件实例调用，这里提供接口） 
    private void notifyBluetoothDataReceived(int[] data) {
        try {
            // 🎯 核心功能：更新CapacitorKtService中的最新接收数据，用于导航数据修改的基础
            CapacitorKtService ktService = CapacitorKtService.getInstance();
            if (ktService != null) {
                ktService.updateLatestReceivedBluetoothData(data);
                Log.d(TAG, "已更新最新接收的蓝牙数据到CapacitorKtService，数据长度: " + data.length + " bytes");
            } else {
                Log.w(TAG, "CapacitorKtService实例不可用，无法更新最新接收数据");
            }
            
            // 通过事件广播通知数据接收（如果需要的话）
            // 注意：BluetoothBackgroundPlugin没有getInstance()方法，所以暂时移除
            // TODO: 如果需要通知前端，可以通过其他方式实现
            
            Log.d(TAG, "蓝牙数据接收处理完成，数据长度: " + data.length + " bytes");
        } catch (Exception e) {
            Log.e(TAG, "处理蓝牙数据接收失败", e);
        }
    }

    private void notifyBluetoothSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
        Log.d(TAG, "Would notify bluetooth sending state changed");
    }

    private void notifyBluetoothError(String error, int errorCode) {
        Log.d(TAG, "Would notify bluetooth error: " + error);
    }

    // =================== Debug 模式崩溃修复 ===================
    
    /**
     * 检查服务初始化状态，用于调试模式下的问题排查
     * @return 服务状态信息
     */
    public static JSObject getServiceInitializationStatus() {
        JSObject status = new JSObject();
        try {
            status.put("isServiceRunning", isServiceRunning);
            status.put("bluetoothManagerInitialized", bluetoothManager != null);
            status.put("lastSendingStatusCached", lastSendingStatus != null);
            status.put("lastSendingStatsCached", lastSendingStats != null);
            status.put("timestamp", System.currentTimeMillis());
            
            if (bluetoothManager != null) {
                status.put("bluetoothManagerDetails", "已初始化 - " + bluetoothManager.toString());
            } else {
                status.put("bluetoothManagerDetails", "未初始化 - 可能服务尚未完全启动");
            }
            
            Log.d(TAG, "服务初始化状态检查: " + status.toString());
        } catch (Exception e) {
            status.put("error", "获取服务状态异常: " + e.getMessage());
            Log.e(TAG, "获取服务初始化状态异常: " + e.getMessage(), e);
        }
        return status;
    }
    
    /**
     * 安全地初始化蓝牙管理器，增加重试机制
     */
    private void safeInitializeBluetoothManager() {
        try {
            if (bluetoothManager == null) {
                Log.d(TAG, "开始初始化蓝牙管理器...");
                bluetoothManager = new NativeBluetoothManager(this);
                bluetoothManager.setCallback(new NativeBluetoothManager.BluetoothCallback() {
                    @Override
                    public void onConnectionStateChanged(boolean connected) {
                        Log.d(TAG, "Bluetooth connection state changed: " + connected);
                        updateNotificationForBluetooth(connected);
                    }

                    @Override
                    public void onDataReceived(int[] data) {
                        Log.d(TAG, "Bluetooth data received, length: " + data.length);
                        // 通过插件通知前端
                        notifyBluetoothDataReceived(data);
                    }

                    @Override
                    public void onSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
                        Log.d(TAG, "Bluetooth sending state changed - Active: " + active + 
                              ", SendCount: " + sendCount + ", ErrorCount: " + errorCount);
                        
                        // 更新缓存状态
                        lastSendingStatus = new NativeBluetoothManager.BluetoothSendingStatus(active, lastSendTime, sendCount, errorCount);
                        
                        // 通过插件通知前端
                        notifyBluetoothSendingStateChanged(active, lastSendTime, sendCount, errorCount);
                    }

                    @Override
                    public void onError(String error, int errorCode) {
                        Log.e(TAG, "Bluetooth error: " + error + " (Code: " + errorCode + ")");
                        // 通过插件通知前端
                        notifyBluetoothError(error, errorCode);
                    }
                });
                
                Log.d(TAG, "蓝牙管理器初始化成功");
            } else {
                Log.d(TAG, "蓝牙管理器已存在，跳过初始化");
            }
        } catch (Exception e) {
            Log.e(TAG, "蓝牙管理器初始化失败: " + e.getMessage(), e);
            // 可以考虑添加重试机制
            scheduleBluetoothManagerRetry();
        }
    }
    
    /**
     * 调度蓝牙管理器重试初始化
     */
    private void scheduleBluetoothManagerRetry() {
        if (keepAliveHandler != null) {
            keepAliveHandler.postDelayed(() -> {
                Log.d(TAG, "重试初始化蓝牙管理器...");
                safeInitializeBluetoothManager();
            }, 2000); // 2秒后重试
        }
    }

    @Override
    public void onDestroy() {
        // 清理蓝牙管理器
        if (bluetoothManager != null) {
            bluetoothManager.cleanup();
            bluetoothManager = null;
        }
        
        super.onDestroy();
        Log.d(TAG, "Service onDestroy");
        
        stopKeepAliveTimer();
        
        // 停止AlarmManager保活机制
        stopAlarmKeepAlive();
        
        // 取消注册广播接收器
        unregisterBroadcastReceivers();
        
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
            Log.d(TAG, "PARTIAL_WAKE_LOCK released");
        }
        
        if (screenWakeLock != null && screenWakeLock.isHeld()) {
            screenWakeLock.release();
            Log.d(TAG, "Screen wake lock released");
        }
        
        if (fullWakeLock != null && fullWakeLock.isHeld()) {
            fullWakeLock.release();
            Log.d(TAG, "Full wake lock released");
        }
        
        // 清理Doze模式对抗处理器
        if (dozeCounterHandler != null) {
            dozeCounterHandler.removeCallbacksAndMessages(null);
            dozeCounterHandler = null;
        }
        
        isServiceRunning = false;
    }
}