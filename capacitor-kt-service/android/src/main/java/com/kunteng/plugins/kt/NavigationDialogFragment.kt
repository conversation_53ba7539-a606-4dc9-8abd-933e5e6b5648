package com.kunteng.plugins.kt

import android.Manifest
import android.app.AlertDialog
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.content.res.Resources
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresPermission
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.getcapacitor.JSObject
import com.getcapacitor.PluginCall
import com.kunteng.plugins.kt.databinding.MapboxActivityNavigationViewBinding
import com.mapbox.api.directions.v5.DirectionsCriteria
import com.mapbox.api.directions.v5.models.RouteOptions
import com.mapbox.bindgen.Expected
import com.mapbox.geojson.Point
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.plugin.animation.camera
import com.mapbox.maps.plugin.locationcomponent.location
import com.mapbox.navigation.base.TimeFormat
import com.mapbox.navigation.base.extensions.applyDefaultNavigationOptions
import com.mapbox.navigation.base.extensions.applyLanguageAndVoiceUnitOptions
import com.mapbox.navigation.base.formatter.DistanceFormatterOptions
import com.mapbox.navigation.base.formatter.Rounding
import com.mapbox.navigation.base.options.NavigationOptions
import com.mapbox.navigation.base.route.NavigationRoute
import com.mapbox.navigation.base.route.NavigationRouterCallback
import com.mapbox.navigation.base.route.RouterFailure
import com.mapbox.navigation.base.route.RouterOrigin
import com.mapbox.navigation.core.MapboxNavigation
import com.mapbox.navigation.core.directions.session.RoutesObserver
import com.mapbox.navigation.core.formatter.MapboxDistanceFormatter
import com.mapbox.navigation.core.lifecycle.MapboxNavigationApp
import com.mapbox.navigation.core.lifecycle.MapboxNavigationObserver
import com.mapbox.navigation.core.trip.session.LocationMatcherResult
import com.mapbox.navigation.core.trip.session.LocationObserver
import com.mapbox.navigation.tripdata.maneuver.api.MapboxManeuverApi


import com.mapbox.navigation.tripdata.progress.api.MapboxTripProgressApi
import com.mapbox.navigation.tripdata.progress.model.DistanceRemainingFormatter
import com.mapbox.navigation.tripdata.progress.model.TripProgressUpdateValue
import com.mapbox.navigation.tripdata.progress.model.EstimatedTimeToArrivalFormatter
import com.mapbox.navigation.tripdata.progress.model.PercentDistanceTraveledFormatter
import com.mapbox.navigation.tripdata.progress.model.TimeRemainingFormatter
import com.mapbox.navigation.tripdata.progress.model.TripProgressUpdateFormatter
import com.mapbox.navigation.ui.maps.NavigationStyles
import com.mapbox.navigation.ui.maps.camera.NavigationCamera
import com.mapbox.navigation.ui.maps.camera.data.MapboxNavigationViewportDataSource
import com.mapbox.navigation.ui.maps.camera.lifecycle.NavigationBasicGesturesHandler
import com.mapbox.navigation.ui.maps.camera.state.NavigationCameraState
import com.mapbox.navigation.ui.maps.camera.transition.NavigationCameraTransitionOptions
import com.mapbox.navigation.ui.maps.location.NavigationLocationProvider
import com.mapbox.navigation.ui.maps.route.arrow.api.MapboxRouteArrowApi
import com.mapbox.navigation.ui.maps.route.arrow.api.MapboxRouteArrowView
import com.mapbox.navigation.ui.maps.route.arrow.model.RouteArrowOptions
import com.mapbox.navigation.ui.maps.route.line.api.MapboxRouteLineApi
import com.mapbox.navigation.ui.maps.route.line.api.MapboxRouteLineView
import com.mapbox.navigation.ui.maps.route.line.model.MapboxRouteLineApiOptions
import com.mapbox.navigation.ui.maps.route.line.model.MapboxRouteLineViewOptions
import com.mapbox.navigation.voice.api.MapboxSpeechApi
import com.mapbox.navigation.voice.api.MapboxVoiceInstructionsPlayer
import com.mapbox.navigation.voice.model.SpeechVolume
import java.util.Locale

class NavigationDialogFragment : DialogFragment(), NavigationUIUpdater {
    private companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private var currentCall: PluginCall? = null
        private var currentInstance: NavigationDialogFragment? = null

        @JvmStatic
        fun setCurrentCall(call: PluginCall?) {
            currentCall = call
        }
        
        @JvmStatic
        fun setCurrentInstance(instance: NavigationDialogFragment?) {
            currentInstance = instance
        }
        
        @JvmStatic
        fun getCurrentInstance(): NavigationDialogFragment? {
            return currentInstance
        }
    }

    private val BUTTON_ANIMATION_DURATION = 1500L
    private lateinit var binding: MapboxActivityNavigationViewBinding
    private lateinit var navigationCamera: NavigationCamera
    private lateinit var viewportDataSource: MapboxNavigationViewportDataSource

    /*
     * Below are generated camera padding values to ensure that the route fits well on screen while
     * other elements are overlaid on top of the map (including instruction view, buttons, etc.)
     */
    private val pixelDensity = Resources.getSystem().displayMetrics.density
    private val overviewPadding: EdgeInsets by lazy {
        EdgeInsets(
            140.0 * pixelDensity,
            40.0 * pixelDensity,
            120.0 * pixelDensity,
            40.0 * pixelDensity
        )
    }
    private val landscapeOverviewPadding: EdgeInsets by lazy {
        EdgeInsets(
            30.0 * pixelDensity,
            380.0 * pixelDensity,
            110.0 * pixelDensity,
            20.0 * pixelDensity
        )
    }
    private val followingPadding: EdgeInsets by lazy {
        EdgeInsets(
            180.0 * pixelDensity,
            40.0 * pixelDensity,
            150.0 * pixelDensity,
            40.0 * pixelDensity
        )
    }
    private val landscapeFollowingPadding: EdgeInsets by lazy {
        EdgeInsets(
            30.0 * pixelDensity,
            380.0 * pixelDensity,
            110.0 * pixelDensity,
            40.0 * pixelDensity
        )
    }
    private lateinit var maneuverApi: MapboxManeuverApi
    private lateinit var tripProgressApi: MapboxTripProgressApi
    private lateinit var routeLineApi: MapboxRouteLineApi
    private lateinit var routeLineView: MapboxRouteLineView
    private val routeArrowApi: MapboxRouteArrowApi = MapboxRouteArrowApi()
    private lateinit var routeArrowView: MapboxRouteArrowView
    private var isVoiceInstructionsMuted = false
    private lateinit var speechApi: MapboxSpeechApi
    private lateinit var voiceInstructionsPlayer: MapboxVoiceInstructionsPlayer
    private val navigationLocationProvider = NavigationLocationProvider()
    
    // 导航管理器
    private lateinit var navigationManager: NavigationManager

    // 位置更新观察者
    private val locationObserver = object : LocationObserver {
        var firstLocationUpdateReceived = false

        override fun onNewLocationMatcherResult(locationMatcherResult: LocationMatcherResult) {
            val enhancedLocation = locationMatcherResult.enhancedLocation
            navigationLocationProvider.changePosition(
                location = enhancedLocation,
                keyPoints = locationMatcherResult.keyPoints,
            )
            viewportDataSource.onLocationChanged(enhancedLocation)
            viewportDataSource.evaluate()
            if (!firstLocationUpdateReceived) {
                firstLocationUpdateReceived = true
                currentLocation =
                    Point.fromLngLat(enhancedLocation.longitude, enhancedLocation.latitude)
                currentLocation?.let { origin ->
                    destination?.let { findRoute(origin, it) }
                }
                navigationCamera.requestNavigationCameraToOverview(
                    stateTransitionOptions = NavigationCameraTransitionOptions.Builder()
                        .maxDuration(0)
                        .build()
                )
            }
        }

        override fun onNewRawLocation(rawLocation: com.mapbox.common.location.Location) {
            // 不处理原始位置
        }
    }

    // 路线进度观察者
    private val routeProgressObserver by lazy {
        navigationManager.createRouteProgressObserver(this)
    }

    // 路线变更观察者
    private val routesObserver = RoutesObserver { routeUpdateResult ->
        if (routeUpdateResult.navigationRoutes.isNotEmpty()) {
            routeLineApi.setNavigationRoutes(
                routeUpdateResult.navigationRoutes
            ) { value ->
                binding.mapView.mapboxMap.style?.apply {
                    routeLineView.renderRouteDrawData(this, value)
                }
            }
            viewportDataSource.onRouteChanged(routeUpdateResult.navigationRoutes.first())
            viewportDataSource.evaluate()
        } else {
            val style = binding.mapView.mapboxMap.style
            if (style != null) {
                routeLineApi.clearRouteLine { value ->
                    routeLineView.renderClearRouteLineValue(
                        style,
                        value
                    )
                }
                routeArrowView.render(style, routeArrowApi.clearArrows())
            }
            viewportDataSource.clearRouteData()
            viewportDataSource.evaluate()
        }
    }

    private lateinit var mapboxNavigation: MapboxNavigation

    private var currentLocation: Point? = null
    private var destination: Point? = null
    private var onNavigationStopCallbackSent = false
    private var isFragmentVisible = true // 控制Fragment可见性状态

    // 独立的 MapboxNavigationObserver 实例
    private val navigationObserver = object : MapboxNavigationObserver {
        @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
        override fun onAttached(mapboxNavigation: MapboxNavigation) {
            // 注册各种观察者
            mapboxNavigation.registerRoutesObserver(routesObserver)
            mapboxNavigation.registerLocationObserver(locationObserver)
            mapboxNavigation.registerRouteProgressObserver(routeProgressObserver)
            mapboxNavigation.startTripSession()
        }

        override fun onDetached(mapboxNavigation: MapboxNavigation) {
            // 注销各种观察者
            mapboxNavigation.unregisterRoutesObserver(routesObserver)
            mapboxNavigation.unregisterLocationObserver(locationObserver)
            mapboxNavigation.unregisterRouteProgressObserver(routeProgressObserver)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (!MapboxNavigationApp.isSetup()) {
            MapboxNavigationApp.setup {
                NavigationOptions.Builder(context).build()
            }
        }
        MapboxNavigationApp.attach(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 从插件获取当前调用并存储到静态变量
        val plugin = CapacitorMapboxNavigationPlugin.getInstance()
        setCurrentCall(plugin?.getCurrentCall())
        currentCall?.resolve()
        // 获取当前的 MapboxNavigation 实例
        mapboxNavigation = MapboxNavigationApp.current()!!
        binding = MapboxActivityNavigationViewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(
        view: View, savedInstanceState: Bundle?
    ) {
        super.onViewCreated(view, savedInstanceState)

        // 设置当前实例，用于外部控制
        setCurrentInstance(this)

        // 将 fragment 的生命周期与 MapboxNavigation 绑定
        MapboxNavigationApp.attach(this)

        // 检查位置权限
        checkLocationPermissionAndRequestRoute()

        initNavigation()
        
        // 设置返回键处理 - 隐藏而不是销毁
        dialog?.setOnKeyListener { _, keyCode, event ->
            if (keyCode == android.view.KeyEvent.KEYCODE_BACK && event.action == android.view.KeyEvent.ACTION_UP) {
                // 显示确认弹框
                AlertDialog.Builder(requireContext())
                    .setTitle("关闭导航")
                    .setMessage("选择操作：隐藏界面（导航继续运行）或完全停止导航")
                    .setPositiveButton("隐藏界面") { _, _ ->
                        hideNavigationInterface()
                    }
                    .setNegativeButton("停止导航") { _, _ ->
                        dismiss()
                    }
                    .setNeutralButton("取消") { _, _ ->
                        // 用户取消，不执行任何操作
                    }
                    .show()
                true // 消费事件，阻止默认的返回行为
            } else {
                false
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        
        // 清理当前实例引用
        setCurrentInstance(null)
        
        // 清空导航数据和镜像状态
        if (::navigationManager.isInitialized) {
            navigationManager.onNavigationFinished()
        }
        
        // 发送 onNavigationStop 回调
        sendOnNavigationStopCallback()
        // 解绑 MapboxNavigation 与 fragment 生命周期
        MapboxNavigationApp.detach(this)
        // 注销观察者
        MapboxNavigationApp.unregisterObserver(navigationObserver)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
    }

    private fun checkLocationPermissionAndRequestRoute() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.ACCESS_FINE_LOCATION
            )
            == PackageManager.PERMISSION_GRANTED
        ) {
            // 权限已授予，如果已有位置则立即请求路线
            currentLocation?.let { origin ->
                destination?.let { findRoute(origin, it) }
            }
        } else {
            // 请求位置权限
            requestPermissions(
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                LOCATION_PERMISSION_REQUEST_CODE
            )
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限授予，尝试请求路线
                checkLocationPermissionAndRequestRoute()
            } else {
                val data = JSObject()
                data.put("message", "位置权限被拒绝，无法获取当前位置")
                dismiss()
            }
        }
    }

    override fun onDestroy() {
        // 发送 onNavigationStop 回调（如果还没有发送过）
        sendOnNavigationStopCallback()
        currentCall = null
        super.onDestroy()
        if (::maneuverApi.isInitialized) maneuverApi.cancel()
        if (::routeLineApi.isInitialized) routeLineApi.cancel()
        if (::routeLineView.isInitialized) routeLineView.cancel()
        if (::speechApi.isInitialized) speechApi.cancel()
        if (::voiceInstructionsPlayer.isInitialized) voiceInstructionsPlayer.shutdown()
    }

    private fun initNavigation() {
        requireActivity().runOnUiThread {
            // 配置地图位置组件
            binding.mapView.location.apply {
                setLocationProvider(navigationLocationProvider)
                enabled = true
            }

            // 获取终点坐标
            destination = Point.fromLngLat(
                requireArguments().getDouble("toLng", 0.0),
                requireArguments().getDouble("toLat", 0.0)
            )

            // 检查位置权限并请求路线
            checkLocationPermissionAndRequestRoute()

            // 初始化导航相机
            viewportDataSource = MapboxNavigationViewportDataSource(binding.mapView.mapboxMap)
            navigationCamera = NavigationCamera(
                binding.mapView.mapboxMap,
                binding.mapView.camera,
                viewportDataSource
            )

            // 添加相机手势处理
            binding.mapView.camera.addCameraAnimationsLifecycleListener(
                NavigationBasicGesturesHandler(navigationCamera)
            )

            // 监听相机状态变化
            navigationCamera.registerNavigationCameraStateChangeObserver { navigationCameraState ->
                when (navigationCameraState) {
                    NavigationCameraState.TRANSITION_TO_FOLLOWING,
                    NavigationCameraState.FOLLOWING -> binding.recenter.visibility = View.INVISIBLE

                    NavigationCameraState.TRANSITION_TO_OVERVIEW,
                    NavigationCameraState.OVERVIEW,
                    NavigationCameraState.IDLE -> binding.recenter.visibility = View.VISIBLE
                }
            }

            // set the padding values depending on screen orientation and visible view layout
            if (this.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                viewportDataSource.overviewPadding = landscapeOverviewPadding
            } else {
                viewportDataSource.overviewPadding = overviewPadding
            }
            if (this.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                viewportDataSource.followingPadding = landscapeFollowingPadding
            } else {
                viewportDataSource.followingPadding = followingPadding
            }

            // 初始化距离格式化器
            val distanceFormatterOptions =
                DistanceFormatterOptions.Builder(requireContext()).roundingIncrement(Rounding.INCREMENT_FIVE).build()
            // 初始化操作 API
            maneuverApi = MapboxManeuverApi(
                MapboxDistanceFormatter(distanceFormatterOptions)
            )

            // 初始化行程进度 API
            tripProgressApi = MapboxTripProgressApi(
                TripProgressUpdateFormatter.Builder(requireContext())
                    .distanceRemainingFormatter(
                        DistanceRemainingFormatter(distanceFormatterOptions)
                    )
                    .timeRemainingFormatter(
                        TimeRemainingFormatter(requireContext())
                    )
                    .percentRouteTraveledFormatter(
                        PercentDistanceTraveledFormatter()
                    )
                    .estimatedTimeToArrivalFormatter(
                        EstimatedTimeToArrivalFormatter(requireContext(), TimeFormat.NONE_SPECIFIED)
                    )
                    .build()
            )

            // 初始化语音 API
            speechApi = MapboxSpeechApi(
                requireContext(),
                Locale.getDefault().language
            )

            // 初始化语音播放器
            voiceInstructionsPlayer = MapboxVoiceInstructionsPlayer(
                requireContext(),
                Locale.getDefault().language
            )

            // 初始化路线线 API 和视图
            val mapboxRouteLineViewOptions = MapboxRouteLineViewOptions.Builder(requireContext())
                .routeLineBelowLayerId("road-label-navigation")
                .build()
            val mapboxRouteLineApiOptions = MapboxRouteLineApiOptions.Builder()
                .build()
            routeLineApi = MapboxRouteLineApi(mapboxRouteLineApiOptions)
            routeLineView = MapboxRouteLineView(mapboxRouteLineViewOptions)

            // 初始化路线箭头 API 和视图
            val routeArrowOptions = RouteArrowOptions.Builder(requireContext()).build()
            routeArrowView = MapboxRouteArrowView(routeArrowOptions)

            // 加载地图样式
            binding.mapView.mapboxMap.loadStyle(NavigationStyles.NAVIGATION_DAY_STYLE) {
                // 样式加载完成
            }

            // 设置按钮点击事件 - 修改为隐藏而不是销毁
            binding.stop.setOnClickListener {
                // 显示确认弹框
                AlertDialog.Builder(requireContext())
                    .setTitle("关闭导航")
                    .setMessage("选择操作：隐藏界面（导航继续运行）或完全停止导航")
                    .setPositiveButton("隐藏界面") { _, _ ->
                        hideNavigationInterface()
                    }
                    .setNegativeButton("停止导航") { _, _ ->
                        dismiss()
                    }
                    .setNeutralButton("取消") { _, _ ->
                        // 用户取消，不执行任何操作
                    }
                    .show()
            }

            binding.recenter.setOnClickListener {
                navigationCamera.requestNavigationCameraToFollowing()
                binding.routeOverview.showTextAndExtend(BUTTON_ANIMATION_DURATION)
            }

            binding.routeOverview.setOnClickListener {
                navigationCamera.requestNavigationCameraToOverview()
                binding.recenter.showTextAndExtend(BUTTON_ANIMATION_DURATION)
            }

            binding.soundButton.setOnClickListener {
                isVoiceInstructionsMuted = !isVoiceInstructionsMuted
                if (isVoiceInstructionsMuted) {
                    binding.soundButton.muteAndExtend(BUTTON_ANIMATION_DURATION)
                    voiceInstructionsPlayer.volume(SpeechVolume(0f))
                } else {
                    binding.soundButton.unmuteAndExtend(BUTTON_ANIMATION_DURATION)
                    voiceInstructionsPlayer.volume(SpeechVolume(1f))
                }
            }

            binding.soundButton.unmute()
            
            // 设置隐藏导航界面按钮点击事件
            binding.hideNavigationButton.setOnClickListener {
                // 显示确认弹框
                AlertDialog.Builder(requireContext())
                    .setTitle("隐藏导航界面")
                    .setMessage("是否隐藏导航界面？导航服务将继续在后台运行。")
                    .setPositiveButton("隐藏") { _, _ ->
                        hideNavigationInterface()
                    }
                    .setNegativeButton("取消") { _, _ ->
                        // 用户取消，不执行任何操作
                    }
                    .show()
            }
            
            // 初始化导航管理器
            navigationManager = NavigationManager(
                context = requireContext(),
                mapboxNavigation = mapboxNavigation,
                navigationCamera = navigationCamera,
                routeArrowApi = routeArrowApi,
                routeArrowView = routeArrowView,
                maneuverApi = maneuverApi,
                tripProgressApi = tripProgressApi,
                viewportDataSource = viewportDataSource
            )
            
            // 设置镜像按钮点击事件
            binding.mirrorButton.setOnClickListener {
                // 显示投屏确认弹框
                val isCurrentlyEnabled = navigationManager.isMirrorEnabled()
                AlertDialog.Builder(requireContext())
                    .setTitle("投屏确认")
                    .setMessage(if (isCurrentlyEnabled) "是否关闭投屏？" else "是否开启投屏？")
                    .setPositiveButton(if (isCurrentlyEnabled) "关闭" else "开启") { _, _ ->
                        val newState = !isCurrentlyEnabled
                        navigationManager.handleMirrorStateChange(newState)
                        
                        // 更新按钮状态
                        if (newState) {
                            binding.mirrorButton.setColorFilter(Color.GREEN)
                        } else {
                            binding.mirrorButton.setColorFilter(Color.GRAY)
                        }
                    }
                    .setNegativeButton("取消") { _, _ ->
                        // 用户取消，不做任何操作
                    }
                    .show()
            }
            
            // 注册 MapboxNavigation 观察者（在所有初始化完成后）
            MapboxNavigationApp.registerObserver(navigationObserver)
        }
    }

    private fun findRoute(origin: Point, destination: Point) {
        mapboxNavigation.requestRoutes(
            RouteOptions.builder()
                .applyDefaultNavigationOptions(DirectionsCriteria.PROFILE_CYCLING)
                .applyLanguageAndVoiceUnitOptions(requireContext())
                .coordinatesList(listOf(origin, destination))
                .layersList(listOf(mapboxNavigation.getZLevel(), null))
                .alternatives(true)
                .build(),
            object : NavigationRouterCallback {
                override fun onCanceled(
                    routeOptions: RouteOptions,
                    @RouterOrigin routerOrigin: String
                ) {
                    Log.e("Mapbox Navigation", "onCanceled")
                    val data = JSObject()
                    data.put("message", "Route Navigation cancelled")
                    dismiss()
                }

                override fun onFailure(reasons: List<RouterFailure>, routeOptions: RouteOptions) {
                    Log.e("Mapbox Navigation", "onFailure: $reasons")
                    val data = JSObject()
                    data.put("message", "Failed to calculate route")
                    dismiss()
                }

                override fun onRoutesReady(
                    routes: List<NavigationRoute>,
                    @RouterOrigin routerOrigin: String
                ) {
                    setRouteAndStartNavigation(routes)
                }
            }
        )
    }

    private fun setRouteAndStartNavigation(routes: List<NavigationRoute>) {
        try {
            mapboxNavigation.setNavigationRoutes(routes)
            binding.soundButton.visibility = View.VISIBLE
            binding.routeOverview.visibility = View.VISIBLE
            binding.tripProgressCard.visibility = View.VISIBLE
            binding.mirrorButton.visibility = View.VISIBLE
            binding.hideNavigationButton.visibility = View.VISIBLE
            isVoiceInstructionsMuted = !isVoiceInstructionsMuted
            
            // 发送导航启动成功状态
            sendNavigationStartSuccess()
            
            // Show screen mirroring confirmation dialog
            AlertDialog.Builder(requireContext())
                .setTitle("投屏确认")
                .setMessage("是否开启投屏？")
                .setPositiveButton("开启") { _, _ ->
                    navigationManager.handleMirrorStateChange(true)
                    // 更新按钮状态
                    binding.mirrorButton.setColorFilter(Color.GREEN)
                }
                .setNegativeButton("取消") { _, _ ->
                    navigationManager.handleMirrorStateChange(false)
                    // 更新按钮状态
                    binding.mirrorButton.setColorFilter(Color.GRAY)
                }
                .show()
            navigationCamera.requestNavigationCameraToFollowing()
        } catch (e: Exception) {
            Log.e("NavigationDialogFragment", "导航启动失败", e)
            sendNavigationStartError(e.message ?: "导航启动失败")
        }
    }
    
    /**
     * 发送导航启动成功状态
     */
    private fun sendNavigationStartSuccess() {
        val plugin = CapacitorMapboxNavigationPlugin.getInstance()
        if (plugin != null && currentCall != null) {
            val result = JSObject()
            result.put("status", "success")
            result.put("message", "导航启动成功")
            result.put("timestamp", System.currentTimeMillis())
            currentCall?.resolve(result)
            currentCall = null
        }
    }
    
    /**
     * 发送导航启动失败状态
     */
    private fun sendNavigationStartError(errorMessage: String) {
        val plugin = CapacitorMapboxNavigationPlugin.getInstance()
        if (plugin != null && currentCall != null) {
            currentCall?.reject("NAVIGATION_START_FAILED", errorMessage)
            currentCall = null
        }
    }



    /**
     * 发送 onNavigationStop 回调
     */
    private fun sendOnNavigationStopCallback() {
        // 避免重复发送
        if (onNavigationStopCallbackSent) {
            return
        }
        
        val plugin = CapacitorMapboxNavigationPlugin.getInstance()
        val result = JSObject()
        result.put("status", "success")
        result.put("type", "onNavigationStop")
        
        val content = JSObject()
        content.put("message", "Navigation stopped")
        content.put("timestamp", System.currentTimeMillis())
        result.put("content", content)
        
        plugin?.triggerOnNavigationStopEvent(result)
    }

    // =================== NavigationUIUpdater 接口实现 ===================
    
    override fun getMapStyle(): com.mapbox.maps.Style? {
        return binding.mapView.mapboxMap.style
    }
    
    override fun onManeuverError(errorMessage: String) {
        // DialogFragment中可以选择不显示错误，或者用其他方式处理
        Log.e("NavigationDialogFragment", "Maneuver error: $errorMessage")
    }

    @Suppress("UNCHECKED_CAST")
    override fun onManeuverUpdate(maneuvers: Expected<*, *>) {
        maneuvers.fold(
            { error ->
                // 处理错误情况
                binding.maneuverView.visibility = View.GONE
            },
            {
                // 处理成功情况
                binding.maneuverView.visibility = View.VISIBLE
                // 使用反射来避免类型检查问题
                try {
                    val renderMethod = binding.maneuverView.javaClass.getMethod("renderManeuvers", Expected::class.java)
                    renderMethod.invoke(binding.maneuverView, maneuvers)
                } catch (e: Exception) {
                    // 如果反射失败，隐藏视图
                    binding.maneuverView.visibility = View.GONE
                }
            }
        )
    }
    
    override fun onTripProgressUpdate(tripProgress: TripProgressUpdateValue) {
        binding.tripProgressView.render(tripProgress)
    }
    
    override fun onNavigationComplete() {
        // 导航完成的UI处理
        Log.d("NavigationDialogFragment", "导航已完成")
    }
    
    /**
     * 隐藏导航界面（但不停止导航）
     */
    fun hideNavigationInterface() {
        try {
            if (::binding.isInitialized && isFragmentVisible) {
                dialog?.window?.let { window ->
                    val layoutParams = window.attributes
                    // 设置内容完全透明
                    layoutParams.alpha = 0.0f
                    // 移除背景遮罩，防止黑色半透明效果
                    layoutParams.dimAmount = 0.0f
                    window.attributes = layoutParams
                    
                    // 清除背景遮罩标志
                    window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_DIM_BEHIND)
                    // 禁用触摸事件，防止拦截用户操作
                    window.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                        android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
                    )
                }
                isFragmentVisible = false
                Log.d("NavigationDialogFragment", "导航界面已隐藏")
            }
        } catch (e: Exception) {
            Log.e("NavigationDialogFragment", "隐藏导航界面失败: ${e.message}")
        }
    }
    
    /**
     * 显示导航界面
     */
    fun showNavigationInterface() {
        try {
            if (::binding.isInitialized && !isFragmentVisible) {
                dialog?.window?.let { window ->
                    val layoutParams = window.attributes
                    // 恢复内容完全可见
                    layoutParams.alpha = 1.0f
                    // 恢复背景遮罩
                    layoutParams.dimAmount = 0.5f  // 标准的半透明遮罩
                    window.attributes = layoutParams
                    
                    // 恢复背景遮罩标志
                    window.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_DIM_BEHIND,
                        android.view.WindowManager.LayoutParams.FLAG_DIM_BEHIND
                    )
                    // 恢复触摸事件
                    window.clearFlags(android.view.WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
                }
                isFragmentVisible = true
                Log.d("NavigationDialogFragment", "导航界面已显示")
            }
        } catch (e: Exception) {
            Log.e("NavigationDialogFragment", "显示导航界面失败: ${e.message}")
        }
    }
    
    /**
     * 获取导航界面可见性状态
     */
    fun getNavigationInterfaceVisibility(): Boolean {
        return isFragmentVisible
    }
}