package com.kunteng.plugins.kt

import android.util.Log
import com.getcapacitor.JSObject
import com.mapbox.navigation.base.trip.model.RouteProgress
import com.mapbox.navigation.base.trip.model.RouteProgressState
import com.mapbox.navigation.core.trip.session.RouteProgressObserver
import com.mapbox.navigation.tripdata.maneuver.api.MapboxManeuverApi

import com.mapbox.bindgen.Expected
import com.mapbox.navigation.tripdata.progress.api.MapboxTripProgressApi
import com.mapbox.navigation.tripdata.progress.model.TripProgressUpdateValue
import com.mapbox.navigation.ui.maps.camera.data.MapboxNavigationViewportDataSource
import com.mapbox.navigation.ui.maps.route.arrow.api.MapboxRouteArrowApi
import com.mapbox.navigation.ui.maps.route.arrow.api.MapboxRouteArrowView
import android.content.Context
import com.mapbox.navigation.core.MapboxNavigation
import com.mapbox.navigation.ui.maps.camera.NavigationCamera

/**
 * 导航管理器 - 管理NavigationActivity和NavigationDialogFragment的共同逻辑
 * 包括路线进度处理、蓝牙数据发送、镜像状态管理等
 */
class NavigationManager(
    private val context: Context,
    private val mapboxNavigation: MapboxNavigation,
    private val navigationCamera: NavigationCamera,
    private val routeArrowApi: MapboxRouteArrowApi,
    private val routeArrowView: MapboxRouteArrowView,
    private val maneuverApi: MapboxManeuverApi,
    private val tripProgressApi: MapboxTripProgressApi,
    private val viewportDataSource: MapboxNavigationViewportDataSource
) {
    
    // 日志标签
    private val tag = "NavigationManager"
    
    // 镜像状态管理
    private var isMirrorEnabled = false
    
    // 全局镜像状态管理 - 供CapacitorKtService获取
    companion object {
        private var globalMirrorEnabled = false
        
        @JvmStatic
        fun getGlobalMirrorState(): Boolean = globalMirrorEnabled
        
        @JvmStatic
        fun setGlobalMirrorState(enabled: Boolean) {
            globalMirrorEnabled = enabled
        }
    }
    
    // 数据构建器
    private val dataBuilder = NavigationDataBuilder()
    
    /**
     * 设置镜像状态
     */
    fun setMirrorEnabled(enabled: Boolean) {
        isMirrorEnabled = enabled
        globalMirrorEnabled = enabled // 同步到全局状态
        Log.d(tag, "镜像状态更新: $enabled")
    }
    
    /**
     * 获取镜像状态
     */
    fun isMirrorEnabled(): Boolean = isMirrorEnabled
    
    /**
     * 创建路线进度观察者
     * @param uiUpdater UI更新回调接口
     */
    fun createRouteProgressObserver(uiUpdater: NavigationUIUpdater): RouteProgressObserver {
        return object : RouteProgressObserver {
            override fun onRouteProgressChanged(routeProgress: RouteProgress) {
                // 处理核心导航逻辑
                handleRouteProgressChange(routeProgress, uiUpdater)
            }
        }
    }
    
    /**
     * 处理路线进度变化的核心逻辑
     */
    private fun handleRouteProgressChange(routeProgress: RouteProgress, uiUpdater: NavigationUIUpdater) {
        Log.d(tag, "RouteProgress: $routeProgress")
        
        // 1. 更新视口数据源
        viewportDataSource.onRouteProgressChanged(routeProgress)
        viewportDataSource.evaluate()
        
        // 2. 更新地图样式和箭头
        uiUpdater.getMapStyle()?.let { style ->
            val maneuverArrowResult = routeArrowApi.addUpcomingManeuverArrow(routeProgress)
            routeArrowView.renderManeuverUpdate(style, maneuverArrowResult)
        }
        
        // 3. 更新操作指示
        val maneuvers = maneuverApi.getManeuvers(routeProgress)
        // 直接传递 Expected 类型给 UI 更新器
        uiUpdater.onManeuverUpdate(maneuvers)
        
        // 4. 更新行程进度
        val tripProgress = tripProgressApi.getTripProgress(routeProgress)
        uiUpdater.onTripProgressUpdate(tripProgress)
        
        // 5. 发送路线进度事件到CapacitorKtService (通过事件监听器)
        sendRouteProgressEvent(routeProgress)
        
        // 6. 处理蓝牙数据发送
        sendNavigationDataToBluetooth(routeProgress)
        
        // 7. 检查是否到达终点
        if (routeProgress.currentState == RouteProgressState.COMPLETE) {
            sendNavigationCompleteEvent()
            uiUpdater.onNavigationComplete()
        }
    }
    
    /**
     * 发送路线进度事件到CapacitorKtService
     */
    private fun sendRouteProgressEvent(routeProgress: RouteProgress) {
        try {
            val ktService = CapacitorKtService.getInstance()
            if (ktService != null) {
                val data = JSObject()
                data.put(
                    "bannerInstructions",
                    routeProgress.bannerInstructions?.toJson()
                )
                data.put("distanceRemaining", routeProgress.distanceRemaining)
                data.put(
                    "stepDistanceRemaining",
                    routeProgress.currentLegProgress?.currentStepProgress?.distanceRemaining
                )
                data.put("timestamp", System.currentTimeMillis())
                
                ktService.triggerRouteProgressEvent(data)
                Log.d(tag, "已发送路线进度事件")
            } else {
                Log.w(tag, "CapacitorKtService实例不可用，无法发送路线进度事件")
            }
        } catch (e: Exception) {
            Log.e(tag, "发送路线进度事件失败", e)
        }
    }
    
    /**
     * 发送导航完成事件到CapacitorKtService
     */
    private fun sendNavigationCompleteEvent() {
        try {
            val ktService = CapacitorKtService.getInstance()
            if (ktService != null) {
                ktService.triggerOnNavigationCompleteEvent()
                Log.d(tag, "已发送导航完成事件")
            } else {
                Log.w(tag, "CapacitorKtService实例不可用，无法发送导航完成事件")
            }
        } catch (e: Exception) {
            Log.e(tag, "发送导航完成事件失败", e)
        }
    }
    
    /**
     * 直接在Native端处理导航数据的蓝牙发送
     * 基于Vue端的完整数据进行修改，确保包含导航信息
     */
    private fun sendNavigationDataToBluetooth(routeProgress: RouteProgress) {
        // 只有在镜像开启时才发送导航数据
        if (!isMirrorEnabled) {
            Log.d(tag, "镜像未开启，跳过导航数据发送")
            return
        }
        
        try {
            // 获取CapacitorKtService实例
            val ktService = CapacitorKtService.getInstance()
            if (ktService == null) {
                Log.e(tag, "❌ CapacitorKtService实例不可用")
                return
            }
            
            // 🔧 正确的数据流：获取当前蓝牙发送数据作为基础，仅更新导航部分(12-15)
            var currentBluetoothData: IntArray? = null
            
            // 步骤1：获取当前正在发送的蓝牙数据
            try {
                val currentData = ktService.getCurrentBluetoothSendDataSync()
                if (currentData != null && currentData.size >= 18) {
                    currentBluetoothData = currentData.clone()
                    Log.d(tag, "✅ 获取当前蓝牙发送数据作为基础，数据长度: ${currentBluetoothData.size}")
                    Log.d(tag, "当前蓝牙发送数据: ${currentBluetoothData.joinToString { "0x%02X".format(it) }}")
                    
                    // 记录当前协议部分和导航部分
                    Log.d(tag, "当前协议部分(0-11): ${currentBluetoothData.sliceArray(0..11).joinToString { "0x%02X".format(it) }}")
                    Log.d(tag, "当前导航部分(12-15): ${currentBluetoothData.sliceArray(12..15).joinToString { "0x%02X".format(it) }}")
                } else {
                    Log.w(tag, "⚠️ 无法获取当前蓝牙发送数据或数据长度不足")
                }
            } catch (e: Exception) {
                Log.e(tag, "❌ 获取当前蓝牙发送数据失败: ${e.message}")
            }
            
            // 步骤2：如果无法获取当前数据，使用默认基础数据（仅首次或异常情况）
            if (currentBluetoothData == null) {
                Log.w(tag, "⚠️ 无法获取当前蓝牙发送数据，使用默认基础数据")
                currentBluetoothData = intArrayOf(
                    0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA,  // 0-7: 基础协议
                    0x84, 0x14, 0x65, 0x32,                           // 8-11: 通信协议
                    0x00, 0x00, 0x00, 0x00,                           // 12-15: 导航数据（将被填充）
                    0x00,                                             // 16: 校验和（将被计算）
                    0x0E                                              // 17: 结束位
                )
                Log.d(tag, "使用默认基础数据: ${currentBluetoothData.joinToString { "0x%02X".format(it) }}")
            }
            
            // 步骤3：仅更新导航数据部分(12-15)，保持协议部分(0-11)不变
            Log.d(tag, "📊 步骤3: 更新导航数据部分(12-15)...")
            val navigationData = dataBuilder.modifyNavigationData(currentBluetoothData, routeProgress)
            
            Log.d(tag, "更新导航数据后: ${navigationData.joinToString { "0x%02X".format(it) }}")
            Log.d(tag, "协议部分保持不变(0-11): ${navigationData.sliceArray(0..11).joinToString { "0x%02X".format(it) }}")
            Log.d(tag, "新的导航数据(12-15): ${navigationData.sliceArray(12..15).joinToString { "0x%02X".format(it) }}")
            
            // 设置镜像位 - 确保镜像状态正确反映
            if (isMirrorEnabled) {
                navigationData[12] = navigationData[12] or 0x80 // 设置镜像位
                Log.d(tag, "✅ 设置镜像位: 字节12 = 0x%02X".format(navigationData[12]))
            } else {
                navigationData[12] = navigationData[12] and 0x7F // 清除镜像位
                Log.d(tag, "✅ 清除镜像位: 字节12 = 0x%02X".format(navigationData[12]))
            }
            
            // 重新计算校验和（因为修改了字节12）
            navigationData[16] = dataBuilder.calculateChecksum(navigationData)
            Log.d(tag, "✅ 重新计算校验和: 字节16 = 0x%02X".format(navigationData[16]))
            
            // 发送修改后的数据
            ktService.updateBluetoothSendDataInternal(navigationData)
            
            Log.d(tag, "🚀 已发送导航数据到蓝牙: ${navigationData.joinToString { "0x%02X".format(it) }}")
            Log.d(tag, "📊 导航数据详情 - 字节12: 0x%02X, 字节13: 0x%02X, 字节14: 0x%02X, 字节15: 0x%02X".format(
                navigationData[12], navigationData[13], navigationData[14], navigationData[15]))
            
            // 验证发送的数据是否与预期一致
            try {
                val verificationData = ktService.getCurrentBluetoothSendDataSync()
                if (verificationData != null && verificationData.contentEquals(navigationData)) {
                    Log.d(tag, "✅ 数据发送验证成功")
                } else {
                    Log.w(tag, "⚠️ 数据发送验证失败，可能存在数据不一致")
                    if (verificationData != null) {
                        Log.w(tag, "实际发送的数据: ${verificationData.joinToString { "0x%02X".format(it) }}")
                    }
                }
            } catch (e: Exception) {
                Log.w(tag, "数据发送验证异常: ${e.message}")
            }
        } catch (e: Exception) {
            Log.e(tag, "发送导航数据到蓝牙失败", e)
        }
    }
    
    /**
     * 构建导航蓝牙数据（兼容性方法）
     * @deprecated 现在使用基于原始数据的修改方法
     */
    @Deprecated("使用基于原始数据的修改方法")
    private fun buildNavigationBluetoothData(routeProgress: RouteProgress): IntArray {
        val result = dataBuilder.buildNavigationBluetoothData(routeProgress)
        
        // 处理镜像位设置 - 确保镜像状态正确反映
        if (isMirrorEnabled) {
            result[12] = result[12] or 0x80 // 设置镜像位
            Log.d(tag, "兼容方法设置镜像位: 字节12 = 0x%02X".format(result[12]))
        } else {
            result[12] = result[12] and 0x7F // 清除镜像位
            Log.d(tag, "兼容方法清除镜像位: 字节12 = 0x%02X".format(result[12]))
        }
        
        // 重新计算校验和（因为修改了字节12）
        result[16] = dataBuilder.calculateChecksum(result)
        Log.d(tag, "兼容方法重新计算校验和: 字节16 = 0x%02X".format(result[16]))
        
        return result
    }

    /**
     * 清空导航数据的蓝牙发送
     * 正确的数据流：获取当前蓝牙发送数据 → 清空导航部分(12-15) → 重新计算校验和 → 发送到Native端
     */
    fun clearNavigationBluetoothData() {
        try {
            Log.d(tag, "🔄 开始清空导航数据（基于当前发送数据）")
            
            val ktService = CapacitorKtService.getInstance()
            if (ktService == null) {
                Log.e(tag, "❌ CapacitorKtService实例不可用")
                return
            }
            
            // 🔧 步骤1：获取当前正在发送的蓝牙数据
            var currentBluetoothData: IntArray? = null
            
            try {
                val currentData = ktService.getCurrentBluetoothSendDataSync()
                if (currentData != null && currentData.size >= 18) {
                    currentBluetoothData = currentData.clone()
                    Log.d(tag, "✅ 获取当前蓝牙发送数据作为基础")
                    Log.d(tag, "清空前数据: ${currentBluetoothData.joinToString { "0x%02X".format(it) }}")
                    Log.d(tag, "清空前导航数据(12-15): ${currentBluetoothData.sliceArray(12..15).joinToString { "0x%02X".format(it) }}")
                } else {
                    Log.w(tag, "⚠️ 无法获取当前蓝牙发送数据或数据长度不足")
                }
            } catch (e: Exception) {
                Log.e(tag, "❌ 获取当前蓝牙发送数据失败: ${e.message}")
            }
            
            // 步骤2：如果无法获取当前数据，使用默认基础数据
            if (currentBluetoothData == null) {
                Log.w(tag, "⚠️ 无法获取当前蓝牙发送数据，使用默认基础数据")
                currentBluetoothData = intArrayOf(
                    0x0F, 0x05, 0xF5, 0x58, 0x2E, 0x00, 0x38, 0xCA,  // 0-7: 基础协议
                    0x84, 0x14, 0x65, 0x32,                           // 8-11: 通信协议
                    0x00, 0x00, 0x00, 0x00,                           // 12-15: 导航数据
                    0x00,                                             // 16: 校验和
                    0x0E                                              // 17: 结束位
                )
            }
            
            // 步骤3：清空导航数据部分(12-15)，保持协议部分(0-11)不变
            Log.d(tag, "📊 步骤3: 清空导航数据部分(12-15)...")
            
            // 保存镜像位状态（字节12的第7位）
            val originalMirrorBit = currentBluetoothData[12] and 0x80
            
            // 清空导航数据
            currentBluetoothData[12] = 0x00  // 清空方向+距离单位
            currentBluetoothData[13] = 0x00  // 清空单次距离低位
            currentBluetoothData[14] = 0x00  // 清空总距离高位+单次距离高位
            currentBluetoothData[15] = 0x00  // 清空总距离低位
            
            // 恢复镜像位
            if (originalMirrorBit != 0) {
                currentBluetoothData[12] = currentBluetoothData[12] or 0x80
                Log.d(tag, "✅ 恢复镜像位: 字节12 = 0x%02X".format(currentBluetoothData[12]))
            }
            
            Log.d(tag, "清空后导航数据(12-15): ${currentBluetoothData.sliceArray(12..15).joinToString { "0x%02X".format(it) }}")
            
            // 步骤4：重新计算校验和（字节16）
            Log.d(tag, "📊 步骤4: 重新计算校验和...")
            var checksum = 0
            
            // 校验和计算：对字节1-15进行异或运算（跳过字节0, 5, 16, 17）
            for (i in 1..15) {
                if (i != 5) { // 跳过字节5
                    checksum = checksum xor currentBluetoothData[i]
                }
            }
            
            currentBluetoothData[16] = checksum and 0xFF
            Log.d(tag, "✅ 重新计算校验和: 字节16 = 0x%02X".format(currentBluetoothData[16]))
            
            // 步骤5：发送清空后的数据
            Log.d(tag, "📊 步骤5: 发送清空后的数据...")
            ktService.updateBluetoothSendDataInternal(currentBluetoothData)
            
            Log.d(tag, "🚀 已发送清空导航数据: ${currentBluetoothData.joinToString { "0x%02X".format(it) }}")
            Log.d(tag, "📊 协议部分保持不变(0-11): ${currentBluetoothData.sliceArray(0..11).joinToString { "0x%02X".format(it) }}")
            Log.d(tag, "📊 导航数据已清空(12-15): ${currentBluetoothData.sliceArray(12..15).joinToString { "0x%02X".format(it) }}")
            
            // 验证清空结果
            try {
                val verificationData = ktService.getCurrentBluetoothSendDataSync()
                if (verificationData != null && verificationData.contentEquals(currentBluetoothData)) {
                    Log.d(tag, "✅ 导航数据清空验证成功")
                } else {
                    Log.w(tag, "⚠️ 导航数据清空验证失败，可能存在数据不一致")
                }
            } catch (e: Exception) {
                Log.w(tag, "导航数据清空验证异常: ${e.message}")
            }
            
        } catch (e: Exception) {
            Log.e(tag, "清空导航蓝牙数据失败", e)
        }
    }
    
    /**
     * 导航结束时的清理工作
     */
    fun onNavigationFinished() {
        isMirrorEnabled = false
        clearNavigationBluetoothData()
        Log.d(tag, "导航已结束，已清理相关数据")
    }
    
    /**
     * 处理镜像状态变化
     */
    fun handleMirrorStateChange(enabled: Boolean) {
        setMirrorEnabled(enabled)
        
        try {
            val ktService = CapacitorKtService.getInstance()
            if (ktService != null) {
                // 发送镜像状态事件
                val data = JSObject()
                data.put("enabled", enabled)
                data.put("timestamp", System.currentTimeMillis())
                ktService.triggerScreenMirroringEvent(data)
                
                // 更新当前蓝牙数据的镜像位并重新计算校验和
                updateCurrentBluetoothDataMirrorBit(enabled)
                
                val action = if (enabled) "开启" else "关闭"
                Log.d(tag, "${action}导航镜像，蓝牙数据发送状态: $enabled")
            } else {
                Log.w(tag, "CapacitorKtService实例不可用，无法发送镜像状态事件")
            }
        } catch (e: Exception) {
            Log.e(tag, "发送镜像状态事件失败", e)
        }
    }
    
    /**
     * 更新当前蓝牙数据的镜像位并重新计算校验和
     */
    private fun updateCurrentBluetoothDataMirrorBit(enabled: Boolean) {
        try {
            // 直接调用BluetoothForegroundService的静态方法获取当前数据
            val currentDataResult = com.kunteng.plugins.kt.BluetoothForegroundService.getCurrentBluetoothSendData()
            if (currentDataResult != null && currentDataResult.has("success") && currentDataResult.getBoolean("success")) {
                val dataArray = currentDataResult.getJSONArray("data")
                if (dataArray != null && dataArray.length() >= 18) {
                    // 转换为IntArray
                    val currentData = IntArray(dataArray.length())
                    for (i in 0 until dataArray.length()) {
                        currentData[i] = dataArray.getInt(i)
                    }
                    
                    // 修改镜像位
                    if (enabled) {
                        currentData[12] = currentData[12] or 0x80 // 设置镜像位
                        Log.d(tag, "镜像状态变化：设置镜像位，字节12 = 0x%02X".format(currentData[12]))
                    } else {
                        currentData[12] = currentData[12] and 0x7F // 清除镜像位
                        Log.d(tag, "镜像状态变化：清除镜像位，字节12 = 0x%02X".format(currentData[12]))
                    }
                    
                    // 重新计算校验和
                    currentData[16] = dataBuilder.calculateChecksum(currentData)
                    Log.d(tag, "镜像状态变化：重新计算校验和，字节16 = 0x%02X".format(currentData[16]))
                    
                    // 发送更新后的数据
                    val ktService = CapacitorKtService.getInstance()
                    if (ktService != null) {
                        ktService.updateBluetoothSendDataInternal(currentData)
                        Log.d(tag, "镜像状态变化：已更新蓝牙数据")
                    } else {
                        Log.w(tag, "镜像状态变化：CapacitorKtService实例不可用")
                    }
                } else {
                    Log.w(tag, "镜像状态变化：当前蓝牙数据无效或长度不足")
                }
            } else {
                Log.w(tag, "镜像状态变化：获取当前蓝牙数据失败")
            }
        } catch (e: Exception) {
            Log.e(tag, "镜像状态变化：更新蓝牙数据失败", e)
        }
    }
}

/**
 * UI更新回调接口 - 让不同的UI实现类提供具体的UI更新逻辑
 */
interface NavigationUIUpdater {
    /**
     * 获取地图样式
     */
    fun getMapStyle(): com.mapbox.maps.Style?
    
    /**
     * 操作指示错误处理
     */
    fun onManeuverError(errorMessage: String)
    
        /**
     * 操作指示更新  
     */
    fun onManeuverUpdate(maneuvers: Expected<*, *>)
    
    /**
     * 行程进度更新
     */
    fun onTripProgressUpdate(tripProgress: TripProgressUpdateValue)
    
    /**
     * 导航完成回调
     */
    fun onNavigationComplete()
}