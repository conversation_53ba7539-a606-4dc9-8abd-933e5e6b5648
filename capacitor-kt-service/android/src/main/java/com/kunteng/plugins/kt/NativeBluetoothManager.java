package com.kunteng.plugins.kt;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.Log;
import java.util.UUID;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

public class NativeBluetoothManager {
    private static final String TAG = "NativeBluetoothManager";
    
    // 蓝牙相关
    private BluetoothAdapter bluetoothAdapter;
    private BluetoothGatt bluetoothGatt;
    private BluetoothGattCharacteristic targetCharacteristic;
    private Context context;
    
    // 连接参数
    private String deviceId;
    private UUID serviceUUID;
    private UUID characteristicUUID;
    
    // 发送参数
    private int[] sendData;
    private volatile long dataVersion = 0; // 数据版本号，用于确保使用最新数据
    private volatile long lastUpdateTime = 0; // 最后更新时间
    private int sendInterval = 106; // 默认106ms
    private AtomicBoolean isSending = new AtomicBoolean(false);
    private AtomicBoolean isConnected = new AtomicBoolean(false);
    
    // 统计信息
    private AtomicLong totalSent = new AtomicLong(0);
    private AtomicLong successCount = new AtomicLong(0);
    private AtomicLong errorCount = new AtomicLong(0);
    private AtomicLong lastSendTime = new AtomicLong(0);
    private String lastError = null;
    
    // 定时器相关
    private ScheduledExecutorService sendExecutor;
    private ScheduledFuture<?> sendTask;
    private HandlerThread bluetoothThread;
    private Handler bluetoothHandler;
    
    // 连接重试
    private static final int MAX_RETRY_COUNT = 5;
    private int retryCount = 0;
    private Handler retryHandler;
    
    // 回调接口
    public interface BluetoothCallback {
        void onConnectionStateChanged(boolean connected);
        void onDataReceived(int[] data);
        void onSendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount);
        void onError(String error, int errorCode);
    }
    
    private BluetoothCallback callback;
    
    public NativeBluetoothManager(Context context) {
        this.context = context;
        this.bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        this.retryHandler = new Handler(Looper.getMainLooper());
        
        // 创建专用的蓝牙线程
        bluetoothThread = new HandlerThread("BluetoothThread");
        bluetoothThread.start();
        bluetoothHandler = new Handler(bluetoothThread.getLooper());
        
        Log.d(TAG, "NativeBluetoothManager initialized");
    }
    
    public void setCallback(BluetoothCallback callback) {
        this.callback = callback;
    }
    
    // 启动蓝牙发送服务
    public boolean startBluetoothSending(String deviceId, String serviceUUID, String characteristicUUID, 
                                       int sendInterval, int[] data) {
        try {
            this.deviceId = deviceId;
            this.serviceUUID = UUID.fromString(serviceUUID);
            this.characteristicUUID = UUID.fromString(characteristicUUID);
            this.sendInterval = sendInterval;
            this.sendData = data.clone();
            
            Log.d(TAG, "Starting bluetooth sending - Device: " + deviceId + ", Interval: " + sendInterval + "ms");
            
            if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
                Log.e(TAG, "Bluetooth adapter not available or disabled");
                notifyError("蓝牙适配器不可用或未启用", -1);
                return false;
            }
            
            // 如果已经在发送，先停止
            if (isSending.get()) {
                stopBluetoothSending();
            }
            
            // 连接设备
            connectToDevice();
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start bluetooth sending: " + e.getMessage());
            notifyError("启动蓝牙发送失败: " + e.getMessage(), -2);
            return false;
        }
    }
    
    // 停止蓝牙发送服务
    public void stopBluetoothSending() {
        Log.d(TAG, "Stopping bluetooth sending");
        
        isSending.set(false);
        
        // 停止发送定时器
        if (sendTask != null && !sendTask.isCancelled()) {
            sendTask.cancel(true);
            sendTask = null;
        }
        
        if (sendExecutor != null && !sendExecutor.isShutdown()) {
            sendExecutor.shutdown();
            sendExecutor = null;
        }
        
        // 断开蓝牙连接
        disconnectDevice();
        
        // 通知状态变化
        notifySendingStateChanged(false, lastSendTime.get(), totalSent.get(), errorCount.get());
    }
    
    // 更新发送数据
    public synchronized void updateSendData(int[] data) {
        if (data != null) {
            this.sendData = data.clone();
            this.dataVersion++; // 增加版本号
            this.lastUpdateTime = System.currentTimeMillis();
            
            // 🔧 增强调试：详细记录数据更新过程
            StringBuilder dataLog = new StringBuilder("📝 蓝牙数据已更新: [");
            for (int i = 0; i < Math.min(data.length, 10); i++) { // 只显示前10个数据避免日志过长
                dataLog.append(data[i]);
                if (i < Math.min(data.length, 10) - 1) dataLog.append(", ");
            }
            if (data.length > 10) dataLog.append("...");
            dataLog.append("] 长度: ").append(data.length);
            dataLog.append(" 版本: ").append(dataVersion);
            
            Log.d(TAG, dataLog.toString());
            
            // 🔧 记录数据更新时间戳，用于调试
            Log.d(TAG, "🕐 数据更新时间戳: " + lastUpdateTime + " (版本: " + dataVersion + ")");
            
            // 🚀 立即触发一次发送以确保新数据生效
            if (isSending.get() && isConnected.get()) {
                Log.d(TAG, "🚀 数据更新后立即触发发送");
                bluetoothHandler.post(this::sendDataImmediately);
            }
        } else {
            Log.w(TAG, "⚠️ 尝试更新空数据，忽略此次更新");
        }
    }
    
    // 获取当前发送数据
    public int[] getCurrentSendData() {
        return sendData != null ? sendData.clone() : new int[0];
    }
    
    // 立即发送数据（用于数据更新后立即生效）
    private void sendDataImmediately() {
        try {
            // 准备发送数据 - 使用同步确保数据一致性
            int[] currentSendData;
            long currentVersion;
            long currentUpdateTime;
            
            synchronized (this) {
                currentSendData = sendData != null ? sendData.clone() : null;
                currentVersion = dataVersion;
                currentUpdateTime = lastUpdateTime;
            }
            
            if (currentSendData == null || currentSendData.length == 0) {
                Log.w(TAG, "写入中止：发送数据为空");
                return;
            }
            
            // 🔧 修复数据转换问题：确保正确的byte转换
            byte[] dataToSend = new byte[currentSendData.length];
            StringBuilder dataLog = new StringBuilder("📤 发送数据转换: ");
            
            for (int i = 0; i < currentSendData.length; i++) {
                int originalValue = currentSendData[i];
                // 确保值在0-255范围内，然后转换为byte
                int clampedValue = originalValue & 0xFF;
                dataToSend[i] = (byte) clampedValue;
                
                // 记录数据转换过程 - 只显示前10个数据
                if (i < 10) {
                    dataLog.append(String.format("[%d: %d->%02X] ", i, originalValue, dataToSend[i] & 0xFF));
                }
            }
            if (currentSendData.length > 10) dataLog.append("...");
            
            // 🔧 添加发送时间戳和版本信息用于调试
            Log.d(TAG, dataLog.toString());
            Log.d(TAG, "🕐 发送时间戳: " + System.currentTimeMillis() + " (数据版本: " + currentVersion + ", 更新时间: " + currentUpdateTime + ")");
            Log.d(TAG, "发送数据长度: " + dataToSend.length + " 字节");
            
            // 🔍 检查数据长度限制（一般BLE设备最大20字节）
            if (dataToSend.length > 20) {
                Log.w(TAG, "警告：数据长度超过20字节 (" + dataToSend.length + ")，可能导致写入失败");
            }
            
            // 写入特征
            if (targetCharacteristic != null && bluetoothGatt != null) {
                targetCharacteristic.setValue(dataToSend);
                boolean writeResult = bluetoothGatt.writeCharacteristic(targetCharacteristic);
                
                if (writeResult) {
                    totalSent.incrementAndGet();
                    lastSendTime.set(System.currentTimeMillis());
                    Log.v(TAG, "写入请求已提交，数据长度: " + dataToSend.length + " (版本: " + currentVersion + ")");
                } else {
                    errorCount.incrementAndGet();
                    lastError = String.format("写入特征失败 - 连接状态: %s, GATT状态: %s, 特征属性: %d, 数据长度: %d", 
                        isConnected.get() ? "已连接" : "未连接",
                        bluetoothGatt != null ? "正常" : "null",
                        targetCharacteristic != null ? targetCharacteristic.getProperties() : -1,
                        dataToSend.length);
                    Log.e(TAG, lastError);
                }
            } else {
                Log.e(TAG, "无法发送：targetCharacteristic或bluetoothGatt为null");
            }
            
        } catch (Exception e) {
            errorCount.incrementAndGet();
            lastError = "发送数据异常: " + e.getMessage() + ", 堆栈: " + e.getClass().getSimpleName();
            Log.e(TAG, lastError, e);
        }
    }
    
    // 连接到蓝牙设备
    private void connectToDevice() {
        bluetoothHandler.post(() -> {
            try {
                BluetoothDevice device = bluetoothAdapter.getRemoteDevice(deviceId);
                if (device == null) {
                    Log.e(TAG, "Device not found: " + deviceId);
                    notifyError("设备未找到: " + deviceId, -3);
                    return;
                }

                Log.d(TAG, "Connecting to device: " + device.getName() + " (" + deviceId + ")");
                
                // 如果已经连接，先断开
                if (bluetoothGatt != null) {
                    bluetoothGatt.disconnect();
                    bluetoothGatt.close();
                    bluetoothGatt = null;
                }
                
                // 建立GATT连接
                bluetoothGatt = device.connectGatt(context, false, gattCallback);
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to connect to device: " + e.getMessage());
                notifyError("连接设备失败: " + e.getMessage(), -4);
            }
        });
    }
    
    // 断开蓝牙设备
    private void disconnectDevice() {
        bluetoothHandler.post(() -> {
            if (bluetoothGatt != null) {
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
                bluetoothGatt = null;
            }
            isConnected.set(false);
            targetCharacteristic = null;
            
            if (callback != null) {
                callback.onConnectionStateChanged(false);
            }
            
            Log.d(TAG, "Device disconnected");
        });
    }
    
    // GATT回调
    private final BluetoothGattCallback gattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "Connected to GATT server");
                isConnected.set(true);
                retryCount = 0; // 重置重试计数
                
                // 发现服务
                gatt.discoverServices();
                
                if (callback != null) {
                    callback.onConnectionStateChanged(true);
                }
                
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                Log.d(TAG, "Disconnected from GATT server");
                isConnected.set(false);
                
                if (callback != null) {
                    callback.onConnectionStateChanged(false);
                }
                
                // 如果正在发送且意外断开，尝试重连
                if (isSending.get() && retryCount < MAX_RETRY_COUNT) {
                    retryCount++;
                    Log.w(TAG, "Connection lost, attempting to reconnect (" + retryCount + "/" + MAX_RETRY_COUNT + ")");
                    
                    retryHandler.postDelayed(() -> {
                        if (isSending.get()) {
                            connectToDevice();
                        }
                    }, 2000); // 2秒后重试
                } else if (retryCount >= MAX_RETRY_COUNT) {
                    Log.e(TAG, "Max retry count reached, stopping bluetooth sending");
                    stopBluetoothSending();
                    notifyError("连接失败，已达到最大重试次数", -5);
                }
            }
        }
        
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "Services discovered");
                
                // 查找目标服务和特征
                BluetoothGattService service = gatt.getService(serviceUUID);
                if (service != null) {
                    targetCharacteristic = service.getCharacteristic(characteristicUUID);
                    if (targetCharacteristic != null) {
                        Log.d(TAG, "Target characteristic found, starting data sending");
                        
                        // 启用通知（如果支持）
                        if ((targetCharacteristic.getProperties() & BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                            gatt.setCharacteristicNotification(targetCharacteristic, true);
                        }
                        
                        // 开始发送数据
                        startDataSending();
                    } else {
                        Log.e(TAG, "Target characteristic not found");
                        notifyError("目标特征未找到", -6);
                    }
                } else {
                    Log.e(TAG, "Target service not found");
                    notifyError("目标服务未找到", -7);
                }
            } else {
                Log.e(TAG, "Service discovery failed with status: " + status);
                notifyError("服务发现失败", status);
            }
        }
        
        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                successCount.incrementAndGet();
                lastSendTime.set(System.currentTimeMillis());
                Log.v(TAG, "✅ 写入成功，总成功: " + successCount.get());
            } else {
                errorCount.incrementAndGet();
                
                // 🔧 详细的错误状态分析
                String statusDescription = parseGattStatus(status);
                lastError = "写入特征失败: " + statusDescription;
                
                Log.e(TAG, String.format("❌ 写入失败 - %s, 总错误: %d, 错误率: %.2f%%", 
                    statusDescription, 
                    errorCount.get(),
                    (double) errorCount.get() / (totalSent.get() + 1) * 100));
                
                // 🔍 针对特定错误类型的处理建议
                if (status == BluetoothGatt.GATT_WRITE_NOT_PERMITTED) {
                    Log.w(TAG, "💡 建议：检查特征写入权限或设备配对状态");
                } else if (status == BluetoothGatt.GATT_CONNECTION_CONGESTED) {
                    Log.w(TAG, "💡 建议：降低发送频率，当前间隔: " + sendInterval + "ms");
                } else if (status == BluetoothGatt.GATT_INVALID_ATTRIBUTE_LENGTH) {
                    Log.w(TAG, "💡 建议：检查数据长度，当前长度: " + (sendData != null ? sendData.length : "null"));
                }
                
                // 🚨 如果错误率过高，建议重连
                if (totalSent.get() > 10 && (double) errorCount.get() / totalSent.get() > 0.5) {
                    Log.w(TAG, "⚠️ 错误率过高，建议检查连接或重启蓝牙服务");
                }
            }
            
            totalSent.incrementAndGet();
        }
        
        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            // 处理接收到的数据
            byte[] data = characteristic.getValue();
            if (data != null && data.length > 0) {
                int[] intData = new int[data.length];
                for (int i = 0; i < data.length; i++) {
                    intData[i] = data[i] & 0xFF; // 转换为无符号整数
                }
                
                Log.d(TAG, "Data received, length: " + data.length);
                
                if (callback != null) {
                    callback.onDataReceived(intData);
                }
            }
        }
    };
    
    // 开始数据发送
    private void startDataSending() {
        if (isSending.get()) {
            Log.w(TAG, "Data sending already active");
            return;
        }
        
        isSending.set(true);
        
        // 创建高精度定时发送器
        sendExecutor = Executors.newSingleThreadScheduledExecutor();
        
        Log.d(TAG, "Starting precise " + sendInterval + "ms data sending");
        
        sendTask = sendExecutor.scheduleWithFixedDelay(() -> {
            // 🔍 增强状态检查，提供详细的诊断信息
            if (!isSending.get()) {
                Log.w(TAG, "写入中止：发送已停止");
                return;
            }
            
            if (!isConnected.get()) {
                Log.w(TAG, "写入中止：设备未连接");
                return;
            }
            
            if (bluetoothGatt == null) {
                Log.e(TAG, "写入中止：BluetoothGatt为null");
                return;
            }
            
            if (targetCharacteristic == null) {
                Log.e(TAG, "写入中止：目标特征为null");
                return;
            }
            
            try {
                // 🔍 检查特征属性
                int properties = targetCharacteristic.getProperties();
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 && 
                    (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
                    errorCount.incrementAndGet();
                    lastError = "写入失败：特征不支持写入操作，属性: " + properties;
                    Log.e(TAG, lastError);
                    return;
                }
                
                // 调用统一的发送方法
                sendDataImmediately();
                
                // 定期通知状态变化（每100次发送通知一次，避免过于频繁）
                if (totalSent.get() % 100 == 0) {
                    notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());
                }
                
            } catch (Exception e) {
                errorCount.incrementAndGet();
                lastError = "发送数据异常: " + e.getMessage() + ", 堆栈: " + e.getClass().getSimpleName();
                Log.e(TAG, lastError, e);
            }
            
        }, 0, sendInterval, TimeUnit.MILLISECONDS);
        
        // 通知发送状态变化
        notifySendingStateChanged(true, lastSendTime.get(), totalSent.get(), errorCount.get());
        
        Log.d(TAG, "Data sending started with " + sendInterval + "ms interval");
    }
    
    // 重新连接设备
    public void reconnectDevice() {
        Log.d(TAG, "Reconnecting device...");
        
        bluetoothHandler.post(() -> {
            if (bluetoothGatt != null) {
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
                bluetoothGatt = null;
            }
            
            isConnected.set(false);
            retryCount = 0;
            
            // 延迟重连
            retryHandler.postDelayed(() -> {
                if (isSending.get()) {
                    connectToDevice();
                }
            }, 1000);
        });
    }
    
    // 获取发送状态
    public BluetoothSendingStatus getSendingStatus() {
        return new BluetoothSendingStatus(
            isSending.get(),
            lastSendTime.get(),
            totalSent.get(),
            errorCount.get()
        );
    }
    
    // 获取发送统计
    public BluetoothSendingStats getSendingStats() {
        long total = totalSent.get();
        long success = successCount.get();
        long errors = errorCount.get();
        
        // 计算平均间隔（基于成功发送的数据）
        double averageInterval = success > 1 ? 
            (double) (System.currentTimeMillis() - (lastSendTime.get() - (success * sendInterval))) / success : 
            sendInterval;
        
        return new BluetoothSendingStats(
            total,
            success,
            errors,
            averageInterval,
            lastError,
            isConnected.get()
        );
    }

    // 🔍 诊断写入失败的具体原因
    private void diagnosisWriteFailure() {
        StringBuilder diagnosis = new StringBuilder("🔍 写入失败诊断: ");
        
        try {
            // 检查蓝牙适配器状态
            if (bluetoothAdapter == null) {
                diagnosis.append("蓝牙适配器为null; ");
            } else if (!bluetoothAdapter.isEnabled()) {
                diagnosis.append("蓝牙未启用; ");
            }
            
            // 检查GATT连接状态
            if (bluetoothGatt == null) {
                diagnosis.append("GATT连接为null; ");
            }
            
            // 检查特征状态
            if (targetCharacteristic == null) {
                diagnosis.append("目标特征为null; ");
            } else {
                int properties = targetCharacteristic.getProperties();
                diagnosis.append(String.format("特征属性: %d ", properties));
                
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) != 0) {
                    diagnosis.append("(支持写入) ");
                }
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                    diagnosis.append("(支持无响应写入) ");
                }
                if ((properties & BluetoothGattCharacteristic.PROPERTY_WRITE) == 0 && 
                    (properties & BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) == 0) {
                    diagnosis.append("(不支持写入) ");
                }
            }
            
            // 检查数据状态
            if (sendData == null) {
                diagnosis.append("发送数据为null; ");
            } else {
                diagnosis.append(String.format("数据长度: %d; ", sendData.length));
            }
            
            // 检查连接计数器
            diagnosis.append(String.format("重试次数: %d/%d; ", retryCount, MAX_RETRY_COUNT));
            
            Log.w(TAG, diagnosis.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "诊断过程异常: " + e.getMessage(), e);
        }
    }

    // 🔧 增强的错误状态码解析
    private String parseGattStatus(int status) {
        switch (status) {
            case BluetoothGatt.GATT_SUCCESS:
                return "GATT_SUCCESS(0) - 操作成功";
            case BluetoothGatt.GATT_READ_NOT_PERMITTED:
                return "GATT_READ_NOT_PERMITTED(2) - 读取不被允许";
            case BluetoothGatt.GATT_WRITE_NOT_PERMITTED:
                return "GATT_WRITE_NOT_PERMITTED(3) - 写入不被允许";
            case BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION:
                return "GATT_INSUFFICIENT_AUTHENTICATION(5) - 认证不足";
            case BluetoothGatt.GATT_REQUEST_NOT_SUPPORTED:
                return "GATT_REQUEST_NOT_SUPPORTED(6) - 请求不被支持";
            case BluetoothGatt.GATT_INSUFFICIENT_ENCRYPTION:
                return "GATT_INSUFFICIENT_ENCRYPTION(15) - 加密不足";
            case BluetoothGatt.GATT_INVALID_ATTRIBUTE_LENGTH:
                return "GATT_INVALID_ATTRIBUTE_LENGTH(13) - 属性长度无效";
            case BluetoothGatt.GATT_CONNECTION_CONGESTED:
                return "GATT_CONNECTION_CONGESTED(143) - 连接拥塞";
            case BluetoothGatt.GATT_FAILURE:
                return "GATT_FAILURE(257) - 通用失败";
            default:
                return String.format("未知状态码(%d)", status);
        }
    }
    
    // 清理资源
    public void cleanup() {
        Log.d(TAG, "Cleaning up NativeBluetoothManager");
        
        stopBluetoothSending();
        
        if (bluetoothThread != null) {
            bluetoothThread.quitSafely();
            bluetoothThread = null;
        }
        
        if (retryHandler != null) {
            retryHandler.removeCallbacksAndMessages(null);
            retryHandler = null;
        }
    }
    
    // 通知方法
    private void notifyError(String error, int errorCode) {
        lastError = error;
        if (callback != null) {
            callback.onError(error, errorCode);
        }
    }
    
    private void notifySendingStateChanged(boolean active, long lastSendTime, long sendCount, long errorCount) {
        if (callback != null) {
            callback.onSendingStateChanged(active, lastSendTime, sendCount, errorCount);
        }
    }
    
    // 内部数据类
    public static class BluetoothSendingStatus {
        public final boolean isActive;
        public final long lastSendTime;
        public final long sendCount;
        public final long errorCount;
        
        public BluetoothSendingStatus(boolean isActive, long lastSendTime, long sendCount, long errorCount) {
            this.isActive = isActive;
            this.lastSendTime = lastSendTime;
            this.sendCount = sendCount;
            this.errorCount = errorCount;
        }
    }
    
    public static class BluetoothSendingStats {
        public final long totalSent;
        public final long successCount;
        public final long errorCount;
        public final double averageInterval;
        public final String lastError;
        public final boolean isConnected;
        
        public BluetoothSendingStats(long totalSent, long successCount, long errorCount, 
                                   double averageInterval, String lastError, boolean isConnected) {
            this.totalSent = totalSent;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.averageInterval = averageInterval;
            this.lastError = lastError;
            this.isConnected = isConnected;
        }
    }
}