{"name": "capacitor-kt-service", "version": "1.0.0", "description": "KunTeng Electronics Native Services - 昆腾电子原生服务，集成蓝牙后台通信、Mapbox导航和搜索功能", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorKtService.podspec"], "author": "KunTeng Electronics Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Dongsipan/capacitor-kt-service.git"}, "bugs": {"url": "https://github.com/Dongsipan/capacitor-kt-service/issues"}, "keywords": ["capacitor", "service", "native", "bluetooth", "background", "foreground-service", "mapbox", "navigation", "search", "kunteng", "ios", "android"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild -scheme CapacitorKtService -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\" --plugin=prettier-plugin-java", "swiftlint": "node-swiftlint", "docgen": "docgen --api CapacitorKtService --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"@capacitor/android": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/docgen": "^0.3.0", "@capacitor/ios": "^7.0.0", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^2.0.0", "eslint": "^8.57.0", "prettier": "^3.4.2", "prettier-plugin-java": "^2.6.6", "rimraf": "^6.0.1", "rollup": "^4.30.1", "swiftlint": "^2.0.0", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}