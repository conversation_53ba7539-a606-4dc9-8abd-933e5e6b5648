import Foundation
import Capacitor
import CoreBluetooth
import CoreLocation
import MapboxSearch
import MapboxSearchUI
import MapboxMaps

/**
 * KunTeng Electronics Native Services
 * 昆腾电子原生服务 - 集成蓝牙后台通信、Mapbox导航和搜索功能
 */
@objc(CapacitorKtService)
public class CapacitorKtService: CAPPlugin {
    
    // 蓝牙后台服务实现
    private let bluetoothImplementation = BluetoothBackground()
    
    // Mapbox导航实现
    private let mapboxNavigationImplementation = CapacitorMapboxNavigation()
    
    // Mapbox搜索实现
    private let mapboxSearchImplementation = CapacitorMapboxSearch()
    
    // 用于存储搜索窗口的引用
    private var searchWindow: UIWindow?
    
    public override func load() {
        super.load()
        // 初始化各个模块
        bluetoothImplementation.initialize()
        initializeMapbox()
    }
    
    private func initializeMapbox() {
        print("Initializing Mapbox in CapacitorKtService...")
        
        // 检查访问令牌
        if let accessToken = Bundle.main.object(forInfoDictionaryKey: "MBXAccessToken") as? String {
            print("Mapbox access token found: \(String(accessToken.prefix(10)))...")
        } else {
            print("WARNING: No Mapbox access token found in Info.plist")
        }
    }

    // MARK: - 蓝牙后台通信功能

    @objc func startBluetoothForegroundService(_ call: CAPPluginCall) {
        bluetoothImplementation.startForegroundService(call)
    }

    @objc func stopBluetoothForegroundService(_ call: CAPPluginCall) {
        bluetoothImplementation.stopForegroundService(call)
    }

    @objc func requestIgnoreBatteryOptimizations(_ call: CAPPluginCall) {
        // iOS不需要电池优化设置
        call.resolve()
    }

    @objc func isBluetoothServiceRunning(_ call: CAPPluginCall) {
        bluetoothImplementation.isServiceRunning(call)
    }

    @objc func updateBluetoothNotification(_ call: CAPPluginCall) {
        // iOS通知更新逻辑
        call.resolve()
    }

    @objc func checkDozeMode(_ call: CAPPluginCall) {
        // iOS没有Doze模式
        call.resolve([
            "isInDozeMode": false,
            "isIgnoringBatteryOptimizations": true
        ])
    }

    @objc func openBatteryOptimizationSettings(_ call: CAPPluginCall) {
        // iOS不需要此功能
        call.resolve()
    }

    @objc func sendKeepAlive(_ call: CAPPluginCall) {
        bluetoothImplementation.sendKeepAlive(call)
    }

    @objc func getDeviceInfo(_ call: CAPPluginCall) {
        let device = UIDevice.current
        call.resolve([
            "manufacturer": "Apple",
            "model": device.model,
            "androidVersion": "N/A",
            "sdkVersion": 0,
            "hasAggressivePowerManagement": false
        ])
    }

    @objc func openAutoStartSettings(_ call: CAPPluginCall) {
        // iOS不需要此功能
        call.resolve()
    }

    @objc func startNativeBluetoothSending(_ call: CAPPluginCall) {
        bluetoothImplementation.startNativeBluetoothSending(call)
    }

    @objc func stopNativeBluetoothSending(_ call: CAPPluginCall) {
        bluetoothImplementation.stopNativeBluetoothSending(call)
    }

    @objc func updateBluetoothSendData(_ call: CAPPluginCall) {
        bluetoothImplementation.updateBluetoothSendData(call)
    }

    @objc func isNativeBluetoothSending(_ call: CAPPluginCall) {
        bluetoothImplementation.isNativeBluetoothSending(call)
    }

    @objc func getBluetoothSendingStats(_ call: CAPPluginCall) {
        bluetoothImplementation.getBluetoothSendingStats(call)
    }

    @objc func reconnectBluetoothDevice(_ call: CAPPluginCall) {
        bluetoothImplementation.reconnectBluetoothDevice(call)
    }

    // MARK: - Mapbox导航功能

    @objc func showMapboxNavigation(_ call: CAPPluginCall) {
        mapboxNavigationImplementation.show(call)
    }

    @objc func getNavigationHistory(_ call: CAPPluginCall) {
        mapboxNavigationImplementation.history(call)
    }

    @objc func requestNavigationPermissions(_ call: CAPPluginCall) {
        mapboxNavigationImplementation.requestPermissions(call)
    }

    @objc func checkNavigationPermissions(_ call: CAPPluginCall) {
        mapboxNavigationImplementation.checkPermissions(call)
    }

    // MARK: - Mapbox搜索功能

    @objc func searchMapboxPlaces(_ call: CAPPluginCall) {
        guard let query = call.getString("query") else {
            call.reject("Query parameter is required")
            return
        }
        
        mapboxSearchImplementation.searchPlaces(query: query) { results in
            let searchResults = results.map { result in
                return [
                    "id": result.id,
                    "name": result.name,
                    "address": result.address?.formattedAddress() ?? "",
                    "coordinate": [
                        "latitude": result.coordinate?.latitude ?? 0,
                        "longitude": result.coordinate?.longitude ?? 0
                    ],
                    "categories": result.categories ?? []
                ]
            }
            
            call.resolve([
                "results": searchResults
            ])
        }
    }
    
    @objc func getMapboxPlaceSuggestions(_ call: CAPPluginCall) {
        guard let query = call.getString("query") else {
            call.reject("Query parameter is required")
            return
        }
        
        mapboxSearchImplementation.getPlaceSuggestions(query: query) { suggestions in
            let suggestionResults = suggestions.map { suggestion in
                return [
                    "name": suggestion.name,
                    "description": suggestion.description ?? "",
                    "address": suggestion.address?.formattedAddress() ?? ""
                ]
            }
            
            call.resolve([
                "suggestions": suggestionResults
            ])
        }
    }
    
    @objc func reverseGeocodeMapbox(_ call: CAPPluginCall) {
        guard let latitude = call.getDouble("latitude"),
              let longitude = call.getDouble("longitude") else {
            call.reject("Latitude and longitude parameters are required")
            return
        }
        
        let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
        mapboxSearchImplementation.reverseGeocode(coordinate: coordinate) { results in
            let geocodeResults = results.map { result in
                return [
                    "name": result.name,
                    "address": result.address?.formattedAddress() ?? "",
                    "coordinate": [
                        "latitude": result.coordinate?.latitude ?? 0,
                        "longitude": result.coordinate?.longitude ?? 0
                    ]
                ]
            }
            
            call.resolve([
                "results": geocodeResults
            ])
        }
    }
    
    @objc func searchMapboxByCategory(_ call: CAPPluginCall) {
        guard let category = call.getString("category"),
              let latitude = call.getDouble("latitude"),
              let longitude = call.getDouble("longitude") else {
            call.reject("Category, latitude and longitude parameters are required")
            return
        }
        
        let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
        mapboxSearchImplementation.searchByCategory(category: category, around: coordinate) { results in
            let categoryResults = results.map { result in
                return [
                    "name": result.name,
                    "address": result.address?.formattedAddress() ?? "",
                    "coordinate": [
                        "latitude": result.coordinate?.latitude ?? 0,
                        "longitude": result.coordinate?.longitude ?? 0
                    ],
                    "categories": result.categories ?? []
                ]
            }
            
            call.resolve([
                "results": categoryResults
            ])
        }
    }
    
    @objc func validateMapboxToken(_ call: CAPPluginCall) {
        let isValid = mapboxSearchImplementation.validateMapboxToken()
        call.resolve([
            "isValid": isValid
        ])
    }

    @objc func openMapboxMap(_ call: CAPPluginCall) {
        guard let location = call.getObject("location"),
              let latitude = location["latitude"] as? Double,
              let longitude = location["longitude"] as? Double else {
            call.reject("Location parameter is required")
            return
        }
        
        DispatchQueue.main.async { [weak self] in
            let mapboxVC = CapacitorPlaceAutocompleteViewController()
            // 可以在这里设置初始位置
            // mapboxVC.initialLatitude = latitude
            // mapboxVC.initialLongitude = longitude
            
            // 设置关闭回调
            mapboxVC.onDismiss = { [weak self] in
                // 清理资源
                self?.searchWindow = nil
            }
            
            let navigationController = UINavigationController(rootViewController: mapboxVC)
            navigationController.modalPresentationStyle = .fullScreen
            
            // 获取当前窗口的根视图控制器并展示新页面
            if let rootVC = UIApplication.shared.windows.first?.rootViewController {
                rootVC.present(navigationController, animated: true, completion: nil)
                call.resolve()
            } else {
                call.reject("Unable to present map view")
            }
        }
    }

    @objc func openMapboxSearchBox(_ call: CAPPluginCall) {
        DispatchQueue.main.async { [weak self] in
            let mapboxVC = CapacitorMapboxSearchViewController()
            
            // 设置关闭回调
            mapboxVC.onDismiss = { [weak self] in
                // 清理资源
                self?.searchWindow = nil
            }
            
            let navigationController = UINavigationController(rootViewController: mapboxVC)
            navigationController.modalPresentationStyle = .fullScreen
            
            // 获取当前窗口的根视图控制器并展示新页面
            if let rootVC = UIApplication.shared.windows.first?.rootViewController {
                rootVC.present(navigationController, animated: true, completion: nil)
                call.resolve()
            } else {
                call.reject("Unable to present search box")
            }
        }
    }

    @objc func openMapboxAutocomplete(_ call: CAPPluginCall) {
        DispatchQueue.main.async { [weak self] in
            let mapboxVC = CapacitorPlaceAutocompleteViewController()
            
            // 设置关闭回调
            mapboxVC.onDismiss = { [weak self] in
                // 清理资源
                self?.searchWindow = nil
            }
            
            let navigationController = UINavigationController(rootViewController: mapboxVC)
            navigationController.modalPresentationStyle = .fullScreen
            
            // 获取当前窗口的根视图控制器并展示新页面
            if let rootVC = UIApplication.shared.windows.first?.rootViewController {
                rootVC.present(navigationController, animated: true, completion: nil)
                call.resolve()
            } else {
                call.reject("Unable to present autocomplete view")
            }
        }
    }
}