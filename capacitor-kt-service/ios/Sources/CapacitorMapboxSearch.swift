import Foundation
import MapboxSearch
import MapboxSearchUI
import CoreLocation

@objc public class CapacitorMapboxSearch: NSObject {
    
    // MARK: - Properties
    private var searchEngine: SearchEngine!
    private var categorySearchEngine: CategorySearchEngine!
    
    // MARK: - Initialization
    public override init() {
        super.init()
        setupSearchEngines()
    }
    
    private func setupSearchEngines() {
        if let accessToken = Bundle.main.object(forInfoDictionaryKey: "MBXAccessToken") as? String {
            searchEngine = SearchEngine(accessToken: accessToken)
            categorySearchEngine = CategorySearchEngine(accessToken: accessToken)
        } else {
            print("Warning: No Mapbox access token found")
        }
    }
    
    // MARK: - Public Methods
    // 搜索地点
    public func searchPlaces(query: String, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        let searchOptions = SearchOptions(query: query)
        searchEngine.search(for: searchOptions) { result in
            switch result {
            case .success(let searchResponse):
                completion(searchResponse.results)
            case .failure(let error):
                print("Search error: \(error)")
                completion([])
            }
        }
    }
    
    // 获取地点建议
    public func getPlaceSuggestions(query: String, completion: @escaping ([PlaceAutocomplete.Suggestion]) -> Void) {
        let options = PlaceAutocomplete.Options(query: query)
        
        placeAutocomplete.suggestions(for: options) { result in
            switch result {
            case .success(let response):
                completion(response.suggestions)
            case .failure(let error):
                print("Autocomplete error: \(error)")
                completion([])
            }
        }
    }
    
    // 根据建议获取详细信息
    public func getPlaceDetails(suggestion: PlaceAutocomplete.Suggestion, completion: @escaping (PlaceAutocomplete.Result?) -> Void) {
        placeAutocomplete.select(suggestion: suggestion) { result in
            switch result {
            case .success(let response):
                completion(response.result)
            case .failure(let error):
                print("Place details error: \(error)")
                completion(nil)
            }
        }
    }
    
    // 反向地理编码
    public func reverseGeocode(coordinate: CLLocationCoordinate2D, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        let reverseOptions = ReverseGeocodingOptions(point: coordinate)
        searchEngine.search(for: reverseOptions) { result in
            switch result {
            case .success(let searchResponse):
                completion(searchResponse.results)
            case .failure(let error):
                print("Reverse geocoding error: \(error)")
                completion([])
            }
        }
    }
    
    // 类别搜索
    public func searchByCategory(category: String, around coordinate: CLLocationCoordinate2D, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        let categoryOptions = CategorySearchOptions(
            categories: [category],
            proximity: coordinate
        )
        
        searchEngine.search(for: categoryOptions) { result in
            switch result {
            case .success(let searchResponse):
                completion(searchResponse.results)
            case .failure(let error):
                print("Category search error: \(error)")
                completion([])
            }
        }
    }
    
    // 检查Mapbox访问令牌
    public func validateMapboxToken() -> Bool {
        if let accessToken = Bundle.main.object(forInfoDictionaryKey: "MBXAccessToken") as? String,
           !accessToken.isEmpty {
            return true
        }
        return false
    }
    
    // 获取当前位置附近的地点
    public func getNearbyPlaces(coordinate: CLLocationCoordinate2D, radius: Double = 1000, completion: @escaping ([SearchResult]) -> Void) {
        guard let searchEngine = searchEngine else {
            completion([])
            return
        }
        
        let searchOptions = SearchOptions(
            query: "",
            proximity: coordinate,
            boundingBox: nil,
            countries: nil,
            fuzzyMatch: true,
            language: nil,
            limit: 20,
            types: nil,
            requestDebounce: nil
        )
        
        searchEngine.search(for: searchOptions) { result in
            switch result {
            case .success(let searchResponse):
                // 过滤距离内的结果
                let nearbyResults = searchResponse.results.filter { searchResult in
                    guard let resultCoordinate = searchResult.coordinate else { return false }
                    let distance = coordinate.distance(to: resultCoordinate)
                    return distance <= radius
                }
                completion(nearbyResults)
            case .failure(let error):
                print("Nearby places search error: \(error)")
                completion([])
            }
        }
    }
}

// MARK: - CLLocationCoordinate2D Extension
extension CLLocationCoordinate2D {
    func distance(to coordinate: CLLocationCoordinate2D) -> Double {
        let location1 = CLLocation(latitude: self.latitude, longitude: self.longitude)
        let location2 = CLLocation(latitude: coordinate.latitude, longitude: coordinate.longitude)
        return location1.distance(from: location2)
    }
}
