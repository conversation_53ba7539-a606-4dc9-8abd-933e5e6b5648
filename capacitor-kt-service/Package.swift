// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "CapacitorKtService",
    platforms: [.iOS(.v13)],
    products: [
        .library(
            name: "CapacitorKtService",
            targets: ["CapacitorKtService"])
    ],
    dependencies: [
        .package(url: "https://github.com/ionic-team/capacitor-swift-pm.git", branch: "main")
    ],
    targets: [
        .target(
            name: "CapacitorKtService",
            dependencies: [
                .product(name: "Capacitor", package: "capacitor-swift-pm"),
                .product(name: "Cordova", package: "capacitor-swift-pm")
            ],
            path: "ios/Sources"),
        .testTarget(
            name: "CapacitorKtServiceTests",
            dependencies: ["CapacitorKtService"],
            path: "ios/Tests")
    ]
)