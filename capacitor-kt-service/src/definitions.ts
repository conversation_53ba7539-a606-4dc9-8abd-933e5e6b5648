import type { PermissionState } from '@capacitor/core';
import type { PluginListenerHandle } from '@capacitor/core/types/definitions';

/**
 * KunTeng Electronics Native Services
 * 昆腾电子原生服务 - 集成蓝牙后台通信、Mapbox导航和搜索功能
 */
export interface CapacitorKtService {
  // =================== 蓝牙后台通信功能 ===================
  
  /**
   * 启动前台服务
   */
  startBluetoothForegroundService(): Promise<void>;

  /**
   * 停止前台服务
   */
  stopBluetoothForegroundService(): Promise<void>;

  /**
   * 请求忽略电池优化
   */
  requestIgnoreBatteryOptimizations(): Promise<void>;

  /**
   * 检查蓝牙服务是否正在运行
   */
  isBluetoothServiceRunning(): Promise<{ isRunning: boolean }>;

  /**
   * 更新蓝牙通知内容
   */
  updateBluetoothNotification(options: { title?: string; message?: string }): Promise<void>;

  /**
   * 检查Doze模式状态
   */
  checkDozeMode(): Promise<{ 
    isInDozeMode: boolean; 
    isIgnoringBatteryOptimizations: boolean; 
  }>;

  /**
   * 打开电池优化设置页面
   */
  openBatteryOptimizationSettings(): Promise<void>;

  /**
   * 发送保活信号
   */
  sendKeepAlive(): Promise<void>;

  /**
   * 获取设备信息
   */
  getDeviceInfo(): Promise<{
    manufacturer: string;
    model: string;
    androidVersion: string;
    sdkVersion: number;
    hasAggressivePowerManagement: boolean;
  }>;

  /**
   * 打开自启动设置页面（针对不同厂商）
   */
  openAutoStartSettings(): Promise<void>;

  /**
   * 启动原生蓝牙发送服务
   * @param options 蓝牙发送配置
   */
  startNativeBluetoothSending(options: BluetoothSendingConfig): Promise<void>;

  /**
   * 停止原生蓝牙发送服务
   */
  stopNativeBluetoothSending(): Promise<void>;

  /**
   * 更新蓝牙发送数据
   * @param options 包含数据数组的选项对象
   */
  updateBluetoothSendData(options: { data: number[] }): Promise<void>;

  /**
   * 检查原生蓝牙发送状态
   */
  isNativeBluetoothSending(): Promise<BluetoothSendingState>;

  /**
   * 获取蓝牙发送统计信息
   */
  getBluetoothSendingStats(): Promise<BluetoothSendingStats>;

  /**
   * 获取当前正在发送的蓝牙数据
   */
  getCurrentBluetoothSendData(): Promise<{ success: boolean; data?: number[]; error?: string }>;

  /**
   * 获取镜像状态
   */
  getMirrorState(): Promise<{ enabled: boolean }>;

  /**
   * 重新连接蓝牙设备（原生实现）
   */
  reconnectBluetoothDevice(): Promise<void>;

  /**
   * 获取服务初始化状态
   */
  getServiceInitializationStatus(): Promise<BluetoothServiceStatus>;

  // =================== Mapbox导航功能 ===================

  /**
   * 显示Mapbox导航界面
   * @param options 导航选项
   */
  showMapboxNavigation(options: MapboxNavigationOptions): Promise<MapboxNavigationResult>;

  /**
   * 隐藏导航界面（不停止导航）
   */
  hideNavigationFragment(): Promise<{ success: boolean }>;

  /**
   * 显示导航界面（恢复显示）
   */
  showNavigationFragment(): Promise<{ success: boolean }>;

  /**
   * 获取导航界面显示状态
   */
  getNavigationFragmentVisibility(): Promise<{ isVisible: boolean }>;

  /**
   * 获取导航历史记录
   */
  getNavigationHistory(): Promise<any>;

  /**
   * 请求导航相关权限
   */
  requestNavigationPermissions(): Promise<NavigationPermissionStatus>;

  /**
   * 检查导航相关权限
   */
  checkNavigationPermissions(): Promise<NavigationPermissionStatus>;

  // =================== Mapbox搜索功能 ===================

  /**
   * 搜索地点
   * @param options 搜索选项
   */
  searchMapboxPlaces(options: MapboxSearchOptions): Promise<MapboxSearchResult>;

  /**
   * 获取地点建议
   * @param options 搜索选项
   */
  getMapboxPlaceSuggestions(options: MapboxSearchOptions): Promise<MapboxSuggestionResult>;

  /**
   * 反向地理编码
   * @param options 坐标选项
   */
  reverseGeocodeMapbox(options: MapboxCoordinateOptions): Promise<MapboxSearchResult>;

  /**
   * 按类别搜索
   * @param options 类别搜索选项
   */
  searchMapboxByCategory(options: MapboxCategorySearchOptions): Promise<MapboxSearchResult>;

  /**
   * 验证Mapbox访问令牌
   */
  validateMapboxToken(): Promise<{ isValid: boolean }>;

  /**
   * 打开Mapbox地图
   * @param options 地图打开选项
   */
  openMapboxMap(options: MapboxOpenOptions): Promise<void>;

  /**
   * 打开Mapbox搜索框
   */
  openMapboxSearchBox(): Promise<void>;

  /**
   * 打开Mapbox自动完成搜索
   */
  openMapboxAutocomplete(): Promise<void>;

  // =================== 事件监听器 ===================

  /**
   * 添加事件监听器
   */
  addListener(
    eventName: 'bluetoothServiceStateChanged' | 
             'bluetoothSendingStateChanged' | 
             'bluetoothDataReceived' | 
             'bluetoothError' |
             'onRouteProgressChange' |
             'onScreenMirroringChange' |
             'onNavigationStop' |
             'onNavigationComplete' |
             'plusButtonClicked' |
             'minusButtonClicked',
    listenerFunc: (data: any) => void,
  ): Promise<PluginListenerHandle>;

  /**
   * 移除所有监听器
   */
  removeAllListeners(): Promise<void>;
}

// =================== 蓝牙相关接口定义 ===================

export interface BluetoothSendingConfig {
  deviceId: string;
  serviceUUID: string;
  characteristicUUID: string;
  sendInterval: number; // 发送间隔（毫秒）
  data: number[]; // 要发送的数据数组
}

export interface BluetoothSendingState {
  isActive: boolean;
  lastSendTime: number;
  sendCount: number;
  errorCount: number;
}

export interface BluetoothSendingStats {
  totalSent: number;
  successCount: number;
  errorCount: number;
  averageInterval: number;
  lastError?: string;
  isConnected: boolean;
}

export interface CurrentBluetoothSendData {
  data: number[];
  length: number;
  success: boolean;
  error?: string;
  timestamp: number;
}

export interface BluetoothDataReceivedEvent {
  data: number[];
  timestamp: number;
  deviceId: string;
}

export interface BluetoothErrorEvent {
  error: string;
  errorCode?: number;
  timestamp: number;
  deviceId?: string;
}

/**
 * 蓝牙服务状态接口
 */
export interface BluetoothServiceStatus {
  isServiceRunning: boolean;
  bluetoothManagerInitialized: boolean;
  lastSendingStatusCached: boolean;
  lastSendingStatsCached: boolean;
  bluetoothManagerDetails: string;
  timestamp: number;
  error?: string;
}

// =================== Mapbox导航相关接口定义 ===================

export interface MapboxNavigationOptions {
  routes: LocationOption[];
  simulating?: boolean;
}

export interface LocationOption {
  latitude: number;
  longitude: number;
}

export interface MapboxNavigationResult {
  status: 'success' | 'failure';
  type?: 'on_failure' | 'on_cancelled' | 'onNavigationStop' | 'on_progress_update';
  message?: string;
  data?: string;
  timestamp?: number;
}

export interface NavigationPermissionStatus {
  location: PermissionState;
}

export interface ScreenMirroringChangeEvent {
  enabled: boolean;
}

export interface OnNavigationStopEvent {
  status: 'success' | 'failure';
  type: 'onNavigationStop';
  content: {
    message: string;
    timestamp: number;
  };
}

// =================== Mapbox搜索相关接口定义 ===================

export interface MapboxSearchOptions {
  query: string;
}

export interface MapboxCoordinateOptions {
  latitude: number;
  longitude: number;
}

export interface MapboxCategorySearchOptions {
  category: string;
  latitude: number;
  longitude: number;
}

export interface MapboxSearchResult {
  results: MapboxPlace[];
}

export interface MapboxSuggestionResult {
  suggestions: MapboxSuggestion[];
}

export interface MapboxPlace {
  id: string;
  name: string;
  address: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  categories: string[];
}

export interface MapboxSuggestion {
  name: string;
  description: string;
  address: string;
}

export interface MapboxOpenOptions {
  location: {
    latitude: number;
    longitude: number;
  };
}
