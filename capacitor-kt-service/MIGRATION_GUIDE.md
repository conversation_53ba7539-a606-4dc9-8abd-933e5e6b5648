# 迁移指南 - Migration Guide

本指南将帮助您从单独的插件迁移到合并后的 `capacitor-kt-plugin`。

## 插件合并概述

原来的三个独立插件：
- `capacitor-bluetooth-background` - 蓝牙后台通信
- `capacitor-mapbox-navigation-plugin` - Mapbox导航
- `capacitor-mapbox-search-plugin` - Mapbox搜索

现在合并为一个统一的插件：
- `capacitor-kt-plugin` - 昆腾电子原生服务插件

## 迁移步骤

### 1. 卸载旧插件

```bash
npm uninstall capacitor-bluetooth-background
npm uninstall capacitor-mapbox-navigation-plugin
npm uninstall capacitor-mapbox-search-plugin
```

### 2. 安装新插件

```bash
npm install capacitor-kt-plugin
npx cap sync
```

### 3. 更新导入语句

#### 原来的导入
```typescript
import { BluetoothBackground } from 'capacitor-bluetooth-background';
import { CapacitorMapboxNavigation } from 'capacitor-mapbox-navigation-plugin';
import { CapacitorMapboxSearch } from 'capacitor-mapbox-search-plugin';
```

#### 新的导入
```typescript
import { CapacitorKt } from 'capacitor-kt-plugin';
```

### 4. 更新方法调用

## 蓝牙后台通信功能迁移

### 方法名变更对照表

| 原方法名 | 新方法名 | 说明 |
|---------|---------|------|
| `startForegroundService()` | `startBluetoothForegroundService()` | 启动蓝牙前台服务 |
| `stopForegroundService()` | `stopBluetoothForegroundService()` | 停止蓝牙前台服务 |
| `isServiceRunning()` | `isBluetoothServiceRunning()` | 检查蓝牙服务状态 |
| `updateNotification()` | `updateBluetoothNotification()` | 更新蓝牙通知 |

### 代码迁移示例

#### 原代码
```typescript
// 启动服务
await BluetoothBackground.startForegroundService();

// 检查状态
const { isRunning } = await BluetoothBackground.isServiceRunning();

// 更新通知
await BluetoothBackground.updateNotification({
  title: '蓝牙服务',
  message: '正在运行'
});

// 启动蓝牙发送
await BluetoothBackground.startNativeBluetoothSending({
  deviceId: 'device-id',
  serviceUUID: 'service-uuid',
  characteristicUUID: 'char-uuid',
  sendInterval: 106,
  data: [0x01, 0x02]
});
```

#### 新代码
```typescript
// 启动服务
await CapacitorKt.startBluetoothForegroundService();

// 检查状态
const { isRunning } = await CapacitorKt.isBluetoothServiceRunning();

// 更新通知
await CapacitorKt.updateBluetoothNotification({
  title: '蓝牙服务',
  message: '正在运行'
});

// 启动蓝牙发送
await CapacitorKt.startNativeBluetoothSending({
  deviceId: 'device-id',
  serviceUUID: 'service-uuid',
  characteristicUUID: 'char-uuid',
  sendInterval: 106,
  data: [0x01, 0x02]
});
```

## Mapbox导航功能迁移

### 方法名变更对照表

| 原方法名 | 新方法名 | 说明 |
|---------|---------|------|
| `show()` | `showMapboxNavigation()` | 显示导航界面 |
| `history()` | `getNavigationHistory()` | 获取导航历史 |
| `requestPermissions()` | `requestNavigationPermissions()` | 请求导航权限 |
| `checkPermissions()` | `checkNavigationPermissions()` | 检查导航权限 |

### 代码迁移示例

#### 原代码
```typescript
// 显示导航
await CapacitorMapboxNavigation.show({
  routes: [
    { latitude: 37.7749, longitude: -122.4194 },
    { latitude: 37.7849, longitude: -122.4094 }
  ],
  simulating: false
});

// 请求权限
await CapacitorMapboxNavigation.requestPermissions();

// 检查权限
const permissions = await CapacitorMapboxNavigation.checkPermissions();
```

#### 新代码
```typescript
// 显示导航
await CapacitorKt.showMapboxNavigation({
  routes: [
    { latitude: 37.7749, longitude: -122.4194 },
    { latitude: 37.7849, longitude: -122.4094 }
  ],
  simulating: false
});

// 请求权限
await CapacitorKt.requestNavigationPermissions();

// 检查权限
const permissions = await CapacitorKt.checkNavigationPermissions();
```

## Mapbox搜索功能迁移

### 方法名变更对照表

| 原方法名 | 新方法名 | 说明 |
|---------|---------|------|
| `openMap()` | `openMapboxMap()` | 打开地图 |
| `openSearchBox()` | `openMapboxSearchBox()` | 打开搜索框 |
| `openAutocomplete()` | `openMapboxAutocomplete()` | 打开自动完成 |

### 代码迁移示例

#### 原代码
```typescript
// 打开地图
await CapacitorMapboxSearch.openMap({
  location: {
    latitude: 37.7749,
    longitude: -122.4194
  }
});

// 打开搜索框
await CapacitorMapboxSearch.openSearchBox();
```

#### 新代码
```typescript
// 新增功能：搜索地点（原插件没有此功能）
const searchResult = await CapacitorKt.searchMapboxPlaces({
  query: '星巴克'
});

// 新增功能：获取地点建议
const suggestions = await CapacitorKt.getMapboxPlaceSuggestions({
  query: '北京'
});

// 新增功能：反向地理编码
const geocodeResult = await CapacitorKt.reverseGeocodeMapbox({
  latitude: 37.7749,
  longitude: -122.4194
});

// 新增功能：按类别搜索
const categoryResult = await CapacitorKt.searchMapboxByCategory({
  category: 'restaurant',
  latitude: 37.7749,
  longitude: -122.4194
});

// 打开地图界面
await CapacitorKt.openMapboxMap({
  location: {
    latitude: 37.7749,
    longitude: -122.4194
  }
});

// 打开搜索框界面
await CapacitorKt.openMapboxSearchBox();
```

## 事件监听器迁移

### 事件名变更对照表

| 原事件名 | 新事件名 | 说明 |
|---------|---------|------|
| `serviceStateChanged` | `bluetoothServiceStateChanged` | 蓝牙服务状态变化 |
| `bluetoothSendingStateChanged` | `bluetoothSendingStateChanged` | 蓝牙发送状态变化（无变化） |
| `bluetoothDataReceived` | `bluetoothDataReceived` | 蓝牙数据接收（无变化） |
| `bluetoothError` | `bluetoothError` | 蓝牙错误（无变化） |

### 代码迁移示例

#### 原代码
```typescript
// 蓝牙事件监听
BluetoothBackground.addListener('serviceStateChanged', (data) => {
  console.log('服务状态变化:', data);
});

// 导航事件监听
CapacitorMapboxNavigation.addListener('onRouteProgressChange', (data) => {
  console.log('导航进度:', data);
});
```

#### 新代码
```typescript
// 蓝牙事件监听
CapacitorKt.addListener('bluetoothServiceStateChanged', (data) => {
  console.log('服务状态变化:', data);
});

// 导航事件监听
CapacitorKt.addListener('onRouteProgressChange', (data) => {
  console.log('导航进度:', data);
});
```

## Android配置迁移

### 原配置
在 `MainActivity.java` 中：
```java
// 原来需要分别添加三个插件
add(BluetoothBackgroundPlugin.class);
add(CapacitorMapboxNavigationPlugin.class);
add(CapacitorMapboxSearchPlugin.class);
```

### 新配置
在 `MainActivity.java` 中：
```java
// 现在只需要添加一个插件
add(CapacitorKtPlugin.class);
```

## iOS配置迁移

iOS平台的配置基本保持不变，只是podspec依赖会自动处理。

## 完整迁移示例

### 原代码（完整示例）
```typescript
import { BluetoothBackground } from 'capacitor-bluetooth-background';
import { CapacitorMapboxNavigation } from 'capacitor-mapbox-navigation-plugin';
import { CapacitorMapboxSearch } from 'capacitor-mapbox-search-plugin';

export class DeviceService {
  async initializeServices() {
    // 启动蓝牙服务
    await BluetoothBackground.startForegroundService();
    
    // 设置事件监听
    BluetoothBackground.addListener('serviceStateChanged', (data) => {
      console.log('蓝牙服务状态:', data.isRunning);
    });
    
    // 检查导航权限
    const permissions = await CapacitorMapboxNavigation.checkPermissions();
    if (permissions.location !== 'granted') {
      await CapacitorMapboxNavigation.requestPermissions();
    }
  }
  
  async startNavigation(routes: any[]) {
    await CapacitorMapboxNavigation.show({
      routes: routes,
      simulating: false
    });
  }
  
  async openMap(location: { latitude: number, longitude: number }) {
    await CapacitorMapboxSearch.openMap({ location });
  }
}
```

### 新代码（完整示例）
```typescript
import { CapacitorKt } from 'capacitor-kt-plugin';

export class DeviceService {
  async initializeServices() {
    // 启动蓝牙服务
    await CapacitorKt.startBluetoothForegroundService();
    
    // 设置事件监听
    CapacitorKt.addListener('bluetoothServiceStateChanged', (data) => {
      console.log('蓝牙服务状态:', data.isRunning);
    });
    
    // 检查导航权限
    const permissions = await CapacitorKt.checkNavigationPermissions();
    if (permissions.location !== 'granted') {
      await CapacitorKt.requestNavigationPermissions();
    }
  }
  
  async startNavigation(routes: any[]) {
    await CapacitorKt.showMapboxNavigation({
      routes: routes,
      simulating: false
    });
  }
  
  async openMap(location: { latitude: number, longitude: number }) {
    await CapacitorKt.openMapboxMap({ location });
  }
}
```

## 注意事项

1. **方法名变更**: 为了提高语义清晰度，部分方法名已更改，请参考上面的对照表
2. **事件名变更**: 蓝牙相关事件名增加了前缀以避免冲突
3. **包名变更**: Android包名从各自的包名统一为 `com.kunteng.plugins.kt`
4. **配置简化**: 现在只需要配置一个插件而不是三个
5. **功能保持**: 所有原有功能都得到保留，只是接口统一了

## 测试迁移

迁移完成后，建议进行以下测试：

1. **蓝牙功能测试**
   - 启动/停止前台服务
   - 蓝牙数据发送
   - 事件监听

2. **导航功能测试**
   - 权限请求
   - 导航启动
   - 导航事件监听

3. **搜索功能测试**
   - 地图打开
   - 搜索框功能

## 获取帮助

如果在迁移过程中遇到问题，请：

1. 检查控制台错误信息
2. 确认所有方法名都已正确更新
3. 验证事件监听器名称
4. 查看完整的API文档
5. 提交Issue获取技术支持

迁移完成后，您将享受到统一API带来的便利和更好的维护性！