# 蓝牙发送逻辑修复总结

## 问题描述

测试过程中发现的问题：

1. **设置保存后数据未实时更新**：当设置变更后，点击保存发送的数据并没有被更新成新的数据
2. **页面切换导致蓝牙停止发送**：点击设置页面的返回按钮蓝牙停止发送了
3. **页面切换后才生效**：当页面切换后发现蓝牙又继续发送了，并且发送的数据是更新后的数据

## 期望的结果

1. 设置页面有数据变更后点击保存按钮更新发送的数据，而且要实时修改成功
2. 点击返回按钮蓝牙还要继续发送
3. 蓝牙的定时器只有蓝牙连接成功后启动，除了应用关闭或者手动关闭，运行过程中不允许停止发送数据

## 根本原因分析

### 1. 数据缓存不同步问题

- `useMessage.ts` 中的 `currentSendData` 缓存在设置更新后没有立即刷新
- 定时器发送的是缓存的旧数据，而不是最新的设置数据
- 只有在重新启动 `sendMessage()` 时才会调用 `updateSendDataCache()` 更新缓存

### 2. 页面生命周期处理不当

- `SettingPage.vue` 的 `onIonViewWillLeave` 虽然同步了数据，但WebView方案的缓存没有更新
- `HomePage.vue` 的 `onIonViewDidEnter` 总是重新启动 `sendMessage()`，导致看起来数据"在页面切换后生效"

### 3. 缺乏实时数据同步机制

- 设置更新和蓝牙发送之间缺乏实时通信机制
- 没有事件驱动的数据更新机制

## 修复方案

### 1. 实现事件驱动的数据更新机制

**在 `SettingPage.vue` 中**：
```typescript
// 保存设置时发送全局事件
window.dispatchEvent(new CustomEvent('settingDataUpdated', {
  detail: {
    writeData: writeData.value,
    timestamp: Date.now(),
    source: 'SettingPage.saveSettings'
  }
}));
```

**在 `useMessage.ts` 中**：
```typescript
// 监听设置数据更新事件
const handleSettingDataUpdate = async (event: CustomEvent) => {
  if (isTimerActive) {
    console.log("🔄 定时器运行中，立即更新发送数据缓存");
    await updateSendDataCache();
    console.log("✅ 发送数据缓存已更新，下次发送将使用新数据");
  }
};

window.addEventListener('settingDataUpdated', handleSettingDataUpdate);
```

### 2. 修复页面切换逻辑

**修改 `SettingPage.vue` 的 `onIonViewWillLeave`**：
```typescript
onIonViewWillLeave(async () => {
  // 同步设置数据但不停止蓝牙发送
  updateSetting();
  await updateNativeBluetoothData();
  
  // 发送设置数据更新事件，确保WebView方案也能实时更新
  window.dispatchEvent(new CustomEvent('settingDataUpdated', {
    detail: { writeData: writeData.value, source: 'SettingPage.onIonViewWillLeave' }
  }));
});
```

**修改 `HomePage.vue` 的 `onIonViewDidEnter`**：
```typescript
onIonViewDidEnter(async () => {
  if (connectedDevice.value.isPaired) {
    setTimeout(async () => {
      // 检查当前是否已经在发送数据
      const isCurrentlySending = isServiceRunning.value;
      
      if (!isCurrentlySending) {
        console.log("🚀 蓝牙未发送，启动发送服务");
        await sendMessage();
      } else {
        console.log("✅ 蓝牙已在发送，无需重复启动");
        // 确保数据是最新的
        if (smartBluetoothHook.updateSendDataCache) {
          await smartBluetoothHook.updateSendDataCache();
        }
      }
    }, 1000);
  }
});
```

### 3. 增强跨平台兼容性

**在 `useSmartBluetoothMessage.ts` 中**：
```typescript
// WebView特有的高级控制方法
...(isAndroid ? {} : {
  updateSendDataCache: webViewHook.updateSendDataCache, // 暴露数据缓存更新方法
}),
```

## 修复效果

### 1. 实时数据更新
- ✅ 设置保存时立即发送 `settingDataUpdated` 事件
- ✅ `useMessage` 监听事件并立即更新缓存
- ✅ 下次定时器发送时使用最新数据
- ✅ **设置修改立即生效，无需等待页面切换**

### 2. 蓝牙发送连续性
- ✅ 页面切换不停止蓝牙发送
- ✅ `onIonViewWillLeave` 只同步数据，不停止定时器
- ✅ 定时器只在蓝牙连接成功后启动
- ✅ **点击返回按钮蓝牙继续发送**

### 3. 重复启动防护
- ✅ `HomePage` 检查 `isServiceRunning` 状态
- ✅ 已在发送时不重复启动
- ✅ 确保数据缓存始终最新
- ✅ **避免不必要的定时器重启**

### 4. 事件驱动更新
- ✅ 全局 `settingDataUpdated` 事件
- ✅ 跨组件数据同步
- ✅ 实时响应设置变更
- ✅ **解决了数据同步的时序问题**

## 修复的文件列表

1. **`src/views/SettingPage.vue`**
   - 修改 `saveSettings` 函数，添加事件发送
   - 修改 `onIonViewWillLeave`，确保不停止蓝牙发送

2. **`src/hooks/useMessage.ts`**
   - 添加 `settingDataUpdated` 事件监听
   - 实现实时数据缓存更新机制
   - 添加清理资源的函数

3. **`src/views/HomePage.vue`**
   - 修改 `onIonViewDidEnter`，添加发送状态检查
   - 避免重复启动蓝牙发送

4. **`src/hooks/useSmartBluetoothMessage.ts`**
   - 暴露 `updateSendDataCache` 方法给WebView方案

## 用户体验改进

### 修复前的问题
- ❌ 设置保存后需要等待页面切换才生效
- ❌ 页面切换会中断蓝牙发送
- ❌ 用户体验不连贯，容易产生困惑

### 修复后的体验
- ✅ **设置修改立即生效，无需等待**
- ✅ **蓝牙发送连续不断，符合预期**
- ✅ **响应速度更快，体验更流畅**
- ✅ **防止异常情况导致的发送中断**

## 技术亮点

1. **事件驱动架构**：使用全局事件实现跨组件数据同步
2. **状态管理优化**：避免重复启动和不必要的资源消耗
3. **生命周期管理**：正确处理页面切换时的数据同步
4. **跨平台兼容**：同时支持Android原生和WebView方案
5. **实时响应**：设置变更立即反映到蓝牙发送数据

## 总结

这次修复解决了蓝牙发送逻辑中的三个核心问题：

1. **数据同步时效性**：从"页面切换后生效"改为"设置保存后立即生效"
2. **发送连续性**：从"页面切换中断发送"改为"始终保持发送状态"
3. **资源管理**：从"重复启动定时器"改为"智能状态检查"

修复后的系统符合用户的期望：**设置页面有数据变更后点击保存按钮更新发送的数据，而且要实时修改成功，点击返回按钮蓝牙还要继续发送，蓝牙的定时器只有蓝牙连接成功后启动，除了应用关闭或者手动关闭，运行过程中不允许停止发送数据**。