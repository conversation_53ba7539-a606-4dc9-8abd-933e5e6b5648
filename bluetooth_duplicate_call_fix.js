// 修复重复调用 updateBluetoothSendData 的解决方案

/**
 * 防重复调用管理器
 * 用于防止在短时间内多次调用同一个异步方法
 */
class DuplicateCallPreventer {
  constructor() {
    this.pendingCalls = new Map(); // 存储正在进行的调用
    this.lastCallTime = new Map(); // 存储最后调用时间
    this.callDebounceTime = 100; // 防抖时间（毫秒）
  }

  /**
   * 防重复调用装饰器
   * @param {string} key - 调用的唯一标识
   * @param {Function} asyncFunction - 要执行的异步函数
   * @param {Array} args - 函数参数
   * @param {number} debounceTime - 防抖时间，默认100ms
   * @returns {Promise} 
   */
  async preventDuplicateCall(key, asyncFunction, args = [], debounceTime = this.callDebounceTime) {
    const now = Date.now();
    const lastCall = this.lastCallTime.get(key) || 0;
    
    // 如果距离上次调用时间太短，跳过这次调用
    if (now - lastCall < debounceTime) {
      console.warn(`⚠️ 防重复调用: ${key} - 距离上次调用仅${now - lastCall}ms，跳过此次调用`);
      return { success: false, reason: 'debounced', lastCall, currentCall: now };
    }
    
    // 如果已有相同的调用在进行中，等待其完成
    if (this.pendingCalls.has(key)) {
      console.warn(`⚠️ 防重复调用: ${key} - 已有相同调用在进行中，等待完成`);
      return await this.pendingCalls.get(key);
    }
    
    // 记录调用开始时间
    this.lastCallTime.set(key, now);
    
    // 创建新的调用Promise
    const callPromise = this._executeCall(key, asyncFunction, args);
    this.pendingCalls.set(key, callPromise);
    
    try {
      const result = await callPromise;
      return result;
    } finally {
      // 调用完成后清理
      this.pendingCalls.delete(key);
    }
  }

  /**
   * 执行实际的异步调用
   * @private
   */
  async _executeCall(key, asyncFunction, args) {
    const startTime = Date.now();
    console.log(`🔄 执行调用: ${key} - 开始时间: ${startTime}`);
    
    try {
      const result = await asyncFunction(...args);
      const duration = Date.now() - startTime;
      console.log(`✅ 调用完成: ${key} - 耗时: ${duration}ms`);
      return { success: true, result, duration, startTime };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 调用失败: ${key} - 耗时: ${duration}ms, 错误:`, error);
      return { success: false, error, duration, startTime };
    }
  }

  /**
   * 清理指定key的调用记录
   */
  clearCall(key) {
    this.pendingCalls.delete(key);
    this.lastCallTime.delete(key);
  }

  /**
   * 清理所有调用记录
   */
  clearAll() {
    this.pendingCalls.clear();
    this.lastCallTime.clear();
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      pendingCalls: Array.from(this.pendingCalls.keys()),
      lastCallTimes: Object.fromEntries(this.lastCallTime),
      totalPending: this.pendingCalls.size
    };
  }
}

// 创建全局实例
const duplicateCallPreventer = new DuplicateCallPreventer();

/**
 * 修复后的 updateBluetoothSendData 包装器
 * 在 CapacitorKtService 层面添加防重复调用
 */
const createProtectedUpdateBluetoothSendData = (originalFunction) => {
  return async function(options) {
    const dataKey = JSON.stringify(options.data); // 使用数据内容作为key的一部分
    const callKey = `updateBluetoothSendData_${dataKey.substring(0, 50)}`; // 截取前50个字符避免key过长
    
    console.log(`📞 调用 updateBluetoothSendData - Key: ${callKey}`);
    console.log(`📊 当前防重复调用状态:`, duplicateCallPreventer.getStatus());
    
    const result = await duplicateCallPreventer.preventDuplicateCall(
      callKey,
      originalFunction.bind(this),
      [options],
      150 // 150ms防抖时间，足够处理UI的多次点击
    );
    
    if (result.success) {
      console.log(`✅ updateBluetoothSendData 执行成功 - 耗时: ${result.duration}ms`);
      return result.result;
    } else if (result.reason === 'debounced') {
      console.log(`⏭️ updateBluetoothSendData 被防抖跳过`);
      // 对于被防抖的调用，我们仍然返回成功，因为数据最终会被更新
      return Promise.resolve();
    } else {
      console.error(`❌ updateBluetoothSendData 执行失败:`, result.error);
      throw result.error;
    }
  };
};

/**
 * 修复 SettingPage 中的多重调用问题
 * 优化 updateAllBluetoothSolutions 方法
 */
const createOptimizedUpdateAllBluetoothSolutions = () => {
  return async function(data) {
    console.log("🔧 开始优化的蓝牙方案数据更新");
    
    try {
      // 判断当前平台
      const isAndroid = /android/i.test(navigator.userAgent);
      console.log(`📱 当前平台: ${isAndroid ? 'Android' : 'iOS/Web'}`);
      
      if (isAndroid) {
        // Android环境：只调用原生蓝牙方案，避免重复
        console.log("🔧 Android环境 - 仅更新原生蓝牙方案");
        
        const { useNativeBluetoothMessage } = await import("@/hooks/useNativeBluetoothMessage");
        const nativeHook = useNativeBluetoothMessage();
        
        if (nativeHook.updateNativeBluetoothData) {
          await nativeHook.updateNativeBluetoothData();
          console.log("✅ Android - 原生蓝牙方案数据已更新");
        }
        
        // 智能蓝牙方案在Android上就是原生方案的别名，不需要重复调用
        console.log("ℹ️ Android - 智能蓝牙方案与原生方案相同，跳过重复调用");
        
      } else {
        // iOS/Web环境：使用WebView方案
        console.log("🔧 iOS/Web环境 - 更新WebView方案");
        
        const { useMessage } = await import("@/hooks/useMessage");
        const messageHook = useMessage();
        
        if (messageHook.updateSendDataCache) {
          await messageHook.updateSendDataCache();
          console.log("✅ iOS/Web - WebView方案数据已更新");
        }
      }
      
      // 发送全局更新事件（只发送一次）
      window.dispatchEvent(new CustomEvent('settingDataUpdated', {
        detail: {
          writeData: data,
          timestamp: Date.now(),
          source: 'OptimizedUpdateAllBluetoothSolutions',
          platform: isAndroid ? 'Android' : 'iOS/Web'
        }
      }));
      
      console.log("✅ 优化的蓝牙方案数据更新完成");
      
    } catch (error) {
      console.error("❌ 优化的蓝牙方案数据更新失败:", error);
      throw error;
    }
  };
};

/**
 * 应用修复的使用说明
 */
const applyFix = () => {
  console.log(`
🔧 修复重复调用 updateBluetoothSendData 的方案

问题分析：
1. SettingPage 中的 saveSettings 调用了 updateAllBluetoothSolutions
2. updateAllBluetoothSolutions 同时调用了3个蓝牙方案的更新方法
3. 在Android环境下，原生方案和智能方案都会调用相同的 updateBluetoothSendData
4. 导致一次保存操作触发多次 updateBluetoothSendData 调用

修复方案：
1. 在 CapacitorKtService 层面添加防重复调用机制
2. 优化 updateAllBluetoothSolutions 方法，根据平台选择合适的更新策略
3. 添加调用状态监控和日志记录

使用方法：
1. 将 createProtectedUpdateBluetoothSendData 应用到 CapacitorKtService
2. 将 createOptimizedUpdateAllBluetoothSolutions 应用到 SettingPage
3. 监控日志确认修复效果

预期效果：
- 一次保存操作只会触发一次 updateBluetoothSendData 调用
- 减少不必要的网络请求和性能开销
- 避免数据竞态条件和不一致问题
  `);
};

// 导出修复方案
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    DuplicateCallPreventer,
    duplicateCallPreventer,
    createProtectedUpdateBluetoothSendData,
    createOptimizedUpdateAllBluetoothSolutions,
    applyFix
  };
}

// 在浏览器环境中添加到全局对象
if (typeof window !== 'undefined') {
  window.BluetoothDuplicateCallFix = {
    DuplicateCallPreventer,
    duplicateCallPreventer,
    createProtectedUpdateBluetoothSendData,
    createOptimizedUpdateAllBluetoothSolutions,
    applyFix
  };
}

// 立即显示使用说明
applyFix();