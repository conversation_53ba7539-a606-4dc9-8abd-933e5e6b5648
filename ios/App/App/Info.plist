<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>KT Smart Bicycle</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Uses Bluetooth to connect and interact with peripheral BLE devices.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Uses Bluetooth to connect and interact with peripheral BLE devices.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Is it allowed to obtain your location information during use to display your current location?</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Is it allowed to obtain your location information during use to display your current location?</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>I need permission</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>bluetooth-peripheral</string>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>background-app-refresh</string>
		<string>location</string>
		<string>audio</string>
	</array>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.dongsipan.smartbicycle.bluetooth-refresh</string>
	</array>
	<key>UIApplicationExitsOnSuspend</key>
	<false/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>MBXAccessToken</key>
	<string>sk.eyJ1IjoicHd1YnBkam4iLCJhIjoiY21hZ2oyencxMDFtcjJsczdwMGV0NTEyayJ9.MueQLGmO1Wq_gwhhV41jVA</string>
</dict>
</plist>