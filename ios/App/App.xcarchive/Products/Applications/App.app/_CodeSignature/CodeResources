<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		DbuEJa7TIevwXJOC1cnsELgbQpY=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		4fHfb9uCQqfdvzgzMYXoP0oESgM=
		</data>
		<key>Assets.car</key>
		<data>
		OfWB17n8t/C4T4u26dcpQnvPqTk=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-snD-IY-ifK.nib</key>
		<data>
		RRHsDTT2tmu/Fu6K8rdPrZA92a4=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		QKqIK7RVvfhu8NjQMMsccYaM/bA=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		XChaGCu9endqpT3P08g+dq6044k=
		</data>
		<key>Frameworks/Capacitor.framework/Capacitor</key>
		<data>
		BtOwHVrMhZJRWD/ge3ymzGxbcVg=
		</data>
		<key>Frameworks/Capacitor.framework/Info.plist</key>
		<data>
		3K5ig8MMjW8aoI+Pjfv8A89fECQ=
		</data>
		<key>Frameworks/Capacitor.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Eq4eiivdfFc9fjHGBSV6laZaNKI=
		</data>
		<key>Frameworks/Capacitor.framework/_CodeSignature/CodeResources</key>
		<data>
		VphePYWcRK+SoiSyxtyDx7egU+w=
		</data>
		<key>Frameworks/Capacitor.framework/native-bridge.js</key>
		<data>
		4zjeio6Z4zOjPa/qKojqqtcsMsE=
		</data>
		<key>Frameworks/CapacitorApp.framework/CapacitorApp</key>
		<data>
		phNMwQUFNKsd6DCAOP5NIaxYzN0=
		</data>
		<key>Frameworks/CapacitorApp.framework/Info.plist</key>
		<data>
		+ZrmMQmz4qn8JPBDF1KWJ6kzlMw=
		</data>
		<key>Frameworks/CapacitorApp.framework/_CodeSignature/CodeResources</key>
		<data>
		sBgjd8CKuhswDlT5C9VZ5e9MUyA=
		</data>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/CapacitorCommunityBluetoothLe</key>
		<data>
		O57cxUhUBLQFwRDUiKF411Kb2Ac=
		</data>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/Info.plist</key>
		<data>
		pO0AmlXGprszmMtjirYjmRAXZVE=
		</data>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/_CodeSignature/CodeResources</key>
		<data>
		PXcgar264ZStUhktDNRN0W1YYR8=
		</data>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/CapacitorCommunityKeepAwake</key>
		<data>
		EHMibTqeN9CFJ+UUKmqPubeyAc0=
		</data>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/Info.plist</key>
		<data>
		HW1E8qSblpbh+lSzveilJ6hhjoI=
		</data>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/_CodeSignature/CodeResources</key>
		<data>
		w26fSuwA1s+gyNQ/J126ABr5b7o=
		</data>
		<key>Frameworks/CapacitorGeolocation.framework/CapacitorGeolocation</key>
		<data>
		32BVqsFpmjEX8bEOkyW89FteT+4=
		</data>
		<key>Frameworks/CapacitorGeolocation.framework/Info.plist</key>
		<data>
		VwvrP40FuqJeWS6+zXgQZVzZXvk=
		</data>
		<key>Frameworks/CapacitorGeolocation.framework/_CodeSignature/CodeResources</key>
		<data>
		TzNrgjyPyafKstBCgZK1OiPX2FQ=
		</data>
		<key>Frameworks/CapacitorHaptics.framework/CapacitorHaptics</key>
		<data>
		ROY7AqAs91+cue2JCFmywJMJc8o=
		</data>
		<key>Frameworks/CapacitorHaptics.framework/Info.plist</key>
		<data>
		IQeuslmjuBhHq+X7s26IzVIzz/k=
		</data>
		<key>Frameworks/CapacitorHaptics.framework/_CodeSignature/CodeResources</key>
		<data>
		pPVNFqR4QMKEsmXJVGWrBp6W5ks=
		</data>
		<key>Frameworks/CapacitorKeyboard.framework/CapacitorKeyboard</key>
		<data>
		cSNc8/Y8aubM8VZwVHtTJUBjOvY=
		</data>
		<key>Frameworks/CapacitorKeyboard.framework/Info.plist</key>
		<data>
		sIAV/vd5pgSApXsD/7O9SijRgys=
		</data>
		<key>Frameworks/CapacitorKeyboard.framework/_CodeSignature/CodeResources</key>
		<data>
		x3tRRCnYb64fHYza/Uvq5vZtrz4=
		</data>
		<key>Frameworks/CapacitorScreenOrientation.framework/CapacitorScreenOrientation</key>
		<data>
		dCYLDBPVD7VmMSfksuctx8Y7IQQ=
		</data>
		<key>Frameworks/CapacitorScreenOrientation.framework/Info.plist</key>
		<data>
		BdX+sZ6WMhMEzeQk+7qYor4NDHo=
		</data>
		<key>Frameworks/CapacitorScreenOrientation.framework/_CodeSignature/CodeResources</key>
		<data>
		f1W4fGVtvV+i/1xclLQ0i+7pbN4=
		</data>
		<key>Frameworks/CapacitorStatusBar.framework/CapacitorStatusBar</key>
		<data>
		mEPVkgehj5FmqKk+/tgBNJk4EAk=
		</data>
		<key>Frameworks/CapacitorStatusBar.framework/Info.plist</key>
		<data>
		9mDbfv2a/8diO6LGpG4srs3NEBQ=
		</data>
		<key>Frameworks/CapacitorStatusBar.framework/_CodeSignature/CodeResources</key>
		<data>
		dORAEoOpcdRQKj7UtI5JXBkYc2c=
		</data>
		<key>Frameworks/Cordova.framework/Cordova</key>
		<data>
		zDatK8yaiD/oEzRRE0E4CJfu0Ss=
		</data>
		<key>Frameworks/Cordova.framework/Info.plist</key>
		<data>
		ifALcZsDhqQrUSuOpA+PZSKr06k=
		</data>
		<key>Frameworks/Cordova.framework/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>Frameworks/Cordova.framework/_CodeSignature/CodeResources</key>
		<data>
		IEjh9N2Ob5RHAbhRanNNgqJRVXw=
		</data>
		<key>Frameworks/IONGeolocationLib.framework/IONGeolocationLib</key>
		<data>
		fSTXUEhu5bWPzExQb7yPBzl1VUc=
		</data>
		<key>Frameworks/IONGeolocationLib.framework/Info.plist</key>
		<data>
		PyGo1wtxsLURP/jZV5dvW/uxFeo=
		</data>
		<key>Frameworks/IONGeolocationLib.framework/_CodeSignature/CodeResources</key>
		<data>
		JwnZwQdTgkKlAI0+bwF6DHZYsPo=
		</data>
		<key>Info.plist</key>
		<data>
		UsKMa7TbUIAxAWiKGttdK9FcoZg=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>capacitor.config.json</key>
		<data>
		EdhTP1nBAZYGykp0LCsBEef+djo=
		</data>
		<key>config.xml</key>
		<data>
		bCZGUCvjDKt2Qr2EGwXDYJ/P+fI=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		DYylkQ4mibiJ5jOaZupaJlaP2k0=
		</data>
		<key>public/assets/BluetoothPage-DsoAftnB.css</key>
		<data>
		GzVX4BFSn8ZR2pc1mk1ntwXaFHE=
		</data>
		<key>public/assets/BluetoothPage-Jb3sujQY.js</key>
		<data>
		eJv/gM0yFrxXVz7Wk+ic4JXJ7/E=
		</data>
		<key>public/assets/BluetoothPage-legacy-CYzdNS46.js</key>
		<data>
		Fxl0h9Ym54RL2Y0Lfqs45bYnYdE=
		</data>
		<key>public/assets/Dashboard-D6evf51t.js</key>
		<data>
		R3qQRAOq2efCm5KyKbmiyG+tbSU=
		</data>
		<key>public/assets/Dashboard-DScu1rav.css</key>
		<data>
		r52qu8D3zT0diYDjfPyIHzteH2s=
		</data>
		<key>public/assets/Dashboard-legacy-DsLAQhXE.js</key>
		<data>
		f245p9qCFWS51Y9gEV7cwy5THnI=
		</data>
		<key>public/assets/HomePage-CFzKyCOo.js</key>
		<data>
		fbvMd+PAHECwpKVV/EUXVDQEzaE=
		</data>
		<key>public/assets/HomePage-CaKqA1Tb.css</key>
		<data>
		I0IlKcTVKIOlnQ/A9Qx0SETb+1U=
		</data>
		<key>public/assets/HomePage-legacy-DRD0n0nK.js</key>
		<data>
		i2C6GxyXsobAy1uhka5c/bRYHtk=
		</data>
		<key>public/assets/InfoPage-kQcLm6KW.js</key>
		<data>
		F5C8eEicxUBUWaFo1S9OTkkoQBE=
		</data>
		<key>public/assets/InfoPage-legacy-BEE6X06R.js</key>
		<data>
		5s0WZehjxbzUHNdeBCU1kMGg6Pc=
		</data>
		<key>public/assets/InfoPage-wxPWyrwp.css</key>
		<data>
		zX2ljow9jIyn0fMpUezhLB+OEEo=
		</data>
		<key>public/assets/PlaceSearch-DeVLaGJp.js</key>
		<data>
		R/jtTiML2V3Amui2S4qV/pjSAvg=
		</data>
		<key>public/assets/PlaceSearch-legacy-VI3sH4Mh.js</key>
		<data>
		/PjckwOQvXUElGVf60xrt2Pbwdc=
		</data>
		<key>public/assets/PlaceSearch-vKFeIUhs.css</key>
		<data>
		3PmhiKIKdS/f7RQEn0Vxd23Cz2k=
		</data>
		<key>public/assets/SettingPage-Cp_2afnK.css</key>
		<data>
		8FJ54dvIWbysggYr9GSLlDD5lkQ=
		</data>
		<key>public/assets/SettingPage-Dymc7l9S.js</key>
		<data>
		V0TcEijmgcz+W+aqskt2mEE/hbE=
		</data>
		<key>public/assets/SettingPage-legacy-CbMsEQoT.js</key>
		<data>
		CpZHuZLVz/+OKMRISMm4+ETOfnw=
		</data>
		<key>public/assets/TabsPage-Bi9Qn3hc.js</key>
		<data>
		d5ch8VxOIGmU5ie2ZYVcICoQp/s=
		</data>
		<key>public/assets/TabsPage-DX1UBFz7.css</key>
		<data>
		762PPkoed9ih8UjbyOEExG8MmRw=
		</data>
		<key>public/assets/TabsPage-legacy-D2U0Hpq0.js</key>
		<data>
		sseKqB8G0h7Il69BOP9YQadDvsQ=
		</data>
		<key>public/assets/_plugin-vue_export-helper-DlAUqK2U.js</key>
		<data>
		eFCz/UrraTh721pgAl0VxBNR1es=
		</data>
		<key>public/assets/_plugin-vue_export-helper-legacy-DgAO6S8O.js</key>
		<data>
		Us1S/WeKqlxGkMi9wcGazHkUUV4=
		</data>
		<key>public/assets/focus-visible-legacy-CdO5cX4I.js</key>
		<data>
		9WK26hPp8RttaQvgVMJKyu3Qg3U=
		</data>
		<key>public/assets/focus-visible-supuXXMI.js</key>
		<data>
		ilOgFj2agyKzBXg2XDJ6FgWnTAs=
		</data>
		<key>public/assets/icon/battery_0.svg</key>
		<data>
		yVjQeGS57FyX2rD+BSlBJtivOeQ=
		</data>
		<key>public/assets/icon/battery_1.svg</key>
		<data>
		m3AE9/aBeHylJSc2CXwU2sONaJU=
		</data>
		<key>public/assets/icon/battery_2.svg</key>
		<data>
		qW/wmmJuzPPLRvJzSmZ5TFbrrgA=
		</data>
		<key>public/assets/icon/battery_3.svg</key>
		<data>
		h/Bsphx/HkIeL4Hk6a2pCRbV2EY=
		</data>
		<key>public/assets/icon/battery_4.svg</key>
		<data>
		2UguIwYs1/hA5QXZxH6K/7CzCR8=
		</data>
		<key>public/assets/icon/brake-outline.svg</key>
		<data>
		yiVyToOLsXkPups6ReKg7817mqk=
		</data>
		<key>public/assets/icon/brake.svg</key>
		<data>
		q3bhxyOaIPLtdp/n1TN3IOopQdQ=
		</data>
		<key>public/assets/icon/caret-down.svg</key>
		<data>
		XlWQirTfELtFzjgYdFl3p+8KQGM=
		</data>
		<key>public/assets/icon/caret-up.svg</key>
		<data>
		id3LLbVbshUJiQs3Cg2FwLG88J4=
		</data>
		<key>public/assets/icon/favicon.png</key>
		<data>
		d5i44Oth1zdcJFr3i79ckWkyvxM=
		</data>
		<key>public/assets/icon/icon.png</key>
		<data>
		K/xbtI8gEhLjkyoHFTsrOh392A0=
		</data>
		<key>public/assets/icon/light-white.svg</key>
		<data>
		w/ygtQ7gPTx8Tw0psVQO1Gh0iAQ=
		</data>
		<key>public/assets/index-Bc1Yyv3I.js</key>
		<data>
		rm2WkM8gF9JIal1Lf21sHKhHD1A=
		</data>
		<key>public/assets/index-BxicBINB.css</key>
		<data>
		jh/uxQ9chbo8PYND4MV01J8gmg4=
		</data>
		<key>public/assets/index-CMzsP7-d.js</key>
		<data>
		En6NF9g4p4Tt9IDXfO5CfWtIA/E=
		</data>
		<key>public/assets/index-legacy-C0iBWw5M.js</key>
		<data>
		hR1fQxWWZXJRnc37kma2sNbX3L4=
		</data>
		<key>public/assets/index-legacy-CZnTL5oV.js</key>
		<data>
		L7TQOPEA1wap/jXjuobfiUhOp+E=
		</data>
		<key>public/assets/index9-QDDmMYOa.js</key>
		<data>
		WM8VOIN9ObeKe9ToHGLynwDslNM=
		</data>
		<key>public/assets/index9-legacy--ew9y9ir.js</key>
		<data>
		8SkiM+lS8kEGEXXkYry2hvdbTiI=
		</data>
		<key>public/assets/input-shims-diKvrJgy.js</key>
		<data>
		1IDcQpHYj5E7tbCDkXzQosozZX0=
		</data>
		<key>public/assets/input-shims-legacy-BcQO-LOK.js</key>
		<data>
		AkPctyq4MWk7KcutawBcVQ52+k4=
		</data>
		<key>public/assets/ios.transition-D7c2iLnG.js</key>
		<data>
		gmYI2aQpVS36fLkok6Tj8IEQN2A=
		</data>
		<key>public/assets/ios.transition-legacy-1iZOyfPK.js</key>
		<data>
		8xVU4DPqZbNpbHCfrStSsX88vKo=
		</data>
		<key>public/assets/md.transition-V8Yau7x4.js</key>
		<data>
		18SZ1ztUy6/dlnkn5eLWNeEMjRY=
		</data>
		<key>public/assets/md.transition-legacy-Bm7OT6Dm.js</key>
		<data>
		aARGXncEWj0MHtfzoBo0LTTCXG8=
		</data>
		<key>public/assets/polyfills-legacy-C5zJNjNQ.js</key>
		<data>
		tzAEK4N/sgA/P0g3zRcdYWWkLZo=
		</data>
		<key>public/assets/shapes.svg</key>
		<data>
		VBPpWtq8sstjmXAFH1Xg3bZKzlM=
		</data>
		<key>public/assets/status-tap-H_Tt5_m6.js</key>
		<data>
		n/PhO9kOgr0xiUYTHcsHFev3xoM=
		</data>
		<key>public/assets/status-tap-legacy-BrVEykYY.js</key>
		<data>
		NosjFUMy/M48tqpTMhyqbtyc4+M=
		</data>
		<key>public/assets/swipe-back-BA_Z6aX7.js</key>
		<data>
		VDe0VriuvrUmApoixJ46ERhYNOg=
		</data>
		<key>public/assets/swipe-back-legacy-BwvAYS7p.js</key>
		<data>
		l0ixvKabg1XYg0TrvDvej6Po0QE=
		</data>
		<key>public/assets/web-BuIenjK2.js</key>
		<data>
		YheLRpmVGifY7EXoA3YVsqIrw9k=
		</data>
		<key>public/assets/web-BwCP9J-L.js</key>
		<data>
		Rdz/RNPND4mu+e8PPlx92yfpbuk=
		</data>
		<key>public/assets/web-CPbwl1Dm.js</key>
		<data>
		Umm2+w+wRU1vNMqLGVrz18G+JDc=
		</data>
		<key>public/assets/web-CYMup8nl.js</key>
		<data>
		dSPr04Dl65zhFjRRSxXHPKV0wvg=
		</data>
		<key>public/assets/web-DCnzWJ0v.js</key>
		<data>
		egGUgFxnvkwrhpakq43tdpCy7U4=
		</data>
		<key>public/assets/web-GMjGWYYT.js</key>
		<data>
		qdhhAJn99BFdM7bHt+VwaOXIIBU=
		</data>
		<key>public/assets/web-legacy-B1gVI8Er.js</key>
		<data>
		jrl3dpNn+V2Ak2LuyZDjBrbacgM=
		</data>
		<key>public/assets/web-legacy-B37T1-df.js</key>
		<data>
		QnyBjMUd/hVl6qtkI2Y/cid/OyI=
		</data>
		<key>public/assets/web-legacy-Cl3t3hJ_.js</key>
		<data>
		de2JIAWJPrGMH7VuNCqgX78zI2s=
		</data>
		<key>public/assets/web-legacy-CzBSJvRu.js</key>
		<data>
		NqLWwsWxTBMYLyOPVikFmMvx2Pk=
		</data>
		<key>public/assets/web-legacy-DMNLHNj6.js</key>
		<data>
		sNBCIU1TSBm+cbxxhpU9EIp+asI=
		</data>
		<key>public/assets/web-legacy-w54beDxd.js</key>
		<data>
		SK4u4lIItnP+2fJIxWwqXr2XZNY=
		</data>
		<key>public/cordova.js</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>public/cordova_plugins.js</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>public/favicon.png</key>
		<data>
		ff/Wl9zm+M19e9qJUfkT+RqYcSw=
		</data>
		<key>public/index.html</key>
		<data>
		+N0ZjGMnk5TOLObML1cgxpzW4PY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			iT4+zuKUJpdEseBxyOLBp2PJ78CltzSFijFJB9LgiX0=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pCnfxqIMOq7qZ75aaG/Kpm2W2/frQZFM/IpzQJ9FNME=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			p0iTthMGPKxqp1W103XKctk5J8wnfMz3QU8a+jhBnw0=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-snD-IY-ifK.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			qycDD8HdP2LEHuczcUV4nXfS1bXF5hm3GLv9O3ixYio=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			EL/M3uiq/NBWmwodq2ES5wuK+w2sA/uRCCPlCXqIAFE=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			+dQTGVrgiXJVSSV9zz0Rr55sQmoSn8A3a3e6kzegAxI=
			</data>
		</dict>
		<key>Frameworks/Capacitor.framework/Capacitor</key>
		<dict>
			<key>hash2</key>
			<data>
			NuSGZTEBfNS1I3dAnc9CRx+W6gL+wurzYCX2n0gVbas=
			</data>
		</dict>
		<key>Frameworks/Capacitor.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ECOWo45JQbo0KoMv5PawKCPGkahkNZian4b9ONa4ep4=
			</data>
		</dict>
		<key>Frameworks/Capacitor.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			G6yCf0myuKU1hJG5aYIDvxkXkabxujo6zjsShdUtLRc=
			</data>
		</dict>
		<key>Frameworks/Capacitor.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			V7miIwjgNoYiRB0KNhmS2CxjFXevObfn4KZDd9fj8Os=
			</data>
		</dict>
		<key>Frameworks/Capacitor.framework/native-bridge.js</key>
		<dict>
			<key>hash2</key>
			<data>
			bldjV2c2mvMEshcXG4SBI1DBF9KpS5G/77ES0DxBafE=
			</data>
		</dict>
		<key>Frameworks/CapacitorApp.framework/CapacitorApp</key>
		<dict>
			<key>hash2</key>
			<data>
			/1A/RmOAw50jOBYqKIHGjvTb0UqhQxFzyLZy89WP2Ow=
			</data>
		</dict>
		<key>Frameworks/CapacitorApp.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Rg4If9JLEqWibF53jNJIsgAmXzVzTAgGHlCGluDl5dA=
			</data>
		</dict>
		<key>Frameworks/CapacitorApp.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			TshQ0voa6+/9OrveT6USvx5Izu4ThI4I0Q/EOV/Stt8=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/CapacitorCommunityBluetoothLe</key>
		<dict>
			<key>hash2</key>
			<data>
			uD8pTpoEgwWqx+6zaO0RfLFxhSM6PS3INBC4QT3b8Qo=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			9JXFIr9Y/EYAgOFUz9gsMiQ3farAoqoxvZF5ayyAYDY=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityBluetoothLe.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4En6Rx7odHQA+tS7iWDfgwnPif5EYJyxPceSU97FpAA=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/CapacitorCommunityKeepAwake</key>
		<dict>
			<key>hash2</key>
			<data>
			vUy1XX62rS7Ly4Hzp901qzeBKnBUAH4V7+5RInJZJrw=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			NZh785oxweK75KbyUJTPpqhPWsnzgZLl3ZZMgJPtNj0=
			</data>
		</dict>
		<key>Frameworks/CapacitorCommunityKeepAwake.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Mg2MVYb/56RRE97Hks4H+ITOHFdKs9Vh5sKj0YDYTRc=
			</data>
		</dict>
		<key>Frameworks/CapacitorGeolocation.framework/CapacitorGeolocation</key>
		<dict>
			<key>hash2</key>
			<data>
			rXn5UJ14/BlIAeIX4eV/ONT2cMorQLszwqASEyp4Se8=
			</data>
		</dict>
		<key>Frameworks/CapacitorGeolocation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FZtfdOuIAOXQAUR7qBmSb3s/x4eoRuVD8iOVdsJGCFc=
			</data>
		</dict>
		<key>Frameworks/CapacitorGeolocation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			3vtHuCZZ9769KRh3eje6PWk7Cldjm8Q3nfdqDBpRE90=
			</data>
		</dict>
		<key>Frameworks/CapacitorHaptics.framework/CapacitorHaptics</key>
		<dict>
			<key>hash2</key>
			<data>
			0MuamEJ9INy8/PKm72snA8J6hPHUbLZcAO2gXNSwG0s=
			</data>
		</dict>
		<key>Frameworks/CapacitorHaptics.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			3SRTqigFr4Eq20DkLr1VIUINLSe9miV0t57olJ20m/g=
			</data>
		</dict>
		<key>Frameworks/CapacitorHaptics.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			rJsu4M6l6tNe8IQYJEuTXmH3SXH/SOnlIwa2UFRaQTo=
			</data>
		</dict>
		<key>Frameworks/CapacitorKeyboard.framework/CapacitorKeyboard</key>
		<dict>
			<key>hash2</key>
			<data>
			qw6GmPBWNaNCVTn4igiysMB2MmlZB9Ok22aqsQN0LSg=
			</data>
		</dict>
		<key>Frameworks/CapacitorKeyboard.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AXBEkjvdwP54PEsyzUUCbTXZVzo2bt67ZAAhbRUQiEc=
			</data>
		</dict>
		<key>Frameworks/CapacitorKeyboard.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			zdihORhSt6IscIsJicrfeieHEHO/j+GCvHlmz2DhAD4=
			</data>
		</dict>
		<key>Frameworks/CapacitorScreenOrientation.framework/CapacitorScreenOrientation</key>
		<dict>
			<key>hash2</key>
			<data>
			ltBXu8BzFo8KCSfFhNCzeRLnZRj3Y5xNoIvASWdYVYE=
			</data>
		</dict>
		<key>Frameworks/CapacitorScreenOrientation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pebtlU4E3UyvoYehB0DBipZnPfgtGpOH1uPzk+hl8CA=
			</data>
		</dict>
		<key>Frameworks/CapacitorScreenOrientation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			rZd/h/FBP/RZb/rcPIhsCdgy0w5CG5/d1rE0kdGFxI4=
			</data>
		</dict>
		<key>Frameworks/CapacitorStatusBar.framework/CapacitorStatusBar</key>
		<dict>
			<key>hash2</key>
			<data>
			CGstxoPYsHElHPPD5IGBs79ypXp3sth83B4kApj60nw=
			</data>
		</dict>
		<key>Frameworks/CapacitorStatusBar.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			OlLGZoaTtYelwQXzr9EKkx6EAjFkVlnWagSbXCHvfdU=
			</data>
		</dict>
		<key>Frameworks/CapacitorStatusBar.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			9qquviVIEbWIrD4ZmR1EoV/GbmXXTin+eao1yntT9Tc=
			</data>
		</dict>
		<key>Frameworks/Cordova.framework/Cordova</key>
		<dict>
			<key>hash2</key>
			<data>
			DcRmqzNv4kHuLliwquNx6Vz9ZZ1omwK7DX8YrdqoGi0=
			</data>
		</dict>
		<key>Frameworks/Cordova.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Bl6s/keEyWAZs6iFVlW/qd3x9gmsuRgsQ+jFbk0w48c=
			</data>
		</dict>
		<key>Frameworks/Cordova.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>Frameworks/Cordova.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			OmAFMLmz+2OWs/80KWPVlWdbhmdxLKRJ12qwI6pQg5A=
			</data>
		</dict>
		<key>Frameworks/IONGeolocationLib.framework/IONGeolocationLib</key>
		<dict>
			<key>hash2</key>
			<data>
			X0iFdHUEvvQY1N7W5H4e1yzQKqc78MQffWcs/lA7Bqk=
			</data>
		</dict>
		<key>Frameworks/IONGeolocationLib.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			NZ1/+7EOhAdnFIosn7Hmu8Vq0LNQySyjbf0TVa3PonE=
			</data>
		</dict>
		<key>Frameworks/IONGeolocationLib.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			WdsSScq/Tzp77L2PzeOrEC1QJ2GQNIir1bfue9d5aEk=
			</data>
		</dict>
		<key>capacitor.config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TY2qCHZfSn/rgsnACk18nnH8jXcKnDjwY656DXkw/lE=
			</data>
		</dict>
		<key>config.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			6dzaST5mPFxNuePLO9loR3pukKqIQLy7pA1y7VS3Z84=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			Hwsg3CD5SdtMckeeiVIpBUU4OgdadI/bj9RZnMeivqQ=
			</data>
		</dict>
		<key>public/assets/BluetoothPage-DsoAftnB.css</key>
		<dict>
			<key>hash2</key>
			<data>
			SiaQzFtsb4Xlm1KCeKJHMZ9HK7apphlh880THoLLJCs=
			</data>
		</dict>
		<key>public/assets/BluetoothPage-Jb3sujQY.js</key>
		<dict>
			<key>hash2</key>
			<data>
			VZH/pkwlfSbzpbMUNwye9bImWOw9ncfB1XtExWfKclo=
			</data>
		</dict>
		<key>public/assets/BluetoothPage-legacy-CYzdNS46.js</key>
		<dict>
			<key>hash2</key>
			<data>
			SKwbOr1xPYZ8wTg4YxiFslw4etsRBC0SI59GMXuEvk8=
			</data>
		</dict>
		<key>public/assets/Dashboard-D6evf51t.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hwT5QDx5SX9iyNirnf3kAs8je8hzRAZg4oe1OGTNuEQ=
			</data>
		</dict>
		<key>public/assets/Dashboard-DScu1rav.css</key>
		<dict>
			<key>hash2</key>
			<data>
			s3t71QFMwr/txGDsVq9XHam0ktC6UcDDi3JCoSHgWzo=
			</data>
		</dict>
		<key>public/assets/Dashboard-legacy-DsLAQhXE.js</key>
		<dict>
			<key>hash2</key>
			<data>
			yOpQXinlGlEYFFOTf9yjL4cakfqHYgayysZfAprAADs=
			</data>
		</dict>
		<key>public/assets/HomePage-CFzKyCOo.js</key>
		<dict>
			<key>hash2</key>
			<data>
			HCOhe6q55QNEJuPjJ9vkbVS8P123BTh/ialciTM+7XE=
			</data>
		</dict>
		<key>public/assets/HomePage-CaKqA1Tb.css</key>
		<dict>
			<key>hash2</key>
			<data>
			A+h/e+n59yK+rhm+lSRU1eEl6vJOhY2T8ZJScIIjJm8=
			</data>
		</dict>
		<key>public/assets/HomePage-legacy-DRD0n0nK.js</key>
		<dict>
			<key>hash2</key>
			<data>
			SkHikZdBDtKa9TPJdilh5OAKRxSDfjOuHFg48GcY714=
			</data>
		</dict>
		<key>public/assets/InfoPage-kQcLm6KW.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YdyQ8ydrfjfQccVyCV59HdgNnlZlC9+s571nsYp3um0=
			</data>
		</dict>
		<key>public/assets/InfoPage-legacy-BEE6X06R.js</key>
		<dict>
			<key>hash2</key>
			<data>
			HvsxHWxqBjf2gE6UUywqncZx6iEbS0Z3fBUsgFzwaeE=
			</data>
		</dict>
		<key>public/assets/InfoPage-wxPWyrwp.css</key>
		<dict>
			<key>hash2</key>
			<data>
			upqPYnypAP7PfHCW3YB2j+kqGEtI9VLCGzz8Ihmk3bQ=
			</data>
		</dict>
		<key>public/assets/PlaceSearch-DeVLaGJp.js</key>
		<dict>
			<key>hash2</key>
			<data>
			j/tmOCdnIEvNP9GH8t/lYakw0MWM/9tJE3+CLBSYy3A=
			</data>
		</dict>
		<key>public/assets/PlaceSearch-legacy-VI3sH4Mh.js</key>
		<dict>
			<key>hash2</key>
			<data>
			XXgxkx3eK8tRfdAGrTD51y2OUOfhJRJ9RL4vJuEWieQ=
			</data>
		</dict>
		<key>public/assets/PlaceSearch-vKFeIUhs.css</key>
		<dict>
			<key>hash2</key>
			<data>
			tc+nUvzY0x0MiqBfMQ799gD0le8CgVN/fPL3+AJkxq8=
			</data>
		</dict>
		<key>public/assets/SettingPage-Cp_2afnK.css</key>
		<dict>
			<key>hash2</key>
			<data>
			1ZEFwMPnKm6zKVoklw3qieGEkJNE7saS3SK/WOkdzb0=
			</data>
		</dict>
		<key>public/assets/SettingPage-Dymc7l9S.js</key>
		<dict>
			<key>hash2</key>
			<data>
			vKs9vXuDOvmF9z20aqRcPfIG2U6uuse2wEk3EtAiPCY=
			</data>
		</dict>
		<key>public/assets/SettingPage-legacy-CbMsEQoT.js</key>
		<dict>
			<key>hash2</key>
			<data>
			NOVFtRAGl5o8FXT40JO+9iKFvtO2MI3pvsZX2brk45s=
			</data>
		</dict>
		<key>public/assets/TabsPage-Bi9Qn3hc.js</key>
		<dict>
			<key>hash2</key>
			<data>
			teZp39LDmiM8qj7IZtlZWs8VnzhH4+Zk9iUntXIJung=
			</data>
		</dict>
		<key>public/assets/TabsPage-DX1UBFz7.css</key>
		<dict>
			<key>hash2</key>
			<data>
			zdWP3+flHKcEvN8kr/J/GKkoCqvJJE2t8Ofku8ngcrs=
			</data>
		</dict>
		<key>public/assets/TabsPage-legacy-D2U0Hpq0.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ks/DGu3XySQdpehB9oVqvwdzY1FFjPoO9qfsH11BxwE=
			</data>
		</dict>
		<key>public/assets/_plugin-vue_export-helper-DlAUqK2U.js</key>
		<dict>
			<key>hash2</key>
			<data>
			y4Ww8mPb4k6FczgwHAYnB2WS6fHxpWYpKfhtLBJkRKo=
			</data>
		</dict>
		<key>public/assets/_plugin-vue_export-helper-legacy-DgAO6S8O.js</key>
		<dict>
			<key>hash2</key>
			<data>
			rSuBXmhxa7ykganm+rOKMUxv7iE/cF/LSFPx21A61K0=
			</data>
		</dict>
		<key>public/assets/focus-visible-legacy-CdO5cX4I.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Cj4h1QAlMZ0r1qoU6ZWBeZdOTt6gieHG/CH0wkNw6CQ=
			</data>
		</dict>
		<key>public/assets/focus-visible-supuXXMI.js</key>
		<dict>
			<key>hash2</key>
			<data>
			iFhmcBXmrCgnHFBid6laG5VK8EWWH4AoMyd02dX18aI=
			</data>
		</dict>
		<key>public/assets/icon/battery_0.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5agtdKof9FczcHayaxqnB3UVySMv8N9VI8Rw4zDX0/g=
			</data>
		</dict>
		<key>public/assets/icon/battery_1.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			u40xlZIU9zSN3aXgOKB9yXwhLZ2KmsFyPM6LAe6bsrI=
			</data>
		</dict>
		<key>public/assets/icon/battery_2.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			t2BP1kpaXeT8jxnEnct+LL1tOUYLMc+X+WsZBxLSlKA=
			</data>
		</dict>
		<key>public/assets/icon/battery_3.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			EnnTFb1tUV7pNi3DhYn77BKP5gfUNgM+7+NEwXFbKnk=
			</data>
		</dict>
		<key>public/assets/icon/battery_4.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SSpWvw4tXlPCefe5gdcGSPA0iBDAnybGYE+gV4+s/ww=
			</data>
		</dict>
		<key>public/assets/icon/brake-outline.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ynP+F2MRp20N88xD5JTq/kgmhHwOllrLpujy/yW3M0s=
			</data>
		</dict>
		<key>public/assets/icon/brake.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			F0ARmiS1qTC8DNcBu9XMTgz9s9XVVLTTY8XIcbuYXrs=
			</data>
		</dict>
		<key>public/assets/icon/caret-down.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ld8KDuyY0oISH8CfTy68+wBZiCMrgsXS/eG5ezPy64A=
			</data>
		</dict>
		<key>public/assets/icon/caret-up.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FX1gQ8IFmIkF3en48209NX8qHZVPIiNtQcbIhjdvXrg=
			</data>
		</dict>
		<key>public/assets/icon/favicon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6CDbRVYzFq1juFV6PLmmgan1n6+JnCUDkxE/y+iF0s0=
			</data>
		</dict>
		<key>public/assets/icon/icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wZK3hjQYwsHehucMXImcS3jJcBI6tFh4s0BKmXD0QS0=
			</data>
		</dict>
		<key>public/assets/icon/light-white.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			9lMQsUzDafre0Dl4CAUsM5tMrJWmsmzBOuEWEbsrV1w=
			</data>
		</dict>
		<key>public/assets/index-Bc1Yyv3I.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3i6lQkKtJ5xRAcLll1wXNBx52egr3qCFk0mK52tj+M8=
			</data>
		</dict>
		<key>public/assets/index-BxicBINB.css</key>
		<dict>
			<key>hash2</key>
			<data>
			S9d3KiZsOsN30AffpwpZt23u2O6U4fcjEkxU8CpVOZE=
			</data>
		</dict>
		<key>public/assets/index-CMzsP7-d.js</key>
		<dict>
			<key>hash2</key>
			<data>
			1C1HW3ezmnCvWU9UEiLZV91XexlUQA0eBPU0jPc7cbQ=
			</data>
		</dict>
		<key>public/assets/index-legacy-C0iBWw5M.js</key>
		<dict>
			<key>hash2</key>
			<data>
			GNMTyRWqnPOd8b8aKOENna8wSPNMhbkUfTrtOz5kZlg=
			</data>
		</dict>
		<key>public/assets/index-legacy-CZnTL5oV.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Qd8JUMfCqRMxn/Yt8c3eRPLfUoO9dadln0s57lqnEWA=
			</data>
		</dict>
		<key>public/assets/index9-QDDmMYOa.js</key>
		<dict>
			<key>hash2</key>
			<data>
			OXd26B94xky2o2jKfD5Gpqy7HfoWy6aw2Bcrr7E8Yx8=
			</data>
		</dict>
		<key>public/assets/index9-legacy--ew9y9ir.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/QvPPPqj5hO5n6ozvchyp8V+BmJ85KDhsFDrQWzyQ8g=
			</data>
		</dict>
		<key>public/assets/input-shims-diKvrJgy.js</key>
		<dict>
			<key>hash2</key>
			<data>
			gsEfSLi5pKiyEHdVRYiLEHRVo8YXZavuBr81mWdfXyA=
			</data>
		</dict>
		<key>public/assets/input-shims-legacy-BcQO-LOK.js</key>
		<dict>
			<key>hash2</key>
			<data>
			lA/0BIhXqNqtxprqZ/SenLtWgtmVv3Z4DNcXgg58N/M=
			</data>
		</dict>
		<key>public/assets/ios.transition-D7c2iLnG.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xn2dt7G5FL+Qia+k2ZXlBWk/Y8vhDB2oREgN9S+STbo=
			</data>
		</dict>
		<key>public/assets/ios.transition-legacy-1iZOyfPK.js</key>
		<dict>
			<key>hash2</key>
			<data>
			XO8Licdi20+8OFLJmtZQwoz0YXdkNdNStvztFTIcNXw=
			</data>
		</dict>
		<key>public/assets/md.transition-V8Yau7x4.js</key>
		<dict>
			<key>hash2</key>
			<data>
			vFTvhJAymUDXhg5kYg1vhKCTkuLDsBAvEIN3tp1b8z8=
			</data>
		</dict>
		<key>public/assets/md.transition-legacy-Bm7OT6Dm.js</key>
		<dict>
			<key>hash2</key>
			<data>
			y4CoMhRgrOyrZziBexvO56lOV7aqP2ZUy5Fmr7emD1w=
			</data>
		</dict>
		<key>public/assets/polyfills-legacy-C5zJNjNQ.js</key>
		<dict>
			<key>hash2</key>
			<data>
			GRfMC8Eo7jXJHMYIDaPWdm92LcwywLiwvnH1CzmvGvw=
			</data>
		</dict>
		<key>public/assets/shapes.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			zxtoKBcW9FLdYg+B/THZi/733VIbgJhDNxC9p6MpGPI=
			</data>
		</dict>
		<key>public/assets/status-tap-H_Tt5_m6.js</key>
		<dict>
			<key>hash2</key>
			<data>
			wlu4720YcHJkAwfUwTL+0wWeWX0V1mxhECoiAuQBEes=
			</data>
		</dict>
		<key>public/assets/status-tap-legacy-BrVEykYY.js</key>
		<dict>
			<key>hash2</key>
			<data>
			a4IRWqW5++1Xz7WORFpDAj2VWr31CucHodxwGtUCBAk=
			</data>
		</dict>
		<key>public/assets/swipe-back-BA_Z6aX7.js</key>
		<dict>
			<key>hash2</key>
			<data>
			92mXmt5FAn8zblY6f5xPK+LRHDl4ehp/C4hmxBx9rYw=
			</data>
		</dict>
		<key>public/assets/swipe-back-legacy-BwvAYS7p.js</key>
		<dict>
			<key>hash2</key>
			<data>
			25fKO6lG+NKSXDFtD4153bJq+73NbEbDB85SFjhsOZw=
			</data>
		</dict>
		<key>public/assets/web-BuIenjK2.js</key>
		<dict>
			<key>hash2</key>
			<data>
			lqFjaud+A5WikY9NOYzpeXb+ouhqNt7wmFmIDUib1eE=
			</data>
		</dict>
		<key>public/assets/web-BwCP9J-L.js</key>
		<dict>
			<key>hash2</key>
			<data>
			4oDFH7GYK9Vb3n05IcE/m/GzQ8OaMD+WcfYtsyK2Urw=
			</data>
		</dict>
		<key>public/assets/web-CPbwl1Dm.js</key>
		<dict>
			<key>hash2</key>
			<data>
			S7yAliUPUh5r9WIVff5bdveaQ9aqgMJnjgSc8Nv9mMk=
			</data>
		</dict>
		<key>public/assets/web-CYMup8nl.js</key>
		<dict>
			<key>hash2</key>
			<data>
			phe95hohIqYfNLWw8CJvtrS7/s6p7XimHLOINfol1tU=
			</data>
		</dict>
		<key>public/assets/web-DCnzWJ0v.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xNqH9hmfWFUURTmkBB3i1jXnhBCxvg1gq98x5e7i7C8=
			</data>
		</dict>
		<key>public/assets/web-GMjGWYYT.js</key>
		<dict>
			<key>hash2</key>
			<data>
			I5OGHGy2gcYbuls4n459wgznKPeoI1o2PzFncCm5HQU=
			</data>
		</dict>
		<key>public/assets/web-legacy-B1gVI8Er.js</key>
		<dict>
			<key>hash2</key>
			<data>
			HPPvo/5yNv65BOA/DNubMllWh7hxDj2j0bVjz1r7Ju4=
			</data>
		</dict>
		<key>public/assets/web-legacy-B37T1-df.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/stjTlhadcpndpqNfYb3URqn/NC2/HV/vcHhQ50960Y=
			</data>
		</dict>
		<key>public/assets/web-legacy-Cl3t3hJ_.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/qL1sFB9UBOYUqRN2zRPrK9SsgpZViarDgEUuBUnZ6I=
			</data>
		</dict>
		<key>public/assets/web-legacy-CzBSJvRu.js</key>
		<dict>
			<key>hash2</key>
			<data>
			muDm6YZo9JchWP0o9a/Ku3+95RW/Q8OBbpMW177bIL0=
			</data>
		</dict>
		<key>public/assets/web-legacy-DMNLHNj6.js</key>
		<dict>
			<key>hash2</key>
			<data>
			iOITXpRjzsCj2T2QvqXrD5tcu7RwRA5pV3XNXBdB3TI=
			</data>
		</dict>
		<key>public/assets/web-legacy-w54beDxd.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ag+MrsDlP+t3WvP4/MhnCl1nji2sLLmz79TqJqmp1F8=
			</data>
		</dict>
		<key>public/cordova.js</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>public/cordova_plugins.js</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>public/favicon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jxtdl5RUjZ+nCLxHIVO+uSbWgItxc8oizem2E6DRx5o=
			</data>
		</dict>
		<key>public/index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			/VWkm9NMLP0OPE+SGSlUyqM1EUXdCkDSXH9khR9nMgs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
