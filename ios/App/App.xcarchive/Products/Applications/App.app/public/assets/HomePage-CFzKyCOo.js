const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-BuIenjK2.js","assets/index-CMzsP7-d.js","assets/index-BxicBINB.css"])))=>i.map(i=>d[i]);
import{d as lt,l as z,m as it,p as kt,q as Z,c as y,o as m,a as t,t as F,w as s,b as e,v as g,x as r,y as $,z as p,n as rt,A as q,B as H,j as i,r as wt,_ as xt,C as ut,D as $t,E as Et,F as At,G as Bt,H as Ct,s as J,J as Tt,K as Dt,L as Lt,M as at,I as zt,N as Ot,O as Nt,P as Rt,Q as nt,h as k,R as G,S as Ht,i as b,T as U,U as W,V as X,W as Kt}from"./index-CMzsP7-d.js";import{d as Vt,e as Gt,f as Ut}from"./index-Bc1Yyv3I.js";import{_ as Wt}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ft={id:"chartEl",class:"dashboard"},Qt={class:"speed-container__unit"},Yt={class:"speed-container__speed"},ot=0,jt=72,qt=20,Jt=lt({__name:"DashboardComponent",props:{speed:{type:Number,default:0},gearPosition:{type:Number,default:0},isAssistance:{type:Boolean,default:!1},isKmUnit:{type:Boolean,default:!1},throttleStatus:{type:Number,default:1}},setup(h){const M=h;let a=0;const O=z("KM/h"),E=()=>new Promise((_,l)=>{var n;try{const d=document.getElementById("chartEl"),w=document.getElementById("canvasEl"),f=document.getElementById("svgEl"),S=document.body.clientHeight,v=document.body.clientWidth,I=Math.min(S,v)-qt,B=q("ipad")?Math.round(I*.6):Math.round(I*.7),V=q("ipad")?Math.round(I*.7):Math.round(I*.8);a=I,d&&(d.style.width="".concat(a,"px"),d.style.height="".concat(V,"px")),w&&(w.width=a,w.height=B);let u=a*.45,C=a/2-u*Math.cos(Math.PI/9),P=a/2+u*Math.sin(Math.PI/9),T=a/2+u*Math.cos(Math.PI/9),N=P,D="M ".concat(C," ").concat(P," A").concat(u," ").concat(u," 0 1 1 ").concat(T," ").concat(N);if(!f)return;const x=(n=f.firstChild)==null?void 0:n.nextSibling;if(!x)return;x.setAttribute("d",D),u=a*.4;const L=x.nextSibling;L.setAttribute("cx",String(a/2)),L.setAttribute("cy",String(a/2)),L.setAttribute("r",String(u)),C=a/2-u*Math.cos(Math.PI/9),P=a/2+u*Math.sin(Math.PI/9),T=a/2+u*Math.cos(Math.PI/9),N=P,D="M ".concat(C," ").concat(P," A").concat(u," ").concat(u," 0 1 0 ").concat(T," ").concat(N," L").concat(a/2," ").concat(a/2),L.nextSibling.setAttribute("d",D),_(!0)}catch(d){l(d)}}),A=_=>{const l=document.getElementById("canvasEl");if(!l)return;const n=l.getContext("2d"),d=a*.4,w=(220*(_-ot)/(jt-ot)-200)*Math.PI/180;l.width=l.width,n==null||n.beginPath();const f=n==null?void 0:n.createRadialGradient(a/2,a/2,0,a/2,a/2,d);f.addColorStop(0,"transparent"),f.addColorStop(.7,"#000"),f.addColorStop(.85,"green"),f.addColorStop(.87,"#030"),f.addColorStop(1,"#020"),n.fillStyle=f,n.arc(a/2,a/2,d,-10*Math.PI/9,w),n.lineTo(a/2,a/2),n.closePath(),n.fill();for(let S=1;S<=5;S++){const v=Math.PI*(220*S/6-20)/180,I=a/2+d*Math.cos(v),B=a/2-d*Math.sin(v);n.beginPath(),n.strokeWidth=3,n.strokeStyle="#000",n.moveTo(a/2,a/2),n.lineTo(I,B),n.stroke()}},K=()=>{E(),A(M.speed)};return it(async()=>{await E(),A(M.speed),window.addEventListener("resize",K)}),kt(()=>{window.removeEventListener("resize",K)}),Z(()=>M.speed,async _=>{A(typeof _=="number"?_:0)}),Z(()=>M.isKmUnit,async _=>{O.value=_?"KM/h":"Mil/h"}),(_,l)=>(m(),y(t(F),{class:"bg-black"},{default:s(()=>[e(t($),null,{default:s(()=>[e(t(g),{class:"ion-no-padding"},{default:s(()=>[r("div",Ft,[l[2]||(l[2]=r("svg",{id:"svgEl"},[r("defs",null,[r("radialGradient",{id:"grad1"},[r("stop",{offset:"0%",style:{"stop-color":"#000"}}),r("stop",{offset:"70%",style:{"stop-color":"#000"}}),r("stop",{offset:"85%",style:{"stop-color":"maroon"}}),r("stop",{offset:"100%",style:{"stop-color":"#000"}})])]),r("path",{class:"path-out",d:"M 0 0"}),r("circle",{cx:"0",cy:"0",r:"0"}),r("path",{class:"path-inner",d:"M 0 0"})],-1)),l[3]||(l[3]=r("canvas",{id:"canvasEl"},null,-1)),e(t(F),{class:"speed-container"},{default:s(()=>[e(t($),null,{default:s(()=>[e(t(g),{size:"12"},{default:s(()=>[r("div",Qt,p(O.value),1),r("div",Yt,p(h.speed),1)]),_:1})]),_:1})]),_:1}),e(t(F),{class:rt(["info-container",{"is-ipad":t(q)("ipad")}])},{default:s(()=>[e(t($),null,{default:s(()=>[h.throttleStatus===2?(m(),y(t(g),{key:0,size:"12"},{default:s(()=>l[0]||(l[0]=[i(" Throttle ")])),_:1})):H("",!0),h.isAssistance&&h.throttleStatus!==2?(m(),y(t(g),{key:1,size:"12"},{default:s(()=>l[1]||(l[1]=[i(" Assist ")])),_:1})):H("",!0)]),_:1}),e(t($),null,{default:s(()=>[e(t(g),{size:"12"},{default:s(()=>[i(p(h.gearPosition),1)]),_:1})]),_:1})]),_:1},8,["class"])])]),_:1})]),_:1})]),_:1}))}}),Xt=wt("App",{web:()=>xt(()=>import("./web-BuIenjK2.js"),__vite__mapDeps([0,1,2])).then(h=>new h.AppWeb)});function Zt(){const{presentToast:h}=Et(),{exitApp:M}=ut(),a=z(!1);return{exitListener:()=>{$t(-1,async()=>{a.value?(await M(),await Xt.exitApp()):(a.value=!0,await h("Press again to exit the application"),setTimeout(()=>{a.value=!1},2e3))})}}}const te={class:"home-page__content"},ee={style:{display:"none"}},se=lt({__name:"HomePage",setup(h){const M=At(),a=Bt(),O=Ct(),{getDisplayType:E,getDisplayUnit:A}=J(O),{speed:K,getSingleMileage:_,getSingleKM:l,getTotalMileage:n,getTotalKM:d,singleTime:w,assistance:f,lightStatus:S,getGearPosition:v,electricQuantity:I,regenative:B,undervoltage:V,reverse:u,turnRight:C,turnLeft:P,throttle:T,cruise:N,brake:D}=J(a),{connectedDevice:x}=J(M),{initialBle:L,disConnectBle:tt}=Tt(),{changeGearPosition:Q,changeLightStatus:ct}=Dt(),{sendMessage:R,stopSendMessage:dt}=ut(),ft=Ot(),{exitListener:pt}=Zt(),gt=z(null);it(()=>{pt(),Q(v.value),L().then(()=>{setTimeout(async()=>{await R()},1e3)})}),Lt(async()=>{x.value.isPaired&&setTimeout(async()=>{await R()},1e3)});const et=z(!1),ht=c=>{et.value=c},_t=["Cancel",{text:"Okay",handler:()=>{dt(),tt(x.value)}}],vt=()=>{ft.push({name:"bluetooth"})},yt=()=>{if(v.value>=5)return;const c=v.value+1;Q(c),R()},mt=()=>{if(v.value<=0)return;const c=v.value-1;Q(c),R()},bt=()=>{const c=!S.value;ct(c),R()},It=at(()=>E.value==="kilometer"),Mt=at(()=>f.value>0);Z(B,c=>{c===1&&St()});const Y=z(!1),st=z("/assets/icon/battery_0.svg"),St=()=>{Y.value=!0;const c=["/assets/icon/battery_0.svg","/assets/icon/battery_1.svg","/assets/icon/battery_2.svg","/assets/icon/battery_3.svg","/assets/icon/battery_4.svg"];let o=0,j=0;const Pt=setInterval(()=>{console.log(c[o]),st.value=c[o],o++,o===c.length&&(o=0,j++),j===2&&(clearInterval(Pt),Y.value=!1)},500)};return(c,o)=>(m(),y(t(zt),{class:"home-page"},{default:s(()=>[e(t(Nt),null,{default:s(()=>[e(t(Rt),null,{default:s(()=>[e(t(nt),{slot:"start"},{default:s(()=>[e(t(k),{class:rt([{"home-page__battery--undervoltage":t(V)},"home-page__battery"]),src:Y.value?st.value:"/assets/icon/battery_".concat(t(I),".svg"),style:{width:"3.5rem",height:"3rem","margin-left":"0.5rem"}},null,8,["class","src"]),t(D)?(m(),y(t(k),{key:0,src:"/assets/icon/brake-outline.svg",class:"ion-margin-start"})):H("",!0),t(P)?(m(),y(t(k),{key:1,icon:t(Vt),class:"ion-margin-start"},null,8,["icon"])):H("",!0),t(C)?(m(),y(t(k),{key:2,icon:t(Gt),class:"ion-margin-start"},null,8,["icon"])):H("",!0)]),_:1}),e(t(nt),{slot:"end"},{default:s(()=>[e(t(G),{class:"home-page__bluetooth-button",size:"large",onClick:vt},{default:s(()=>[e(t(k),{color:t(x).isPaired?"primary":"light",icon:t(Ut),class:"bluetooth ion-padding-start"},null,8,["color","icon"])]),_:1})]),_:1})]),_:1})]),_:1}),e(t(Ht),{"force-overscroll":!0,fullscreen:!0,scrollY:!1},{default:s(()=>[r("div",te,[e(Jt,{class:"home-page__dashboard",ref_key:"dashboard",ref:gt,"gear-position":t(v),"is-assistance":Mt.value,"is-km-unit":It.value,speed:t(K),"throttle-status":t(T)},null,8,["gear-position","is-assistance","is-km-unit","speed","throttle-status"]),e(t(F),{class:"home-page__display"},{default:s(()=>[e(t($),null,{default:s(()=>[e(t(g),null,{default:s(()=>[e(t(b),null,{default:s(()=>o[1]||(o[1]=[i("DST")])),_:1}),e(t(U),{lines:"none"},{default:s(()=>[t(E)==="kilometer"?(m(),y(t(b),{key:0,class:"value"},{default:s(()=>[i(p(t(l)),1)]),_:1})):(m(),y(t(b),{key:1,class:"value"},{default:s(()=>[i(p(t(_)),1)]),_:1})),e(t(W),{slot:"end"},{default:s(()=>[i(p(t(A)),1)]),_:1})]),_:1})]),_:1}),e(t(g),null,{default:s(()=>[e(t(b),null,{default:s(()=>o[2]||(o[2]=[i("TM")])),_:1}),e(t(U),{lines:"none"},{default:s(()=>[e(t(b),{class:"value"},{default:s(()=>[i(p(t(w)),1)]),_:1}),e(t(W),{slot:"end"},{default:s(()=>o[3]||(o[3]=[i("H:M")])),_:1})]),_:1})]),_:1})]),_:1}),e(t($),null,{default:s(()=>[e(t(g),null,{default:s(()=>[e(t(b),null,{default:s(()=>o[4]||(o[4]=[i("ODO")])),_:1}),e(t(U),{lines:"none"},{default:s(()=>[t(E)==="kilometer"?(m(),y(t(b),{key:0,class:"value"},{default:s(()=>[i(p(t(d)),1)]),_:1})):(m(),y(t(b),{key:1,class:"value"},{default:s(()=>[i(p(t(n)),1)]),_:1})),e(t(W),{slot:"end"},{default:s(()=>[i(p(t(A)),1)]),_:1})]),_:1})]),_:1}),e(t(g),null,{default:s(()=>[e(t(b),null,{default:s(()=>o[5]||(o[5]=[i("CADENCE")])),_:1}),e(t(U),{lines:"none"},{default:s(()=>[e(t(b),{class:"value"},{default:s(()=>[i(p(t(f)),1)]),_:1}),e(t(W),{slot:"end"},{default:s(()=>o[6]||(o[6]=[i("RPM")])),_:1})]),_:1})]),_:1})]),_:1}),e(t($),null,{default:s(()=>[e(t(g),{class:"text-align-center"},{default:s(()=>[e(t(G),{class:"icon-only-button",shape:"round",onClick:yt},{default:s(()=>[e(t(k),{slot:"icon-only",src:"/assets/icon/caret-up.svg"}),e(t(X))]),_:1})]),_:1}),e(t(g),{class:"text-align-center"},{default:s(()=>[e(t(G),{class:"icon-only-button",shape:"round",onClick:mt},{default:s(()=>[e(t(k),{slot:"icon-only",src:"/assets/icon/caret-down.svg"}),e(t(X))]),_:1})]),_:1}),e(t(g),{class:"text-align-center"},{default:s(()=>[e(t(G),{color:t(S)?"primary":"light",class:"icon-only-button",shape:"round",onClick:bt},{default:s(()=>[e(t(k),{slot:"icon-only",src:"/assets/icon/light-white.svg"}),e(t(X))]),_:1},8,["color"])]),_:1})]),_:1}),r("div",ee,p("反冲电: ".concat(t(B),",欠压:").concat(t(V),",倒档: ").concat(t(u),",右转: ").concat(t(C),",左转: ").concat(t(P),",转把状态: ").concat(t(T),",巡航状态: ").concat(t(N),",刹车状态: ").concat(t(D))),1)]),_:1})]),e(t(Kt),{buttons:_t,"is-open":et.value,header:"Alert","sub-header":"Do you want to disconnect the Bluetooth!",onDidDismiss:o[0]||(o[0]=j=>ht(!1))},null,8,["is-open"])]),_:1})]),_:1}))}}),ie=Wt(se,[["__scopeId","data-v-67677f0b"]]);export{ie as default};
