System.register(["./index-legacy-CZnTL5oV.js","./index-legacy-C0iBWw5M.js"],(function(e,l){"use strict";var n,t,a,o,u,i,s,c,f,r,d,_,g,z,p,m,y,h,S,b,x;return{setters:[e=>{n=e.d,t=e.u,a=e.s,o=e.c,u=e.w,i=e.a,s=e.I,c=e.b,f=e.P,r=e.Z,d=e.j,_=e.O,g=e.t,z=e.y,p=e.v,m=e.h,y=e.U,h=e.S,S=e.o},e=>{b=e.b,x=e.w}],execute:function(){var l=document.createElement("style");l.textContent=".info-page .info-page__bicycle{display:flex;align-items:center;justify-content:center}.info-page .info-page__bicycle ion-icon{font-size:15rem}.info-page ion-icon{font-size:2rem}\n/*$vite$:1*/",document.head.appendChild(l),e("default",n({__name:"InfoPage",setup(e){const l=t(),{current:n,throttle:j,motorPhase:v,motorHall:P,torqueSensor:C,speedSensor:I}=a(l);return(e,l)=>(S(),o(i(s),{class:"info-page"},{default:u((()=>[c(i(_),null,{default:u((()=>[c(i(f),null,{default:u((()=>[c(i(r),null,{default:u((()=>l[0]||(l[0]=[d("Error Information")]))),_:1})])),_:1})])),_:1}),c(i(h),{fullscreen:!0,class:"ion-padding-horizontal"},{default:u((()=>[c(i(g),null,{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{class:"info-page__bicycle"},{default:u((()=>[c(i(m),{icon:i(b)},null,8,["icon"])])),_:1})])),_:1}),c(i(z),null,{default:u((()=>[c(i(p),{size:"6"},{default:u((()=>[c(i(z),{"align-items-center":""},{default:u((()=>[c(i(p),{size:"3"},{default:u((()=>[c(i(m),{color:i(n)?"primary":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[1]||(l[1]=[d("Current")]))),_:1})])),_:1})])),_:1})])),_:1}),c(i(p),{size:"6"},{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{class:"ion-align-self-center",size:"3"},{default:u((()=>[c(i(m),{color:i(j)?"danger":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[2]||(l[2]=[d("Throttle")]))),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),c(i(z),null,{default:u((()=>[c(i(p),{size:"6"},{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{size:"3"},{default:u((()=>[c(i(m),{color:i(v)?"danger":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[3]||(l[3]=[d("MotorPhase")]))),_:1})])),_:1})])),_:1})])),_:1}),c(i(p),{size:"6"},{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{size:"3"},{default:u((()=>[c(i(m),{color:i(P)?"danger":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[4]||(l[4]=[d("MotorHall")]))),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),c(i(z),null,{default:u((()=>[c(i(p),{size:"6"},{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{size:"3"},{default:u((()=>[c(i(m),{color:i(C)?"danger":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[5]||(l[5]=[d("TorqueSensor")]))),_:1})])),_:1})])),_:1})])),_:1}),c(i(p),{size:"6"},{default:u((()=>[c(i(z),null,{default:u((()=>[c(i(p),{size:"3"},{default:u((()=>[c(i(m),{color:i(I)?"danger":"",icon:i(x)},null,8,["color","icon"])])),_:1}),c(i(p),{class:"ion-align-self-center",size:"9"},{default:u((()=>[c(i(y),null,{default:u((()=>l[6]||(l[6]=[d("SpeedSensor")]))),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}))}}))}}}));
