System.register(["./index-legacy-CZnTL5oV.js","./index-legacy-C0iBWw5M.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js"],(function(e,l){"use strict";var a,u,n,t,d,o,m,i,r,p,c,s,f,v,V,_,b,g,h,y,C,S,P,U,x,k,M,$,L,T,I,j,D,K,A,B,O,R,w,z,H,N,Z,q,E;return{setters:[e=>{a=e.d,u=e.l,n=e.M,t=e.c,d=e.a,o=e.T,m=e.w,i=e.b,r=e.X,p=e.h,c=e.Y,s=e.O,f=e.P,v=e.Z,V=e.j,_=e.z,b=e.Q,g=e.R,h=e.$,y=e.a0,C=e.a1,S=e.a2,P=e.a3,U=e.o,x=e.a4,k=e.H,M=e.u,$=e.s,L=e.C,T=e.K,I=e.a5,j=e.a6,D=e.I,K=e.a7,A=e.a8,B=e.i,O=e.a9,R=e.aa,w=e.ab,z=e.t,H=e.y,N=e.v,Z=e.S},e=>{q=e.g},e=>{E=e._}],execute:function(){var l=document.createElement("style");l.textContent="ion-modal[data-v-9997b305]{--height: auto;align-items:end}ion-title[data-v-9997b305]{text-align:center}ion-picker[data-v-9997b305]{margin-bottom:var(--ion-safe-area-bottom)}.sc-ion-input-ios-s>[slot=end][data-v-9997b305]:first-of-type{margin-inline-start:4px;fill:#828384}.sc-ion-input-md-s>[slot=end][data-v-9997b305]:first-of-type{margin-inline-start:4px;fill:#828384}.page-setting ion-list-header{--color: var(--ion-color-primary)}.page-setting ion-button:not(.button-round){--border-radius: 8px;--padding-start: 12px;--padding-end: 12px}.page-setting ion-input{text-align:right}.page-setting .page-setting__restore{display:flex;justify-content:center}\n/*$vite$:1*/",document.head.appendChild(l);const F=E(a({__name:"ModalSelect",props:{modelValue:{},list:{},label:{default:"Select"},name:{default:""},placeholder:{default:"Please select"},modalName:{default:"modal"}},emits:["update:modelValue"],setup(e,{emit:l}){const a=e,k=l,M=u(null),$=u(a.modelValue),L=u(null),T=n((()=>`open-modal-${a.modalName}-${Math.random().toString(36).slice(2,11)}`)),I=n((()=>{const e=a.list.find((e=>e.value===a.modelValue));return(null==e?void 0:e.name)||""})),j=e=>{$.value=e.detail.value},D=e=>{"confirm"===e.detail.role&&k("update:modelValue",e.detail.data)},K=()=>{var e;null===(e=M.value)||void 0===e||e.$el.dismiss(null,"cancel")},A=()=>{var e,l;const a=null===(e=L.value)||void 0===e?void 0:e.$el.activeItem;a&&($.value=a.value),null===(l=M.value)||void 0===l||l.$el.dismiss($.value,"confirm")};return(e,l)=>(U(),t(d(o),null,{default:m((()=>[i(d(r),{id:T.value,readonly:"",value:I.value,label:e.label,name:e.name,placeholder:e.placeholder},{default:m((()=>[i(d(p),{slot:"end",icon:d(q)},null,8,["icon"])])),_:1},8,["id","value","label","name","placeholder"]),i(d(c),{ref_key:"modal",ref:M,trigger:T.value,onDidDismiss:D},{default:m((()=>[i(d(s),null,{default:m((()=>[i(d(f),null,{default:m((()=>[i(d(v),null,{default:m((()=>[V(_(e.label),1)])),_:1}),i(d(b),{slot:"start"},{default:m((()=>[i(d(g),{onClick:K},{default:m((()=>l[0]||(l[0]=[V("Cancel")]))),_:1})])),_:1}),i(d(b),{slot:"end"},{default:m((()=>[i(d(g),{color:"primary",onClick:A},{default:m((()=>l[1]||(l[1]=[V("OK")]))),_:1})])),_:1})])),_:1})])),_:1}),i(d(h),null,{default:m((()=>[i(d(y),{ref_key:"pickerColumn",ref:L,value:$.value,onIonChange:j},{default:m((()=>[(U(!0),C(P,null,S(e.list,(e=>(U(),t(d(x),{key:e.value,value:e.value},{default:m((()=>[V(_(e.name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1})])),_:1},8,["trigger"])])),_:1}))}}),[["__scopeId","data-v-9997b305"]]);e("default",a({__name:"SettingPage",setup(e){const l=k(),a=M(),{maxSpeed:u,dimension:n,dimensionList:p,p1:c,p2:_,p3:h,p4:y,p5:C,c1:S,c2:P,c3:x,c4:q,c5:E,c7:Q,c12:X,c13:Y,c14:G,levelList:J,handlebarMaxSpeed:W,percent:ee,candidateParam:le,displayType:ae}=$(l),{stopSendMessage:ue}=L(),{updateSetting:ne}=T();I((()=>{ue()})),j((()=>{ne()}));const te=()=>{l.$reset(),a.$reset()};return(e,l)=>(U(),t(d(D),{class:"page-setting"},{default:m((()=>[i(d(s),null,{default:m((()=>[i(d(f),null,{default:m((()=>[i(d(v),null,{default:m((()=>l[20]||(l[20]=[V("Setting")]))),_:1}),i(d(b),{slot:"end"},{default:m((()=>[i(d(g),{fill:"solid",onClick:d(ne)},{default:m((()=>l[21]||(l[21]=[V("Save")]))),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),i(d(Z),{fullscreen:!0},{default:m((()=>[i(d(K),null,{default:m((()=>[i(d(A),null,{default:m((()=>[i(d(B),null,{default:m((()=>l[22]||(l[22]=[V("Parameters")]))),_:1})])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(u),"onUpdate:modelValue":l[0]||(l[0]=e=>O(u)?u.value=e:null),max:72,maxlength:2,min:10,inputmode:"numeric",label:"MaxSpeed",name:"maxSpeed",placeholder:"km/h",type:"number"},null,8,["modelValue"])])),_:1}),i(F,{modelValue:d(n),"onUpdate:modelValue":l[1]||(l[1]=e=>O(n)?n.value=e:null),list:d(p),"modal-name":"dimension",label:"Bike Dimension",placeholder:"Select Dimension"},null,8,["modelValue","list"]),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(c),"onUpdate:modelValue":l[2]||(l[2]=e=>O(c)?c.value=e:null),inputmode:"numeric",label:"MotorSetting (P1)",name:"p1",placeholder:"P1",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(_),"onUpdate:modelValue":l[3]||(l[3]=e=>O(_)?_.value=e:null),inputmode:"numeric",label:"SpeedSensor (P2)",name:"p2",placeholder:"P2",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(h),"onUpdate:modelValue":l[4]||(l[4]=e=>O(h)?h.value=e:null),inputmode:"numeric",label:"Torque (P3)",name:"p3",placeholder:"P3",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(y),"onUpdate:modelValue":l[5]||(l[5]=e=>O(y)?y.value=e:null),inputmode:"numeric",label:"ZeroStart (P4)",name:"p4",placeholder:"P4",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(C),"onUpdate:modelValue":l[6]||(l[6]=e=>O(C)?C.value=e:null),inputmode:"numeric",label:"Battery (P5)",name:"p5",placeholder:"P5",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(S),"onUpdate:modelValue":l[7]||(l[7]=e=>O(S)?S.value=e:null),inputmode:"numeric",label:"PAS (C1)",name:"c1",placeholder:"C1",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(P),"onUpdate:modelValue":l[8]||(l[8]=e=>O(P)?P.value=e:null),inputmode:"numeric",label:"MotorPhase (C2)",name:"c2",placeholder:"C2",type:"number"},null,8,["modelValue"])])),_:1}),i(F,{modelValue:d(x),"onUpdate:modelValue":l[9]||(l[9]=e=>O(x)?x.value=e:null),list:d(J),"modal-name":"initLevel",label:"InitLevel (C3)",placeholder:"Select InitLevel"},null,8,["modelValue","list"]),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(q),"onUpdate:modelValue":l[10]||(l[10]=e=>O(q)?q.value=e:null),inputmode:"numeric",label:"Throttle (C4)",name:"c4",placeholder:"C4",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(E),"onUpdate:modelValue":l[11]||(l[11]=e=>O(E)?E.value=e:null),inputmode:"numeric",label:"Current (C5)",name:"c5",placeholder:"C5",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(Q),"onUpdate:modelValue":l[12]||(l[12]=e=>O(Q)?Q.value=e:null),inputmode:"numeric",label:"Cruise (C7)",name:"c7",placeholder:"C7",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(X),"onUpdate:modelValue":l[13]||(l[13]=e=>O(X)?X.value=e:null),inputmode:"numeric",label:"UVLO (C12)",name:"c12",placeholder:"C12",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(Y),"onUpdate:modelValue":l[14]||(l[14]=e=>O(Y)?Y.value=e:null),inputmode:"numeric",label:"Regenerative (C13)",name:"c13",placeholder:"C13",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(G),"onUpdate:modelValue":l[15]||(l[15]=e=>O(G)?G.value=e:null),inputmode:"numeric",label:"PASPower (C14)",name:"c14",placeholder:"C14",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(W),"onUpdate:modelValue":l[16]||(l[16]=e=>O(W)?W.value=e:null),inputmode:"numeric",label:"Handlebar Maximum Speed",name:"handleBarSpeed",placeholder:"data.displayType === 'kilometer' ? 'km/h' : 'mile/h'",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(ee),"onUpdate:modelValue":l[17]||(l[17]=e=>O(ee)?ee.value=e:null),inputmode:"numeric",label:"FirstLevelPercent (Throttle)",name:"percent",placeholder:"percentage",type:"number"},null,8,["modelValue"])])),_:1}),i(d(o),null,{default:m((()=>[i(d(r),{modelValue:d(le),"onUpdate:modelValue":l[18]||(l[18]=e=>O(le)?le.value=e:null),inputmode:"numeric",label:"Candidate",name:"candidate",placeholder:"Signal of PAS",type:"number"},null,8,["modelValue"])])),_:1}),i(d(R),{modelValue:d(ae),"onUpdate:modelValue":l[19]||(l[19]=e=>O(ae)?ae.value=e:null),name:"displayType"},{default:m((()=>[i(d(A),null,{default:m((()=>l[23]||(l[23]=[V("Kilometer／Mile")]))),_:1}),i(d(o),null,{default:m((()=>[i(d(w),{value:"kilometer"},{default:m((()=>l[24]||(l[24]=[V("Kilometer")]))),_:1})])),_:1}),i(d(o),null,{default:m((()=>[i(d(w),{value:"mile"},{default:m((()=>l[25]||(l[25]=[V("Mile")]))),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1}),i(d(z),null,{default:m((()=>[i(d(H),null,{default:m((()=>[i(d(N),{class:"page-setting__restore"},{default:m((()=>[i(d(g),{fill:"outline",shape:"round",size:"small",onClick:te},{default:m((()=>l[26]||(l[26]=[V("Restore Settings ")]))),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}))}}))}}}));
