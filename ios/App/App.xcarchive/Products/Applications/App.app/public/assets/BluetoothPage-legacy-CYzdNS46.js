System.register(["./index-legacy-CZnTL5oV.js","./index-legacy-C0iBWw5M.js"],(function(e,a){"use strict";var t,l,n,i,s,o,c,u,d,r,f,v,_,y,m,h,w,k,g,p,B,C,b,D,I,x,P,S,j,A,N,T,z,E,O,$,q,F,H,J,M,Q,R,U,Z,G,K,L,V,W;return{setters:[e=>{t=e.d,l=e.F,n=e.s,i=e.J,s=e.C,o=e.m,c=e.af,u=e.p,d=e.l,r=e.ag,f=e.c,v=e.w,_=e.a,y=e.I,m=e.b,h=e.P,w=e.B,k=e.Q,g=e.ah,p=e.Z,B=e.j,C=e.R,b=e.h,D=e.n,I=e.O,x=e.ai,P=e.aj,S=e.ak,j=e.z,A=e.al,N=e.am,T=e.a7,z=e.an,E=e.a1,O=e.ao,$=e.i,q=e.a2,F=e.a3,H=e.T,J=e.A,M=e.ap,Q=e.aq,R=e.S,U=e.ar,Z=e.as,G=e.N,K=e.o,L=e.E},e=>{V=e.r,W=e.f}],execute:function(){var a=document.createElement("style");a.textContent="@keyframes refresh{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.icon-refresh--on{animation-name:refresh;animation-duration:1s;animation-iteration-count:1}\n/*$vite$:1*/",document.head.appendChild(a),e("default",t({__name:"BluetoothPage",setup(e){const a=l(),{connectedDevice:t,availableDevices:X}=n(a),{scan:Y,scanning:ee,connectBle:ae,disConnectBle:te}=i(),{stopSendMessage:le}=s(),ne=G(),{on:ie}=c();let se;o((()=>{t.value.isPaired||ce()})),ie((async()=>{await ce()})),u((()=>{clearInterval(se)}));const oe=L(),ce=async()=>{await Y(),se=setInterval((async()=>{await Y()}),15e3)},ue=["Cancel",{text:"Okay",handler:async()=>{var e;null===(e=de.value)||void 0===e||e.dismiss(),await ye();try{var a;await le(),await te(t.value),null===(a=_e.value)||void 0===a||a.dismiss()}catch(n){var l;null===(l=_e.value)||void 0===l||l.dismiss()}setTimeout((async()=>{await ce()}),1e3)}}],de=d(),re=async()=>{de.value=await U.create({header:"Alert",subHeader:"Do you want to disconnect the Bluetooth!",buttons:ue}),await de.value.present()};let fe={};const ve=async()=>{fe=await Z.create({message:"Connecting to Bluetooth device"}),await fe.present()},_e=r(),ye=async()=>{_e.value=await Z.create({message:"Disconnecting Bluetooth device"}),await _e.value.present()};return(e,a)=>(K(),f(_(y),{ref:"page"},{default:v((()=>[m(_(I),null,{default:v((()=>[m(_(h),null,{default:v((()=>[m(_(k),{slot:"start"},{default:v((()=>[m(_(g))])),_:1}),m(_(p),null,{default:v((()=>a[0]||(a[0]=[B("Bluetooth")]))),_:1}),_(t).isPaired?w("",!0):(K(),f(_(k),{key:0,slot:"end"},{default:v((()=>[m(_(C),{onClick:_(Y)},{default:v((()=>[m(_(b),{slot:"icon-only",class:D({"icon-refresh--on":_(ee)}),icon:_(V)},null,8,["class","icon"])])),_:1},8,["onClick"])])),_:1}))])),_:1})])),_:1}),m(_(R),{fullscreen:!0},{default:v((()=>[_(t).isPaired?(K(),f(_(x),{key:0},{default:v((()=>[m(_(P),null,{default:v((()=>[m(_(S),{class:"device-modal__title"},{default:v((()=>[B(j(_(t).name),1)])),_:1}),m(_(A),null,{default:v((()=>a[1]||(a[1]=[B("Device Information")]))),_:1})])),_:1}),m(_(N),null,{default:v((()=>[B(" Device ID:"+j(_(t).deviceId),1)])),_:1}),m(_(C),{fill:"clear",size:"small",onClick:re},{default:v((()=>a[2]||(a[2]=[B("Disconnect ")]))),_:1})])),_:1})):(K(),f(_(T),{key:1},{default:v((()=>[m(_(z),null,{default:v((()=>[m(_(O),null,{default:v((()=>[m(_($),null,{default:v((()=>a[3]||(a[3]=[B("Available Devices")]))),_:1})])),_:1}),(K(!0),E(F,null,q(_(X),((e,a)=>(K(),f(_(H),{key:a,onClick:a=>(async e=>{clearInterval(se),await ve();try{await ae(e),await fe.dismiss(),ne.back()}catch(a){console.log("connect error",a),await fe.dismiss(),await oe.presentToast("Unmatched Bluetooth device"),await ce()}})(e)},{default:v((()=>[m(_(b),{slot:"start",icon:_(W)},null,8,["icon"]),m(_($),null,{default:v((()=>[B(j(e.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128)),_(ee)?(K(),f(_(H),{key:0,lines:"none"},{default:v((()=>[_(J)("ios")?(K(),f(_(M),{key:0},{default:v((()=>a[4]||(a[4]=[B(" Searching for available devices... ")]))),_:1})):(K(),f(_(M),{key:1,slot:"start"},{default:v((()=>a[5]||(a[5]=[B(" Searching for available devices... ")]))),_:1})),m(_(Q),{slot:"end"})])),_:1})):w("",!0),0!==_(X).length||_(ee)?w("",!0):(K(),f(_(H),{key:1,lines:"none"},{default:v((()=>[_(J)("ios")?(K(),f(_(M),{key:0},{default:v((()=>a[6]||(a[6]=[B(" No available Bluetooth devices found ")]))),_:1})):(K(),f(_(M),{key:1,slot:"start"},{default:v((()=>a[7]||(a[7]=[B(" No available Bluetooth devices found ")]))),_:1}))])),_:1}))])),_:1})])),_:1}))])),_:1})])),_:1},512))}}))}}}));
