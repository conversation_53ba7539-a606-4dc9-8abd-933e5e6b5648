System.register(["./index-legacy-CZnTL5oV.js","./index-legacy-C0iBWw5M.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js"],(function(e,t){"use strict";var a,n,o,s,l,i,r,d,c,u,h,f,g,p,m,_,v,b,y,x,M,w,k,$,S,P,C,A,E,I,z,T,D,j,B,L,K,G,N,H,O,R,U,W,Q,q,F,J,V;return{setters:[e=>{a=e.d,n=e.l,o=e.m,s=e.p,l=e.q,i=e.c,r=e.o,d=e.a,c=e.t,u=e.w,h=e.b,f=e.v,g=e.x,p=e.y,m=e.z,_=e.n,v=e.A,b=e.B,y=e.j,x=e.r,M=e._,w=e.C,k=e.D,$=e.E,S=e.F,P=e.G,C=e.H,A=e.s,E=e.J,I=e.K,z=e.L,T=e.M,D=e.I,j=e.N,B=e.O,L=e.P,K=e.Q,G=e.h,N=e.R,H=e.S,O=e.i,R=e.T,U=e.U,W=e.V,Q=e.W},e=>{q=e.d,F=e.e,J=e.f},e=>{V=e._}],execute:function(){var Y=document.createElement("style");Y.textContent=".dashboard{margin:auto;position:relative;background:#000}.dashboard svg{width:100%;height:100%}.dashboard svg .path-out{stroke-width:3;stroke:gray}.dashboard svg .path-inner{fill:#000}.dashboard svg circle{fill:url(#grad1)}.dashboard canvas{position:absolute;left:0}.dashboard .speed-container{display:flex;flex-direction:column;justify-content:center;align-items:center;position:absolute;bottom:0;width:100%;height:100%;color:#fff}.dashboard .speed-container ion-col{position:relative;display:flex;flex-direction:column;justify-content:center}.dashboard .speed-container ion-col .speed-container__unit{font-size:1rem;text-align:center}.dashboard .speed-container ion-col .speed-container__speed{font-size:6rem;line-height:5rem;text-align:center}.dashboard .info-container{display:flex;flex-direction:column;justify-content:flex-start;align-items:center;position:absolute;bottom:0;width:100%;height:6.375rem;color:#fff;font-size:1.5rem}.dashboard .info-container.is-ipad{height:auto;bottom:-10px}.dashboard .info-container ion-row:first-child{height:2rem;font-size:1rem}.dashboard .info-container ion-row:last-child{font-size:3rem;line-height:3rem}.dashboard .info-container ion-row:last-child ion-col{padding:0}@keyframes twinkle-67677f0b{0%{opacity:.8}to{opacity:0}}.home-page .home-page__battery[data-v-67677f0b]{width:2.5rem}.home-page .home-page__battery.home-page__battery--undervoltage[data-v-67677f0b]{animation:twinkle-67677f0b .5s infinite alternate}.home-page .home-page__bluetooth-button ion-icon[data-v-67677f0b]{font-size:2.2rem}.home-page .text-align-center[data-v-67677f0b]{display:flex;justify-content:center;align-content:center}.home-page .text-align-center .icon-only-button[data-v-67677f0b]{height:4rem;width:4rem;--border-radius: 50%;--padding-start: 0;--padding-end: 0}.home-page ion-icon[data-v-67677f0b]{font-size:2.5rem}.home-page ion-toolbar[data-v-67677f0b]{--background: #000;--color: white}.home-page .home-page__content[data-v-67677f0b]{display:flex;flex-direction:column;height:100%}.home-page .home-page__content ion-grid[data-v-67677f0b]{width:100%}.home-page .home-page__content .home-page__dashboard[data-v-67677f0b]{flex:0}.home-page .home-page__content .home-page__display[data-v-67677f0b]{display:flex;flex-direction:column;justify-content:space-around;flex:1}.home-page .home-page__content .home-page__display .value[data-v-67677f0b]{font-weight:700}.home-page ion-content[data-v-67677f0b]{--background: #000;--color: #fff}.home-page ion-item[data-v-67677f0b]{--background: #000;--color: #fff}\n/*$vite$:1*/",document.head.appendChild(Y);const X={id:"chartEl",class:"dashboard"},Z={class:"speed-container__unit"},ee={class:"speed-container__speed"},te=a({__name:"DashboardComponent",props:{speed:{type:Number,default:0},gearPosition:{type:Number,default:0},isAssistance:{type:Boolean,default:!1},isKmUnit:{type:Boolean,default:!1},throttleStatus:{type:Number,default:1}},setup(e){const t=e;let a=0;const x=n("KM/h"),M=()=>new Promise(((e,t)=>{try{var n;const t=document.getElementById("chartEl"),o=document.getElementById("canvasEl"),s=document.getElementById("svgEl"),l=document.body.clientHeight,i=document.body.clientWidth,r=Math.min(l,i)-20,d=v("ipad")?Math.round(.6*r):Math.round(.7*r),c=v("ipad")?Math.round(.7*r):Math.round(.8*r);a=r,t&&(t.style.width=`${a}px`,t.style.height=`${c}px`),o&&(o.width=a,o.height=d);let u=.45*a,h=a/2-u*Math.cos(Math.PI/9),f=a/2+u*Math.sin(Math.PI/9),g=a/2+u*Math.cos(Math.PI/9),p=f,m=`M ${h} ${f} A${u} ${u} 0 1 1 ${g} ${p}`;if(!s)return;const _=null===(n=s.firstChild)||void 0===n?void 0:n.nextSibling;if(!_)return;_.setAttribute("d",m),u=.4*a;const b=_.nextSibling;b.setAttribute("cx",String(a/2)),b.setAttribute("cy",String(a/2)),b.setAttribute("r",String(u)),h=a/2-u*Math.cos(Math.PI/9),f=a/2+u*Math.sin(Math.PI/9),g=a/2+u*Math.cos(Math.PI/9),p=f,m=`M ${h} ${f} A${u} ${u} 0 1 0 ${g} ${p} L${a/2} ${a/2}`,b.nextSibling.setAttribute("d",m),e(!0)}catch(o){t(o)}})),w=e=>{const t=document.getElementById("canvasEl");if(!t)return;const n=t.getContext("2d"),o=.4*a,s=(220*(e-0)/72-200)*Math.PI/180;t.width=t.width,null==n||n.beginPath();const l=null==n?void 0:n.createRadialGradient(a/2,a/2,0,a/2,a/2,o);l.addColorStop(0,"transparent"),l.addColorStop(.7,"#000"),l.addColorStop(.85,"green"),l.addColorStop(.87,"#030"),l.addColorStop(1,"#020"),n.fillStyle=l,n.arc(a/2,a/2,o,-10*Math.PI/9,s),n.lineTo(a/2,a/2),n.closePath(),n.fill();for(let i=1;i<=5;i++){const e=Math.PI*(220*i/6-20)/180,t=a/2+o*Math.cos(e),s=a/2-o*Math.sin(e);n.beginPath(),n.strokeWidth=3,n.strokeStyle="#000",n.moveTo(a/2,a/2),n.lineTo(t,s),n.stroke()}},k=()=>{M(),w(t.speed)};return o((async()=>{await M(),w(t.speed),window.addEventListener("resize",k)})),s((()=>{window.removeEventListener("resize",k)})),l((()=>t.speed),(async e=>{w("number"==typeof e?e:0)})),l((()=>t.isKmUnit),(async e=>{x.value=e?"KM/h":"Mil/h"})),(t,a)=>(r(),i(d(c),{class:"bg-black"},{default:u((()=>[h(d(p),null,{default:u((()=>[h(d(f),{class:"ion-no-padding"},{default:u((()=>[g("div",X,[a[2]||(a[2]=g("svg",{id:"svgEl"},[g("defs",null,[g("radialGradient",{id:"grad1"},[g("stop",{offset:"0%",style:{"stop-color":"#000"}}),g("stop",{offset:"70%",style:{"stop-color":"#000"}}),g("stop",{offset:"85%",style:{"stop-color":"maroon"}}),g("stop",{offset:"100%",style:{"stop-color":"#000"}})])]),g("path",{class:"path-out",d:"M 0 0"}),g("circle",{cx:"0",cy:"0",r:"0"}),g("path",{class:"path-inner",d:"M 0 0"})],-1)),a[3]||(a[3]=g("canvas",{id:"canvasEl"},null,-1)),h(d(c),{class:"speed-container"},{default:u((()=>[h(d(p),null,{default:u((()=>[h(d(f),{size:"12"},{default:u((()=>[g("div",Z,m(x.value),1),g("div",ee,m(e.speed),1)])),_:1})])),_:1})])),_:1}),h(d(c),{class:_(["info-container",{"is-ipad":d(v)("ipad")}])},{default:u((()=>[h(d(p),null,{default:u((()=>[2===e.throttleStatus?(r(),i(d(f),{key:0,size:"12"},{default:u((()=>a[0]||(a[0]=[y(" Throttle ")]))),_:1})):b("",!0),e.isAssistance&&2!==e.throttleStatus?(r(),i(d(f),{key:1,size:"12"},{default:u((()=>a[1]||(a[1]=[y(" Assist ")]))),_:1})):b("",!0)])),_:1}),h(d(p),null,{default:u((()=>[h(d(f),{size:"12"},{default:u((()=>[y(m(e.gearPosition),1)])),_:1})])),_:1})])),_:1},8,["class"])])])),_:1})])),_:1})])),_:1}))}}),ae=x("App",{web:()=>M((()=>t.import("./web-legacy-B1gVI8Er.js")),void 0).then((e=>new e.AppWeb))}),ne={class:"home-page__content"},oe={style:{display:"none"}};e("default",V(a({__name:"HomePage",setup(e){const t=S(),a=P(),s=C(),{getDisplayType:v,getDisplayUnit:x}=A(s),{speed:M,getSingleMileage:V,getSingleKM:Y,getTotalMileage:X,getTotalKM:Z,singleTime:ee,assistance:se,lightStatus:le,getGearPosition:ie,electricQuantity:re,regenative:de,undervoltage:ce,reverse:ue,turnRight:he,turnLeft:fe,throttle:ge,cruise:pe,brake:me}=A(a),{connectedDevice:_e}=A(t),{initialBle:ve,disConnectBle:be}=E(),{changeGearPosition:ye,changeLightStatus:xe}=I(),{sendMessage:Me,stopSendMessage:we}=w(),ke=j(),{exitListener:$e}=function(){const{presentToast:e}=$(),{exitApp:t}=w(),a=n(!1);return{exitListener:()=>{k(-1,(async()=>{a.value?(await t(),await ae.exitApp()):(a.value=!0,await e("Press again to exit the application"),setTimeout((()=>{a.value=!1}),2e3))}))}}}(),Se=n(null);o((()=>{$e(),ye(ie.value),ve().then((()=>{setTimeout((async()=>{await Me()}),1e3)}))})),z((async()=>{_e.value.isPaired&&setTimeout((async()=>{await Me()}),1e3)}));const Pe=n(!1),Ce=["Cancel",{text:"Okay",handler:()=>{we(),be(_e.value)}}],Ae=()=>{ke.push({name:"bluetooth"})},Ee=()=>{if(ie.value>=5)return;const e=ie.value+1;ye(e),Me()},Ie=()=>{if(ie.value<=0)return;const e=ie.value-1;ye(e),Me()},ze=()=>{const e=!le.value;xe(e),Me()},Te=T((()=>"kilometer"===v.value)),De=T((()=>se.value>0));l(de,(e=>{1===e&&Le()}));const je=n(!1),Be=n("/assets/icon/battery_0.svg"),Le=()=>{je.value=!0;const e=["/assets/icon/battery_0.svg","/assets/icon/battery_1.svg","/assets/icon/battery_2.svg","/assets/icon/battery_3.svg","/assets/icon/battery_4.svg"];let t=0,a=0;const n=setInterval((()=>{console.log(e[t]),Be.value=e[t],t++,t===e.length&&(t=0,a++),2===a&&(clearInterval(n),je.value=!1)}),500)};return(e,t)=>(r(),i(d(D),{class:"home-page"},{default:u((()=>[h(d(B),null,{default:u((()=>[h(d(L),null,{default:u((()=>[h(d(K),{slot:"start"},{default:u((()=>[h(d(G),{class:_([{"home-page__battery--undervoltage":d(ce)},"home-page__battery"]),src:je.value?Be.value:`/assets/icon/battery_${d(re)}.svg`,style:{width:"3.5rem",height:"3rem","margin-left":"0.5rem"}},null,8,["class","src"]),d(me)?(r(),i(d(G),{key:0,src:"/assets/icon/brake-outline.svg",class:"ion-margin-start"})):b("",!0),d(fe)?(r(),i(d(G),{key:1,icon:d(q),class:"ion-margin-start"},null,8,["icon"])):b("",!0),d(he)?(r(),i(d(G),{key:2,icon:d(F),class:"ion-margin-start"},null,8,["icon"])):b("",!0)])),_:1}),h(d(K),{slot:"end"},{default:u((()=>[h(d(N),{class:"home-page__bluetooth-button",size:"large",onClick:Ae},{default:u((()=>[h(d(G),{color:d(_e).isPaired?"primary":"light",icon:d(J),class:"bluetooth ion-padding-start"},null,8,["color","icon"])])),_:1})])),_:1})])),_:1})])),_:1}),h(d(H),{"force-overscroll":!0,fullscreen:!0,scrollY:!1},{default:u((()=>[g("div",ne,[h(te,{class:"home-page__dashboard",ref_key:"dashboard",ref:Se,"gear-position":d(ie),"is-assistance":De.value,"is-km-unit":Te.value,speed:d(M),"throttle-status":d(ge)},null,8,["gear-position","is-assistance","is-km-unit","speed","throttle-status"]),h(d(c),{class:"home-page__display"},{default:u((()=>[h(d(p),null,{default:u((()=>[h(d(f),null,{default:u((()=>[h(d(O),null,{default:u((()=>t[1]||(t[1]=[y("DST")]))),_:1}),h(d(R),{lines:"none"},{default:u((()=>["kilometer"===d(v)?(r(),i(d(O),{key:0,class:"value"},{default:u((()=>[y(m(d(Y)),1)])),_:1})):(r(),i(d(O),{key:1,class:"value"},{default:u((()=>[y(m(d(V)),1)])),_:1})),h(d(U),{slot:"end"},{default:u((()=>[y(m(d(x)),1)])),_:1})])),_:1})])),_:1}),h(d(f),null,{default:u((()=>[h(d(O),null,{default:u((()=>t[2]||(t[2]=[y("TM")]))),_:1}),h(d(R),{lines:"none"},{default:u((()=>[h(d(O),{class:"value"},{default:u((()=>[y(m(d(ee)),1)])),_:1}),h(d(U),{slot:"end"},{default:u((()=>t[3]||(t[3]=[y("H:M")]))),_:1})])),_:1})])),_:1})])),_:1}),h(d(p),null,{default:u((()=>[h(d(f),null,{default:u((()=>[h(d(O),null,{default:u((()=>t[4]||(t[4]=[y("ODO")]))),_:1}),h(d(R),{lines:"none"},{default:u((()=>["kilometer"===d(v)?(r(),i(d(O),{key:0,class:"value"},{default:u((()=>[y(m(d(Z)),1)])),_:1})):(r(),i(d(O),{key:1,class:"value"},{default:u((()=>[y(m(d(X)),1)])),_:1})),h(d(U),{slot:"end"},{default:u((()=>[y(m(d(x)),1)])),_:1})])),_:1})])),_:1}),h(d(f),null,{default:u((()=>[h(d(O),null,{default:u((()=>t[5]||(t[5]=[y("CADENCE")]))),_:1}),h(d(R),{lines:"none"},{default:u((()=>[h(d(O),{class:"value"},{default:u((()=>[y(m(d(se)),1)])),_:1}),h(d(U),{slot:"end"},{default:u((()=>t[6]||(t[6]=[y("RPM")]))),_:1})])),_:1})])),_:1})])),_:1}),h(d(p),null,{default:u((()=>[h(d(f),{class:"text-align-center"},{default:u((()=>[h(d(N),{class:"icon-only-button",shape:"round",onClick:Ee},{default:u((()=>[h(d(G),{slot:"icon-only",src:"/assets/icon/caret-up.svg"}),h(d(W))])),_:1})])),_:1}),h(d(f),{class:"text-align-center"},{default:u((()=>[h(d(N),{class:"icon-only-button",shape:"round",onClick:Ie},{default:u((()=>[h(d(G),{slot:"icon-only",src:"/assets/icon/caret-down.svg"}),h(d(W))])),_:1})])),_:1}),h(d(f),{class:"text-align-center"},{default:u((()=>[h(d(N),{color:d(le)?"primary":"light",class:"icon-only-button",shape:"round",onClick:ze},{default:u((()=>[h(d(G),{slot:"icon-only",src:"/assets/icon/light-white.svg"}),h(d(W))])),_:1},8,["color"])])),_:1})])),_:1}),g("div",oe,m(`反冲电: ${d(de)},欠压:${d(ce)},倒档: ${d(ue)},右转: ${d(he)},左转: ${d(fe)},转把状态: ${d(ge)},巡航状态: ${d(pe)},刹车状态: ${d(me)}`),1)])),_:1})]),h(d(Q),{buttons:Ce,"is-open":Pe.value,header:"Alert","sub-header":"Do you want to disconnect the Bluetooth!",onDidDismiss:t[0]||(t[0]=e=>{return t=!1,void(Pe.value=t);var t})},null,8,["is-open"])])),_:1})])),_:1}))}}),[["__scopeId","data-v-67677f0b"]]))}}}));
