import{at as i}from"./index-CMzsP7-d.js";class r extends i{constructor(){super(...arguments),this.wakeLock=null,this._isSupported=typeof navigator<"u"&&"wakeLock"in navigator,this.handleVisibilityChange=()=>{document.visibilityState==="visible"&&this.keepAwake()}}async keepAwake(){this._isSupported||this.throwUnsupportedError(),this.wakeLock&&await this.allowSleep(),this.wakeLock=await navigator.wakeLock.request("screen"),document.addEventListener("visibilitychange",this.handleVisibilityChange),document.addEventListener("fullscreenchange",this.handleVisibilityChange)}async allowSleep(){var e;this._isSupported||this.throwUnsupportedError(),(e=this.wakeLock)===null||e===void 0||e.release(),this.wakeLock=null,document.removeEventListener("visibilitychange",this.handleVisibilityChange),document.removeEventListener("fullscreenchange",this.handleVisibilityChange)}async isSupported(){return{isSupported:this._isSupported}}async isKeptAwake(){return this._isSupported||this.throwUnsupportedError(),{isKeptAwake:!!this.wakeLock}}throwUnsupportedError(){throw this.unavailable("Screen Wake Lock API not available in this browser.")}}export{r as KeepAwakeWeb};
