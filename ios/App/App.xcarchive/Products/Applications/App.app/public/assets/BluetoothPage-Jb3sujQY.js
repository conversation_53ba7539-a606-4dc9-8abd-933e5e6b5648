import{d as F,F as O,s as U,J as q,C as G,m as J,af as Q,p as Z,l as $,ag as K,c as o,w as a,a as e,I as W,b as n,P as X,B as C,Q as _,ah as Y,Z as ee,j as l,R as b,h as D,n as ae,O as te,ai as ne,aj as se,ak as oe,z as g,al as le,am as ie,a7 as re,an as ue,a1 as ce,ao as de,i as h,a2 as fe,a3 as ve,T as k,A as T,ap as v,aq as Ie,S as me,ar as we,as as L,N as ye,o as s,E as Ce}from"./index-CMzsP7-d.js";import{r as ge,f as ke}from"./index-Bc1Yyv3I.js";const De=F({__name:"BluetoothPage",setup(Be){const S=O(),{connectedDevice:i,availableDevices:B}=U(S),{scan:I,scanning:m,connectBle:N,disConnectBle:P}=q(),{stopSendMessage:x}=G(),A=ye(),{on:R}=Q();let w;J(()=>{i.value.isPaired||c()}),R(async()=>{await c()}),Z(()=>{clearInterval(w)});const z=Ce(),c=async()=>{await I(),w=setInterval(async()=>{await I()},1e3*15)},E=async r=>{clearInterval(w),await V();try{await N(r),await d.dismiss(),A.back()}catch(t){console.log("connect error",t),await d.dismiss(),await z.presentToast("Unmatched Bluetooth device"),await c()}},H=["Cancel",{text:"Okay",handler:async()=>{var r,t,u;(r=y.value)==null||r.dismiss(),await j();try{await x(),await P(i.value),(t=f.value)==null||t.dismiss()}catch(p){(u=f.value)==null||u.dismiss()}setTimeout(async()=>{await c()},1e3)}}],y=$(),M=async()=>{y.value=await we.create({header:"Alert",subHeader:"Do you want to disconnect the Bluetooth!",buttons:H}),await y.value.present()};let d={};const V=async()=>{d=await L.create({message:"Connecting to Bluetooth device"}),await d.present()},f=K(),j=async()=>{f.value=await L.create({message:"Disconnecting Bluetooth device"}),await f.value.present()};return(r,t)=>(s(),o(e(W),{ref:"page"},{default:a(()=>[n(e(te),null,{default:a(()=>[n(e(X),null,{default:a(()=>[n(e(_),{slot:"start"},{default:a(()=>[n(e(Y))]),_:1}),n(e(ee),null,{default:a(()=>t[0]||(t[0]=[l("Bluetooth")])),_:1}),e(i).isPaired?C("",!0):(s(),o(e(_),{key:0,slot:"end"},{default:a(()=>[n(e(b),{onClick:e(I)},{default:a(()=>[n(e(D),{slot:"icon-only",class:ae({"icon-refresh--on":e(m)}),icon:e(ge)},null,8,["class","icon"])]),_:1},8,["onClick"])]),_:1}))]),_:1})]),_:1}),n(e(me),{fullscreen:!0},{default:a(()=>[e(i).isPaired?(s(),o(e(ne),{key:0},{default:a(()=>[n(e(se),null,{default:a(()=>[n(e(oe),{class:"device-modal__title"},{default:a(()=>[l(g(e(i).name),1)]),_:1}),n(e(le),null,{default:a(()=>t[1]||(t[1]=[l("Device Information")])),_:1})]),_:1}),n(e(ie),null,{default:a(()=>[l(" Device ID:"+g(e(i).deviceId),1)]),_:1}),n(e(b),{fill:"clear",size:"small",onClick:M},{default:a(()=>t[2]||(t[2]=[l("Disconnect ")])),_:1})]),_:1})):(s(),o(e(re),{key:1},{default:a(()=>[n(e(ue),null,{default:a(()=>[n(e(de),null,{default:a(()=>[n(e(h),null,{default:a(()=>t[3]||(t[3]=[l("Available Devices")])),_:1})]),_:1}),(s(!0),ce(ve,null,fe(e(B),(u,p)=>(s(),o(e(k),{key:p,onClick:pe=>E(u)},{default:a(()=>[n(e(D),{slot:"start",icon:e(ke)},null,8,["icon"]),n(e(h),null,{default:a(()=>[l(g(u.name),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128)),e(m)?(s(),o(e(k),{key:0,lines:"none"},{default:a(()=>[e(T)("ios")?(s(),o(e(v),{key:0},{default:a(()=>t[4]||(t[4]=[l(" Searching for available devices... ")])),_:1})):(s(),o(e(v),{key:1,slot:"start"},{default:a(()=>t[5]||(t[5]=[l(" Searching for available devices... ")])),_:1})),n(e(Ie),{slot:"end"})]),_:1})):C("",!0),e(B).length===0&&!e(m)?(s(),o(e(k),{key:1,lines:"none"},{default:a(()=>[e(T)("ios")?(s(),o(e(v),{key:0},{default:a(()=>t[6]||(t[6]=[l(" No available Bluetooth devices found ")])),_:1})):(s(),o(e(v),{key:1,slot:"start"},{default:a(()=>t[7]||(t[7]=[l(" No available Bluetooth devices found ")])),_:1}))]),_:1})):C("",!0)]),_:1})]),_:1}))]),_:1})]),_:1},512))}});export{De as default};
