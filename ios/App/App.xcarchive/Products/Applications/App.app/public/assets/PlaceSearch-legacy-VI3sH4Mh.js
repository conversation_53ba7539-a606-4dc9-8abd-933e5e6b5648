System.register(["./index-legacy-CZnTL5oV.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js"],(function(e,o){"use strict";var t,a,l,n,i,s,d,r,c,p,u,m,g,f,v,b,w,h,y;return{setters:[e=>{t=e.d,a=e.ac,l=e.ad,n=e.s,i=e.m,s=e.ae,d=e.c,r=e.w,c=e.a,p=e.I,u=e.b,m=e.P,g=e.Z,f=e.j,v=e.O,b=e.x,w=e.S,h=e.o},e=>{y=e._}],execute:function(){var o=document.createElement("style");o.textContent="#map[data-v-fc46b1be]{height:100%}#place-autocomplete-card[data-v-fc46b1be]{background-color:#fff;border-radius:5px;box-shadow:rgba(0,0,0,.35) 0 5px 15px;margin:10px;padding:5px;font-family:Roboto,sans-serif;font-size:large;font-weight:700}gmp-place-autocomplete[data-v-fc46b1be]{width:300px}#infowindow-content .title[data-v-fc46b1be]{font-weight:700}#map #infowindow-content[data-v-fc46b1be]{display:inline}#sidebar[data-v-fc46b1be]{flex-basis:15rem;flex-grow:1;padding:1rem;max-width:30rem;box-sizing:border-box;overflow:auto;background-color:#fff}#sidebar[data-v-fc46b1be]{position:absolute;top:0;right:0;flex:0 1 auto;padding:0}#sidebar>div[data-v-fc46b1be]{padding:.5rem}\n/*$vite$:1*/",document.head.appendChild(o);const L={ref:"mapRef",id:"map"};e("default",y(t({__name:"PlaceSearch",setup(e){let o=null,t=null,y=null,x=null,_=null,E=null;const{getCurrentPosition:P}=a(),C=l(),{currentPosition:I}=n(C);i((()=>{k(),P().then((e=>{var o;const a=new google.maps.LatLng(e.coords.latitude,e.coords.longitude);null===(o=t)||void 0===o||o.setCenter(a)}))}));const k=async()=>{var e;await Promise.all([google.maps.importLibrary("maps"),google.maps.importLibrary("marker"),google.maps.importLibrary("places")]),o=s.isNativePlatform()?new google.maps.LatLng(I.value.coords.latitude,I.value.coords.longitude):new google.maps.LatLng(31.298054640071758,120.54363905144386),_=new google.maps.DirectionsRenderer,E=new google.maps.DirectionsService,t=new google.maps.Map(document.getElementById("map"),{zoom:10,center:o,mapId:"4504f8b37365c3d0",mapTypeControl:!1,fullscreenControl:!1});const a=new google.maps.places.PlaceAutocompleteElement({});a.id="place-autocomplete-input";const l=document.getElementById("place-autocomplete-card");l&&(l.appendChild(a),null===(e=t)||void 0===e||e.controls[google.maps.ControlPosition.TOP_LEFT].push(l),x=new google.maps.marker.AdvancedMarkerElement({map:t}),y=new google.maps.InfoWindow({}),a.addEventListener("gmp-placeselect",(async e=>{const{place:o}=e;var a,l,n;await o.fetchFields({fields:["displayName","formattedAddress","location"]}),o.viewport?null===(a=t)||void 0===a||a.fitBounds(o.viewport):(null===(l=t)||void 0===l||l.setCenter(o.location),null===(n=t)||void 0===n||n.setZoom(17));const i='<div id="infowindow-content"><span id="place-displayname" class="title">'+o.displayName+'</span><br /><span id="place-address">'+o.formattedAddress+"</span><button id='destination'>前往这里</button>";x.position=o.location,x.title=o.displayName,B(i,o.location)})),_.setMap(t),_.setPanel(document.getElementById("sidebar")))},B=(e,a)=>{var l,n,i;null===(l=y)||void 0===l||l.setContent(e),null===(n=y)||void 0===n||n.setPosition(a),null===(i=y)||void 0===i||i.open({map:t,shouldFocus:!1},x),setTimeout((()=>{const e=document.getElementById("destination");null==e||e.addEventListener("click",(()=>{var e;null===(e=y)||void 0===e||e.close(),M(o,a)}))}),500)},M=(e,o)=>{var t;null===(t=E)||void 0===t||t.route({origin:e,destination:o,travelMode:google.maps.TravelMode.WALKING}).then((e=>{var o;console.log(e),null===(o=_)||void 0===o||o.setDirections(e);const t=new google.maps.LatLng(31.3012,120.5481);A(t,e.routes[0].legs[0].steps)})).catch((e=>{console.log(e),window.alert("Directions request failed due to "+e.message)}))},A=(e,o)=>{let t=1/0,a=0;return o.forEach(((o,l)=>{const n=o.path;let i=1/0;n.forEach((o=>{const t=google.maps.geometry.spherical.computeDistanceBetween(new google.maps.LatLng(e.lat(),e.lng()),new google.maps.LatLng(o.lat(),o.lng()));t<i&&(i=t)})),i<t&&(t=i,a=l)})),a};return(e,o)=>(h(),d(c(p),null,{default:r((()=>[u(c(v),null,{default:r((()=>[u(c(m),null,{default:r((()=>[u(c(g),null,{default:r((()=>o[0]||(o[0]=[f("Google Map")]))),_:1})])),_:1})])),_:1}),u(c(w),null,{default:r((()=>[o[1]||(o[1]=b("div",{class:"place-autocomplete-card",id:"place-autocomplete-card"},[b("p",null,"Search for a place here:")],-1)),b("div",L,null,512),o[2]||(o[2]=b("div",{id:"sidebar"},null,-1))])),_:1})])),_:1}))}}),[["__scopeId","data-v-fc46b1be"]]))}}}));
