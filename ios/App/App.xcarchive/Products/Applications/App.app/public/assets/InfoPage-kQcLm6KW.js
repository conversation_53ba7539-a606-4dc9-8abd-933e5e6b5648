import{d as I,u as m,s as S,c as T,w as n,a as l,I as b,b as e,P as w,Z as x,j as s,O as C,t as P,y as t,v as o,h as u,U as r,S as y,o as H}from"./index-CMzsP7-d.js";import{b as k,w as i}from"./index-Bc1Yyv3I.js";const N=I({__name:"InfoPage",setup(q){const f=m(),{current:d,throttle:_,motorPhase:c,motorHall:g,torqueSensor:z,speedSensor:p}=S(f);return(B,a)=>(H(),T(l(b),{class:"info-page"},{default:n(()=>[e(l(C),null,{default:n(()=>[e(l(w),null,{default:n(()=>[e(l(x),null,{default:n(()=>a[0]||(a[0]=[s("Error Information")])),_:1})]),_:1})]),_:1}),e(l(y),{fullscreen:!0,class:"ion-padding-horizontal"},{default:n(()=>[e(l(P),null,{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{class:"info-page__bicycle"},{default:n(()=>[e(l(u),{icon:l(k)},null,8,["icon"])]),_:1})]),_:1}),e(l(t),null,{default:n(()=>[e(l(o),{size:"6"},{default:n(()=>[e(l(t),{"align-items-center":""},{default:n(()=>[e(l(o),{size:"3"},{default:n(()=>[e(l(u),{color:l(d)?"primary":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[1]||(a[1]=[s("Current")])),_:1})]),_:1})]),_:1})]),_:1}),e(l(o),{size:"6"},{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{class:"ion-align-self-center",size:"3"},{default:n(()=>[e(l(u),{color:l(_)?"danger":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[2]||(a[2]=[s("Throttle")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(l(t),null,{default:n(()=>[e(l(o),{size:"6"},{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{size:"3"},{default:n(()=>[e(l(u),{color:l(c)?"danger":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[3]||(a[3]=[s("MotorPhase")])),_:1})]),_:1})]),_:1})]),_:1}),e(l(o),{size:"6"},{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{size:"3"},{default:n(()=>[e(l(u),{color:l(g)?"danger":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[4]||(a[4]=[s("MotorHall")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(l(t),null,{default:n(()=>[e(l(o),{size:"6"},{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{size:"3"},{default:n(()=>[e(l(u),{color:l(z)?"danger":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[5]||(a[5]=[s("TorqueSensor")])),_:1})]),_:1})]),_:1})]),_:1}),e(l(o),{size:"6"},{default:n(()=>[e(l(t),null,{default:n(()=>[e(l(o),{size:"3"},{default:n(()=>[e(l(u),{color:l(p)?"danger":"",icon:l(i)},null,8,["color","icon"])]),_:1}),e(l(o),{class:"ion-align-self-center",size:"9"},{default:n(()=>[e(l(r),null,{default:n(()=>a[6]||(a[6]=[s("SpeedSensor")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}))}});export{N as default};
