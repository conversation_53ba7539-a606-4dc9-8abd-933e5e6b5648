import{d as M,ac as T,ad as B,s as D,m as N,ae as S,c as k,w as c,a as r,I as x,b as u,P as A,Z as G,j as F,O as R,x as g,S as V,o as O}from"./index-CMzsP7-d.js";import{_ as Z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const j={ref:"mapRef",id:"map"},q=M({__name:"PlaceSearch",setup(z){let m=null,e=null,s=null,i=null,n=null,d=null;const{getCurrentPosition:I}=T(),v=B(),{currentPosition:L}=D(v);N(()=>{_(),I().then(a=>{const t=new google.maps.LatLng(a.coords.latitude,a.coords.longitude);e==null||e.setCenter(t)})});const _=async()=>{await Promise.all([google.maps.importLibrary("maps"),google.maps.importLibrary("marker"),google.maps.importLibrary("places")]),m=S.isNativePlatform()?new google.maps.LatLng(L.value.coords.latitude,L.value.coords.longitude):new google.maps.LatLng(31.298054640071758,120.54363905144386),n=new google.maps.DirectionsRenderer,d=new google.maps.DirectionsService,e=new google.maps.Map(document.getElementById("map"),{zoom:10,center:m,mapId:"4504f8b37365c3d0",mapTypeControl:!1,fullscreenControl:!1});const a=new google.maps.places.PlaceAutocompleteElement({});a.id="place-autocomplete-input";const t=document.getElementById("place-autocomplete-card");t&&(t.appendChild(a),e==null||e.controls[google.maps.ControlPosition.TOP_LEFT].push(t),i=new google.maps.marker.AdvancedMarkerElement({map:e}),s=new google.maps.InfoWindow({}),a.addEventListener("gmp-placeselect",async o=>{const{place:l}=o;await l.fetchFields({fields:["displayName","formattedAddress","location"]}),l.viewport?e==null||e.fitBounds(l.viewport):(e==null||e.setCenter(l.location),e==null||e.setZoom(17));const f='<div id="infowindow-content"><span id="place-displayname" class="title">'+l.displayName+'</span><br /><span id="place-address">'+l.formattedAddress+"</span><button id='destination'>前往这里</button>";i.position=l.location,i.title=l.displayName,b(f,l.location)}),n.setMap(e),n.setPanel(document.getElementById("sidebar")))},b=(a,t)=>{s==null||s.setContent(a),s==null||s.setPosition(t),s==null||s.open({map:e,shouldFocus:!1},i),setTimeout(()=>{const o=document.getElementById("destination");o==null||o.addEventListener("click",()=>{s==null||s.close(),P(m,t)})},500)},P=(a,t)=>{d==null||d.route({origin:a,destination:t,travelMode:google.maps.TravelMode.WALKING}).then(o=>{console.log(o),n==null||n.setDirections(o);const l=new google.maps.LatLng(31.3012,120.5481);h(l,o.routes[0].legs[0].steps)}).catch(o=>{console.log(o),window.alert("Directions request failed due to "+o.message)})},h=(a,t)=>{let o=1/0,l=0;debugger;return t.forEach((f,C)=>{const E=f.path;let p=1/0;E.forEach(w=>{const y=google.maps.geometry.spherical.computeDistanceBetween(new google.maps.LatLng(a.lat(),a.lng()),new google.maps.LatLng(w.lat(),w.lng()));y<p&&(p=y)}),p<o&&(o=p,l=C)}),l};return(a,t)=>(O(),k(r(x),null,{default:c(()=>[u(r(R),null,{default:c(()=>[u(r(A),null,{default:c(()=>[u(r(G),null,{default:c(()=>t[0]||(t[0]=[F("Google Map")])),_:1})]),_:1})]),_:1}),u(r(V),null,{default:c(()=>[t[1]||(t[1]=g("div",{class:"place-autocomplete-card",id:"place-autocomplete-card"},[g("p",null,"Search for a place here:")],-1)),g("div",j,null,512),t[2]||(t[2]=g("div",{id:"sidebar"},null,-1))]),_:1})]),_:1}))}}),J=Z(q,[["__scopeId","data-v-fc46b1be"]]);export{J as default};
