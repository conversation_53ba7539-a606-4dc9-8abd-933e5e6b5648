System.register(["./index-legacy-CZnTL5oV.js","./index-legacy-C0iBWw5M.js","./_plugin-vue_export-helper-legacy-DgAO6S8O.js"],(function(t,a){"use strict";var e,n,i,o,l,s,c,r,d,b,u,_,f,h,p,m,g,y,v,I,S,w,E,C,M,j;return{setters:[t=>{e=t.r,n=t._,i=t.d,o=t.u,l=t.s,s=t.c,c=t.w,r=t.a,d=t.I,b=t.b,u=t.e,_=t.f,f=t.g,h=t.h,p=t.i,m=t.j,g=t.n,y=t.k,v=t.o},t=>{I=t.s,S=t.a,w=t.b,E=t.n,C=t.m,M=t.c},t=>{j=t._}],execute:function(){var k,x,H=document.createElement("style");H.textContent="ion-tab-button:not(.tab-selected) ion-icon[data-v-968918b1]{color:#fff}.tabs .tabs__item[data-v-968918b1]{position:relative}.tabs .tabs__item .tabs-icon__alert[data-v-968918b1]{position:absolute;font-size:1rem;top:0;right:20px}.tabs .tabs__item .tabs-icon__alert.tabs-icon__alert--on[data-v-968918b1]{display:block;animation:twinkle-968918b1 .5s infinite alternate}.tabs .tabs__item .tabs-icon__alert.tabs-icon__alert--off[data-v-968918b1]{display:none}@keyframes twinkle-968918b1{0%{opacity:.8}to{opacity:0}}\n/*$vite$:1*/",document.head.appendChild(H),t("I",k),function(t){t.Heavy="HEAVY",t.Medium="MEDIUM",t.Light="LIGHT"}(k||t("I",k={})),t("N",x),function(t){t.Success="SUCCESS",t.Warning="WARNING",t.Error="ERROR"}(x||t("N",x={}));const T=e("Haptics",{web:()=>n((()=>a.import("./web-legacy-w54beDxd.js")),void 0).then((t=>new t.HapticsWeb))}),L=async()=>{await T.impact({style:k.Medium})},N=async()=>{await T.impact({style:k.Light}),console.log("hapticsImpactLight")},R=async()=>{await T.vibrate()},W=async()=>{await T.selectionStart()},z=async()=>{await T.selectionChanged()},G=async()=>{await T.selectionEnd()};function O(){return{hapticsImpactMedium:L,hapticsImpactLight:N,hapticsVibrate:R,hapticsSelectionStart:W,hapticsSelectionChanged:z,hapticsSelectionEnd:G}}const A=i({__name:"TabsPage",setup(t){const{hapticsImpactMedium:a}=O(),e=o(),{hasError:n}=l(e),i=()=>{a(),console.log("handleIonTabButtonClick")};return(t,a)=>(v(),s(r(d),null,{default:c((()=>[b(r(y),{onIonTabsWillChange:i,class:"tabs"},{default:c((()=>[b(r(u)),b(r(_),{slot:"bottom"},{default:c((()=>[b(r(f),{href:"/tabs/home",tab:"home"},{default:c((()=>[b(r(h),{icon:r(I),"aria-hidden":"true"},null,8,["icon"]),b(r(p),null,{default:c((()=>a[0]||(a[0]=[m("Home")]))),_:1})])),_:1}),b(r(f),{href:"/tabs/setting",tab:"setting"},{default:c((()=>[b(r(h),{icon:r(S),"aria-hidden":"true"},null,8,["icon"]),b(r(p),null,{default:c((()=>a[1]||(a[1]=[m("Setting")]))),_:1})])),_:1}),b(r(f),{class:"tabs__item",href:"/tabs/info",tab:"info"},{default:c((()=>[b(r(h),{icon:r(w),"aria-hidden":"true"},null,8,["icon"]),b(r(p),null,{default:c((()=>a[2]||(a[2]=[m("Info")]))),_:1}),b(r(h),{class:g([r(n)?"tabs-icon__alert--on":"tabs-icon__alert--off","tabs-icon__alert tabs-icon__alert--on"]),icon:r(E),color:"danger",size:"mini"},null,8,["class","icon"])])),_:1}),b(r(f),{href:"/tabs/google-map/place-search",tab:"navi"},{default:c((()=>[b(r(h),{icon:r(C),"aria-hidden":"true"},null,8,["icon"]),b(r(p),null,{default:c((()=>a[3]||(a[3]=[m("GoogleMap")]))),_:1})])),_:1}),b(r(f),{href:"/tabs/dashboard",tab:"dashboard"},{default:c((()=>[b(r(h),{icon:r(M),"aria-hidden":"true"},null,8,["icon"]),b(r(p),null,{default:c((()=>a[4]||(a[4]=[m("Dashboard")]))),_:1})])),_:1})])),_:1})])),_:1})])),_:1}))}}),D=j(A,[["__scopeId","data-v-968918b1"]]),P=Object.freeze(Object.defineProperty({__proto__:null,default:D},Symbol.toStringTag,{value:"Module"}));t("T",P)}}}));
