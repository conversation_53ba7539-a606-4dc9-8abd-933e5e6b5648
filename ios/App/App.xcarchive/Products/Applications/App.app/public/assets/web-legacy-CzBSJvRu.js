System.register(["./index-legacy-CZnTL5oV.js"],(function(e,n){"use strict";var t;return{setters:[e=>{t=e.at}],execute:function(){e("ScreenOrientationWeb",class extends t{constructor(){super(),"undefined"!=typeof screen&&void 0!==screen.orientation&&screen.orientation.addEventListener("change",(()=>{const e=screen.orientation.type;this.notifyListeners("screenOrientationChange",{type:e})}))}async orientation(){if("undefined"==typeof screen||!screen.orientation)throw this.unavailable("ScreenOrientation API not available in this browser");return{type:screen.orientation.type}}async lock(e){if("undefined"==typeof screen||!screen.orientation||!screen.orientation.lock)throw this.unavailable("ScreenOrientation API not available in this browser");try{await screen.orientation.lock(e.orientation)}catch(n){throw this.unavailable("ScreenOrientation API not available in this browser")}}async unlock(){if("undefined"==typeof screen||!screen.orientation||!screen.orientation.unlock)throw this.unavailable("ScreenOrientation API not available in this browser");try{screen.orientation.unlock()}catch(e){throw this.unavailable("ScreenOrientation API not available in this browser")}}})}}}));
