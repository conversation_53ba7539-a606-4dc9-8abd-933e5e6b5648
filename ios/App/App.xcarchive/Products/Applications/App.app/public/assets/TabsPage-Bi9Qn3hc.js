const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web-DCnzWJ0v.js","assets/index-CMzsP7-d.js","assets/index-BxicBINB.css","assets/index-Bc1Yyv3I.js","assets/_plugin-vue_export-helper-DlAUqK2U.js"])))=>i.map(i=>d[i]);
import{r as m,_ as h,d as g,u as I,s as T,c as S,w as e,a,I as E,b as t,e as w,f as y,g as l,h as o,i as r,j as c,n as C,k as v,o as H}from"./index-CMzsP7-d.js";import{s as M,a as P,b as L,n as O,m as R,c as x}from"./index-Bc1Yyv3I.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";var u;(function(n){n.Heavy="HEAVY",n.Medium="MEDIUM",n.Light="LIGHT"})(u||(u={}));var d;(function(n){n.Success="SUCCESS",n.Warning="WARNING",n.Error="ERROR"})(d||(d={}));const i=m("Haptics",{web:()=>h(()=>import("./web-DCnzWJ0v.js"),__vite__mapDeps([0,1,2,3,4])).then(n=>new n.HapticsWeb)}),V=async()=>{await i.impact({style:u.Medium})},W=async()=>{await i.impact({style:u.Light}),console.log("hapticsImpactLight")},k=async()=>{await i.vibrate()},N=async()=>{await i.selectionStart()},j=async()=>{await i.selectionChanged()},z=async()=>{await i.selectionEnd()};function A(){return{hapticsImpactMedium:V,hapticsImpactLight:W,hapticsVibrate:k,hapticsSelectionStart:N,hapticsSelectionChanged:j,hapticsSelectionEnd:z}}const D=g({__name:"TabsPage",setup(n){const{hapticsImpactMedium:b}=A(),f=I(),{hasError:_}=T(f),p=()=>{b(),console.log("handleIonTabButtonClick")};return(U,s)=>(H(),S(a(E),null,{default:e(()=>[t(a(v),{onIonTabsWillChange:p,class:"tabs"},{default:e(()=>[t(a(w)),t(a(y),{slot:"bottom"},{default:e(()=>[t(a(l),{href:"/tabs/home",tab:"home"},{default:e(()=>[t(a(o),{icon:a(M),"aria-hidden":"true"},null,8,["icon"]),t(a(r),null,{default:e(()=>s[0]||(s[0]=[c("Home")])),_:1})]),_:1}),t(a(l),{href:"/tabs/setting",tab:"setting"},{default:e(()=>[t(a(o),{icon:a(P),"aria-hidden":"true"},null,8,["icon"]),t(a(r),null,{default:e(()=>s[1]||(s[1]=[c("Setting")])),_:1})]),_:1}),t(a(l),{class:"tabs__item",href:"/tabs/info",tab:"info"},{default:e(()=>[t(a(o),{icon:a(L),"aria-hidden":"true"},null,8,["icon"]),t(a(r),null,{default:e(()=>s[2]||(s[2]=[c("Info")])),_:1}),t(a(o),{class:C([a(_)?"tabs-icon__alert--on":"tabs-icon__alert--off","tabs-icon__alert tabs-icon__alert--on"]),icon:a(O),color:"danger",size:"mini"},null,8,["class","icon"])]),_:1}),t(a(l),{href:"/tabs/google-map/place-search",tab:"navi"},{default:e(()=>[t(a(o),{icon:a(R),"aria-hidden":"true"},null,8,["icon"]),t(a(r),null,{default:e(()=>s[3]||(s[3]=[c("GoogleMap")])),_:1})]),_:1}),t(a(l),{href:"/tabs/dashboard",tab:"dashboard"},{default:e(()=>[t(a(o),{icon:a(x),"aria-hidden":"true"},null,8,["icon"]),t(a(r),null,{default:e(()=>s[4]||(s[4]=[c("Dashboard")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1}))}}),G=B(D,[["__scopeId","data-v-968918b1"]]),F=Object.freeze(Object.defineProperty({__proto__:null,default:G},Symbol.toStringTag,{value:"Module"}));export{u as I,d as N,F as T};
