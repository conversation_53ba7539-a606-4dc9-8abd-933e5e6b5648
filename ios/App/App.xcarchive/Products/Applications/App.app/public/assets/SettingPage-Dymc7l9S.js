import{d as G,l as w,M as N,c as D,a as e,T as d,w as u,b as l,X as m,h as Y,Y as J,O as W,P as Z,Z as j,j as i,z as A,Q as O,R as c,$ as h,a0 as ee,a1 as le,a2 as ae,a3 as ue,o as U,a4 as ne,H as oe,u as te,s as de,C as me,K as re,a5 as ie,a6 as se,I as pe,a7 as fe,a8 as E,i as Ve,a9 as t,aa as ve,ab as F,t as be,y as Ce,v as Ie,S as Se}from"./index-CMzsP7-d.js";import{g as ye}from"./index-Bc1Yyv3I.js";import{_ as ge}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Pe=G({__name:"ModalSelect",props:{modelValue:{},list:{},label:{default:"Select"},name:{default:""},placeholder:{default:"Please select"},modalName:{default:"modal"}},emits:["update:modelValue"],setup(H,{emit:k}){const f=H,C=k,p=w(null),V=w(f.modelValue),v=w(null),b=N(()=>"open-modal-".concat(f.modalName,"-").concat(Math.random().toString(36).slice(2,11))),I=N(()=>{debugger;const o=f.list.find(r=>r.value===f.modelValue);return(o==null?void 0:o.name)||""}),S=o=>{V.value=o.detail.value},y=o=>{o.detail.role==="confirm"&&C("update:modelValue",o.detail.data)},g=()=>{var o;(o=p.value)==null||o.$el.dismiss(null,"cancel")},P=()=>{var r,s;const o=(r=v.value)==null?void 0:r.$el.activeItem;o&&(V.value=o.value),(s=p.value)==null||s.$el.dismiss(V.value,"confirm")};return(o,r)=>(U(),D(e(d),null,{default:u(()=>[l(e(m),{id:b.value,readonly:"",value:I.value,label:o.label,name:o.name,placeholder:o.placeholder},{default:u(()=>[l(e(Y),{slot:"end",icon:e(ye)},null,8,["icon"])]),_:1},8,["id","value","label","name","placeholder"]),l(e(J),{ref_key:"modal",ref:p,trigger:b.value,onDidDismiss:y},{default:u(()=>[l(e(W),null,{default:u(()=>[l(e(Z),null,{default:u(()=>[l(e(j),null,{default:u(()=>[i(A(o.label),1)]),_:1}),l(e(O),{slot:"start"},{default:u(()=>[l(e(c),{onClick:g},{default:u(()=>r[0]||(r[0]=[i("Cancel")])),_:1})]),_:1}),l(e(O),{slot:"end"},{default:u(()=>[l(e(c),{color:"primary",onClick:P},{default:u(()=>r[1]||(r[1]=[i("OK")])),_:1})]),_:1})]),_:1})]),_:1}),l(e(h),null,{default:u(()=>[l(e(ee),{ref_key:"pickerColumn",ref:v,value:V.value,onIonChange:S},{default:u(()=>[(U(!0),le(ue,null,ae(o.list,s=>(U(),D(e(ne),{key:s.value,value:s.value},{default:u(()=>[i(A(s.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1},8,["trigger"])]),_:1}))}}),z=ge(Pe,[["__scopeId","data-v-9997b305"]]),Le=G({__name:"SettingPage",setup(H){const k=oe(),f=te(),{maxSpeed:C,dimension:p,dimensionList:V,p1:v,p2:b,p3:I,p4:S,p5:y,c1:g,c2:P,c3:o,c4:r,c5:s,c7:M,c12:L,c13:T,c14:_,levelList:q,handlebarMaxSpeed:$,percent:x,candidateParam:B,displayType:R}=de(k),{stopSendMessage:Q}=me(),{updateSetting:K}=re();ie(()=>{Q()}),se(()=>{K()});const X=()=>{k.$reset(),f.$reset()};return(ke,a)=>(U(),D(e(pe),{class:"page-setting"},{default:u(()=>[l(e(W),null,{default:u(()=>[l(e(Z),null,{default:u(()=>[l(e(j),null,{default:u(()=>a[20]||(a[20]=[i("Setting")])),_:1}),l(e(O),{slot:"end"},{default:u(()=>[l(e(c),{fill:"solid",onClick:e(K)},{default:u(()=>a[21]||(a[21]=[i("Save")])),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1}),l(e(Se),{fullscreen:!0},{default:u(()=>[l(e(fe),null,{default:u(()=>[l(e(E),null,{default:u(()=>[l(e(Ve),null,{default:u(()=>a[22]||(a[22]=[i("Parameters")])),_:1})]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(C),"onUpdate:modelValue":a[0]||(a[0]=n=>t(C)?C.value=n:null),max:72,maxlength:2,min:10,inputmode:"numeric",label:"MaxSpeed",name:"maxSpeed",placeholder:"km/h",type:"number"},null,8,["modelValue"])]),_:1}),l(z,{modelValue:e(p),"onUpdate:modelValue":a[1]||(a[1]=n=>t(p)?p.value=n:null),list:e(V),"modal-name":"dimension",label:"Bike Dimension",placeholder:"Select Dimension"},null,8,["modelValue","list"]),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(v),"onUpdate:modelValue":a[2]||(a[2]=n=>t(v)?v.value=n:null),inputmode:"numeric",label:"MotorSetting (P1)",name:"p1",placeholder:"P1",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(b),"onUpdate:modelValue":a[3]||(a[3]=n=>t(b)?b.value=n:null),inputmode:"numeric",label:"SpeedSensor (P2)",name:"p2",placeholder:"P2",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(I),"onUpdate:modelValue":a[4]||(a[4]=n=>t(I)?I.value=n:null),inputmode:"numeric",label:"Torque (P3)",name:"p3",placeholder:"P3",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(S),"onUpdate:modelValue":a[5]||(a[5]=n=>t(S)?S.value=n:null),inputmode:"numeric",label:"ZeroStart (P4)",name:"p4",placeholder:"P4",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(y),"onUpdate:modelValue":a[6]||(a[6]=n=>t(y)?y.value=n:null),inputmode:"numeric",label:"Battery (P5)",name:"p5",placeholder:"P5",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(g),"onUpdate:modelValue":a[7]||(a[7]=n=>t(g)?g.value=n:null),inputmode:"numeric",label:"PAS (C1)",name:"c1",placeholder:"C1",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(P),"onUpdate:modelValue":a[8]||(a[8]=n=>t(P)?P.value=n:null),inputmode:"numeric",label:"MotorPhase (C2)",name:"c2",placeholder:"C2",type:"number"},null,8,["modelValue"])]),_:1}),l(z,{modelValue:e(o),"onUpdate:modelValue":a[9]||(a[9]=n=>t(o)?o.value=n:null),list:e(q),"modal-name":"initLevel",label:"InitLevel (C3)",placeholder:"Select InitLevel"},null,8,["modelValue","list"]),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(r),"onUpdate:modelValue":a[10]||(a[10]=n=>t(r)?r.value=n:null),inputmode:"numeric",label:"Throttle (C4)",name:"c4",placeholder:"C4",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(s),"onUpdate:modelValue":a[11]||(a[11]=n=>t(s)?s.value=n:null),inputmode:"numeric",label:"Current (C5)",name:"c5",placeholder:"C5",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(M),"onUpdate:modelValue":a[12]||(a[12]=n=>t(M)?M.value=n:null),inputmode:"numeric",label:"Cruise (C7)",name:"c7",placeholder:"C7",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(L),"onUpdate:modelValue":a[13]||(a[13]=n=>t(L)?L.value=n:null),inputmode:"numeric",label:"UVLO (C12)",name:"c12",placeholder:"C12",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(T),"onUpdate:modelValue":a[14]||(a[14]=n=>t(T)?T.value=n:null),inputmode:"numeric",label:"Regenerative (C13)",name:"c13",placeholder:"C13",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(_),"onUpdate:modelValue":a[15]||(a[15]=n=>t(_)?_.value=n:null),inputmode:"numeric",label:"PASPower (C14)",name:"c14",placeholder:"C14",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e($),"onUpdate:modelValue":a[16]||(a[16]=n=>t($)?$.value=n:null),inputmode:"numeric",label:"Handlebar Maximum Speed",name:"handleBarSpeed",placeholder:"data.displayType === 'kilometer' ? 'km/h' : 'mile/h'",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(x),"onUpdate:modelValue":a[17]||(a[17]=n=>t(x)?x.value=n:null),inputmode:"numeric",label:"FirstLevelPercent (Throttle)",name:"percent",placeholder:"percentage",type:"number"},null,8,["modelValue"])]),_:1}),l(e(d),null,{default:u(()=>[l(e(m),{modelValue:e(B),"onUpdate:modelValue":a[18]||(a[18]=n=>t(B)?B.value=n:null),inputmode:"numeric",label:"Candidate",name:"candidate",placeholder:"Signal of PAS",type:"number"},null,8,["modelValue"])]),_:1}),l(e(ve),{modelValue:e(R),"onUpdate:modelValue":a[19]||(a[19]=n=>t(R)?R.value=n:null),name:"displayType"},{default:u(()=>[l(e(E),null,{default:u(()=>a[23]||(a[23]=[i("Kilometer／Mile")])),_:1}),l(e(d),null,{default:u(()=>[l(e(F),{value:"kilometer"},{default:u(()=>a[24]||(a[24]=[i("Kilometer")])),_:1})]),_:1}),l(e(d),null,{default:u(()=>[l(e(F),{value:"mile"},{default:u(()=>a[25]||(a[25]=[i("Mile")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(e(be),null,{default:u(()=>[l(e(Ce),null,{default:u(()=>[l(e(Ie),{class:"page-setting__restore"},{default:u(()=>[l(e(c),{fill:"outline",shape:"round",size:"small",onClick:X},{default:u(()=>a[26]||(a[26]=[i("Restore Settings ")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}))}});export{Le as default};
