import{at as o}from"./index-CMzsP7-d.js";class a extends o{async getCurrentPosition(e){return new Promise((i,n)=>{navigator.geolocation.getCurrentPosition(t=>{i(t)},t=>{n(t)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0},e))})}async watchPosition(e,i){const n=navigator.geolocation.watchPosition(t=>{i(t)},t=>{i(null,t)},Object.assign({enableHighAccuracy:!1,timeout:1e4,maximumAge:0,minimumUpdateInterval:5e3},e));return"".concat(n)}async clearWatch(e){navigator.geolocation.clearWatch(parseInt(e.id,10))}async checkPermissions(){if(typeof navigator>"u"||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");const e=await navigator.permissions.query({name:"geolocation"});return{location:e.state,coarseLocation:e.state}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}}const c=new a;export{c as Geolocation,a as GeolocationWeb};
