System.register(["./index-legacy-CZnTL5oV.js"],(function(e,t){"use strict";var i;return{setters:[e=>{i=e.at}],execute:function(){e("KeepAwakeWeb",class extends i{constructor(){super(...arguments),this.wakeLock=null,this._isSupported="undefined"!=typeof navigator&&"wakeLock"in navigator,this.handleVisibilityChange=()=>{"visible"===document.visibilityState&&this.keepAwake()}}async keepAwake(){this._isSupported||this.throwUnsupportedError(),this.wakeLock&&await this.allowSleep(),this.wakeLock=await navigator.wakeLock.request("screen"),document.addEventListener("visibilitychange",this.handleVisibilityChange),document.addEventListener("fullscreenchange",this.handleVisibilityChange)}async allowSleep(){var e;this._isSupported||this.throwUnsupportedError(),null===(e=this.wakeLock)||void 0===e||e.release(),this.wakeLock=null,document.removeEventListener("visibilitychange",this.handleVisibilityChange),document.removeEventListener("fullscreenchange",this.handleVisibilityChange)}async isSupported(){return{isSupported:this._isSupported}}async isKeptAwake(){return this._isSupported||this.throwUnsupportedError(),{isKeptAwake:!!this.wakeLock}}throwUnsupportedError(){throw this.unavailable("Screen Wake Lock API not available in this browser.")}})}}}));
