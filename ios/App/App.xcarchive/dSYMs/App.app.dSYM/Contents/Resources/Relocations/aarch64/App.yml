---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/InstallationBuildProductsLocation/Applications/App.app/App'
relocations:
  - { offsetInCU: 0x69, offset: 0x582E6, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100008000, symSize: 0x10 }
  - { offsetInCU: 0x89, offset: 0x58306, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100008000, symSize: 0x10 }
  - { offsetInCU: 0xD0, offset: 0x5834D, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x100008010, symSize: 0x34 }
  - { offsetInCU: 0x113, offset: 0x58390, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0i6LaunchH3KeyaypGSgtFTo', symObjAddr: 0x44, symBinAddr: 0x100008044, symSize: 0x8 }
  - { offsetInCU: 0x12F, offset: 0x583AC, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC27applicationWillResignActiveyySo13UIApplicationCFTo', symObjAddr: 0x4C, symBinAddr: 0x10000804C, symSize: 0x4 }
  - { offsetInCU: 0x14B, offset: 0x583C8, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC29applicationDidEnterBackgroundyySo13UIApplicationCFTo', symObjAddr: 0x50, symBinAddr: 0x100008050, symSize: 0x4 }
  - { offsetInCU: 0x167, offset: 0x583E4, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC30applicationWillEnterForegroundyySo13UIApplicationCFTo', symObjAddr: 0x54, symBinAddr: 0x100008054, symSize: 0x4 }
  - { offsetInCU: 0x183, offset: 0x58400, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC26applicationDidBecomeActiveyySo13UIApplicationCFTo', symObjAddr: 0x58, symBinAddr: 0x100008058, symSize: 0x4 }
  - { offsetInCU: 0x19F, offset: 0x5841C, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC24applicationWillTerminateyySo13UIApplicationCFTo', symObjAddr: 0x5C, symBinAddr: 0x10000805C, symSize: 0x4 }
  - { offsetInCU: 0x1FD, offset: 0x5847A, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC11application_4open7optionsSbSo13UIApplicationC_10Foundation3URLVSDySo0F17OpenURLOptionsKeyaypGtFTo', symObjAddr: 0x60, symBinAddr: 0x100008060, symSize: 0x15C }
  - { offsetInCU: 0x28D, offset: 0x5850A, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateC11application_8continue18restorationHandlerSbSo13UIApplicationC_So14NSUserActivityCySaySo06UIUserI9Restoring_pGSgctFTo', symObjAddr: 0x1BC, symBinAddr: 0x1000081BC, symSize: 0xFC }
  - { offsetInCU: 0x2FA, offset: 0x58577, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateCACycfcTo', symObjAddr: 0x30C, symBinAddr: 0x10000830C, symSize: 0x48 }
  - { offsetInCU: 0x335, offset: 0x585B2, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateCfD', symObjAddr: 0x354, symBinAddr: 0x100008354, symSize: 0x30 }
  - { offsetInCU: 0x363, offset: 0x585E0, size: 0x8, addend: 0x0, symName: '_$sSo7NSArrayCSgIeyBy_SaySo23UIUserActivityRestoring_pGSgIegg_TR', symObjAddr: 0x2B8, symBinAddr: 0x1000082B8, symSize: 0x54 }
  - { offsetInCU: 0x37B, offset: 0x585F8, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateCfETo', symObjAddr: 0x384, symBinAddr: 0x100008384, symSize: 0x10 }
  - { offsetInCU: 0x3AA, offset: 0x58627, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x394, symBinAddr: 0x100008394, symSize: 0x5C }
  - { offsetInCU: 0x3C8, offset: 0x58645, size: 0x8, addend: 0x0, symName: '_$s3App0A8DelegateCMa', symObjAddr: 0x3F0, symBinAddr: 0x1000083F0, symSize: 0x20 }
  - { offsetInCU: 0x3DC, offset: 0x58659, size: 0x8, addend: 0x0, symName: '_$sSo7NSArrayCSgIeyBy_SaySo23UIUserActivityRestoring_pGSgIegg_TRTA', symObjAddr: 0x434, symBinAddr: 0x100008434, symSize: 0x8 }
  - { offsetInCU: 0x3F0, offset: 0x5866D, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x43C, symBinAddr: 0x10000843C, symSize: 0x40 }
  - { offsetInCU: 0x404, offset: 0x58681, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaMa', symObjAddr: 0x47C, symBinAddr: 0x10000847C, symSize: 0x54 }
  - { offsetInCU: 0x465, offset: 0x586E2, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x77C, symBinAddr: 0x10000877C, symSize: 0x24 }
  - { offsetInCU: 0x479, offset: 0x586F6, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x7A0, symBinAddr: 0x1000087A0, symSize: 0x24 }
  - { offsetInCU: 0x48D, offset: 0x5870A, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSHSCSQWb', symObjAddr: 0x7C4, symBinAddr: 0x1000087C4, symSize: 0x24 }
  - { offsetInCU: 0x4A1, offset: 0x5871E, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo30UIApplicationOpenURLOptionsKeya_Tgq5Tf4nnd_n', symObjAddr: 0x7E8, symBinAddr: 0x1000087E8, symSize: 0x80 }
  - { offsetInCU: 0x4B9, offset: 0x58736, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo30UIApplicationOpenURLOptionsKeya_Tgq5Tf4nnd_n', symObjAddr: 0x868, symBinAddr: 0x100008868, symSize: 0x90 }
  - { offsetInCU: 0x52B, offset: 0x587A8, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4DC, symBinAddr: 0x1000084DC, symSize: 0x4 }
  - { offsetInCU: 0x54B, offset: 0x587C8, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4DC, symBinAddr: 0x1000084DC, symSize: 0x4 }
  - { offsetInCU: 0x55C, offset: 0x587D9, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4E0, symBinAddr: 0x1000084E0, symSize: 0x4 }
  - { offsetInCU: 0x57C, offset: 0x587F9, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x4E0, symBinAddr: 0x1000084E0, symSize: 0x4 }
  - { offsetInCU: 0x58D, offset: 0x5880A, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromE1Cyx01_E5CTypeQzSgFZTW', symObjAddr: 0x4E4, symBinAddr: 0x1000084E4, symSize: 0x40 }
  - { offsetInCU: 0x5BE, offset: 0x5883B, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x56C, symBinAddr: 0x10000856C, symSize: 0x40 }
  - { offsetInCU: 0x5F5, offset: 0x58872, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x5AC, symBinAddr: 0x1000085AC, symSize: 0x70 }
  - { offsetInCU: 0x635, offset: 0x588B2, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x61C, symBinAddr: 0x10000861C, symSize: 0x88 }
  - { offsetInCU: 0x686, offset: 0x58903, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x710, symBinAddr: 0x100008710, symSize: 0x6C }
  - { offsetInCU: 0x703, offset: 0x58980, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSYSCSY8rawValuexSg03RawF0Qz_tcfCTW', symObjAddr: 0x6A4, symBinAddr: 0x1000086A4, symSize: 0x44 }
  - { offsetInCU: 0x743, offset: 0x589C0, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x6E8, symBinAddr: 0x1000086E8, symSize: 0x28 }
  - { offsetInCU: 0x2B, offset: 0x58B76, size: 0x8, addend: 0x0, symName: '_$s3App19ResourceBundleClass33_F7158B72C9BBE77223A1208D6EDC39F1LLCfD', symObjAddr: 0x0, symBinAddr: 0x100008938, symSize: 0x10 }
  - { offsetInCU: 0x55, offset: 0x58BA0, size: 0x8, addend: 0x0, symName: '_$s3App19ResourceBundleClass33_F7158B72C9BBE77223A1208D6EDC39F1LLCfD', symObjAddr: 0x0, symBinAddr: 0x100008938, symSize: 0x10 }
  - { offsetInCU: 0x86, offset: 0x58BD1, size: 0x8, addend: 0x0, symName: '_$s3App19ResourceBundleClass33_F7158B72C9BBE77223A1208D6EDC39F1LLCMa', symObjAddr: 0x10, symBinAddr: 0x100008948, symSize: 0x20 }
...
