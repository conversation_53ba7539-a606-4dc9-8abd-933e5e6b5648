---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/Cordova.framework/Cordova'
relocations:
  - { offsetInCU: 0x34, offset: 0x568E9, size: 0x8, addend: 0x0, symName: _CordovaVersionString, symObjAddr: 0x0, symBinAddr: 0x81F0, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5691E, size: 0x8, addend: 0x0, symName: _CordovaVersionNumber, symObjAddr: 0x28, symBinAddr: 0x8218, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5695B, size: 0x8, addend: 0x0, symName: '-[AppDelegate viewController]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offsetInCU: 0xB4, offset: 0x569E8, size: 0x8, addend: 0x0, symName: '-[AppDelegate viewController]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offsetInCU: 0xEB, offset: 0x56A1F, size: 0x8, addend: 0x0, symName: '-[AppDelegate setViewController:]', symObjAddr: 0x8, symBinAddr: 0x4008, symSize: 0xC }
  - { offsetInCU: 0x12C, offset: 0x56A60, size: 0x8, addend: 0x0, symName: '-[AppDelegate .cxx_destruct]', symObjAddr: 0x14, symBinAddr: 0x4014, symSize: 0xC }
  - { offsetInCU: 0x27, offset: 0x56AD7, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl initWithWebView:pluginManager:]', symObjAddr: 0x0, symBinAddr: 0x4020, symSize: 0x108 }
  - { offsetInCU: 0x1E6, offset: 0x56C96, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl initWithWebView:pluginManager:]', symObjAddr: 0x0, symBinAddr: 0x4020, symSize: 0x108 }
  - { offsetInCU: 0x25C, offset: 0x56D0C, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl pathForResource:]', symObjAddr: 0x108, symBinAddr: 0x4128, symSize: 0x190 }
  - { offsetInCU: 0x2FF, offset: 0x56DAF, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl flushCommandQueueWithDelayedJs]', symObjAddr: 0x298, symBinAddr: 0x42B8, symSize: 0x8 }
  - { offsetInCU: 0x330, offset: 0x56DE0, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl evalJsHelper2:]', symObjAddr: 0x2A0, symBinAddr: 0x42C0, symSize: 0x84 }
  - { offsetInCU: 0x3FD, offset: 0x56EAD, size: 0x8, addend: 0x0, symName: '___40-[CDVCommandDelegateImpl evalJsHelper2:]_block_invoke', symObjAddr: 0x324, symBinAddr: 0x4344, symSize: 0x40 }
  - { offsetInCU: 0x44C, offset: 0x56EFC, size: 0x8, addend: 0x0, symName: '___40-[CDVCommandDelegateImpl evalJsHelper2:]_block_invoke_2', symObjAddr: 0x364, symBinAddr: 0x4384, symSize: 0x5C }
  - { offsetInCU: 0x4AA, offset: 0x56F5A, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s40s, symObjAddr: 0x3C0, symBinAddr: 0x43E0, symSize: 0x28 }
  - { offsetInCU: 0x4D3, offset: 0x56F83, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s40s, symObjAddr: 0x3E8, symBinAddr: 0x4408, symSize: 0x28 }
  - { offsetInCU: 0x4F2, offset: 0x56FA2, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl isValidCallbackId:]', symObjAddr: 0x410, symBinAddr: 0x4430, symSize: 0x94 }
  - { offsetInCU: 0x539, offset: 0x56FE9, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl sendPluginResult:callbackId:]', symObjAddr: 0x4A4, symBinAddr: 0x44C4, symSize: 0x138 }
  - { offsetInCU: 0x5D8, offset: 0x57088, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl evalJs:]', symObjAddr: 0x5DC, symBinAddr: 0x45FC, symSize: 0x8 }
  - { offsetInCU: 0x617, offset: 0x570C7, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl evalJs:scheduledOnRunLoop:]', symObjAddr: 0x5E4, symBinAddr: 0x4604, symSize: 0x58 }
  - { offsetInCU: 0x666, offset: 0x57116, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl getCommandInstance:]', symObjAddr: 0x63C, symBinAddr: 0x465C, symSize: 0x64 }
  - { offsetInCU: 0x6AD, offset: 0x5715D, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl runInBackground:]', symObjAddr: 0x6A0, symBinAddr: 0x46C0, symSize: 0x40 }
  - { offsetInCU: 0x73C, offset: 0x571EC, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl settings]', symObjAddr: 0x6E0, symBinAddr: 0x4700, symSize: 0x40 }
  - { offsetInCU: 0x773, offset: 0x57223, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl urlTransformer]', symObjAddr: 0x720, symBinAddr: 0x4740, symSize: 0x8 }
  - { offsetInCU: 0x7AA, offset: 0x5725A, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl setUrlTransformer:]', symObjAddr: 0x728, symBinAddr: 0x4748, symSize: 0x8 }
  - { offsetInCU: 0x7E9, offset: 0x57299, size: 0x8, addend: 0x0, symName: '-[CDVCommandDelegateImpl .cxx_destruct]', symObjAddr: 0x730, symBinAddr: 0x4750, symSize: 0x48 }
  - { offsetInCU: 0x27, offset: 0x574AA, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser init]', symObjAddr: 0x0, symBinAddr: 0x4798, symSize: 0xDC }
  - { offsetInCU: 0xD8, offset: 0x5755B, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser init]', symObjAddr: 0x0, symBinAddr: 0x4798, symSize: 0xDC }
  - { offsetInCU: 0x10F, offset: 0x57592, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser parser:didStartElement:namespaceURI:qualifiedName:attributes:]', symObjAddr: 0xDC, symBinAddr: 0x4874, symSize: 0x2AC }
  - { offsetInCU: 0x1CD, offset: 0x57650, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser parser:didEndElement:namespaceURI:qualifiedName:]', symObjAddr: 0x388, symBinAddr: 0x4B20, symSize: 0x44 }
  - { offsetInCU: 0x234, offset: 0x576B7, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser parser:parseErrorOccurred:]', symObjAddr: 0x3CC, symBinAddr: 0x4B64, symSize: 0x4 }
  - { offsetInCU: 0x27B, offset: 0x576FE, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser pluginsDict]', symObjAddr: 0x3D0, symBinAddr: 0x4B68, symSize: 0x8 }
  - { offsetInCU: 0x2B2, offset: 0x57735, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser setPluginsDict:]', symObjAddr: 0x3D8, symBinAddr: 0x4B70, symSize: 0xC }
  - { offsetInCU: 0x2F3, offset: 0x57776, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser settings]', symObjAddr: 0x3E4, symBinAddr: 0x4B7C, symSize: 0x8 }
  - { offsetInCU: 0x32A, offset: 0x577AD, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser setSettings:]', symObjAddr: 0x3EC, symBinAddr: 0x4B84, symSize: 0xC }
  - { offsetInCU: 0x36B, offset: 0x577EE, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser startPage]', symObjAddr: 0x3F8, symBinAddr: 0x4B90, symSize: 0x8 }
  - { offsetInCU: 0x3A2, offset: 0x57825, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser setStartPage:]', symObjAddr: 0x400, symBinAddr: 0x4B98, symSize: 0xC }
  - { offsetInCU: 0x3E3, offset: 0x57866, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser startupPluginNames]', symObjAddr: 0x40C, symBinAddr: 0x4BA4, symSize: 0x8 }
  - { offsetInCU: 0x41A, offset: 0x5789D, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser setStartupPluginNames:]', symObjAddr: 0x414, symBinAddr: 0x4BAC, symSize: 0xC }
  - { offsetInCU: 0x45B, offset: 0x578DE, size: 0x8, addend: 0x0, symName: '-[CDVConfigParser .cxx_destruct]', symObjAddr: 0x420, symBinAddr: 0x4BB8, symSize: 0x54 }
  - { offsetInCU: 0x27, offset: 0x57994, size: 0x8, addend: 0x0, symName: '+[CDVInvokedUrlCommand commandFromJson:]', symObjAddr: 0x0, symBinAddr: 0x4C0C, symSize: 0x4C }
  - { offsetInCU: 0xC6, offset: 0x57A33, size: 0x8, addend: 0x0, symName: '+[CDVInvokedUrlCommand commandFromJson:]', symObjAddr: 0x0, symBinAddr: 0x4C0C, symSize: 0x4C }
  - { offsetInCU: 0x109, offset: 0x57A76, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand initFromJson:]', symObjAddr: 0x4C, symBinAddr: 0x4C58, symSize: 0x11C }
  - { offsetInCU: 0x1A0, offset: 0x57B0D, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand initWithArguments:callbackId:className:methodName:]', symObjAddr: 0x168, symBinAddr: 0x4D74, symSize: 0x104 }
  - { offsetInCU: 0x217, offset: 0x57B84, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand massageArguments]', symObjAddr: 0x26C, symBinAddr: 0x4E78, symSize: 0x1C0 }
  - { offsetInCU: 0x2D8, offset: 0x57C45, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand argumentAtIndex:]', symObjAddr: 0x42C, symBinAddr: 0x5038, symSize: 0x8 }
  - { offsetInCU: 0x31B, offset: 0x57C88, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand argumentAtIndex:withDefault:]', symObjAddr: 0x434, symBinAddr: 0x5040, symSize: 0x8 }
  - { offsetInCU: 0x36C, offset: 0x57CD9, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand argumentAtIndex:withDefault:andClass:]', symObjAddr: 0x43C, symBinAddr: 0x5048, symSize: 0xE4 }
  - { offsetInCU: 0x3E3, offset: 0x57D50, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand arguments]', symObjAddr: 0x520, symBinAddr: 0x512C, symSize: 0x8 }
  - { offsetInCU: 0x41A, offset: 0x57D87, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand callbackId]', symObjAddr: 0x528, symBinAddr: 0x5134, symSize: 0x8 }
  - { offsetInCU: 0x451, offset: 0x57DBE, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand className]', symObjAddr: 0x530, symBinAddr: 0x513C, symSize: 0x8 }
  - { offsetInCU: 0x488, offset: 0x57DF5, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand methodName]', symObjAddr: 0x538, symBinAddr: 0x5144, symSize: 0x8 }
  - { offsetInCU: 0x4BF, offset: 0x57E2C, size: 0x8, addend: 0x0, symName: '-[CDVInvokedUrlCommand .cxx_destruct]', symObjAddr: 0x540, symBinAddr: 0x514C, symSize: 0x48 }
  - { offsetInCU: 0x27, offset: 0x57EEB, size: 0x8, addend: 0x0, symName: '-[UIView(org_apache_cordova_UIView_Extension) scrollView]', symObjAddr: 0x0, symBinAddr: 0x5194, symSize: 0x58 }
  - { offsetInCU: 0x41, offset: 0x57F05, size: 0x8, addend: 0x0, symName: _CDVPageDidLoadNotification, symObjAddr: 0xF30, symBinAddr: 0xC178, symSize: 0x0 }
  - { offsetInCU: 0x61, offset: 0x57F25, size: 0x8, addend: 0x0, symName: _CDVPluginHandleOpenURLNotification, symObjAddr: 0xF38, symBinAddr: 0xC180, symSize: 0x0 }
  - { offsetInCU: 0x77, offset: 0x57F3B, size: 0x8, addend: 0x0, symName: _CDVPluginHandleOpenURLWithAppSourceAndAnnotationNotification, symObjAddr: 0xF40, symBinAddr: 0xC188, symSize: 0x0 }
  - { offsetInCU: 0x8D, offset: 0x57F51, size: 0x8, addend: 0x0, symName: _CDVPluginResetNotification, symObjAddr: 0xF48, symBinAddr: 0xC190, symSize: 0x0 }
  - { offsetInCU: 0xA3, offset: 0x57F67, size: 0x8, addend: 0x0, symName: _CDVLocalNotification, symObjAddr: 0xF50, symBinAddr: 0xC198, symSize: 0x0 }
  - { offsetInCU: 0xB9, offset: 0x57F7D, size: 0x8, addend: 0x0, symName: _CDVRemoteNotification, symObjAddr: 0xF58, symBinAddr: 0xC1A0, symSize: 0x0 }
  - { offsetInCU: 0xCF, offset: 0x57F93, size: 0x8, addend: 0x0, symName: _CDVRemoteNotificationError, symObjAddr: 0xF60, symBinAddr: 0xC1A8, symSize: 0x0 }
  - { offsetInCU: 0xE5, offset: 0x57FA9, size: 0x8, addend: 0x0, symName: _CDVViewWillAppearNotification, symObjAddr: 0xF68, symBinAddr: 0xC1B0, symSize: 0x0 }
  - { offsetInCU: 0xFB, offset: 0x57FBF, size: 0x8, addend: 0x0, symName: _CDVViewDidAppearNotification, symObjAddr: 0xF70, symBinAddr: 0xC1B8, symSize: 0x0 }
  - { offsetInCU: 0x111, offset: 0x57FD5, size: 0x8, addend: 0x0, symName: _CDVViewWillDisappearNotification, symObjAddr: 0xF78, symBinAddr: 0xC1C0, symSize: 0x0 }
  - { offsetInCU: 0x127, offset: 0x57FEB, size: 0x8, addend: 0x0, symName: _CDVViewDidDisappearNotification, symObjAddr: 0xF80, symBinAddr: 0xC1C8, symSize: 0x0 }
  - { offsetInCU: 0x13D, offset: 0x58001, size: 0x8, addend: 0x0, symName: _CDVViewWillLayoutSubviewsNotification, symObjAddr: 0xF88, symBinAddr: 0xC1D0, symSize: 0x0 }
  - { offsetInCU: 0x153, offset: 0x58017, size: 0x8, addend: 0x0, symName: _CDVViewDidLayoutSubviewsNotification, symObjAddr: 0xF90, symBinAddr: 0xC1D8, symSize: 0x0 }
  - { offsetInCU: 0x169, offset: 0x5802D, size: 0x8, addend: 0x0, symName: _CDVViewWillTransitionToSizeNotification, symObjAddr: 0xF98, symBinAddr: 0xC1E0, symSize: 0x0 }
  - { offsetInCU: 0x290, offset: 0x58154, size: 0x8, addend: 0x0, symName: '-[UIView(org_apache_cordova_UIView_Extension) scrollView]', symObjAddr: 0x0, symBinAddr: 0x5194, symSize: 0x58 }
  - { offsetInCU: 0x2F8, offset: 0x581BC, size: 0x8, addend: 0x0, symName: '-[CDVPlugin initWithWebViewEngine:]', symObjAddr: 0x58, symBinAddr: 0x51EC, symSize: 0x6C }
  - { offsetInCU: 0x33F, offset: 0x58203, size: 0x8, addend: 0x0, symName: '-[CDVPlugin pluginInitialize]', symObjAddr: 0xC4, symBinAddr: 0x5258, symSize: 0xD4 }
  - { offsetInCU: 0x372, offset: 0x58236, size: 0x8, addend: 0x0, symName: '-[CDVPlugin dispose]', symObjAddr: 0x198, symBinAddr: 0x532C, symSize: 0x30 }
  - { offsetInCU: 0x3A5, offset: 0x58269, size: 0x8, addend: 0x0, symName: '-[CDVPlugin handleOpenURL:]', symObjAddr: 0x1C8, symBinAddr: 0x535C, symSize: 0x48 }
  - { offsetInCU: 0x3F4, offset: 0x582B8, size: 0x8, addend: 0x0, symName: '-[CDVPlugin onAppTerminate]', symObjAddr: 0x210, symBinAddr: 0x53A4, symSize: 0x4 }
  - { offsetInCU: 0x423, offset: 0x582E7, size: 0x8, addend: 0x0, symName: '-[CDVPlugin onMemoryWarning]', symObjAddr: 0x214, symBinAddr: 0x53A8, symSize: 0x4 }
  - { offsetInCU: 0x452, offset: 0x58316, size: 0x8, addend: 0x0, symName: '-[CDVPlugin onReset]', symObjAddr: 0x218, symBinAddr: 0x53AC, symSize: 0x4 }
  - { offsetInCU: 0x481, offset: 0x58345, size: 0x8, addend: 0x0, symName: '-[CDVPlugin dealloc]', symObjAddr: 0x21C, symBinAddr: 0x53B0, symSize: 0x68 }
  - { offsetInCU: 0x4B4, offset: 0x58378, size: 0x8, addend: 0x0, symName: '-[CDVPlugin appDelegate]', symObjAddr: 0x284, symBinAddr: 0x5418, symSize: 0x4C }
  - { offsetInCU: 0x4E7, offset: 0x583AB, size: 0x8, addend: 0x0, symName: '-[CDVPlugin webViewEngine]', symObjAddr: 0x2D0, symBinAddr: 0x5464, symSize: 0x18 }
  - { offsetInCU: 0x51E, offset: 0x583E2, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setWebViewEngine:]', symObjAddr: 0x2E8, symBinAddr: 0x547C, symSize: 0xC }
  - { offsetInCU: 0x55F, offset: 0x58423, size: 0x8, addend: 0x0, symName: '-[CDVPlugin viewController]', symObjAddr: 0x2F4, symBinAddr: 0x5488, symSize: 0x18 }
  - { offsetInCU: 0x596, offset: 0x5845A, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setViewController:]', symObjAddr: 0x30C, symBinAddr: 0x54A0, symSize: 0xC }
  - { offsetInCU: 0x5D7, offset: 0x5849B, size: 0x8, addend: 0x0, symName: '-[CDVPlugin commandDelegate]', symObjAddr: 0x318, symBinAddr: 0x54AC, symSize: 0x18 }
  - { offsetInCU: 0x60E, offset: 0x584D2, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setCommandDelegate:]', symObjAddr: 0x330, symBinAddr: 0x54C4, symSize: 0xC }
  - { offsetInCU: 0x64F, offset: 0x58513, size: 0x8, addend: 0x0, symName: '-[CDVPlugin hasPendingOperation]', symObjAddr: 0x33C, symBinAddr: 0x54D0, symSize: 0xC }
  - { offsetInCU: 0x686, offset: 0x5854A, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setHasPendingOperation:]', symObjAddr: 0x348, symBinAddr: 0x54DC, symSize: 0x8 }
  - { offsetInCU: 0x6C3, offset: 0x58587, size: 0x8, addend: 0x0, symName: '-[CDVPlugin webView]', symObjAddr: 0x350, symBinAddr: 0x54E4, symSize: 0x18 }
  - { offsetInCU: 0x6FA, offset: 0x585BE, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setWebView:]', symObjAddr: 0x368, symBinAddr: 0x54FC, symSize: 0xC }
  - { offsetInCU: 0x73B, offset: 0x585FF, size: 0x8, addend: 0x0, symName: '-[CDVPlugin className]', symObjAddr: 0x374, symBinAddr: 0x5508, symSize: 0x8 }
  - { offsetInCU: 0x772, offset: 0x58636, size: 0x8, addend: 0x0, symName: '-[CDVPlugin setClassName:]', symObjAddr: 0x37C, symBinAddr: 0x5510, symSize: 0xC }
  - { offsetInCU: 0x7B3, offset: 0x58677, size: 0x8, addend: 0x0, symName: '-[CDVPlugin .cxx_destruct]', symObjAddr: 0x388, symBinAddr: 0x551C, symSize: 0x44 }
  - { offsetInCU: 0x27, offset: 0x586EF, size: 0x8, addend: 0x0, symName: '-[CDVPlugin(CDVPluginResources) pluginLocalizedString:]', symObjAddr: 0x0, symBinAddr: 0x5560, symSize: 0xF0 }
  - { offsetInCU: 0x4A, offset: 0x58712, size: 0x8, addend: 0x0, symName: '-[CDVPlugin(CDVPluginResources) pluginLocalizedString:]', symObjAddr: 0x0, symBinAddr: 0x5560, symSize: 0xF0 }
  - { offsetInCU: 0xDA, offset: 0x587A2, size: 0x8, addend: 0x0, symName: '-[CDVPlugin(CDVPluginResources) pluginImageResource:]', symObjAddr: 0xF0, symBinAddr: 0x5650, symSize: 0xB0 }
  - { offsetInCU: 0x27, offset: 0x588D4, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager initWithParser:viewController:webView:]', symObjAddr: 0x0, symBinAddr: 0x5700, symSize: 0x1D8 }
  - { offsetInCU: 0x12F, offset: 0x589DC, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager initWithParser:viewController:webView:]', symObjAddr: 0x0, symBinAddr: 0x5700, symSize: 0x1D8 }
  - { offsetInCU: 0x196, offset: 0x58A43, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager getCommandInstance:]', symObjAddr: 0x1D8, symBinAddr: 0x58D8, symSize: 0x224 }
  - { offsetInCU: 0x269, offset: 0x58B16, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager registerPlugin:withClassName:]', symObjAddr: 0x3FC, symBinAddr: 0x5AFC, symSize: 0xF4 }
  - { offsetInCU: 0x2BC, offset: 0x58B69, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager onAppDidEnterBackground:]', symObjAddr: 0x4F0, symBinAddr: 0x5BF0, symSize: 0x38 }
  - { offsetInCU: 0x2FB, offset: 0x58BA8, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager onAppWillEnterForeground:]', symObjAddr: 0x528, symBinAddr: 0x5C28, symSize: 0x38 }
  - { offsetInCU: 0x33A, offset: 0x58BE7, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager pluginsMap]', symObjAddr: 0x560, symBinAddr: 0x5C60, symSize: 0x8 }
  - { offsetInCU: 0x371, offset: 0x58C1E, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setPluginsMap:]', symObjAddr: 0x568, symBinAddr: 0x5C68, symSize: 0xC }
  - { offsetInCU: 0x3B2, offset: 0x58C5F, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager pluginObjects]', symObjAddr: 0x574, symBinAddr: 0x5C74, symSize: 0x8 }
  - { offsetInCU: 0x3E9, offset: 0x58C96, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setPluginObjects:]', symObjAddr: 0x57C, symBinAddr: 0x5C7C, symSize: 0xC }
  - { offsetInCU: 0x42A, offset: 0x58CD7, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager settings]', symObjAddr: 0x588, symBinAddr: 0x5C88, symSize: 0x8 }
  - { offsetInCU: 0x461, offset: 0x58D0E, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setSettings:]', symObjAddr: 0x590, symBinAddr: 0x5C90, symSize: 0xC }
  - { offsetInCU: 0x4A2, offset: 0x58D4F, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager viewController]', symObjAddr: 0x59C, symBinAddr: 0x5C9C, symSize: 0x18 }
  - { offsetInCU: 0x4D9, offset: 0x58D86, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setViewController:]', symObjAddr: 0x5B4, symBinAddr: 0x5CB4, symSize: 0xC }
  - { offsetInCU: 0x51A, offset: 0x58DC7, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager webView]', symObjAddr: 0x5C0, symBinAddr: 0x5CC0, symSize: 0x18 }
  - { offsetInCU: 0x551, offset: 0x58DFE, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setWebView:]', symObjAddr: 0x5D8, symBinAddr: 0x5CD8, symSize: 0xC }
  - { offsetInCU: 0x592, offset: 0x58E3F, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager commandDelegate]', symObjAddr: 0x5E4, symBinAddr: 0x5CE4, symSize: 0x8 }
  - { offsetInCU: 0x5C9, offset: 0x58E76, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager setCommandDelegate:]', symObjAddr: 0x5EC, symBinAddr: 0x5CEC, symSize: 0xC }
  - { offsetInCU: 0x60A, offset: 0x58EB7, size: 0x8, addend: 0x0, symName: '-[CDVPluginManager .cxx_destruct]', symObjAddr: 0x5F8, symBinAddr: 0x5CF8, symSize: 0x58 }
  - { offsetInCU: 0x27, offset: 0x58FE9, size: 0x8, addend: 0x0, symName: _messageFromArrayBuffer, symObjAddr: 0x0, symBinAddr: 0x5D50, symSize: 0xB4 }
  - { offsetInCU: 0x41, offset: 0x59003, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_NO_RESULT, symObjAddr: 0xB60, symBinAddr: 0x8228, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x59079, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_OK, symObjAddr: 0xB68, symBinAddr: 0x8230, symSize: 0x0 }
  - { offsetInCU: 0xCD, offset: 0x5908F, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_CLASS_NOT_FOUND_EXCEPTION, symObjAddr: 0xB70, symBinAddr: 0x8238, symSize: 0x0 }
  - { offsetInCU: 0xE3, offset: 0x590A5, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_ILLEGAL_ACCESS_EXCEPTION, symObjAddr: 0xB78, symBinAddr: 0x8240, symSize: 0x0 }
  - { offsetInCU: 0xF9, offset: 0x590BB, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_INSTANTIATION_EXCEPTION, symObjAddr: 0xB80, symBinAddr: 0x8248, symSize: 0x0 }
  - { offsetInCU: 0x10F, offset: 0x590D1, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_MALFORMED_URL_EXCEPTION, symObjAddr: 0xB88, symBinAddr: 0x8250, symSize: 0x0 }
  - { offsetInCU: 0x125, offset: 0x590E7, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_IO_EXCEPTION, symObjAddr: 0xB90, symBinAddr: 0x8258, symSize: 0x0 }
  - { offsetInCU: 0x13B, offset: 0x590FD, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_INVALID_ACTION, symObjAddr: 0xB98, symBinAddr: 0x8260, symSize: 0x0 }
  - { offsetInCU: 0x151, offset: 0x59113, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_JSON_EXCEPTION, symObjAddr: 0xBA0, symBinAddr: 0x8268, symSize: 0x0 }
  - { offsetInCU: 0x167, offset: 0x59129, size: 0x8, addend: 0x0, symName: _SWIFT_CDVCommandStatus_ERROR, symObjAddr: 0xBA8, symBinAddr: 0x8270, symSize: 0x0 }
  - { offsetInCU: 0x17D, offset: 0x5913F, size: 0x8, addend: 0x0, symName: _org_apache_cordova_CommandStatusMsgs, symObjAddr: 0x58C0, symBinAddr: 0x120E0, symSize: 0x0 }
  - { offsetInCU: 0x198, offset: 0x5915A, size: 0x8, addend: 0x0, symName: _gIsVerbose, symObjAddr: 0x58C8, symBinAddr: 0x120E8, symSize: 0x0 }
  - { offsetInCU: 0x25E, offset: 0x59220, size: 0x8, addend: 0x0, symName: _messageFromArrayBuffer, symObjAddr: 0x0, symBinAddr: 0x5D50, symSize: 0xB4 }
  - { offsetInCU: 0x289, offset: 0x5924B, size: 0x8, addend: 0x0, symName: _massageMessage, symObjAddr: 0xB4, symBinAddr: 0x5E04, symSize: 0x68 }
  - { offsetInCU: 0x2CA, offset: 0x5928C, size: 0x8, addend: 0x0, symName: _messageFromMultipart, symObjAddr: 0x11C, symBinAddr: 0x5E6C, symSize: 0x128 }
  - { offsetInCU: 0x332, offset: 0x592F4, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult initialize]', symObjAddr: 0x244, symBinAddr: 0x5F94, symSize: 0x9C }
  - { offsetInCU: 0x361, offset: 0x59323, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult init]', symObjAddr: 0x2E0, symBinAddr: 0x6030, symSize: 0xC }
  - { offsetInCU: 0x396, offset: 0x59358, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult initWithStatus:message:]', symObjAddr: 0x2EC, symBinAddr: 0x603C, symSize: 0xD0 }
  - { offsetInCU: 0x3ED, offset: 0x593AF, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:]', symObjAddr: 0x3BC, symBinAddr: 0x610C, symSize: 0x2C }
  - { offsetInCU: 0x434, offset: 0x593F6, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsString:]', symObjAddr: 0x3E8, symBinAddr: 0x6138, symSize: 0x58 }
  - { offsetInCU: 0x48B, offset: 0x5944D, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsArray:]', symObjAddr: 0x440, symBinAddr: 0x6190, symSize: 0x58 }
  - { offsetInCU: 0x4E2, offset: 0x594A4, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsInt:]', symObjAddr: 0x498, symBinAddr: 0x61E8, symSize: 0x6C }
  - { offsetInCU: 0x539, offset: 0x594FB, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsNSInteger:]', symObjAddr: 0x504, symBinAddr: 0x6254, symSize: 0x6C }
  - { offsetInCU: 0x590, offset: 0x59552, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsNSUInteger:]', symObjAddr: 0x570, symBinAddr: 0x62C0, symSize: 0x6C }
  - { offsetInCU: 0x5E7, offset: 0x595A9, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsDouble:]', symObjAddr: 0x5DC, symBinAddr: 0x632C, symSize: 0x74 }
  - { offsetInCU: 0x63E, offset: 0x59600, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsBool:]', symObjAddr: 0x650, symBinAddr: 0x63A0, symSize: 0x6C }
  - { offsetInCU: 0x695, offset: 0x59657, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsDictionary:]', symObjAddr: 0x6BC, symBinAddr: 0x640C, symSize: 0x58 }
  - { offsetInCU: 0x6EC, offset: 0x596AE, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsArrayBuffer:]', symObjAddr: 0x714, symBinAddr: 0x6464, symSize: 0x7C }
  - { offsetInCU: 0x759, offset: 0x5971B, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageAsMultipart:]', symObjAddr: 0x790, symBinAddr: 0x64E0, symSize: 0x7C }
  - { offsetInCU: 0x7C6, offset: 0x59788, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult resultWithStatus:messageToErrorObject:]', symObjAddr: 0x80C, symBinAddr: 0x655C, symSize: 0xD8 }
  - { offsetInCU: 0x82D, offset: 0x597EF, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult setKeepCallbackAsBool:]', symObjAddr: 0x8E4, symBinAddr: 0x6634, symSize: 0x44 }
  - { offsetInCU: 0x870, offset: 0x59832, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult argumentsAsJSON]', symObjAddr: 0x928, symBinAddr: 0x6678, symSize: 0xDC }
  - { offsetInCU: 0x8D3, offset: 0x59895, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult setVerbose:]', symObjAddr: 0xA04, symBinAddr: 0x6754, symSize: 0xC }
  - { offsetInCU: 0x90E, offset: 0x598D0, size: 0x8, addend: 0x0, symName: '+[CDVPluginResult isVerbose]', symObjAddr: 0xA10, symBinAddr: 0x6760, symSize: 0xC }
  - { offsetInCU: 0x941, offset: 0x59903, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult JSONStringFromArray:]', symObjAddr: 0xA1C, symBinAddr: 0x676C, symSize: 0xC0 }
  - { offsetInCU: 0x9A4, offset: 0x59966, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult status]', symObjAddr: 0xADC, symBinAddr: 0x682C, symSize: 0x8 }
  - { offsetInCU: 0x9DB, offset: 0x5999D, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult message]', symObjAddr: 0xAE4, symBinAddr: 0x6834, symSize: 0x8 }
  - { offsetInCU: 0xA12, offset: 0x599D4, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult keepCallback]', symObjAddr: 0xAEC, symBinAddr: 0x683C, symSize: 0x8 }
  - { offsetInCU: 0xA49, offset: 0x59A0B, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult setKeepCallback:]', symObjAddr: 0xAF4, symBinAddr: 0x6844, symSize: 0xC }
  - { offsetInCU: 0xA8A, offset: 0x59A4C, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult associatedObject]', symObjAddr: 0xB00, symBinAddr: 0x6850, symSize: 0x8 }
  - { offsetInCU: 0xAC1, offset: 0x59A83, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult setAssociatedObject:]', symObjAddr: 0xB08, symBinAddr: 0x6858, symSize: 0xC }
  - { offsetInCU: 0xB02, offset: 0x59AC4, size: 0x8, addend: 0x0, symName: '-[CDVPluginResult .cxx_destruct]', symObjAddr: 0xB14, symBinAddr: 0x6864, symSize: 0x48 }
  - { offsetInCU: 0x27, offset: 0x59B77, size: 0x8, addend: 0x0, symName: '+[CDVURLProtocol canInitWithRequest:]', symObjAddr: 0x0, symBinAddr: 0x68AC, symSize: 0x8 }
  - { offsetInCU: 0x41, offset: 0x59B91, size: 0x8, addend: 0x0, symName: _kCDVAssetsLibraryPrefixes, symObjAddr: 0x2A8, symBinAddr: 0xC1E8, symSize: 0x0 }
  - { offsetInCU: 0x7B, offset: 0x59BCB, size: 0x8, addend: 0x0, symName: '+[CDVURLProtocol canInitWithRequest:]', symObjAddr: 0x0, symBinAddr: 0x68AC, symSize: 0x8 }
  - { offsetInCU: 0xBA, offset: 0x59C0A, size: 0x8, addend: 0x0, symName: '+[CDVURLProtocol canonicalRequestForRequest:]', symObjAddr: 0x8, symBinAddr: 0x68B4, symSize: 0x18 }
  - { offsetInCU: 0xFD, offset: 0x59C4D, size: 0x8, addend: 0x0, symName: '-[CDVURLProtocol startLoading]', symObjAddr: 0x20, symBinAddr: 0x68CC, symSize: 0x4 }
  - { offsetInCU: 0x12C, offset: 0x59C7C, size: 0x8, addend: 0x0, symName: '-[CDVURLProtocol stopLoading]', symObjAddr: 0x24, symBinAddr: 0x68D0, symSize: 0x4 }
  - { offsetInCU: 0x15B, offset: 0x59CAB, size: 0x8, addend: 0x0, symName: '+[CDVURLProtocol requestIsCacheEquivalent:toRequest:]', symObjAddr: 0x28, symBinAddr: 0x68D4, symSize: 0x8 }
  - { offsetInCU: 0x1A6, offset: 0x59CF6, size: 0x8, addend: 0x0, symName: '-[CDVURLProtocol sendResponseWithResponseCode:data:mimeType:]', symObjAddr: 0x30, symBinAddr: 0x68DC, symSize: 0x1C4 }
  - { offsetInCU: 0x27, offset: 0x59DE2, size: 0x8, addend: 0x0, symName: '-[CDVViewController pluginObjects]', symObjAddr: 0x0, symBinAddr: 0x6AA0, symSize: 0x10 }
  - { offsetInCU: 0xA5, offset: 0x59E60, size: 0x8, addend: 0x0, symName: '-[CDVViewController pluginObjects]', symObjAddr: 0x0, symBinAddr: 0x6AA0, symSize: 0x10 }
  - { offsetInCU: 0xDC, offset: 0x59E97, size: 0x8, addend: 0x0, symName: '-[CDVViewController setPluginObjects:]', symObjAddr: 0x10, symBinAddr: 0x6AB0, symSize: 0x14 }
  - { offsetInCU: 0x11D, offset: 0x59ED8, size: 0x8, addend: 0x0, symName: '-[CDVViewController settings]', symObjAddr: 0x24, symBinAddr: 0x6AC4, symSize: 0x10 }
  - { offsetInCU: 0x154, offset: 0x59F0F, size: 0x8, addend: 0x0, symName: '-[CDVViewController webView]', symObjAddr: 0x34, symBinAddr: 0x6AD4, symSize: 0x20 }
  - { offsetInCU: 0x18B, offset: 0x59F46, size: 0x8, addend: 0x0, symName: '-[CDVViewController .cxx_destruct]', symObjAddr: 0x54, symBinAddr: 0x6AF4, symSize: 0x50 }
  - { offsetInCU: 0x27, offset: 0x59FBD, size: 0x8, addend: 0x0, symName: '+[CDVWebViewProcessPoolFactory sharedFactory]', symObjAddr: 0x0, symBinAddr: 0x6B44, symSize: 0x40 }
  - { offsetInCU: 0x41, offset: 0x59FD7, size: 0x8, addend: 0x0, symName: _factory, symObjAddr: 0x22B0, symBinAddr: 0x120F0, symSize: 0x0 }
  - { offsetInCU: 0x80, offset: 0x5A016, size: 0x8, addend: 0x0, symName: '+[CDVWebViewProcessPoolFactory sharedFactory]', symObjAddr: 0x0, symBinAddr: 0x6B44, symSize: 0x40 }
  - { offsetInCU: 0xAA, offset: 0x5A040, size: 0x8, addend: 0x0, symName: _sharedFactory.onceToken, symObjAddr: 0x22B8, symBinAddr: 0x120F8, symSize: 0x0 }
  - { offsetInCU: 0x16E, offset: 0x5A104, size: 0x8, addend: 0x0, symName: '___45+[CDVWebViewProcessPoolFactory sharedFactory]_block_invoke', symObjAddr: 0x40, symBinAddr: 0x6B84, symSize: 0x2C }
  - { offsetInCU: 0x195, offset: 0x5A12B, size: 0x8, addend: 0x0, symName: '-[CDVWebViewProcessPoolFactory init]', symObjAddr: 0x6C, symBinAddr: 0x6BB0, symSize: 0x64 }
  - { offsetInCU: 0x1CC, offset: 0x5A162, size: 0x8, addend: 0x0, symName: '-[CDVWebViewProcessPoolFactory sharedProcessPool]', symObjAddr: 0xD0, symBinAddr: 0x6C14, symSize: 0x8 }
  - { offsetInCU: 0x203, offset: 0x5A199, size: 0x8, addend: 0x0, symName: '-[CDVWebViewProcessPoolFactory sharedPool]', symObjAddr: 0xD8, symBinAddr: 0x6C1C, symSize: 0x8 }
  - { offsetInCU: 0x23A, offset: 0x5A1D0, size: 0x8, addend: 0x0, symName: '-[CDVWebViewProcessPoolFactory setSharedPool:]', symObjAddr: 0xE0, symBinAddr: 0x6C24, symSize: 0xC }
  - { offsetInCU: 0x27B, offset: 0x5A211, size: 0x8, addend: 0x0, symName: '-[CDVWebViewProcessPoolFactory .cxx_destruct]', symObjAddr: 0xEC, symBinAddr: 0x6C30, symSize: 0xC }
  - { offsetInCU: 0x27, offset: 0x5A30E, size: 0x8, addend: 0x0, symName: '-[NSDictionary(CordovaPreferences) cordovaSettingForKey:]', symObjAddr: 0x0, symBinAddr: 0x6C3C, symSize: 0x54 }
  - { offsetInCU: 0x43, offset: 0x5A32A, size: 0x8, addend: 0x0, symName: '-[NSDictionary(CordovaPreferences) cordovaSettingForKey:]', symObjAddr: 0x0, symBinAddr: 0x6C3C, symSize: 0x54 }
  - { offsetInCU: 0x8A, offset: 0x5A371, size: 0x8, addend: 0x0, symName: '-[NSDictionary(CordovaPreferences) cordovaBoolSettingForKey:defaultValue:]', symObjAddr: 0x54, symBinAddr: 0x6C90, symSize: 0x48 }
  - { offsetInCU: 0x101, offset: 0x5A3E8, size: 0x8, addend: 0x0, symName: '-[NSDictionary(CordovaPreferences) cordovaFloatSettingForKey:defaultValue:]', symObjAddr: 0x9C, symBinAddr: 0x6CD8, symSize: 0x50 }
  - { offsetInCU: 0x178, offset: 0x5A45F, size: 0x8, addend: 0x0, symName: '-[NSMutableDictionary(CordovaPreferences) setCordovaSetting:forKey:]', symObjAddr: 0xEC, symBinAddr: 0x6D28, symSize: 0x64 }
...
