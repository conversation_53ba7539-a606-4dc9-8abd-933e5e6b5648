---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorHaptics.framework/CapacitorHaptics'
relocations:
  - { offsetInCU: 0x34, offset: 0x62DE2, size: 0x8, addend: 0x0, symName: _CapacitorHapticsVersionString, symObjAddr: 0x0, symBinAddr: 0x88F0, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x62E17, size: 0x8, addend: 0x0, symName: _CapacitorHapticsVersionNumber, symObjAddr: 0x30, symBinAddr: 0x8920, symSize: 0x0 }
  - { offsetInCU: 0x3F, offset: 0x62E6C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C26selectionFeedbackGeneratorSo011UISelectiondE0CSgvpfi', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offsetInCU: 0x63, offset: 0x62E90, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C6impactyySo21UIImpactFeedbackStyleVF', symObjAddr: 0x8, symBinAddr: 0x4008, symSize: 0x4C }
  - { offsetInCU: 0x111, offset: 0x62F3E, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C6impactyySo21UIImpactFeedbackStyleVFTo', symObjAddr: 0x54, symBinAddr: 0x4054, symSize: 0x78 }
  - { offsetInCU: 0x181, offset: 0x62FAE, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12notificationyySo26UINotificationFeedbackTypeVF', symObjAddr: 0xCC, symBinAddr: 0x40CC, symSize: 0x4C }
  - { offsetInCU: 0x22F, offset: 0x6305C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12notificationyySo26UINotificationFeedbackTypeVFTo', symObjAddr: 0x118, symBinAddr: 0x4118, symSize: 0x78 }
  - { offsetInCU: 0x2E7, offset: 0x63114, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C14selectionStartyyF', symObjAddr: 0x190, symBinAddr: 0x4190, symSize: 0x64 }
  - { offsetInCU: 0x392, offset: 0x631BF, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C14selectionStartyyFTo', symObjAddr: 0x1F4, symBinAddr: 0x41F4, symSize: 0x84 }
  - { offsetInCU: 0x414, offset: 0x63241, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C16selectionChangedyyF', symObjAddr: 0x278, symBinAddr: 0x4278, symSize: 0x74 }
  - { offsetInCU: 0x49E, offset: 0x632CB, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C16selectionChangedyyFTo', symObjAddr: 0x2EC, symBinAddr: 0x42EC, symSize: 0x84 }
  - { offsetInCU: 0x500, offset: 0x6332D, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12selectionEndyyF', symObjAddr: 0x370, symBinAddr: 0x4370, symSize: 0x14 }
  - { offsetInCU: 0x520, offset: 0x6334D, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12selectionEndyyF', symObjAddr: 0x370, symBinAddr: 0x4370, symSize: 0x14 }
  - { offsetInCU: 0x560, offset: 0x6338D, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12selectionEndyyFTo', symObjAddr: 0x384, symBinAddr: 0x4384, symSize: 0x18 }
  - { offsetInCU: 0x580, offset: 0x633AD, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12selectionEndyyFTo', symObjAddr: 0x384, symBinAddr: 0x4384, symSize: 0x18 }
  - { offsetInCU: 0x59D, offset: 0x633CA, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C12selectionEndyyFTo', symObjAddr: 0x384, symBinAddr: 0x4384, symSize: 0x18 }
  - { offsetInCU: 0x5C0, offset: 0x633ED, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyyF', symObjAddr: 0x39C, symBinAddr: 0x439C, symSize: 0x8 }
  - { offsetInCU: 0x5F9, offset: 0x63426, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyyFTo', symObjAddr: 0x3A4, symBinAddr: 0x43A4, symSize: 0x8 }
  - { offsetInCU: 0x619, offset: 0x63446, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyyFTo', symObjAddr: 0x3A4, symBinAddr: 0x43A4, symSize: 0x8 }
  - { offsetInCU: 0x636, offset: 0x63463, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyySdF', symObjAddr: 0x3AC, symBinAddr: 0x43AC, symSize: 0x560 }
  - { offsetInCU: 0x88D, offset: 0x636BA, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyySdFyycfU_', symObjAddr: 0x90C, symBinAddr: 0x490C, symSize: 0xD8 }
  - { offsetInCU: 0x8DE, offset: 0x6370B, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyySdFTo', symObjAddr: 0xA5C, symBinAddr: 0x4A5C, symSize: 0x3C }
  - { offsetInCU: 0x8FA, offset: 0x63727, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CACycfC', symObjAddr: 0xA98, symBinAddr: 0x4A98, symSize: 0x20 }
  - { offsetInCU: 0x90E, offset: 0x6373B, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CACycfc', symObjAddr: 0xAB8, symBinAddr: 0x4AB8, symSize: 0x3C }
  - { offsetInCU: 0x949, offset: 0x63776, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CACycfcTo', symObjAddr: 0xAF4, symBinAddr: 0x4AF4, symSize: 0x48 }
  - { offsetInCU: 0x984, offset: 0x637B1, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CfD', symObjAddr: 0xB3C, symBinAddr: 0x4B3C, symSize: 0x30 }
  - { offsetInCU: 0xA1B, offset: 0x63848, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0C7vibrateyySdFyycfU_TA', symObjAddr: 0xA10, symBinAddr: 0x4A10, symSize: 0x8 }
  - { offsetInCU: 0xA2F, offset: 0x6385C, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0xA18, symBinAddr: 0x4A18, symSize: 0x2C }
  - { offsetInCU: 0xA47, offset: 0x63874, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xA44, symBinAddr: 0x4A44, symSize: 0x10 }
  - { offsetInCU: 0xA5B, offset: 0x63888, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xA54, symBinAddr: 0x4A54, symSize: 0x8 }
  - { offsetInCU: 0xA6F, offset: 0x6389C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CfETo', symObjAddr: 0xB6C, symBinAddr: 0x4B6C, symSize: 0x10 }
  - { offsetInCU: 0xA9E, offset: 0x638CB, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC10identifierSSvpfi', symObjAddr: 0xB7C, symBinAddr: 0x4B7C, symSize: 0x24 }
  - { offsetInCU: 0xAB6, offset: 0x638E3, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6jsNameSSvpfi', symObjAddr: 0xBA0, symBinAddr: 0x4BA0, symSize: 0x18 }
  - { offsetInCU: 0xAFA, offset: 0x63927, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvpfi', symObjAddr: 0xBB8, symBinAddr: 0x4BB8, symSize: 0x350 }
  - { offsetInCU: 0xC83, offset: 0x63AB0, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC14implementation33_A9C68EC01C54EFB1A27799A130670B29LLAA0B0Cvpfi', symObjAddr: 0xF08, symBinAddr: 0x4F08, symSize: 0x20 }
  - { offsetInCU: 0xCBC, offset: 0x63AE9, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo22CHHapticEventParameterC_Tg5Tf4d_n', symObjAddr: 0x106C, symBinAddr: 0x506C, symSize: 0x64 }
  - { offsetInCU: 0xCE9, offset: 0x63B16, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo13CHHapticEventC_Tg5Tf4d_n', symObjAddr: 0x10D0, symBinAddr: 0x50D0, symSize: 0x64 }
  - { offsetInCU: 0xD16, offset: 0x63B43, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B0CMa', symObjAddr: 0x1134, symBinAddr: 0x5134, symSize: 0x20 }
  - { offsetInCU: 0xD2A, offset: 0x63B57, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo15CAPPluginMethodC_Tg5Tf4d_n', symObjAddr: 0x1154, symBinAddr: 0x5154, symSize: 0x64 }
  - { offsetInCU: 0xD57, offset: 0x63B84, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x1224, symBinAddr: 0x5224, symSize: 0x40 }
  - { offsetInCU: 0xFE3, offset: 0x63E10, size: 0x8, addend: 0x0, symName: '_$sSo15CHHapticPatternC6events10parametersABSaySo0A5EventCG_SaySo0A16DynamicParameterCGtKcfcTO', symObjAddr: 0xF28, symBinAddr: 0x4F28, symSize: 0x144 }
  - { offsetInCU: 0x6D, offset: 0x63FEB, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvgTo', symObjAddr: 0xAC, symBinAddr: 0x5348, symSize: 0x60 }
  - { offsetInCU: 0xA8, offset: 0x64026, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvg', symObjAddr: 0x10C, symBinAddr: 0x53A8, symSize: 0x10 }
  - { offsetInCU: 0xC5, offset: 0x64043, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6impactyySo13CAPPluginCallCF', symObjAddr: 0x11C, symBinAddr: 0x53B8, symSize: 0x3C8 }
  - { offsetInCU: 0x1F8, offset: 0x64176, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6impactyySo13CAPPluginCallCFyyScMYccfU_Tf2ni_n', symObjAddr: 0x4E4, symBinAddr: 0x5780, symSize: 0x4C }
  - { offsetInCU: 0x295, offset: 0x64213, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6impactyySo13CAPPluginCallCFTo', symObjAddr: 0x530, symBinAddr: 0x57CC, symSize: 0x50 }
  - { offsetInCU: 0x2B1, offset: 0x6422F, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12notificationyySo13CAPPluginCallCF', symObjAddr: 0x580, symBinAddr: 0x581C, symSize: 0x3CC }
  - { offsetInCU: 0x3E4, offset: 0x64362, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12notificationyySo13CAPPluginCallCFyyScMYccfU_Tf2ni_n', symObjAddr: 0x94C, symBinAddr: 0x5BE8, symSize: 0x4C }
  - { offsetInCU: 0x481, offset: 0x643FF, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12notificationyySo13CAPPluginCallCFTo', symObjAddr: 0x998, symBinAddr: 0x5C34, symSize: 0x50 }
  - { offsetInCU: 0x49F, offset: 0x6441D, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC14selectionStartyySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0xA04, symBinAddr: 0x5CA0, symSize: 0x74 }
  - { offsetInCU: 0x4EE, offset: 0x6446C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC14selectionStartyySo13CAPPluginCallCFTo', symObjAddr: 0xA78, symBinAddr: 0x5D14, symSize: 0x50 }
  - { offsetInCU: 0x50C, offset: 0x6448A, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC16selectionChangedyySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0xCD8, symBinAddr: 0x5F74, symSize: 0x84 }
  - { offsetInCU: 0x542, offset: 0x644C0, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC16selectionChangedyySo13CAPPluginCallCFTo', symObjAddr: 0xD5C, symBinAddr: 0x5FF8, symSize: 0x50 }
  - { offsetInCU: 0x55E, offset: 0x644DC, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12selectionEndyySo13CAPPluginCallCFTo', symObjAddr: 0xDC8, symBinAddr: 0x6064, symSize: 0x50 }
  - { offsetInCU: 0x57A, offset: 0x644F8, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC7vibrateyySo13CAPPluginCallCF', symObjAddr: 0xE18, symBinAddr: 0x60B4, symSize: 0x270 }
  - { offsetInCU: 0x64A, offset: 0x645C8, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC7vibrateyySo13CAPPluginCallCFTo', symObjAddr: 0x1088, symBinAddr: 0x6324, symSize: 0x50 }
  - { offsetInCU: 0x666, offset: 0x645E4, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x10D8, symBinAddr: 0x6374, symSize: 0xB4 }
  - { offsetInCU: 0x684, offset: 0x64602, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x118C, symBinAddr: 0x6428, symSize: 0x484 }
  - { offsetInCU: 0x781, offset: 0x646FF, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x1610, symBinAddr: 0x68AC, symSize: 0x74 }
  - { offsetInCU: 0x79D, offset: 0x6471B, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCACycfC', symObjAddr: 0x1684, symBinAddr: 0x6920, symSize: 0x20 }
  - { offsetInCU: 0x7BB, offset: 0x64739, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCACycfc', symObjAddr: 0x16A4, symBinAddr: 0x6940, symSize: 0x40C }
  - { offsetInCU: 0x88E, offset: 0x6480C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCACycfcTo', symObjAddr: 0x1AB0, symBinAddr: 0x6D4C, symSize: 0x20 }
  - { offsetInCU: 0x8AA, offset: 0x64828, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCfD', symObjAddr: 0x1AD0, symBinAddr: 0x6D6C, symSize: 0x30 }
  - { offsetInCU: 0xA6B, offset: 0x649E9, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCfETo', symObjAddr: 0x1B00, symBinAddr: 0x6D9C, symSize: 0x60 }
  - { offsetInCU: 0xAA5, offset: 0x64A23, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x1B60, symBinAddr: 0x6DFC, symSize: 0x30 }
  - { offsetInCU: 0xAD2, offset: 0x64A50, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0x1B90, symBinAddr: 0x6E2C, symSize: 0x34 }
  - { offsetInCU: 0xAE6, offset: 0x64A64, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x1BC4, symBinAddr: 0x6E60, symSize: 0x40 }
  - { offsetInCU: 0xAFA, offset: 0x64A78, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC6impactyySo13CAPPluginCallCFyyScMYccfU_Tf2ni_nTA', symObjAddr: 0x1C68, symBinAddr: 0x6EC4, symSize: 0x8 }
  - { offsetInCU: 0xB0E, offset: 0x64A8C, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1C70, symBinAddr: 0x6ECC, symSize: 0x10 }
  - { offsetInCU: 0xB22, offset: 0x64AA0, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1C80, symBinAddr: 0x6EDC, symSize: 0x8 }
  - { offsetInCU: 0xB36, offset: 0x64AB4, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x1C88, symBinAddr: 0x6EE4, symSize: 0x48 }
  - { offsetInCU: 0xB4A, offset: 0x64AC8, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x1CD0, symBinAddr: 0x6F2C, symSize: 0x4C }
  - { offsetInCU: 0xB5E, offset: 0x64ADC, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x1D1C, symBinAddr: 0x6F78, symSize: 0x44 }
  - { offsetInCU: 0xB72, offset: 0x64AF0, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x1D60, symBinAddr: 0x6FBC, symSize: 0x3C }
  - { offsetInCU: 0xB86, offset: 0x64B04, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12notificationyySo13CAPPluginCallCFyyScMYccfU_Tf2ni_nTA', symObjAddr: 0x1D9C, symBinAddr: 0x6FF8, symSize: 0x8 }
  - { offsetInCU: 0xB9A, offset: 0x64B18, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC14selectionStartyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x1DA4, symBinAddr: 0x7000, symSize: 0x8 }
  - { offsetInCU: 0xBAE, offset: 0x64B2C, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC16selectionChangedyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x1DD0, symBinAddr: 0x702C, symSize: 0x8 }
  - { offsetInCU: 0xBC2, offset: 0x64B40, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC12selectionEndyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x1DD8, symBinAddr: 0x7034, symSize: 0x28 }
  - { offsetInCU: 0xC1F, offset: 0x64B9D, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginC7vibrateyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x1E00, symBinAddr: 0x705C, symSize: 0x30 }
  - { offsetInCU: 0xC5D, offset: 0x64BDB, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x1E30, symBinAddr: 0x708C, symSize: 0xC4 }
  - { offsetInCU: 0xC8A, offset: 0x64C08, size: 0x8, addend: 0x0, symName: '_$s16CapacitorHaptics0B6PluginCMa', symObjAddr: 0x1EF4, symBinAddr: 0x7150, symSize: 0x20 }
  - { offsetInCU: 0xC9E, offset: 0x64C1C, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0x1F4C, symBinAddr: 0x71A8, symSize: 0x3C }
...
