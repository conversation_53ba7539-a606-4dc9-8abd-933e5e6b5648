---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorStatusBar.framework/CapacitorStatusBar'
relocations:
  - { offsetInCU: 0x34, offset: 0x589A3, size: 0x8, addend: 0x0, symName: _CapacitorStatusBarVersionString, symObjAddr: 0x0, symBinAddr: 0xC470, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x589D8, size: 0x8, addend: 0x0, symName: _CapacitorStatusBarVersionNumber, symObjAddr: 0x30, symBinAddr: 0xC4A0, symSize: 0x0 }
  - { offsetInCU: 0x50, offset: 0x58A3E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC0A9StatusBarE13viewDidAppearyySbF', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x140 }
  - { offsetInCU: 0x9C, offset: 0x58A8A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC0A9StatusBarE13viewDidAppearyySbFTo', symObjAddr: 0x140, symBinAddr: 0x4140, symSize: 0x3C }
  - { offsetInCU: 0xC9, offset: 0x58AB7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC0A9StatusBarE18viewWillTransition2to4withySo6CGSizeV_So06UIViewdI11Coordinator_ptF', symObjAddr: 0x17C, symBinAddr: 0x417C, symSize: 0x158 }
  - { offsetInCU: 0x125, offset: 0x58B13, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC0A9StatusBarE18viewWillTransition2to4withySo6CGSizeV_So06UIViewdI11Coordinator_ptFTo', symObjAddr: 0x2D4, symBinAddr: 0x42D4, symSize: 0x60 }
  - { offsetInCU: 0x141, offset: 0x58B2F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C19isOverlayingWebview33_E987C2C5A6992534E2B1BA247EBFB96DLLSbvpfi', symObjAddr: 0x334, symBinAddr: 0x4334, symSize: 0x8 }
  - { offsetInCU: 0x159, offset: 0x58B47, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14backgroundView33_E987C2C5A6992534E2B1BA247EBFB96DLLSo6UIViewCSgvpfi', symObjAddr: 0x340, symBinAddr: 0x4340, symSize: 0x8 }
  - { offsetInCU: 0x171, offset: 0x58B5F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C9observers33_E987C2C5A6992534E2B1BA247EBFB96DLLSaySo8NSObject_pGvpfi', symObjAddr: 0x348, symBinAddr: 0x4348, symSize: 0xC }
  - { offsetInCU: 0x189, offset: 0x58B77, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigV15overlaysWebViewSbvpfi', symObjAddr: 0x354, symBinAddr: 0x4354, symSize: 0x8 }
  - { offsetInCU: 0x1A1, offset: 0x58B8F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigV5styleSo08UIStatusC5StyleVvpfi', symObjAddr: 0x38C, symBinAddr: 0x438C, symSize: 0x8 }
  - { offsetInCU: 0x1B9, offset: 0x58BA7, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvpfi', symObjAddr: 0x394, symBinAddr: 0x4394, symSize: 0x8 }
  - { offsetInCU: 0x1D1, offset: 0x58BBF, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV7visibleSbSgvpfi', symObjAddr: 0x39C, symBinAddr: 0x439C, symSize: 0x8 }
  - { offsetInCU: 0x1E9, offset: 0x58BD7, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5styleSSSgvpfi', symObjAddr: 0x3A4, symBinAddr: 0x43A4, symSize: 0xC }
  - { offsetInCU: 0x201, offset: 0x58BEF, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5colorSSSgvpfi', symObjAddr: 0x3B0, symBinAddr: 0x43B0, symSize: 0xC }
  - { offsetInCU: 0x219, offset: 0x58C07, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV6height12CoreGraphics7CGFloatVSgvpfi', symObjAddr: 0x3BC, symBinAddr: 0x43BC, symSize: 0xC }
  - { offsetInCU: 0x231, offset: 0x58C1F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC10identifierSSvpfi', symObjAddr: 0x3C8, symBinAddr: 0x43C8, symSize: 0x24 }
  - { offsetInCU: 0x249, offset: 0x58C37, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC6jsNameSSvpfi', symObjAddr: 0x3EC, symBinAddr: 0x43EC, symSize: 0x1C }
  - { offsetInCU: 0x28D, offset: 0x58C7B, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvpfi', symObjAddr: 0x408, symBinAddr: 0x4408, symSize: 0x32C }
  - { offsetInCU: 0x416, offset: 0x58E04, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC06statusC033_6123A04549419EC02BBFD2C366AE0C9BLLAA0bC0CSgvpfi', symObjAddr: 0x734, symBinAddr: 0x4734, symSize: 0x8 }
  - { offsetInCU: 0x42E, offset: 0x58E1C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC06statusC17VisibilityChanged33_6123A04549419EC02BBFD2C366AE0C9BLLSSvpfi', symObjAddr: 0x73C, symBinAddr: 0x473C, symSize: 0x1C }
  - { offsetInCU: 0x446, offset: 0x58E34, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC06statusC14OverlayChanged33_6123A04549419EC02BBFD2C366AE0C9BLLSSvpfi', symObjAddr: 0x758, symBinAddr: 0x4758, symSize: 0x1C }
  - { offsetInCU: 0x45E, offset: 0x58E4C, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo8NSObject_p_Tg5Tf4d_n', symObjAddr: 0x7C4, symBinAddr: 0x4774, symSize: 0xC }
  - { offsetInCU: 0x481, offset: 0x58E6F, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo15CAPPluginMethodC_Tg5Tf4d_n', symObjAddr: 0x7D0, symBinAddr: 0x4780, symSize: 0x54 }
  - { offsetInCU: 0x4AE, offset: 0x58E9C, size: 0x8, addend: 0x0, symName: '_$sSo16UIStatusBarStyleVMa', symObjAddr: 0x824, symBinAddr: 0x47D4, symSize: 0x54 }
  - { offsetInCU: 0x4C2, offset: 0x58EB0, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x878, symBinAddr: 0x4828, symSize: 0x40 }
  - { offsetInCU: 0x4D6, offset: 0x58EC4, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodCMa', symObjAddr: 0x8B8, symBinAddr: 0x4868, symSize: 0x3C }
  - { offsetInCU: 0x4B, offset: 0x5911C, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE22capacitorViewDidAppearABvpZ', symObjAddr: 0x1978, symBinAddr: 0x157F0, symSize: 0x0 }
  - { offsetInCU: 0x65, offset: 0x59136, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE27capacitorViewWillTransitionABvpZ', symObjAddr: 0x1980, symBinAddr: 0x157F8, symSize: 0x0 }
  - { offsetInCU: 0x73, offset: 0x59144, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE22capacitorViewDidAppearABvau', symObjAddr: 0x0, symBinAddr: 0x48A4, symSize: 0x40 }
  - { offsetInCU: 0x91, offset: 0x59162, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE27capacitorViewWillTransitionABvau', symObjAddr: 0x40, symBinAddr: 0x48E4, symSize: 0x40 }
  - { offsetInCU: 0xAF, offset: 0x59180, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE22capacitorViewDidAppear_WZ', symObjAddr: 0x80, symBinAddr: 0x4924, symSize: 0x34 }
  - { offsetInCU: 0xC9, offset: 0x5919A, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea18CapacitorStatusBarE27capacitorViewWillTransition_WZ', symObjAddr: 0xD0, symBinAddr: 0x4974, symSize: 0x34 }
  - { offsetInCU: 0xB1, offset: 0x5934A, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0Cfd', symObjAddr: 0x0, symBinAddr: 0x49FC, symSize: 0x168 }
  - { offsetInCU: 0x20D, offset: 0x594A6, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0CfD', symObjAddr: 0x168, symBinAddr: 0x4B64, symSize: 0x1C }
  - { offsetInCU: 0x23A, offset: 0x594D3, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tF', symObjAddr: 0x184, symBinAddr: 0x4B80, symSize: 0x480 }
  - { offsetInCU: 0x475, offset: 0x5970E, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU_', symObjAddr: 0x604, symBinAddr: 0x5000, symSize: 0xB4 }
  - { offsetInCU: 0x526, offset: 0x597BF, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU0_', symObjAddr: 0x75C, symBinAddr: 0x5158, symSize: 0xCC }
  - { offsetInCU: 0x561, offset: 0x597FA, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU1_', symObjAddr: 0x828, symBinAddr: 0x5224, symSize: 0x54 }
  - { offsetInCU: 0x61B, offset: 0x598B4, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C24handleViewWillTransition33_E987C2C5A6992534E2B1BA247EBFB96DLLyyF', symObjAddr: 0x87C, symBinAddr: 0x5278, symSize: 0x274 }
  - { offsetInCU: 0x673, offset: 0x5990C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C24handleViewWillTransition33_E987C2C5A6992534E2B1BA247EBFB96DLLyyFyyScMYccfU_', symObjAddr: 0xAF0, symBinAddr: 0x54EC, symSize: 0xC4 }
  - { offsetInCU: 0x6F7, offset: 0x59990, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C12setAnimationyySSF', symObjAddr: 0xBB4, symBinAddr: 0x55B0, symSize: 0xC8 }
  - { offsetInCU: 0x78A, offset: 0x59A23, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4hide9animationySS_tF', symObjAddr: 0xC7C, symBinAddr: 0x5678, symSize: 0x2B4 }
  - { offsetInCU: 0x7F2, offset: 0x59A8B, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4hide9animationySS_tFyyScMYccfU_', symObjAddr: 0xF30, symBinAddr: 0x592C, symSize: 0x10C }
  - { offsetInCU: 0x81E, offset: 0x59AB7, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4show9animationySS_tF', symObjAddr: 0x103C, symBinAddr: 0x5A38, symSize: 0x29C }
  - { offsetInCU: 0x886, offset: 0x59B1F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4show9animationySS_tFyyScMYccfU_', symObjAddr: 0x12D8, symBinAddr: 0x5CD4, symSize: 0xEC }
  - { offsetInCU: 0x8F2, offset: 0x59B8B, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C7getInfoAA0bcE0VyF', symObjAddr: 0x13C4, symBinAddr: 0x5DC0, symSize: 0xF4 }
  - { offsetInCU: 0x9A0, offset: 0x59C39, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C18setOverlaysWebViewyySbF', symObjAddr: 0x14B8, symBinAddr: 0x5EB4, symSize: 0x1B0 }
  - { offsetInCU: 0xA8A, offset: 0x59D23, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C13resizeWebView33_E987C2C5A6992534E2B1BA247EBFB96DLLyyF', symObjAddr: 0x1668, symBinAddr: 0x6064, symSize: 0x268 }
  - { offsetInCU: 0xB79, offset: 0x59E12, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C03getbC5Frame33_E987C2C5A6992534E2B1BA247EBFB96DLLSo6CGRectVyFTf4d_n', symObjAddr: 0x2064, symBinAddr: 0x6A60, symSize: 0x21C }
  - { offsetInCU: 0xDCA, offset: 0x5A063, size: 0x8, addend: 0x0, symName: '_$s10Foundation12NotificationVIeghn_So14NSNotificationCIeyBhy_TR', symObjAddr: 0x6B8, symBinAddr: 0x50B4, symSize: 0xA4 }
  - { offsetInCU: 0xE12, offset: 0x5A0AB, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo8NSObject_p_Tg5', symObjAddr: 0x18D0, symBinAddr: 0x62CC, symSize: 0x90 }
  - { offsetInCU: 0xEEF, offset: 0x5A188, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo8NSObject_p_Tg5', symObjAddr: 0x1960, symBinAddr: 0x635C, symSize: 0x14C }
  - { offsetInCU: 0x106C, offset: 0x5A305, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo8NSObject_p_Tg5', symObjAddr: 0x1AAC, symBinAddr: 0x64A8, symSize: 0x1F0 }
  - { offsetInCU: 0x10BE, offset: 0x5A357, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0CMa', symObjAddr: 0x1C9C, symBinAddr: 0x6698, symSize: 0x20 }
  - { offsetInCU: 0x10DD, offset: 0x5A376, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo8UIWindowC_Tg5', symObjAddr: 0x1CBC, symBinAddr: 0x66B8, symSize: 0x210 }
  - { offsetInCU: 0x1145, offset: 0x5A3DE, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo8NSObject_p_Tg5Tf4nnd_n', symObjAddr: 0x1ECC, symBinAddr: 0x68C8, symSize: 0x80 }
  - { offsetInCU: 0x11CF, offset: 0x5A468, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo8NSObject_p_Tg5Tf4nng_n', symObjAddr: 0x1F4C, symBinAddr: 0x6948, symSize: 0x118 }
  - { offsetInCU: 0x1388, offset: 0x5A621, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperV0A9StatusBarSo7UIColorCRbzlE3hex9fromColorSSSgAF_tFZAF_Tg5Tf4nd_n', symObjAddr: 0x2280, symBinAddr: 0x6C7C, symSize: 0x418 }
  - { offsetInCU: 0x17A5, offset: 0x5AA3E, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4show9animationySS_tFyyScMYccfU_TA', symObjAddr: 0x2710, symBinAddr: 0x70CC, symSize: 0x8 }
  - { offsetInCU: 0x17B9, offset: 0x5AA52, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2718, symBinAddr: 0x70D4, symSize: 0x10 }
  - { offsetInCU: 0x17CD, offset: 0x5AA66, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2728, symBinAddr: 0x70E4, symSize: 0x8 }
  - { offsetInCU: 0x17E1, offset: 0x5AA7A, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x2730, symBinAddr: 0x70EC, symSize: 0x48 }
  - { offsetInCU: 0x17F5, offset: 0x5AA8E, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x2778, symBinAddr: 0x7134, symSize: 0x4C }
  - { offsetInCU: 0x1809, offset: 0x5AAA2, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x27C4, symBinAddr: 0x7180, symSize: 0x44 }
  - { offsetInCU: 0x181D, offset: 0x5AAB6, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C4hide9animationySS_tFyyScMYccfU_TA', symObjAddr: 0x282C, symBinAddr: 0x71E8, symSize: 0x8 }
  - { offsetInCU: 0x1831, offset: 0x5AACA, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C24handleViewWillTransition33_E987C2C5A6992534E2B1BA247EBFB96DLLyyFyyScMYccfU_TA', symObjAddr: 0x2834, symBinAddr: 0x71F0, symSize: 0x8 }
  - { offsetInCU: 0x1845, offset: 0x5AADE, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU_TA', symObjAddr: 0x2868, symBinAddr: 0x7224, symSize: 0x10 }
  - { offsetInCU: 0x1859, offset: 0x5AAF2, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU0_TA', symObjAddr: 0x2878, symBinAddr: 0x7234, symSize: 0x8 }
  - { offsetInCU: 0x186D, offset: 0x5AB06, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC0C14setupObservers33_E987C2C5A6992534E2B1BA247EBFB96DLL4withyAA0bC6ConfigV_tFy10Foundation12NotificationVYbcfU1_TA', symObjAddr: 0x2880, symBinAddr: 0x723C, symSize: 0x8 }
  - { offsetInCU: 0x27, offset: 0x5AF25, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwCP', symObjAddr: 0x0, symBinAddr: 0x726C, symSize: 0x34 }
  - { offsetInCU: 0x3F, offset: 0x5AF3D, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwCP', symObjAddr: 0x0, symBinAddr: 0x726C, symSize: 0x34 }
  - { offsetInCU: 0x53, offset: 0x5AF51, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwxx', symObjAddr: 0x34, symBinAddr: 0x72A0, symSize: 0x8 }
  - { offsetInCU: 0x67, offset: 0x5AF65, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwcp', symObjAddr: 0x3C, symBinAddr: 0x72A8, symSize: 0x34 }
  - { offsetInCU: 0x7B, offset: 0x5AF79, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwca', symObjAddr: 0x70, symBinAddr: 0x72DC, symSize: 0x54 }
  - { offsetInCU: 0x8F, offset: 0x5AF8D, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0xC4, symBinAddr: 0x7330, symSize: 0x14 }
  - { offsetInCU: 0xA3, offset: 0x5AFA1, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwta', symObjAddr: 0xD8, symBinAddr: 0x7344, symSize: 0x44 }
  - { offsetInCU: 0xB7, offset: 0x5AFB5, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwet', symObjAddr: 0x11C, symBinAddr: 0x7388, symSize: 0x48 }
  - { offsetInCU: 0xCB, offset: 0x5AFC9, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVwst', symObjAddr: 0x164, symBinAddr: 0x73D0, symSize: 0x40 }
  - { offsetInCU: 0xDF, offset: 0x5AFDD, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6ConfigVMa', symObjAddr: 0x1A4, symBinAddr: 0x7410, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x5B0C5, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvg', symObjAddr: 0x0, symBinAddr: 0x7420, symSize: 0x8 }
  - { offsetInCU: 0x46, offset: 0x5B0E4, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvg', symObjAddr: 0x0, symBinAddr: 0x7420, symSize: 0x8 }
  - { offsetInCU: 0x62, offset: 0x5B100, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvs', symObjAddr: 0x8, symBinAddr: 0x7428, symSize: 0x8 }
  - { offsetInCU: 0x7E, offset: 0x5B11C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvM', symObjAddr: 0x10, symBinAddr: 0x7430, symSize: 0x10 }
  - { offsetInCU: 0x9A, offset: 0x5B138, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV8overlaysSbSgvM.resume.0', symObjAddr: 0x20, symBinAddr: 0x7440, symSize: 0x4 }
  - { offsetInCU: 0xB6, offset: 0x5B154, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV7visibleSbSgvg', symObjAddr: 0x24, symBinAddr: 0x7444, symSize: 0x8 }
  - { offsetInCU: 0xD2, offset: 0x5B170, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV7visibleSbSgvs', symObjAddr: 0x2C, symBinAddr: 0x744C, symSize: 0x8 }
  - { offsetInCU: 0xEE, offset: 0x5B18C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV7visibleSbSgvM', symObjAddr: 0x34, symBinAddr: 0x7454, symSize: 0x10 }
  - { offsetInCU: 0x10A, offset: 0x5B1A8, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV7visibleSbSgvM.resume.0', symObjAddr: 0x44, symBinAddr: 0x7464, symSize: 0x4 }
  - { offsetInCU: 0x126, offset: 0x5B1C4, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5styleSSSgvg', symObjAddr: 0x48, symBinAddr: 0x7468, symSize: 0x2C }
  - { offsetInCU: 0x142, offset: 0x5B1E0, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5styleSSSgvs', symObjAddr: 0x74, symBinAddr: 0x7494, symSize: 0x34 }
  - { offsetInCU: 0x15E, offset: 0x5B1FC, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5styleSSSgvM', symObjAddr: 0xA8, symBinAddr: 0x74C8, symSize: 0x10 }
  - { offsetInCU: 0x17A, offset: 0x5B218, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5styleSSSgvM.resume.0', symObjAddr: 0xB8, symBinAddr: 0x74D8, symSize: 0x4 }
  - { offsetInCU: 0x196, offset: 0x5B234, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5colorSSSgvg', symObjAddr: 0xBC, symBinAddr: 0x74DC, symSize: 0x2C }
  - { offsetInCU: 0x1B2, offset: 0x5B250, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5colorSSSgvs', symObjAddr: 0xE8, symBinAddr: 0x7508, symSize: 0x34 }
  - { offsetInCU: 0x1CE, offset: 0x5B26C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5colorSSSgvM', symObjAddr: 0x11C, symBinAddr: 0x753C, symSize: 0x10 }
  - { offsetInCU: 0x1EA, offset: 0x5B288, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV5colorSSSgvM.resume.0', symObjAddr: 0x12C, symBinAddr: 0x754C, symSize: 0x4 }
  - { offsetInCU: 0x206, offset: 0x5B2A4, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV6height12CoreGraphics7CGFloatVSgvg', symObjAddr: 0x130, symBinAddr: 0x7550, symSize: 0xC }
  - { offsetInCU: 0x222, offset: 0x5B2C0, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV6height12CoreGraphics7CGFloatVSgvs', symObjAddr: 0x13C, symBinAddr: 0x755C, symSize: 0x10 }
  - { offsetInCU: 0x23E, offset: 0x5B2DC, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV6height12CoreGraphics7CGFloatVSgvM', symObjAddr: 0x14C, symBinAddr: 0x756C, symSize: 0x10 }
  - { offsetInCU: 0x25A, offset: 0x5B2F8, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoV6height12CoreGraphics7CGFloatVSgvM.resume.0', symObjAddr: 0x15C, symBinAddr: 0x757C, symSize: 0x4 }
  - { offsetInCU: 0x277, offset: 0x5B315, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwCP', symObjAddr: 0x160, symBinAddr: 0x7580, symSize: 0x30 }
  - { offsetInCU: 0x28B, offset: 0x5B329, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwxx', symObjAddr: 0x190, symBinAddr: 0x75B0, symSize: 0x28 }
  - { offsetInCU: 0x29F, offset: 0x5B33D, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwcp', symObjAddr: 0x1B8, symBinAddr: 0x75D8, symSize: 0x54 }
  - { offsetInCU: 0x2B3, offset: 0x5B351, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwca', symObjAddr: 0x20C, symBinAddr: 0x762C, symSize: 0x8C }
  - { offsetInCU: 0x2C7, offset: 0x5B365, size: 0x8, addend: 0x0, symName: ___swift_memcpy49_8, symObjAddr: 0x298, symBinAddr: 0x76B8, symSize: 0x1C }
  - { offsetInCU: 0x2DB, offset: 0x5B379, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwta', symObjAddr: 0x2B4, symBinAddr: 0x76D4, symSize: 0x5C }
  - { offsetInCU: 0x2EF, offset: 0x5B38D, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwet', symObjAddr: 0x310, symBinAddr: 0x7730, symSize: 0x5C }
  - { offsetInCU: 0x303, offset: 0x5B3A1, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVwst', symObjAddr: 0x36C, symBinAddr: 0x778C, symSize: 0x64 }
  - { offsetInCU: 0x317, offset: 0x5B3B5, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVMa', symObjAddr: 0x3D0, symBinAddr: 0x77F0, symSize: 0x10 }
  - { offsetInCU: 0x43, offset: 0x5B4DF, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x7800, symSize: 0x2C }
  - { offsetInCU: 0x85, offset: 0x5B521, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvgTo', symObjAddr: 0xD8, symBinAddr: 0x78D8, symSize: 0x60 }
  - { offsetInCU: 0xC0, offset: 0x5B55C, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvg', symObjAddr: 0x138, symBinAddr: 0x7938, symSize: 0x10 }
  - { offsetInCU: 0x107, offset: 0x5B5A3, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4loadyyF', symObjAddr: 0x148, symBinAddr: 0x7948, symSize: 0x114 }
  - { offsetInCU: 0x1B9, offset: 0x5B655, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4loadyyFTo', symObjAddr: 0x25C, symBinAddr: 0x7A5C, symSize: 0x2C }
  - { offsetInCU: 0x1E4, offset: 0x5B680, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC06statusC6Config33_6123A04549419EC02BBFD2C366AE0C9BLLAA0bcF0VyF', symObjAddr: 0x288, symBinAddr: 0x7A88, symSize: 0x1F8 }
  - { offsetInCU: 0x2A8, offset: 0x5B744, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC8setStyleyySo13CAPPluginCallCF', symObjAddr: 0x480, symBinAddr: 0x7C80, symSize: 0x1E4 }
  - { offsetInCU: 0x3D7, offset: 0x5B873, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC8setStyleyySo13CAPPluginCallCFTo', symObjAddr: 0x664, symBinAddr: 0x7E64, symSize: 0x50 }
  - { offsetInCU: 0x3F3, offset: 0x5B88F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setBackgroundColoryySo13CAPPluginCallCF', symObjAddr: 0x6B4, symBinAddr: 0x7EB4, symSize: 0x380 }
  - { offsetInCU: 0x4DD, offset: 0x5B979, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setBackgroundColoryySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0xA34, symBinAddr: 0x8234, symSize: 0xB0 }
  - { offsetInCU: 0x557, offset: 0x5B9F3, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setBackgroundColoryySo13CAPPluginCallCFTo', symObjAddr: 0xAE4, symBinAddr: 0x82E4, symSize: 0x50 }
  - { offsetInCU: 0x573, offset: 0x5BA0F, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4hideyySo13CAPPluginCallCFTo', symObjAddr: 0xB50, symBinAddr: 0x8350, symSize: 0x50 }
  - { offsetInCU: 0x58F, offset: 0x5BA2B, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4showyySo13CAPPluginCallCFTo', symObjAddr: 0x1080, symBinAddr: 0x8880, symSize: 0x50 }
  - { offsetInCU: 0x5AB, offset: 0x5BA47, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC7getInfoyySo13CAPPluginCallCF', symObjAddr: 0x10D0, symBinAddr: 0x88D0, symSize: 0x208 }
  - { offsetInCU: 0x613, offset: 0x5BAAF, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC7getInfoyySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0x12D8, symBinAddr: 0x8AD8, symSize: 0x110 }
  - { offsetInCU: 0x6BE, offset: 0x5BB5A, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC7getInfoyySo13CAPPluginCallCFTo', symObjAddr: 0x13E8, symBinAddr: 0x8BE8, symSize: 0x50 }
  - { offsetInCU: 0x6DA, offset: 0x5BB76, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setOverlaysWebViewyySo13CAPPluginCallCF', symObjAddr: 0x1438, symBinAddr: 0x8C38, symSize: 0x32C }
  - { offsetInCU: 0x7A9, offset: 0x5BC45, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setOverlaysWebViewyySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0x1764, symBinAddr: 0x8F64, symSize: 0x224 }
  - { offsetInCU: 0x87B, offset: 0x5BD17, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setOverlaysWebViewyySo13CAPPluginCallCFTo', symObjAddr: 0x1988, symBinAddr: 0x9188, symSize: 0x50 }
  - { offsetInCU: 0x897, offset: 0x5BD33, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x19D8, symBinAddr: 0x91D8, symSize: 0xB4 }
  - { offsetInCU: 0x8B5, offset: 0x5BD51, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x1A8C, symBinAddr: 0x928C, symSize: 0x48C }
  - { offsetInCU: 0x99D, offset: 0x5BE39, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x1F18, symBinAddr: 0x9718, symSize: 0x74 }
  - { offsetInCU: 0x9B9, offset: 0x5BE55, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCACycfC', symObjAddr: 0x1F8C, symBinAddr: 0x978C, symSize: 0x20 }
  - { offsetInCU: 0x9D7, offset: 0x5BE73, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCACycfc', symObjAddr: 0x1FAC, symBinAddr: 0x97AC, symSize: 0x418 }
  - { offsetInCU: 0xA95, offset: 0x5BF31, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCACycfcTo', symObjAddr: 0x23C4, symBinAddr: 0x9BC4, symSize: 0x20 }
  - { offsetInCU: 0xAB1, offset: 0x5BF4D, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCfD', symObjAddr: 0x23E4, symBinAddr: 0x9BE4, symSize: 0x30 }
  - { offsetInCU: 0xADE, offset: 0x5BF7A, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC5style33_6123A04549419EC02BBFD2C366AE0C9BLL10fromStringSo08UIStatusC5StyleVSS_tFTf4nd_n', symObjAddr: 0x2928, symBinAddr: 0xA010, symSize: 0x1D8 }
  - { offsetInCU: 0xB6B, offset: 0x5C007, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC6toDict33_6123A04549419EC02BBFD2C366AE0C9BLLySDySSypGAA0bC4InfoVFTf4nd_n', symObjAddr: 0x2BFC, symBinAddr: 0xA2E4, symSize: 0x27C }
  - { offsetInCU: 0xDB6, offset: 0x5C252, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCfETo', symObjAddr: 0x2414, symBinAddr: 0x9C14, symSize: 0x88 }
  - { offsetInCU: 0xDFB, offset: 0x5C297, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x249C, symBinAddr: 0x9C9C, symSize: 0x64 }
  - { offsetInCU: 0xE48, offset: 0x5C2E4, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x2500, symBinAddr: 0x9D00, symSize: 0x30 }
  - { offsetInCU: 0xE8B, offset: 0x5C327, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x2530, symBinAddr: 0x9D30, symSize: 0xE0 }
  - { offsetInCU: 0xED5, offset: 0x5C371, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x2610, symBinAddr: 0x9E10, symSize: 0xC4 }
  - { offsetInCU: 0xF02, offset: 0x5C39E, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginCMa', symObjAddr: 0x26D4, symBinAddr: 0x9ED4, symSize: 0x20 }
  - { offsetInCU: 0xF16, offset: 0x5C3B2, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0x26F4, symBinAddr: 0x9EF4, symSize: 0x34 }
  - { offsetInCU: 0xF2A, offset: 0x5C3C6, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x2728, symBinAddr: 0x9F28, symSize: 0x40 }
  - { offsetInCU: 0xF3E, offset: 0x5C3DA, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setOverlaysWebViewyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x27F0, symBinAddr: 0x9FB0, symSize: 0xC }
  - { offsetInCU: 0xF52, offset: 0x5C3EE, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x27FC, symBinAddr: 0x9FBC, symSize: 0x10 }
  - { offsetInCU: 0xF66, offset: 0x5C402, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x280C, symBinAddr: 0x9FCC, symSize: 0x8 }
  - { offsetInCU: 0xF7A, offset: 0x5C416, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x28EC, symBinAddr: 0x9FD4, symSize: 0x3C }
  - { offsetInCU: 0x1049, offset: 0x5C4E5, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC4InfoVWOs', symObjAddr: 0x2E78, symBinAddr: 0xA560, symSize: 0x34 }
  - { offsetInCU: 0x105D, offset: 0x5C4F9, size: 0x8, addend: 0x0, symName: '_$sSSSgWOr', symObjAddr: 0x2EF0, symBinAddr: 0xA5D8, symSize: 0x28 }
  - { offsetInCU: 0x1071, offset: 0x5C50D, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x2F18, symBinAddr: 0xA600, symSize: 0x48 }
  - { offsetInCU: 0x1085, offset: 0x5C521, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2F60, symBinAddr: 0xA648, symSize: 0x10 }
  - { offsetInCU: 0x1099, offset: 0x5C535, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0x2F70, symBinAddr: 0xA658, symSize: 0x3C }
  - { offsetInCU: 0x10AD, offset: 0x5C549, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC7getInfoyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x2FB0, symBinAddr: 0xA698, symSize: 0x8 }
  - { offsetInCU: 0x10C1, offset: 0x5C55D, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4showyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x2FBC, symBinAddr: 0xA6A4, symSize: 0x24 }
  - { offsetInCU: 0x10D5, offset: 0x5C571, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC4hideyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x3010, symBinAddr: 0xA6F8, symSize: 0x24 }
  - { offsetInCU: 0x10E9, offset: 0x5C585, size: 0x8, addend: 0x0, symName: '_$s18CapacitorStatusBar0bC6PluginC18setBackgroundColoryySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x3064, symBinAddr: 0xA74C, symSize: 0x8 }
  - { offsetInCU: 0x11BE, offset: 0x5C65A, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0x2B00, symBinAddr: 0xA1E8, symSize: 0xFC }
  - { offsetInCU: 0x27, offset: 0x5C9F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperV0A9StatusBarSo7UIColorCRbzlE3hex9fromColorSSSgAF_tFZ', symObjAddr: 0x0, symBinAddr: 0xA7AC, symSize: 0x418 }
  - { offsetInCU: 0x6B, offset: 0x5CA38, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperV0A9StatusBarSo7UIColorCRbzlE3hex9fromColorSSSgAF_tFZ', symObjAddr: 0x0, symBinAddr: 0xA7AC, symSize: 0x418 }
...
