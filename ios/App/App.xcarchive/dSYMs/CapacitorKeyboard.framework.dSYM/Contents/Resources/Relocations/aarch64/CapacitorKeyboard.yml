---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorKeyboard.framework/CapacitorKeyboard'
relocations:
  - { offsetInCU: 0x33, offset: 0x4C67, size: 0x8, addend: 0x0, symName: _CapacitorKeyboardVersionString, symObjAddr: 0x0, symBinAddr: 0xA6D0, symSize: 0x0 }
  - { offsetInCU: 0x67, offset: 0x4C9B, size: 0x8, addend: 0x0, symName: _CapacitorKeyboardVersionNumber, symObjAddr: 0x30, symBinAddr: 0xA700, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x4CD7, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin load]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x3B4 }
  - { offsetInCU: 0x4E, offset: 0x4CFE, size: 0x8, addend: 0x0, symName: _hideTimer, symObjAddr: 0x9D38, symBinAddr: 0x10E50, symSize: 0x0 }
  - { offsetInCU: 0xB8, offset: 0x4D68, size: 0x8, addend: 0x0, symName: _UIClassString, symObjAddr: 0x9D20, symBinAddr: 0x10E38, symSize: 0x0 }
  - { offsetInCU: 0xD2, offset: 0x4D82, size: 0x8, addend: 0x0, symName: _WKClassString, symObjAddr: 0x9D28, symBinAddr: 0x10E40, symSize: 0x0 }
  - { offsetInCU: 0xE7, offset: 0x4D97, size: 0x8, addend: 0x0, symName: _UITraitsClassString, symObjAddr: 0x9D30, symBinAddr: 0x10E48, symSize: 0x0 }
  - { offsetInCU: 0xFC, offset: 0x4DAC, size: 0x8, addend: 0x0, symName: _stageManagerOffset, symObjAddr: 0x9D40, symBinAddr: 0x10E58, symSize: 0x0 }
  - { offsetInCU: 0x118, offset: 0x4DC8, size: 0x8, addend: 0x0, symName: _UIOriginalImp, symObjAddr: 0x9D48, symBinAddr: 0x10E60, symSize: 0x0 }
  - { offsetInCU: 0x16B, offset: 0x4E1B, size: 0x8, addend: 0x0, symName: _WKOriginalImp, symObjAddr: 0x9D50, symBinAddr: 0x10E68, symSize: 0x0 }
  - { offsetInCU: 0x2ED, offset: 0x4F9D, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin load]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x3B4 }
  - { offsetInCU: 0x359, offset: 0x5009, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin resetScrollView]', symObjAddr: 0x3B4, symBinAddr: 0x83B4, symSize: 0x5C }
  - { offsetInCU: 0x398, offset: 0x5048, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin onKeyboardWillHide:]', symObjAddr: 0x410, symBinAddr: 0x8410, symSize: 0xD4 }
  - { offsetInCU: 0x3D3, offset: 0x5083, size: 0x8, addend: 0x0, symName: '___37-[KeyboardPlugin onKeyboardWillHide:]_block_invoke', symObjAddr: 0x4E4, symBinAddr: 0x84E4, symSize: 0x5C }
  - { offsetInCU: 0x41A, offset: 0x50CA, size: 0x8, addend: 0x0, symName: ___copy_helper_block_e8_32s, symObjAddr: 0x540, symBinAddr: 0x8540, symSize: 0x8 }
  - { offsetInCU: 0x43E, offset: 0x50EE, size: 0x8, addend: 0x0, symName: ___destroy_helper_block_e8_32s, symObjAddr: 0x548, symBinAddr: 0x8548, symSize: 0x8 }
  - { offsetInCU: 0x45B, offset: 0x510B, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin onKeyboardWillShow:]', symObjAddr: 0x550, symBinAddr: 0x8550, symSize: 0x380 }
  - { offsetInCU: 0x502, offset: 0x51B2, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin onKeyboardDidShow:]', symObjAddr: 0x8D0, symBinAddr: 0x88D0, symSize: 0x188 }
  - { offsetInCU: 0x57D, offset: 0x522D, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin onKeyboardDidHide:]', symObjAddr: 0xA58, symBinAddr: 0x8A58, symSize: 0x6C }
  - { offsetInCU: 0x5B8, offset: 0x5268, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setKeyboardHeight:delay:]', symObjAddr: 0xAC4, symBinAddr: 0x8AC4, symSize: 0x150 }
  - { offsetInCU: 0x624, offset: 0x52D4, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin resizeElement:withPaddingBottom:withScreenHeight:]', symObjAddr: 0xC14, symBinAddr: 0x8C14, symSize: 0x9C }
  - { offsetInCU: 0x690, offset: 0x5340, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin _updateFrame]', symObjAddr: 0xCB0, symBinAddr: 0x8CB0, symSize: 0x32C }
  - { offsetInCU: 0x719, offset: 0x53C9, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setHideFormAccessoryBar:]', symObjAddr: 0xFDC, symBinAddr: 0x8FDC, symSize: 0xE8 }
  - { offsetInCU: 0x908, offset: 0x55B8, size: 0x8, addend: 0x0, symName: '___42-[KeyboardPlugin setHideFormAccessoryBar:]_block_invoke', symObjAddr: 0x10C4, symBinAddr: 0x90C4, symSize: 0x8 }
  - { offsetInCU: 0x93F, offset: 0x55EF, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setDisableScroll:]', symObjAddr: 0x10CC, symBinAddr: 0x90CC, symSize: 0x8C }
  - { offsetInCU: 0xA2A, offset: 0x56DA, size: 0x8, addend: 0x0, symName: '___35-[KeyboardPlugin setDisableScroll:]_block_invoke', symObjAddr: 0x1158, symBinAddr: 0x9158, symSize: 0xE8 }
  - { offsetInCU: 0xA79, offset: 0x5729, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin scrollViewDidScroll:]', symObjAddr: 0x1240, symBinAddr: 0x9240, symSize: 0x14 }
  - { offsetInCU: 0xAB6, offset: 0x5766, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setAccessoryBarVisible:]', symObjAddr: 0x1254, symBinAddr: 0x9254, symSize: 0x78 }
  - { offsetInCU: 0xB07, offset: 0x57B7, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin hide:]', symObjAddr: 0x12CC, symBinAddr: 0x92CC, symSize: 0x7C }
  - { offsetInCU: 0xB5C, offset: 0x580C, size: 0x8, addend: 0x0, symName: '___23-[KeyboardPlugin hide:]_block_invoke', symObjAddr: 0x1348, symBinAddr: 0x9348, symSize: 0x38 }
  - { offsetInCU: 0xB9B, offset: 0x584B, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin show:]', symObjAddr: 0x1380, symBinAddr: 0x9380, symSize: 0x8 }
  - { offsetInCU: 0xBD8, offset: 0x5888, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setStyle:]', symObjAddr: 0x1388, symBinAddr: 0x9388, symSize: 0x98 }
  - { offsetInCU: 0xC19, offset: 0x58C9, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setResizeMode:]', symObjAddr: 0x1420, symBinAddr: 0x9420, symSize: 0xB4 }
  - { offsetInCU: 0xC6A, offset: 0x591A, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin getResizeMode:]', symObjAddr: 0x14D4, symBinAddr: 0x94D4, symSize: 0xB8 }
  - { offsetInCU: 0xCCB, offset: 0x597B, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setScroll:]', symObjAddr: 0x158C, symBinAddr: 0x958C, symSize: 0x50 }
  - { offsetInCU: 0xD0C, offset: 0x59BC, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin changeKeyboardStyle:]', symObjAddr: 0x15DC, symBinAddr: 0x95DC, symSize: 0x1D8 }
  - { offsetInCU: 0xE3C, offset: 0x5AEC, size: 0x8, addend: 0x0, symName: '___38-[KeyboardPlugin changeKeyboardStyle:]_block_invoke', symObjAddr: 0x17B4, symBinAddr: 0x97B4, symSize: 0x8 }
  - { offsetInCU: 0xE73, offset: 0x5B23, size: 0x8, addend: 0x0, symName: '___38-[KeyboardPlugin changeKeyboardStyle:]_block_invoke_2', symObjAddr: 0x17BC, symBinAddr: 0x97BC, symSize: 0x8 }
  - { offsetInCU: 0xEAA, offset: 0x5B5A, size: 0x8, addend: 0x0, symName: '___38-[KeyboardPlugin changeKeyboardStyle:]_block_invoke_3', symObjAddr: 0x17C4, symBinAddr: 0x97C4, symSize: 0x8 }
  - { offsetInCU: 0xEE1, offset: 0x5B91, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin dealloc]', symObjAddr: 0x17CC, symBinAddr: 0x97CC, symSize: 0x68 }
  - { offsetInCU: 0xF12, offset: 0x5BC2, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin disableScroll]', symObjAddr: 0x1834, symBinAddr: 0x9834, symSize: 0x10 }
  - { offsetInCU: 0xF46, offset: 0x5BF6, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin hideFormAccessoryBar]', symObjAddr: 0x1844, symBinAddr: 0x9844, symSize: 0x10 }
  - { offsetInCU: 0xF7A, offset: 0x5C2A, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin keyboardIsVisible]', symObjAddr: 0x1854, symBinAddr: 0x9854, symSize: 0x10 }
  - { offsetInCU: 0xFAE, offset: 0x5C5E, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setKeyboardIsVisible:]', symObjAddr: 0x1864, symBinAddr: 0x9864, symSize: 0x10 }
  - { offsetInCU: 0xFE5, offset: 0x5C95, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin keyboardResizes]', symObjAddr: 0x1874, symBinAddr: 0x9874, symSize: 0x10 }
  - { offsetInCU: 0x1019, offset: 0x5CC9, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setKeyboardResizes:]', symObjAddr: 0x1884, symBinAddr: 0x9884, symSize: 0x10 }
  - { offsetInCU: 0x1052, offset: 0x5D02, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin keyboardStyle]', symObjAddr: 0x1894, symBinAddr: 0x9894, symSize: 0x10 }
  - { offsetInCU: 0x1086, offset: 0x5D36, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setKeyboardStyle:]', symObjAddr: 0x18A4, symBinAddr: 0x98A4, symSize: 0x10 }
  - { offsetInCU: 0x10BF, offset: 0x5D6F, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin paddingBottom]', symObjAddr: 0x18B4, symBinAddr: 0x98B4, symSize: 0x10 }
  - { offsetInCU: 0x10F3, offset: 0x5DA3, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin setPaddingBottom:]', symObjAddr: 0x18C4, symBinAddr: 0x98C4, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x615B, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x98D4, symSize: 0x190 }
  - { offsetInCU: 0x49, offset: 0x617D, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x98D4, symSize: 0x190 }
  - { offsetInCU: 0x88, offset: 0x61BC, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin(CAPPluginCategory) identifier]', symObjAddr: 0x190, symBinAddr: 0x9A64, symSize: 0xC }
  - { offsetInCU: 0xB8, offset: 0x61EC, size: 0x8, addend: 0x0, symName: '-[KeyboardPlugin(CAPPluginCategory) jsName]', symObjAddr: 0x19C, symBinAddr: 0x9A70, symSize: 0xC }
...
