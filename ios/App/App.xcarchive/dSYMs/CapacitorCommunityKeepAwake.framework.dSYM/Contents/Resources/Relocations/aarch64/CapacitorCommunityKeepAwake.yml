---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorCommunityKeepAwake.framework/CapacitorCommunityKeepAwake'
relocations:
  - { offsetInCU: 0x34, offset: 0x5BFBC, size: 0x8, addend: 0x0, symName: _CapacitorCommunityKeepAwakeVersionString, symObjAddr: 0x0, symBinAddr: 0x6090, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5BFF1, size: 0x8, addend: 0x0, symName: _CapacitorCommunityKeepAwakeVersionNumber, symObjAddr: 0x40, symBinAddr: 0x60D0, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5C02E, size: 0x8, addend: 0x0, symName: '-[KeepAwakePlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x100 }
  - { offsetInCU: 0x4A, offset: 0x5C051, size: 0x8, addend: 0x0, symName: '-[KeepAwakePlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x100 }
  - { offsetInCU: 0x8D, offset: 0x5C094, size: 0x8, addend: 0x0, symName: '-[KeepAwakePlugin(CAPPluginCategory) identifier]', symObjAddr: 0x100, symBinAddr: 0x4100, symSize: 0xC }
  - { offsetInCU: 0xC0, offset: 0x5C0C7, size: 0x8, addend: 0x0, symName: '-[KeepAwakePlugin(CAPPluginCategory) jsName]', symObjAddr: 0x10C, symBinAddr: 0x410C, symSize: 0xC }
  - { offsetInCU: 0x3F, offset: 0x5C175, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x4118, symSize: 0x2C }
  - { offsetInCU: 0x72, offset: 0x5C1A8, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC04keepD0yySo13CAPPluginCallCFTo', symObjAddr: 0x2C, symBinAddr: 0x4144, symSize: 0x64 }
  - { offsetInCU: 0xB3, offset: 0x5C1E9, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC10allowSleepyySo13CAPPluginCallCFTo', symObjAddr: 0x90, symBinAddr: 0x41A8, symSize: 0x64 }
  - { offsetInCU: 0x10F, offset: 0x5C245, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC11isSupportedyySo13CAPPluginCallCFTo', symObjAddr: 0xF4, symBinAddr: 0x420C, symSize: 0x140 }
  - { offsetInCU: 0x205, offset: 0x5C33B, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC06isKeptD0yySo13CAPPluginCallCFTo', symObjAddr: 0x234, symBinAddr: 0x434C, symSize: 0x64 }
  - { offsetInCU: 0x237, offset: 0x5C36D, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC6bridge8pluginId0G4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x298, symBinAddr: 0x43B0, symSize: 0xB4 }
  - { offsetInCU: 0x255, offset: 0x5C38B, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC6bridge8pluginId0G4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x34C, symBinAddr: 0x4464, symSize: 0xB4 }
  - { offsetInCU: 0x2CE, offset: 0x5C404, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC6bridge8pluginId0G4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x420, symBinAddr: 0x4538, symSize: 0xD8 }
  - { offsetInCU: 0x325, offset: 0x5C45B, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginCACycfC', symObjAddr: 0x4F8, symBinAddr: 0x4610, symSize: 0x20 }
  - { offsetInCU: 0x343, offset: 0x5C479, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginCACycfc', symObjAddr: 0x518, symBinAddr: 0x4630, symSize: 0x30 }
  - { offsetInCU: 0x37E, offset: 0x5C4B4, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginCACycfcTo', symObjAddr: 0x548, symBinAddr: 0x4660, symSize: 0x3C }
  - { offsetInCU: 0x3B9, offset: 0x5C4EF, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginCfD', symObjAddr: 0x584, symBinAddr: 0x469C, symSize: 0x30 }
  - { offsetInCU: 0x477, offset: 0x5C5AD, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginCMa', symObjAddr: 0x400, symBinAddr: 0x4518, symSize: 0x20 }
  - { offsetInCU: 0x4A1, offset: 0x5C5D7, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x5B4, symBinAddr: 0x46CC, symSize: 0x64 }
  - { offsetInCU: 0x504, offset: 0x5C63A, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x618, symBinAddr: 0x4730, symSize: 0xE0 }
  - { offsetInCU: 0x5BC, offset: 0x5C6F2, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x9C4, symBinAddr: 0x4ADC, symSize: 0x3C }
  - { offsetInCU: 0x5D0, offset: 0x5C706, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC06isKeptD0yySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0xA24, symBinAddr: 0x4B3C, symSize: 0x14C }
  - { offsetInCU: 0x6A2, offset: 0x5C7D8, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xB70, symBinAddr: 0x4C88, symSize: 0x10 }
  - { offsetInCU: 0x6B6, offset: 0x5C7EC, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xB80, symBinAddr: 0x4C98, symSize: 0x8 }
  - { offsetInCU: 0x6CA, offset: 0x5C800, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0xB88, symBinAddr: 0x4CA0, symSize: 0x48 }
  - { offsetInCU: 0x6DE, offset: 0x5C814, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xBD0, symBinAddr: 0x4CE8, symSize: 0x40 }
  - { offsetInCU: 0x6F2, offset: 0x5C828, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0xC10, symBinAddr: 0x4D28, symSize: 0x4C }
  - { offsetInCU: 0x706, offset: 0x5C83C, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0xC5C, symBinAddr: 0x4D74, symSize: 0x44 }
  - { offsetInCU: 0x71A, offset: 0x5C850, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0xCA0, symBinAddr: 0x4DB8, symSize: 0x48 }
  - { offsetInCU: 0x72E, offset: 0x5C864, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0xCE8, symBinAddr: 0x4E00, symSize: 0x10 }
  - { offsetInCU: 0x742, offset: 0x5C878, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC10allowSleepyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0xCF8, symBinAddr: 0x4E10, symSize: 0xAC }
  - { offsetInCU: 0x76B, offset: 0x5C8A1, size: 0x8, addend: 0x0, symName: '_$s27CapacitorCommunityKeepAwake0cD6PluginC04keepD0yySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0xDA4, symBinAddr: 0x4EBC, symSize: 0xAC }
  - { offsetInCU: 0x802, offset: 0x5C938, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0x6F8, symBinAddr: 0x4810, symSize: 0xFC }
...
