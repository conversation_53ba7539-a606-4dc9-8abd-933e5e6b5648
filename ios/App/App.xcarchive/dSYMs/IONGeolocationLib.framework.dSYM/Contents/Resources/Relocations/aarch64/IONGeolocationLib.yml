---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/IONGeolocationLib-ccjangdrtksdrtccewaulrfpazca/Build/Intermediates.noindex/ArchiveIntermediates/IONGeolocationLib/InstallationBuildProductsLocation/Library/Frameworks/IONGeolocationLib.framework/IONGeolocationLib'
relocations:
  - { offsetInCU: 0x34, offset: 0x2F06A, size: 0x8, addend: 0x0, symName: _IONGeolocationLibVersionString, symObjAddr: 0x0, symBinAddr: 0x8320, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x2F09F, size: 0x8, addend: 0x0, symName: _IONGeolocationLibVersionNumber, symObjAddr: 0x40, symBinAddr: 0x8360, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x2F0DC, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationO2eeoiySbAC_ACtFZ', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x14 }
  - { offsetInCU: 0x4B, offset: 0x2F100, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationO2eeoiySbAC_ACtFZ', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x14 }
  - { offsetInCU: 0x7E, offset: 0x2F133, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationO4hash4intoys6HasherVz_tF', symObjAddr: 0x14, symBinAddr: 0x4014, symSize: 0x28 }
  - { offsetInCU: 0x114, offset: 0x2F1C9, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationO9hashValueSivg', symObjAddr: 0x3C, symBinAddr: 0x403C, symSize: 0x44 }
  - { offsetInCU: 0x20E, offset: 0x2F2C3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x80, symBinAddr: 0x4080, symSize: 0x14 }
  - { offsetInCU: 0x22E, offset: 0x2F2E3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x80, symBinAddr: 0x4080, symSize: 0x14 }
  - { offsetInCU: 0x26E, offset: 0x2F323, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSHAASH9hashValueSivgTW', symObjAddr: 0x94, symBinAddr: 0x4094, symSize: 0x44 }
  - { offsetInCU: 0x351, offset: 0x2F406, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xD8, symBinAddr: 0x40D8, symSize: 0x28 }
  - { offsetInCU: 0x3DA, offset: 0x2F48F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSHAASQWb', symObjAddr: 0x140, symBinAddr: 0x4140, symSize: 0x4 }
  - { offsetInCU: 0x3EE, offset: 0x2F4A3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOACSQAAWl', symObjAddr: 0x144, symBinAddr: 0x4144, symSize: 0x44 }
  - { offsetInCU: 0x402, offset: 0x2F4B7, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x188, symBinAddr: 0x4188, symSize: 0xC }
  - { offsetInCU: 0x416, offset: 0x2F4CB, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x194, symBinAddr: 0x4194, symSize: 0x4 }
  - { offsetInCU: 0x42A, offset: 0x2F4DF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOwet', symObjAddr: 0x198, symBinAddr: 0x4198, symSize: 0x90 }
  - { offsetInCU: 0x43E, offset: 0x2F4F3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOwst', symObjAddr: 0x228, symBinAddr: 0x4228, symSize: 0xBC }
  - { offsetInCU: 0x452, offset: 0x2F507, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOwug', symObjAddr: 0x2E4, symBinAddr: 0x42E4, symSize: 0x8 }
  - { offsetInCU: 0x466, offset: 0x2F51B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOwup', symObjAddr: 0x2EC, symBinAddr: 0x42EC, symSize: 0x4 }
  - { offsetInCU: 0x47A, offset: 0x2F52F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOwui', symObjAddr: 0x2F0, symBinAddr: 0x42F0, symSize: 0x8 }
  - { offsetInCU: 0x48E, offset: 0x2F543, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOMa', symObjAddr: 0x2F8, symBinAddr: 0x42F8, symSize: 0x10 }
  - { offsetInCU: 0x508, offset: 0x2F5BD, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCAuthorisationOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x100, symBinAddr: 0x4100, symSize: 0x40 }
  - { offsetInCU: 0x2B, offset: 0x2F6FF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV8altitudeSdvg', symObjAddr: 0x0, symBinAddr: 0x4308, symSize: 0x8 }
  - { offsetInCU: 0x4A, offset: 0x2F71E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV8altitudeSdvg', symObjAddr: 0x0, symBinAddr: 0x4308, symSize: 0x8 }
  - { offsetInCU: 0x68, offset: 0x2F73C, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV6courseSdvg', symObjAddr: 0x8, symBinAddr: 0x4310, symSize: 0x8 }
  - { offsetInCU: 0x86, offset: 0x2F75A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV18horizontalAccuracySdvg', symObjAddr: 0x10, symBinAddr: 0x4318, symSize: 0x8 }
  - { offsetInCU: 0xA4, offset: 0x2F778, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV8latitudeSdvg', symObjAddr: 0x18, symBinAddr: 0x4320, symSize: 0x8 }
  - { offsetInCU: 0xC2, offset: 0x2F796, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV9longitudeSdvg', symObjAddr: 0x20, symBinAddr: 0x4328, symSize: 0x8 }
  - { offsetInCU: 0xE0, offset: 0x2F7B4, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV5speedSdvg', symObjAddr: 0x28, symBinAddr: 0x4330, symSize: 0x8 }
  - { offsetInCU: 0xFE, offset: 0x2F7D2, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV9timestampSdvg', symObjAddr: 0x30, symBinAddr: 0x4338, symSize: 0x8 }
  - { offsetInCU: 0x11C, offset: 0x2F7F0, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV16verticalAccuracySdvg', symObjAddr: 0x38, symBinAddr: 0x4340, symSize: 0x8 }
  - { offsetInCU: 0x13A, offset: 0x2F80E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV2eeoiySbAC_ACtFZ', symObjAddr: 0x40, symBinAddr: 0x4348, symSize: 0x4 }
  - { offsetInCU: 0x15D, offset: 0x2F831, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x44, symBinAddr: 0x434C, symSize: 0x4 }
  - { offsetInCU: 0x17D, offset: 0x2F851, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x44, symBinAddr: 0x434C, symSize: 0x4 }
  - { offsetInCU: 0x18E, offset: 0x2F862, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV2eeoiySbAC_ACtFZTf4nnd_n', symObjAddr: 0x1B8, symBinAddr: 0x44C0, symSize: 0x9C }
  - { offsetInCU: 0x1C9, offset: 0x2F89D, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV6create4fromACSo10CLLocationC_tFZ', symObjAddr: 0x48, symBinAddr: 0x4350, symSize: 0x170 }
  - { offsetInCU: 0x227, offset: 0x2F8FB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVwCP', symObjAddr: 0x254, symBinAddr: 0x455C, symSize: 0x30 }
  - { offsetInCU: 0x23B, offset: 0x2F90F, size: 0x8, addend: 0x0, symName: ___swift_memcpy64_8, symObjAddr: 0x288, symBinAddr: 0x458C, symSize: 0x14 }
  - { offsetInCU: 0x24F, offset: 0x2F923, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVwet', symObjAddr: 0x29C, symBinAddr: 0x45A0, symSize: 0x20 }
  - { offsetInCU: 0x263, offset: 0x2F937, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVwst', symObjAddr: 0x2BC, symBinAddr: 0x45C0, symSize: 0x3C }
  - { offsetInCU: 0x277, offset: 0x2F94B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelVMa', symObjAddr: 0x2F8, symBinAddr: 0x45FC, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x2FA2A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO20requestAuthorization5usingySo17CLLocationManagerC_tFyycAGcfu_yycfu0_', symObjAddr: 0x0, symBinAddr: 0x460C, symSize: 0xC }
  - { offsetInCU: 0x4B, offset: 0x2FA4E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO20requestAuthorization5usingySo17CLLocationManagerC_tFyycAGcfu_yycfu0_', symObjAddr: 0x0, symBinAddr: 0x460C, symSize: 0xC }
  - { offsetInCU: 0x72, offset: 0x2FA75, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO20requestAuthorization5usingySo17CLLocationManagerC_tFyycAGcfu1_yycfu2_', symObjAddr: 0xC, symBinAddr: 0x4618, symSize: 0xC }
  - { offsetInCU: 0x99, offset: 0x2FA9C, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO2eeoiySbAC_ACtFZ', symObjAddr: 0x18, symBinAddr: 0x4624, symSize: 0x18 }
  - { offsetInCU: 0xCC, offset: 0x2FACF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO4hash4intoys6HasherVz_tF', symObjAddr: 0x30, symBinAddr: 0x463C, symSize: 0x28 }
  - { offsetInCU: 0x162, offset: 0x2FB65, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO9hashValueSivg', symObjAddr: 0x58, symBinAddr: 0x4664, symSize: 0x44 }
  - { offsetInCU: 0x25C, offset: 0x2FC5F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x9C, symBinAddr: 0x46A8, symSize: 0x18 }
  - { offsetInCU: 0x27C, offset: 0x2FC7F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x9C, symBinAddr: 0x46A8, symSize: 0x18 }
  - { offsetInCU: 0x2BC, offset: 0x2FCBF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSHAASH9hashValueSivgTW', symObjAddr: 0xB4, symBinAddr: 0x46C0, symSize: 0x44 }
  - { offsetInCU: 0x39F, offset: 0x2FDA2, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xF8, symBinAddr: 0x4704, symSize: 0x28 }
  - { offsetInCU: 0x428, offset: 0x2FE2B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSHAASQWb', symObjAddr: 0x160, symBinAddr: 0x476C, symSize: 0x4 }
  - { offsetInCU: 0x43C, offset: 0x2FE3F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOACSQAAWl', symObjAddr: 0x164, symBinAddr: 0x4770, symSize: 0x44 }
  - { offsetInCU: 0x450, offset: 0x2FE53, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOwet', symObjAddr: 0x1B8, symBinAddr: 0x47B4, symSize: 0x90 }
  - { offsetInCU: 0x464, offset: 0x2FE67, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOwst', symObjAddr: 0x248, symBinAddr: 0x4844, symSize: 0xBC }
  - { offsetInCU: 0x478, offset: 0x2FE7B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOwug', symObjAddr: 0x304, symBinAddr: 0x4900, symSize: 0x8 }
  - { offsetInCU: 0x48C, offset: 0x2FE8F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOwup', symObjAddr: 0x30C, symBinAddr: 0x4908, symSize: 0x4 }
  - { offsetInCU: 0x4A0, offset: 0x2FEA3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOwui', symObjAddr: 0x310, symBinAddr: 0x490C, symSize: 0xC }
  - { offsetInCU: 0x4B4, offset: 0x2FEB7, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOMa', symObjAddr: 0x31C, symBinAddr: 0x4918, symSize: 0x10 }
  - { offsetInCU: 0x512, offset: 0x2FF15, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x120, symBinAddr: 0x472C, symSize: 0x40 }
  - { offsetInCU: 0x4A, offset: 0x30071, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib24IONGLOCServicesValidatorVACycfC', symObjAddr: 0x0, symBinAddr: 0x4928, symSize: 0x4 }
  - { offsetInCU: 0x68, offset: 0x3008F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib24IONGLOCServicesValidatorV26areLocationServicesEnabledSbyF', symObjAddr: 0x4, symBinAddr: 0x492C, symSize: 0x28 }
  - { offsetInCU: 0x8D, offset: 0x300B4, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib24IONGLOCServicesValidatorVAA0C7CheckerA2aDP26areLocationServicesEnabledSbyFTW', symObjAddr: 0x2C, symBinAddr: 0x4954, symSize: 0x28 }
  - { offsetInCU: 0x114, offset: 0x3013B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19authorisationStatusAA20IONGLOCAuthorisationOvg', symObjAddr: 0x140, symBinAddr: 0x4A68, symSize: 0x64 }
  - { offsetInCU: 0x133, offset: 0x3015A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19authorisationStatusAA20IONGLOCAuthorisationOvs', symObjAddr: 0x1AC, symBinAddr: 0x4AD4, symSize: 0x6C }
  - { offsetInCU: 0x15C, offset: 0x30183, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19authorisationStatusAA20IONGLOCAuthorisationOvM', symObjAddr: 0x218, symBinAddr: 0x4B40, symSize: 0x78 }
  - { offsetInCU: 0x1D3, offset: 0x301FA, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC20$authorisationStatus7Combine9PublishedV9PublisherVyAA20IONGLOCAuthorisationO_GvM', symObjAddr: 0x304, symBinAddr: 0x4C2C, symSize: 0xC8 }
  - { offsetInCU: 0x22D, offset: 0x30254, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC28authorisationStatusPublisher7Combine9PublishedV0G0VyAA20IONGLOCAuthorisationO_Gvg', symObjAddr: 0x3D0, symBinAddr: 0x4CF8, symSize: 0x60 }
  - { offsetInCU: 0x2B4, offset: 0x302DB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15currentLocationAA20IONGLOCPositionModelVSgvg', symObjAddr: 0x568, symBinAddr: 0x4E90, symSize: 0x64 }
  - { offsetInCU: 0x2D3, offset: 0x302FA, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15currentLocationAA20IONGLOCPositionModelVSgvs', symObjAddr: 0x5D4, symBinAddr: 0x4EFC, symSize: 0xA8 }
  - { offsetInCU: 0x2FC, offset: 0x30323, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15currentLocationAA20IONGLOCPositionModelVSgvM', symObjAddr: 0x67C, symBinAddr: 0x4FA4, symSize: 0x78 }
  - { offsetInCU: 0x373, offset: 0x3039A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC16$currentLocation7Combine9PublishedV9PublisherVyAA20IONGLOCPositionModelVSg_GvM', symObjAddr: 0xA34, symBinAddr: 0x535C, symSize: 0xC8 }
  - { offsetInCU: 0x3CD, offset: 0x303F4, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC24currentLocationPublisher7Combine03AnyG0VyAA20IONGLOCPositionModelVAA20IONGLOCLocationErrorOGvg', symObjAddr: 0xC2C, symBinAddr: 0x5554, symSize: 0x2C0 }
  - { offsetInCU: 0x41B, offset: 0x30442, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC24currentLocationPublisher7Combine03AnyG0VyAA20IONGLOCPositionModelVAA20IONGLOCLocationErrorOGvgA2ISgKcfU_', symObjAddr: 0xF30, symBinAddr: 0x5858, symSize: 0x68 }
  - { offsetInCU: 0x45F, offset: 0x30486, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC24currentLocationPublisher7Combine03AnyG0VyAA20IONGLOCPositionModelVAA20IONGLOCLocationErrorOGvgAKs0M0_pcfU0_', symObjAddr: 0xF98, symBinAddr: 0x58C0, symSize: 0x78 }
  - { offsetInCU: 0x48B, offset: 0x304B2, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager15servicesCheckerACSo010CLLocationF0C_AA015IONGLOCServicesH0_ptcfC', symObjAddr: 0x1094, symBinAddr: 0x59BC, symSize: 0x40 }
  - { offsetInCU: 0x4A9, offset: 0x304D0, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager15servicesCheckerACSo010CLLocationF0C_AA015IONGLOCServicesH0_ptcfc', symObjAddr: 0x10D4, symBinAddr: 0x59FC, symSize: 0x1C4 }
  - { offsetInCU: 0x594, offset: 0x305BB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC20requestAuthorisation8withTypeyAA027IONGLOCAuthorisationRequestH0O_tF', symObjAddr: 0x1338, symBinAddr: 0x5C60, symSize: 0x80 }
  - { offsetInCU: 0x5F4, offset: 0x3061B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC23startMonitoringLocationyyF', symObjAddr: 0x13EC, symBinAddr: 0x5D14, symSize: 0x18 }
  - { offsetInCU: 0x611, offset: 0x30638, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC22stopMonitoringLocationyyF', symObjAddr: 0x1404, symBinAddr: 0x5D2C, symSize: 0x18 }
  - { offsetInCU: 0x62E, offset: 0x30655, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC21requestSingleLocationyyF', symObjAddr: 0x141C, symBinAddr: 0x5D44, symSize: 0x18 }
  - { offsetInCU: 0x64B, offset: 0x30672, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19updateConfigurationyyAA25IONGLOCConfigurationModelVF', symObjAddr: 0x1434, symBinAddr: 0x5D5C, symSize: 0xAC }
  - { offsetInCU: 0x6D1, offset: 0x306F8, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC26areLocationServicesEnabledSbyF', symObjAddr: 0x14E0, symBinAddr: 0x5E08, symSize: 0x50 }
  - { offsetInCU: 0x6F0, offset: 0x30717, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCACycfC', symObjAddr: 0x1554, symBinAddr: 0x5E7C, symSize: 0x20 }
  - { offsetInCU: 0x70E, offset: 0x30735, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCACycfc', symObjAddr: 0x1574, symBinAddr: 0x5E9C, symSize: 0x2C }
  - { offsetInCU: 0x771, offset: 0x30798, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCACycfcTo', symObjAddr: 0x15A0, symBinAddr: 0x5EC8, symSize: 0x2C }
  - { offsetInCU: 0x7D8, offset: 0x307FF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCfD', symObjAddr: 0x15CC, symBinAddr: 0x5EF4, symSize: 0x34 }
  - { offsetInCU: 0x823, offset: 0x3084A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA22IONGLOCServicesCheckerA2aDP26areLocationServicesEnabledSbyFTW', symObjAddr: 0x1690, symBinAddr: 0x5FB8, symSize: 0x54 }
  - { offsetInCU: 0x855, offset: 0x3087C, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA27IONGLOCAuthorisationHandlerA2aDP19authorisationStatusAA0E0OvgTW', symObjAddr: 0x16E4, symBinAddr: 0x600C, symSize: 0x68 }
  - { offsetInCU: 0x8A5, offset: 0x308CC, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA27IONGLOCAuthorisationHandlerA2aDP28authorisationStatusPublisher7Combine9PublishedV0I0VyAA0E0O_GvgTW', symObjAddr: 0x174C, symBinAddr: 0x6074, symSize: 0x64 }
  - { offsetInCU: 0x917, offset: 0x3093E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA27IONGLOCAuthorisationHandlerA2aDP20requestAuthorisation8withTypeyAA0e7RequestJ0O_tFTW', symObjAddr: 0x17B0, symBinAddr: 0x60D8, symSize: 0x84 }
  - { offsetInCU: 0x9A9, offset: 0x309D0, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA28IONGLOCSingleLocationHandlerA2aDP013requestSingleF0yyFTW', symObjAddr: 0x1834, symBinAddr: 0x615C, symSize: 0x1C }
  - { offsetInCU: 0x9F9, offset: 0x30A20, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA29IONGLOCMonitorLocationHandlerA2aDP015startMonitoringF0yyFTW', symObjAddr: 0x1850, symBinAddr: 0x6178, symSize: 0x1C }
  - { offsetInCU: 0xA49, offset: 0x30A70, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA29IONGLOCMonitorLocationHandlerA2aDP014stopMonitoringF0yyFTW', symObjAddr: 0x186C, symBinAddr: 0x6194, symSize: 0x1C }
  - { offsetInCU: 0xA7B, offset: 0x30AA2, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA22IONGLOCLocationHandlerA2aDP15currentLocationAA20IONGLOCPositionModelVSgvgTW', symObjAddr: 0x1888, symBinAddr: 0x61B0, symSize: 0x68 }
  - { offsetInCU: 0xAAD, offset: 0x30AD4, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA22IONGLOCLocationHandlerA2aDP24currentLocationPublisher7Combine03AnyI0VyAA20IONGLOCPositionModelVAA0E5ErrorOGvgTW', symObjAddr: 0x18F0, symBinAddr: 0x6218, symSize: 0x20 }
  - { offsetInCU: 0xAE7, offset: 0x30B0E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCAA22IONGLOCLocationHandlerA2aDP19updateConfigurationyyAA25IONGLOCConfigurationModelVFTW', symObjAddr: 0x1910, symBinAddr: 0x6238, symSize: 0xB0 }
  - { offsetInCU: 0xB6F, offset: 0x30B96, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19authorisationStatusAA20IONGLOCAuthorisationOvpACTK', symObjAddr: 0x54, symBinAddr: 0x497C, symSize: 0x7C }
  - { offsetInCU: 0xB9C, offset: 0x30BC3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC19authorisationStatusAA20IONGLOCAuthorisationOvpACTk', symObjAddr: 0xD0, symBinAddr: 0x49F8, symSize: 0x70 }
  - { offsetInCU: 0xBDA, offset: 0x30C01, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x2A8, symBinAddr: 0x4BD0, symSize: 0x40 }
  - { offsetInCU: 0xBEE, offset: 0x30C15, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15currentLocationAA20IONGLOCPositionModelVSgvpACTK', symObjAddr: 0x430, symBinAddr: 0x4D58, symSize: 0x8C }
  - { offsetInCU: 0xC27, offset: 0x30C4E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15currentLocationAA20IONGLOCPositionModelVSgvpACTk', symObjAddr: 0x4BC, symBinAddr: 0x4DE4, symSize: 0xAC }
  - { offsetInCU: 0xC67, offset: 0x30C8E, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0xEEC, symBinAddr: 0x5814, symSize: 0x44 }
  - { offsetInCU: 0xC7B, offset: 0x30CA2, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOACs0D0AAWl', symObjAddr: 0x1010, symBinAddr: 0x5938, symSize: 0x44 }
  - { offsetInCU: 0xCE5, offset: 0x30D0C, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib22IONGLOCServicesChecker_pWOc', symObjAddr: 0x1298, symBinAddr: 0x5BC0, symSize: 0x44 }
  - { offsetInCU: 0xCF9, offset: 0x30D20, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCMa', symObjAddr: 0x12DC, symBinAddr: 0x5C04, symSize: 0x3C }
  - { offsetInCU: 0xD0D, offset: 0x30D34, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x1318, symBinAddr: 0x5C40, symSize: 0x20 }
  - { offsetInCU: 0xD76, offset: 0x30D9D, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO20requestAuthorization5usingySo17CLLocationManagerC_tFyycAGcfu1_yycfu2_TA', symObjAddr: 0x13DC, symBinAddr: 0x5D04, symSize: 0x8 }
  - { offsetInCU: 0xD8A, offset: 0x30DB1, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO20requestAuthorization5usingySo17CLLocationManagerC_tFyycAGcfu_yycfu0_TA', symObjAddr: 0x13E4, symBinAddr: 0x5D0C, symSize: 0x8 }
  - { offsetInCU: 0xD9E, offset: 0x30DC5, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x1530, symBinAddr: 0x5E58, symSize: 0x24 }
  - { offsetInCU: 0xDC8, offset: 0x30DEF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCfETo', symObjAddr: 0x1600, symBinAddr: 0x5F28, symSize: 0x90 }
  - { offsetInCU: 0xDF7, offset: 0x30E1E, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC37locationManagerDidChangeAuthorizationyySo010CLLocationF0CF', symObjAddr: 0x19C0, symBinAddr: 0x62E8, symSize: 0x7C }
  - { offsetInCU: 0xE9A, offset: 0x30EC1, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC37locationManagerDidChangeAuthorizationyySo010CLLocationF0CFTo', symObjAddr: 0x1A3C, symBinAddr: 0x6364, symSize: 0x50 }
  - { offsetInCU: 0xEB6, offset: 0x30EDD, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtF', symObjAddr: 0x1A8C, symBinAddr: 0x63B4, symSize: 0x8 }
  - { offsetInCU: 0xED9, offset: 0x30F00, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTo', symObjAddr: 0x1A94, symBinAddr: 0x63BC, symSize: 0x84 }
  - { offsetInCU: 0xF0B, offset: 0x30F32, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptF', symObjAddr: 0x1B18, symBinAddr: 0x6440, symSize: 0x78 }
  - { offsetInCU: 0xF94, offset: 0x30FBB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager_16didFailWithErrorySo010CLLocationF0C_s0J0_ptFTo', symObjAddr: 0x1B90, symBinAddr: 0x64B8, symSize: 0x7C }
  - { offsetInCU: 0x10FF, offset: 0x31126, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperC15locationManager_18didUpdateLocationsySo010CLLocationF0C_SaySo0J0CGtFTf4dnn_n', symObjAddr: 0x1E0C, symBinAddr: 0x6734, symSize: 0x2B0 }
  - { offsetInCU: 0x132C, offset: 0x31353, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib24IONGLOCServicesValidatorVMa', symObjAddr: 0x215C, symBinAddr: 0x6A84, symSize: 0x10 }
  - { offsetInCU: 0x1340, offset: 0x31367, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCMU', symObjAddr: 0x216C, symBinAddr: 0x6A94, symSize: 0x8 }
  - { offsetInCU: 0x1354, offset: 0x3137B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib21IONGLOCManagerWrapperCMr', symObjAddr: 0x2174, symBinAddr: 0x6A9C, symSize: 0xA0 }
  - { offsetInCU: 0x1368, offset: 0x3138F, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVy17IONGeolocationLib20IONGLOCAuthorisationOGMa', symObjAddr: 0x2458, symBinAddr: 0x6D80, symSize: 0x54 }
  - { offsetInCU: 0x137C, offset: 0x313A3, size: 0x8, addend: 0x0, symName: '_$s7Combine9PublishedVy17IONGeolocationLib20IONGLOCPositionModelVSgGMa', symObjAddr: 0x24AC, symBinAddr: 0x6DD4, symSize: 0x58 }
  - { offsetInCU: 0x1390, offset: 0x313B7, size: 0x8, addend: 0x0, symName: '_$sSo10CLLocationCMa', symObjAddr: 0x2504, symBinAddr: 0x6E2C, symSize: 0x3C }
  - { offsetInCU: 0x143F, offset: 0x31466, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo10CLLocationC_Tg5', symObjAddr: 0x1C0C, symBinAddr: 0x6534, symSize: 0x200 }
  - { offsetInCU: 0x4A, offset: 0x316A7, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib25IONGLOCConfigurationModelV18enableHighAccuracy29minimumUpdateDistanceInMetersACSb_SdSgtcfC', symObjAddr: 0x10, symBinAddr: 0x6E84, symSize: 0x14 }
  - { offsetInCU: 0x86, offset: 0x316E3, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwCP', symObjAddr: 0x44, symBinAddr: 0x6EB8, symSize: 0x30 }
  - { offsetInCU: 0x9A, offset: 0x316F7, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwxx', symObjAddr: 0x74, symBinAddr: 0x6EE8, symSize: 0x8 }
  - { offsetInCU: 0xAE, offset: 0x3170B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwcp', symObjAddr: 0x7C, symBinAddr: 0x6EF0, symSize: 0x30 }
  - { offsetInCU: 0xC2, offset: 0x3171F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwca', symObjAddr: 0xAC, symBinAddr: 0x6F20, symSize: 0x38 }
  - { offsetInCU: 0xD6, offset: 0x31733, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0xE4, symBinAddr: 0x6F58, symSize: 0xC }
  - { offsetInCU: 0xEA, offset: 0x31747, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwta', symObjAddr: 0xF0, symBinAddr: 0x6F64, symSize: 0x30 }
  - { offsetInCU: 0xFE, offset: 0x3175B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwet', symObjAddr: 0x120, symBinAddr: 0x6F94, symSize: 0x50 }
  - { offsetInCU: 0x112, offset: 0x3176F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwst', symObjAddr: 0x170, symBinAddr: 0x6FE4, symSize: 0x54 }
  - { offsetInCU: 0x126, offset: 0x31783, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwug', symObjAddr: 0x1C4, symBinAddr: 0x7038, symSize: 0x18 }
  - { offsetInCU: 0x13A, offset: 0x31797, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwup', symObjAddr: 0x1DC, symBinAddr: 0x7050, symSize: 0x4 }
  - { offsetInCU: 0x14E, offset: 0x317AB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOwui', symObjAddr: 0x1E0, symBinAddr: 0x7054, symSize: 0x20 }
  - { offsetInCU: 0x162, offset: 0x317BF, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOMa', symObjAddr: 0x200, symBinAddr: 0x7074, symSize: 0x10 }
  - { offsetInCU: 0x176, offset: 0x317D3, size: 0x8, addend: 0x0, symName: ___swift_memcpy17_8, symObjAddr: 0x238, symBinAddr: 0x70AC, symSize: 0x14 }
  - { offsetInCU: 0x18A, offset: 0x317E7, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib25IONGLOCConfigurationModelVwet', symObjAddr: 0x250, symBinAddr: 0x70C0, symSize: 0x54 }
  - { offsetInCU: 0x19E, offset: 0x317FB, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib25IONGLOCConfigurationModelVwst', symObjAddr: 0x2A4, symBinAddr: 0x7114, symSize: 0x44 }
  - { offsetInCU: 0x1B2, offset: 0x3180F, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib25IONGLOCConfigurationModelVMa', symObjAddr: 0x2E8, symBinAddr: 0x7158, symSize: 0x10 }
  - { offsetInCU: 0x1D7, offset: 0x31834, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOs0D0AAsADP7_domainSSvgTW', symObjAddr: 0x0, symBinAddr: 0x6E74, symSize: 0x4 }
  - { offsetInCU: 0x1F3, offset: 0x31850, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOs0D0AAsADP5_codeSivgTW', symObjAddr: 0x4, symBinAddr: 0x6E78, symSize: 0x4 }
  - { offsetInCU: 0x20F, offset: 0x3186C, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOs0D0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x8, symBinAddr: 0x6E7C, symSize: 0x4 }
  - { offsetInCU: 0x22B, offset: 0x31888, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCLocationErrorOs0D0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xC, symBinAddr: 0x6E80, symSize: 0x4 }
...
