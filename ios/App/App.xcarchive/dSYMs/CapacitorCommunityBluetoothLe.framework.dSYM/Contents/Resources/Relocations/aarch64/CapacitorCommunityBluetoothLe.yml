---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorCommunityBluetoothLe.framework/CapacitorCommunityBluetoothLe'
relocations:
  - { offsetInCU: 0x34, offset: 0x5D107, size: 0x8, addend: 0x0, symName: _CapacitorCommunityBluetoothLeVersionString, symObjAddr: 0x0, symBinAddr: 0x1E740, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5D13C, size: 0x8, addend: 0x0, symName: _CapacitorCommunityBluetoothLeVersionNumber, symObjAddr: 0x40, symBinAddr: 0x1E780, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5D179, size: 0x8, addend: 0x0, symName: '-[BluetoothLe(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x6A0 }
  - { offsetInCU: 0x4A, offset: 0x5D19C, size: 0x8, addend: 0x0, symName: '-[BluetoothLe(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x6A0 }
  - { offsetInCU: 0x8D, offset: 0x5D1DF, size: 0x8, addend: 0x0, symName: '-[BluetoothLe(CAPPluginCategory) identifier]', symObjAddr: 0x6A0, symBinAddr: 0x46A0, symSize: 0xC }
  - { offsetInCU: 0xC0, offset: 0x5D212, size: 0x8, addend: 0x0, symName: '-[BluetoothLe(CAPPluginCategory) jsName]', symObjAddr: 0x6AC, symBinAddr: 0x46AC, symSize: 0xC }
  - { offsetInCU: 0x121, offset: 0x5D3A2, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe23descriptorValueToStringySSypF', symObjAddr: 0x0, symBinAddr: 0x46B8, symSize: 0x1F8 }
  - { offsetInCU: 0x2D5, offset: 0x5D556, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV29CapacitorCommunityBluetoothLeE11toHexStringSSyF', symObjAddr: 0x1F8, symBinAddr: 0x48B0, symSize: 0x100 }
  - { offsetInCU: 0x388, offset: 0x5D609, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV29CapacitorCommunityBluetoothLeE11toHexStringSSyFSiSrys5UInt8VGXEfU_', symObjAddr: 0x308, symBinAddr: 0x49C0, symSize: 0x1A4 }
  - { offsetInCU: 0x677, offset: 0x5D8F8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe12stringToDatay10Foundation0G0VSSF', symObjAddr: 0x4CC, symBinAddr: 0x4B84, symSize: 0x46C }
  - { offsetInCU: 0x9DA, offset: 0x5DC5B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe14cbuuidToStringySSSo6CBUUIDCF', symObjAddr: 0x948, symBinAddr: 0x5000, symSize: 0x14C }
  - { offsetInCU: 0xB1F, offset: 0x5DDA0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13deviceManager33_35AFEEDA0955870F13937D5130BD6AA8LLAA06DeviceF0CSgvpfi', symObjAddr: 0xA94, symBinAddr: 0x514C, symSize: 0x8 }
  - { offsetInCU: 0xB37, offset: 0x5DDB8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C9deviceMap33_35AFEEDA0955870F13937D5130BD6AA8LLSDySSAA6DeviceCGvpfi', symObjAddr: 0xA9C, symBinAddr: 0x5154, symSize: 0xC }
  - { offsetInCU: 0xB4F, offset: 0x5DDD0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C14displayStrings33_35AFEEDA0955870F13937D5130BD6AA8LLSDyS2SGvpfi', symObjAddr: 0xAA8, symBinAddr: 0x5160, symSize: 0xC }
  - { offsetInCU: 0xBB4, offset: 0x5DE35, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0xDDC, symBinAddr: 0x5418, symSize: 0x50 }
  - { offsetInCU: 0xC75, offset: 0x5DEF6, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFs16IndexingIteratorVySS8UTF8ViewVG_Sit_Tg5', symObjAddr: 0xE2C, symBinAddr: 0x5468, symSize: 0x30C }
  - { offsetInCU: 0xD62, offset: 0x5DFE3, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0V15withUnsafeBytesyxxSWKXEKlFyt_Tgq5015$s10Foundation4B42VyACxcSTRzs5UInt8V7ElementRtzlufcySWXEfU3_ACTf1ncn_n', symObjAddr: 0x1148, symBinAddr: 0x5784, symSize: 0xD4 }
  - { offsetInCU: 0xDC1, offset: 0x5E042, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufcAC15_RepresentationOSWXEfU_', symObjAddr: 0x121C, symBinAddr: 0x5858, symSize: 0x74 }
  - { offsetInCU: 0xE2C, offset: 0x5E0AD, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC22withUnsafeMutableBytes2in5applyxSnySiG_xSwKXEtKlFs16IndexingIteratorVySS8UTF8ViewVG_Sit_Tg5', symObjAddr: 0x1290, symBinAddr: 0x58CC, symSize: 0xAC }
  - { offsetInCU: 0xEA1, offset: 0x5E122, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSs_s5UInt8VTg5', symObjAddr: 0x133C, symBinAddr: 0x5978, symSize: 0xEC }
  - { offsetInCU: 0xFC4, offset: 0x5E245, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixxSgSRys5UInt8VG_Sits010FixedWidthB0RzlFAF_Tg5', symObjAddr: 0x1428, symBinAddr: 0x5A64, symSize: 0x2BC }
  - { offsetInCU: 0x103B, offset: 0x5E2BC, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x16E4, symBinAddr: 0x5D20, symSize: 0x8C }
  - { offsetInCU: 0x1053, offset: 0x5E2D4, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x1770, symBinAddr: 0x5DAC, symSize: 0x4C }
  - { offsetInCU: 0x10A8, offset: 0x5E329, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x17BC, symBinAddr: 0x5DF8, symSize: 0x154 }
  - { offsetInCU: 0x1116, offset: 0x5E397, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x1910, symBinAddr: 0x5F4C, symSize: 0xF0 }
  - { offsetInCU: 0x113B, offset: 0x5E3BC, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x1A00, symBinAddr: 0x603C, symSize: 0x214 }
  - { offsetInCU: 0x1174, offset: 0x5E3F5, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x1C14, symBinAddr: 0x6250, symSize: 0x78 }
  - { offsetInCU: 0x118C, offset: 0x5E40D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV29CapacitorCommunityBluetoothLeE11toHexStringSSyFSiSrys5UInt8VGXEfU_TA', symObjAddr: 0x1C8C, symBinAddr: 0x62C8, symSize: 0x1C }
  - { offsetInCU: 0x11BD, offset: 0x5E43E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_AI_SitSryAEGXEfU_SS8UTF8ViewV_TG5', symObjAddr: 0x1CA8, symBinAddr: 0x62E4, symSize: 0x88 }
  - { offsetInCU: 0x1230, offset: 0x5E4B1, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_s5UInt8VSgTG5', symObjAddr: 0x1D30, symBinAddr: 0x636C, symSize: 0x44 }
  - { offsetInCU: 0x1284, offset: 0x5E505, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x1D74, symBinAddr: 0x63B0, symSize: 0xC4 }
  - { offsetInCU: 0x12FA, offset: 0x5E57B, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1E38, symBinAddr: 0x6474, symSize: 0x78 }
  - { offsetInCU: 0x1327, offset: 0x5E5A8, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x1EB0, symBinAddr: 0x64EC, symSize: 0x80 }
  - { offsetInCU: 0x137C, offset: 0x5E5FD, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x1F30, symBinAddr: 0x656C, symSize: 0x68 }
  - { offsetInCU: 0x13CD, offset: 0x5E64E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO5countAESi_tcfCTf4nd_n', symObjAddr: 0x1F98, symBinAddr: 0x65D4, symSize: 0x9C }
  - { offsetInCU: 0x153B, offset: 0x5E7BC, size: 0x8, addend: 0x0, symName: '_$ss17FixedWidthIntegerPsE_5radixxSgqd___SitcSyRd__lufcADSRys5UInt8VGXEfU_AF_SsTG5TA', symObjAddr: 0x25BC, symBinAddr: 0x6BF8, symSize: 0x38 }
  - { offsetInCU: 0x1579, offset: 0x5E7FA, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x25F4, symBinAddr: 0x6C30, symSize: 0x40 }
  - { offsetInCU: 0x158D, offset: 0x5E80E, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x2634, symBinAddr: 0x6C70, symSize: 0x44 }
  - { offsetInCU: 0x15A1, offset: 0x5E822, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x2678, symBinAddr: 0x6CB4, symSize: 0x24 }
  - { offsetInCU: 0x15B5, offset: 0x5E836, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x269C, symBinAddr: 0x6CD8, symSize: 0x20 }
  - { offsetInCU: 0x15C9, offset: 0x5E84A, size: 0x8, addend: 0x0, symName: '_$sS2sSTsWl', symObjAddr: 0x26BC, symBinAddr: 0x6CF8, symSize: 0x44 }
  - { offsetInCU: 0x15DD, offset: 0x5E85E, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE016_forceBridgeFromD1C_6resultyAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgq5Tf4nnd_n', symObjAddr: 0x2700, symBinAddr: 0x6D3C, symSize: 0x80 }
  - { offsetInCU: 0x15F5, offset: 0x5E876, size: 0x8, addend: 0x0, symName: '_$ss20_SwiftNewtypeWrapperPss21_ObjectiveCBridgeable8RawValueRpzrlE024_conditionallyBridgeFromD1C_6resultSbAD_01_D5CTypeQZ_xSgztFZSo38UIApplicationOpenExternalURLOptionsKeya_Tgq5Tf4nnd_n', symObjAddr: 0x2780, symBinAddr: 0x6DBC, symSize: 0x90 }
  - { offsetInCU: 0x160D, offset: 0x5E88E, size: 0x8, addend: 0x0, symName: '_$sSw17withMemoryRebound2to_q_xm_q_SryxGKXEtKr0_lFs5UInt8V_s16IndexingIteratorVySS8UTF8ViewVG_SitTg5Tf4dnn_n', symObjAddr: 0x2810, symBinAddr: 0x6E4C, symSize: 0x60 }
  - { offsetInCU: 0x164D, offset: 0x5E8CE, size: 0x8, addend: 0x0, symName: '_$ss5SliceV32withContiguousStorageIfAvailableyqd__Sgqd__SRy7ElementQzGKXEKlFqd__AGKXEfU_SS8UTF8ViewV_s5UInt8VSgTg5Tf4xnn_n', symObjAddr: 0x2870, symBinAddr: 0x6EAC, symSize: 0x1EC }
  - { offsetInCU: 0x16CE, offset: 0x5E94F, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x2A5C, symBinAddr: 0x7098, symSize: 0x3C }
  - { offsetInCU: 0x16E2, offset: 0x5E963, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x2AD8, symBinAddr: 0x7114, symSize: 0x44 }
  - { offsetInCU: 0x16F6, offset: 0x5E977, size: 0x8, addend: 0x0, symName: '_$s10Foundation15ContiguousBytes_pSgWOh', symObjAddr: 0x2B1C, symBinAddr: 0x7158, symSize: 0x40 }
  - { offsetInCU: 0x1740, offset: 0x5E9C1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_SS8UTF8ViewV_TG5TA', symObjAddr: 0x2B5C, symBinAddr: 0x7198, symSize: 0x58 }
  - { offsetInCU: 0x1793, offset: 0x5EA14, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOSgWOe', symObjAddr: 0x2BB4, symBinAddr: 0x71F0, symSize: 0x14 }
  - { offsetInCU: 0x17A7, offset: 0x5EA28, size: 0x8, addend: 0x0, symName: '_$s10Foundation15ContiguousBytes_pWOb', symObjAddr: 0x2BC8, symBinAddr: 0x7204, symSize: 0x18 }
  - { offsetInCU: 0x17BB, offset: 0x5EA3C, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_AI_SitSryAEGXEfU_SS8UTF8ViewV_TG5TA', symObjAddr: 0x2BE0, symBinAddr: 0x721C, symSize: 0x18 }
  - { offsetInCU: 0x17CF, offset: 0x5EA50, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaMa', symObjAddr: 0x2BF8, symBinAddr: 0x7234, symSize: 0x54 }
  - { offsetInCU: 0x17E3, offset: 0x5EA64, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x2C4C, symBinAddr: 0x7288, symSize: 0x24 }
  - { offsetInCU: 0x17F7, offset: 0x5EA78, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x2C70, symBinAddr: 0x72AC, symSize: 0x24 }
  - { offsetInCU: 0x180B, offset: 0x5EA8C, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0x2C94, symBinAddr: 0x72D0, symSize: 0x24 }
  - { offsetInCU: 0x193D, offset: 0x5EBBE, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0xAE0, symBinAddr: 0x5178, symSize: 0x4 }
  - { offsetInCU: 0x195D, offset: 0x5EBDE, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0xAE0, symBinAddr: 0x5178, symSize: 0x4 }
  - { offsetInCU: 0x196E, offset: 0x5EBEF, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0xAE4, symBinAddr: 0x517C, symSize: 0x4 }
  - { offsetInCU: 0x198E, offset: 0x5EC0F, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0xAE4, symBinAddr: 0x517C, symSize: 0x4 }
  - { offsetInCU: 0x199F, offset: 0x5EC20, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP026_unconditionallyBridgeFromF1Cyx01_F5CTypeQzSgFZTW', symObjAddr: 0xAE8, symBinAddr: 0x5180, symSize: 0x40 }
  - { offsetInCU: 0x19D0, offset: 0x5EC51, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB70, symBinAddr: 0x5208, symSize: 0x40 }
  - { offsetInCU: 0x1A01, offset: 0x5EC82, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xBB0, symBinAddr: 0x5248, symSize: 0x70 }
  - { offsetInCU: 0x1A41, offset: 0x5ECC2, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0xC2C, symBinAddr: 0x52B8, symSize: 0x88 }
  - { offsetInCU: 0x1A8C, offset: 0x5ED0D, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0xD70, symBinAddr: 0x53AC, symSize: 0x6C }
  - { offsetInCU: 0x1C02, offset: 0x5EE83, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufCSS8UTF8ViewV_Tg5Tf4nd_n', symObjAddr: 0x2034, symBinAddr: 0x6670, symSize: 0x4E4 }
  - { offsetInCU: 0x1DF2, offset: 0x5F073, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV8capacityACSi_tcfCTf4nd_n', symObjAddr: 0x2528, symBinAddr: 0x6B64, symSize: 0x94 }
  - { offsetInCU: 0x1F14, offset: 0x5F195, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0xD04, symBinAddr: 0x5340, symSize: 0x44 }
  - { offsetInCU: 0x1F54, offset: 0x5F1D5, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSYSCSY8rawValue03RawG0QzvgTW', symObjAddr: 0xD48, symBinAddr: 0x5384, symSize: 0x28 }
  - { offsetInCU: 0x79, offset: 0x5F2FE, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCyq_SgxcigADyXEfU_SS_ySb_SStcTg5', symObjAddr: 0x27FC, symBinAddr: 0x9AF0, symSize: 0xB8 }
  - { offsetInCU: 0x11F, offset: 0x5F3A4, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCyACSo12CBPeripheralCcfc', symObjAddr: 0x0, symBinAddr: 0x7334, symSize: 0x298 }
  - { offsetInCU: 0x27F, offset: 0x5F504, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_19didDiscoverServicesySo12CBPeripheralC_s5Error_pSgtF', symObjAddr: 0x298, symBinAddr: 0x75CC, symSize: 0x3B8 }
  - { offsetInCU: 0x5D2, offset: 0x5F857, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_19didDiscoverServicesySo12CBPeripheralC_s5Error_pSgtFTo', symObjAddr: 0x650, symBinAddr: 0x7984, symSize: 0x7C }
  - { offsetInCU: 0x5FD, offset: 0x5F882, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_29didDiscoverCharacteristicsFor5errorySo12CBPeripheralC_So9CBServiceCs5Error_pSgtFTo', symObjAddr: 0x6CC, symBinAddr: 0x7A00, symSize: 0x98 }
  - { offsetInCU: 0x63E, offset: 0x5F8C3, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_25didDiscoverDescriptorsFor5errorySo12CBPeripheralC_So16CBCharacteristicCs5Error_pSgtFTo', symObjAddr: 0x764, symBinAddr: 0x7A98, symSize: 0x88 }
  - { offsetInCU: 0x6AC, offset: 0x5F931, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC8readRssiyySd_ySb_SStctF', symObjAddr: 0x7EC, symBinAddr: 0x7B20, symSize: 0x18C }
  - { offsetInCU: 0x7F4, offset: 0x5FA79, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC17getCharacteristic030_E4FE6F39444F7C07B77D29B6A3C42H1DLLySo16CBCharacteristicCSgSo6CBUUIDC_AJtF', symObjAddr: 0x984, symBinAddr: 0x7CB8, symSize: 0x328 }
  - { offsetInCU: 0xAF4, offset: 0x5FD79, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC13getDescriptor030_E4FE6F39444F7C07B77D29B6A3C42H1DLLySo12CBDescriptorCSgSo6CBUUIDC_A2JtF', symObjAddr: 0xCAC, symBinAddr: 0x7FE0, symSize: 0x1CC }
  - { offsetInCU: 0xCAA, offset: 0x5FF2F, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC4readyySo6CBUUIDC_AFSdySb_SStctF', symObjAddr: 0xE78, symBinAddr: 0x81AC, symSize: 0x2D0 }
  - { offsetInCU: 0xF10, offset: 0x60195, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC14readDescriptoryySo6CBUUIDC_A2FSdySb_SStctF', symObjAddr: 0x1154, symBinAddr: 0x8488, symSize: 0x350 }
  - { offsetInCU: 0x1243, offset: 0x604C8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC5writeyySo6CBUUIDC_AFSSSo25CBCharacteristicWriteTypeVSdySb_SStctF', symObjAddr: 0x14B0, symBinAddr: 0x87E4, symSize: 0x2CC }
  - { offsetInCU: 0x1458, offset: 0x606DD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC15writeDescriptoryySo6CBUUIDC_A2FSSSdySb_SStctF', symObjAddr: 0x17C4, symBinAddr: 0x8AF8, symSize: 0x314 }
  - { offsetInCU: 0x1733, offset: 0x609B8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC16setNotificationsyySo6CBUUIDC_AFSbySb_SStcSgSdySb_SStctF', symObjAddr: 0x1B84, symBinAddr: 0x8EB8, symSize: 0x468 }
  - { offsetInCU: 0x1B8A, offset: 0x60E0F, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10setTimeout030_E4FE6F39444F7C07B77D29B6A3C42H1DLLyySS_SSSdtF', symObjAddr: 0x2404, symBinAddr: 0x9738, symSize: 0x2F4 }
  - { offsetInCU: 0x1CC0, offset: 0x60F45, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCACycfcTo', symObjAddr: 0x26F8, symBinAddr: 0x9A2C, symSize: 0x2C }
  - { offsetInCU: 0x1D27, offset: 0x60FAC, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCfD', symObjAddr: 0x2724, symBinAddr: 0x9A58, symSize: 0x30 }
  - { offsetInCU: 0x1E08, offset: 0x6108D, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_29didDiscoverCharacteristicsFor5errorySo12CBPeripheralC_So9CBServiceCs5Error_pSgtFTf4nndn_n', symObjAddr: 0x2AF4, symBinAddr: 0x9D60, symSize: 0x360 }
  - { offsetInCU: 0x2179, offset: 0x613FE, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_25didDiscoverDescriptorsFor5errorySo12CBPeripheralC_So16CBCharacteristicCs5Error_pSgtFTf4dddn_n', symObjAddr: 0x2E54, symBinAddr: 0xA0C0, symSize: 0xD4 }
  - { offsetInCU: 0x2235, offset: 0x614BA, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_11didReadRSSI5errorySo12CBPeripheralC_So8NSNumberCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x2F28, symBinAddr: 0xA194, symSize: 0x164 }
  - { offsetInCU: 0x229B, offset: 0x61520, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC6getKey030_E4FE6F39444F7C07B77D29B6A3C42H1DLLyS2S_So16CBCharacteristicCSgtFTf4nnd_n', symObjAddr: 0x308C, symBinAddr: 0xA2F8, symSize: 0x1B0 }
  - { offsetInCU: 0x24BE, offset: 0x61743, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_17didUpdateValueFor5errorySo12CBPeripheralC_So16CBCharacteristicCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x323C, symBinAddr: 0xA4A8, symSize: 0x348 }
  - { offsetInCU: 0x25FF, offset: 0x61884, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC6getKey030_E4FE6F39444F7C07B77D29B6A3C42H1DLLyS2S_So12CBDescriptorCtFTf4nnd_n', symObjAddr: 0x3584, symBinAddr: 0xA7F0, symSize: 0xF8 }
  - { offsetInCU: 0x274A, offset: 0x619CF, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_17didUpdateValueFor5errorySo12CBPeripheralC_So12CBDescriptorCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x367C, symBinAddr: 0xA8E8, symSize: 0x228 }
  - { offsetInCU: 0x27F1, offset: 0x61A76, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10peripheral_16didWriteValueFor5errorySo12CBPeripheralC_So12CBDescriptorCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x38A4, symBinAddr: 0xAB10, symSize: 0x174 }
  - { offsetInCU: 0x2AF3, offset: 0x61D78, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCfETo', symObjAddr: 0x2754, symBinAddr: 0x9A88, symSize: 0x48 }
  - { offsetInCU: 0x2B22, offset: 0x61DA7, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCMa', symObjAddr: 0x279C, symBinAddr: 0x9AD0, symSize: 0x20 }
  - { offsetInCU: 0x2B41, offset: 0x61DC6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceC10setTimeout030_E4FE6F39444F7C07B77D29B6A3C42H1DLLyySS_SSSdtFyycfU_TA', symObjAddr: 0x28E8, symBinAddr: 0x9BDC, symSize: 0x2C }
  - { offsetInCU: 0x2B89, offset: 0x61E0E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2914, symBinAddr: 0x9C08, symSize: 0x10 }
  - { offsetInCU: 0x2B9D, offset: 0x61E22, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2924, symBinAddr: 0x9C18, symSize: 0x8 }
  - { offsetInCU: 0x2BB1, offset: 0x61E36, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x292C, symBinAddr: 0x9C20, symSize: 0x48 }
  - { offsetInCU: 0x2BC5, offset: 0x61E4A, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x2974, symBinAddr: 0x9C68, symSize: 0x4C }
  - { offsetInCU: 0x2BD9, offset: 0x61E5E, size: 0x8, addend: 0x0, symName: '_$sxq_q0_r1_lySbSSytIsegnnr_SgWOe', symObjAddr: 0x2A18, symBinAddr: 0x9CC8, symSize: 0x10 }
  - { offsetInCU: 0x2BED, offset: 0x61E72, size: 0x8, addend: 0x0, symName: '_$sSbSSIegyg_SbSSytIegnnr_TRTA', symObjAddr: 0x2A3C, symBinAddr: 0x9CEC, symSize: 0x8 }
  - { offsetInCU: 0x2C01, offset: 0x61E86, size: 0x8, addend: 0x0, symName: '_$sSbSSIegyg_SgWOy', symObjAddr: 0x2A44, symBinAddr: 0x9CF4, symSize: 0x10 }
  - { offsetInCU: 0x2C89, offset: 0x61F0E, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x3B70, symBinAddr: 0xADDC, symSize: 0x48 }
  - { offsetInCU: 0x2C9D, offset: 0x61F22, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x3BB8, symBinAddr: 0xAE24, symSize: 0x40 }
  - { offsetInCU: 0x2CB1, offset: 0x61F36, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x3BF8, symBinAddr: 0xAE64, symSize: 0x20 }
  - { offsetInCU: 0x4F, offset: 0x623BA, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerCyACSo16UIViewControllerCSg_SDyS2SGySb_SStctcfc', symObjAddr: 0x0, symBinAddr: 0xAEF8, symSize: 0x278 }
  - { offsetInCU: 0x19F, offset: 0x6250A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF14DidUpdateStateyySo09CBCentralF0CF', symObjAddr: 0x278, symBinAddr: 0xB170, symSize: 0x19C }
  - { offsetInCU: 0x254, offset: 0x625BF, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF14DidUpdateStateyySo09CBCentralF0CFTo', symObjAddr: 0x414, symBinAddr: 0xB30C, symSize: 0x50 }
  - { offsetInCU: 0x3F0, offset: 0x6275B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC13startScanningyySaySo6CBUUIDCG_SSSgAHS2bSdSgySb_SStcyAA0E0C_SDySSypGSo8NSNumberCtctF', symObjAddr: 0x464, symBinAddr: 0xB35C, symSize: 0x67C }
  - { offsetInCU: 0x778, offset: 0x62AE3, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC8stopScanyyF', symObjAddr: 0xAE0, symBinAddr: 0xB9D8, symSize: 0x2C8 }
  - { offsetInCU: 0x8DC, offset: 0x62C47, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC8stopScanyyFyyScMYccfU_', symObjAddr: 0xDA8, symBinAddr: 0xBCA0, symSize: 0x2A0 }
  - { offsetInCU: 0xA60, offset: 0x62DCB, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFyyScMYccfU0_', symObjAddr: 0x1048, symBinAddr: 0xBF40, symSize: 0x1DC }
  - { offsetInCU: 0xB00, offset: 0x62E6B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFyyScMYccfU0_ySo13UIAlertActionCcfU_', symObjAddr: 0x1224, symBinAddr: 0xC11C, symSize: 0x1FC }
  - { offsetInCU: 0xC26, offset: 0x62F91, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFTo', symObjAddr: 0x1470, symBinAddr: 0xC368, symSize: 0xD0 }
  - { offsetInCU: 0xC58, offset: 0x62FC3, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyF', symObjAddr: 0x1540, symBinAddr: 0xC438, symSize: 0x1E0 }
  - { offsetInCU: 0xCB0, offset: 0x6301B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_', symObjAddr: 0x1720, symBinAddr: 0xC618, symSize: 0x434 }
  - { offsetInCU: 0xE3B, offset: 0x631A6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_ySo13UIAlertActionCcfU_', symObjAddr: 0x1B54, symBinAddr: 0xCA4C, symSize: 0x150 }
  - { offsetInCU: 0xF51, offset: 0x632BC, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC7connectyyAA0E0C_SdySb_SStctF', symObjAddr: 0x1CA4, symBinAddr: 0xCB9C, symSize: 0x2C4 }
  - { offsetInCU: 0x120C, offset: 0x63577, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_10didConnectySo09CBCentralF0C_So12CBPeripheralCtFTo', symObjAddr: 0x1F68, symBinAddr: 0xCE60, symSize: 0x74 }
  - { offsetInCU: 0x123E, offset: 0x635A9, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC17setOnDisconnectedyyAA0E0C_ySb_SStctF', symObjAddr: 0x1FE8, symBinAddr: 0xCEE0, symSize: 0x1BC }
  - { offsetInCU: 0x13C3, offset: 0x6372E, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC10disconnectyyAA0E0C_SdySb_SStctF', symObjAddr: 0x21A4, symBinAddr: 0xD09C, symSize: 0x31C }
  - { offsetInCU: 0x16DE, offset: 0x63A49, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC10setTimeout33_3ACCA5217AECE08CA882169CA7FBF0E3LLyySS_SSSdtF', symObjAddr: 0x2890, symBinAddr: 0xD788, symSize: 0x2F4 }
  - { offsetInCU: 0x17F6, offset: 0x63B61, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC20setConnectionTimeout33_3ACCA5217AECE08CA882169CA7FBF0E3LLyySS_SSAA0E0CSdtF', symObjAddr: 0x2B84, symBinAddr: 0xDA7C, symSize: 0x2F0 }
  - { offsetInCU: 0x18E8, offset: 0x63C53, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC20setConnectionTimeout33_3ACCA5217AECE08CA882169CA7FBF0E3LLyySS_SSAA0E0CSdtFyycfU_', symObjAddr: 0x2E74, symBinAddr: 0xDD6C, symSize: 0x1D4 }
  - { offsetInCU: 0x1B23, offset: 0x63E8E, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerCACycfcTo', symObjAddr: 0x3048, symBinAddr: 0xDF40, symSize: 0x2C }
  - { offsetInCU: 0x1B8A, offset: 0x63EF5, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerCfD', symObjAddr: 0x3074, symBinAddr: 0xDF6C, symSize: 0x30 }
  - { offsetInCU: 0x1D25, offset: 0x64090, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFTf4dnnnn_n', symObjAddr: 0x371C, symBinAddr: 0xE4FC, symSize: 0x8B4 }
  - { offsetInCU: 0x20D9, offset: 0x64444, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_10didConnectySo09CBCentralF0C_So12CBPeripheralCtFTf4dnn_n', symObjAddr: 0x3FD0, symBinAddr: 0xEDB0, symSize: 0x1EC }
  - { offsetInCU: 0x222A, offset: 0x64595, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_16didFailToConnect5errorySo09CBCentralF0C_So12CBPeripheralCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x41BC, symBinAddr: 0xEF9C, symSize: 0x1E8 }
  - { offsetInCU: 0x22F5, offset: 0x64660, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_23didDisconnectPeripheral5errorySo09CBCentralF0C_So12CBPeripheralCs5Error_pSgtFTf4dnnn_n', symObjAddr: 0x43A4, symBinAddr: 0xF184, symSize: 0x3C8 }
  - { offsetInCU: 0x2654, offset: 0x649BF, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x1420, symBinAddr: 0xC318, symSize: 0x50 }
  - { offsetInCU: 0x26DA, offset: 0x64A45, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerCfETo', symObjAddr: 0x30A4, symBinAddr: 0xDF9C, symSize: 0xE8 }
  - { offsetInCU: 0x2709, offset: 0x64A74, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerCMa', symObjAddr: 0x318C, symBinAddr: 0xE084, symSize: 0x20 }
  - { offsetInCU: 0x271D, offset: 0x64A88, size: 0x8, addend: 0x0, symName: '_$sSbSSIegyg_SbSSytIegnnr_TRTA', symObjAddr: 0x31D0, symBinAddr: 0xE0C8, symSize: 0x8 }
  - { offsetInCU: 0x2731, offset: 0x64A9C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC20setConnectionTimeout33_3ACCA5217AECE08CA882169CA7FBF0E3LLyySS_SSAA0E0CSdtFyycfU_TA', symObjAddr: 0x320C, symBinAddr: 0xE104, symSize: 0xC }
  - { offsetInCU: 0x2745, offset: 0x64AB0, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x3218, symBinAddr: 0xE110, symSize: 0x10 }
  - { offsetInCU: 0x2759, offset: 0x64AC4, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x3228, symBinAddr: 0xE120, symSize: 0x8 }
  - { offsetInCU: 0x276D, offset: 0x64AD8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC10setTimeout33_3ACCA5217AECE08CA882169CA7FBF0E3LLyySS_SSSdtFyycfU_TA', symObjAddr: 0x337C, symBinAddr: 0xE15C, symSize: 0x2C }
  - { offsetInCU: 0x27B5, offset: 0x64B20, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_TA', symObjAddr: 0x33CC, symBinAddr: 0xE1AC, symSize: 0x8 }
  - { offsetInCU: 0x27C9, offset: 0x64B34, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_ySo13UIAlertActionCcfU_TA', symObjAddr: 0x33D4, symBinAddr: 0xE1B4, symSize: 0x8 }
  - { offsetInCU: 0x27DD, offset: 0x64B48, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_ySo13UIAlertActionCcfU_TA.32', symObjAddr: 0x33DC, symBinAddr: 0xE1BC, symSize: 0x8 }
  - { offsetInCU: 0x27F1, offset: 0x64B5C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_ySo13UIAlertActionCcfU_TA.33', symObjAddr: 0x33E4, symBinAddr: 0xE1C4, symSize: 0x8 }
  - { offsetInCU: 0x2805, offset: 0x64B70, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC04showE4ListyyFyyScMYccfU_ySo13UIAlertActionCcfU_TA.34', symObjAddr: 0x33EC, symBinAddr: 0xE1CC, symSize: 0x8 }
  - { offsetInCU: 0x2819, offset: 0x64B84, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC8stopScanyyFyyScMYccfU_TA', symObjAddr: 0x33F4, symBinAddr: 0xE1D4, symSize: 0x8 }
  - { offsetInCU: 0x282D, offset: 0x64B98, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCSDySSypGSo8NSNumberCIegggg_SgWOe', symObjAddr: 0x33FC, symBinAddr: 0xE1DC, symSize: 0x10 }
  - { offsetInCU: 0x2841, offset: 0x64BAC, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC13startScanningyySaySo6CBUUIDCG_SSSgAHS2bSdSgySb_SStcyAA0E0C_SDySSypGSo8NSNumberCtctFyycfU_TA', symObjAddr: 0x3468, symBinAddr: 0xE248, symSize: 0x20 }
  - { offsetInCU: 0x28D6, offset: 0x64C41, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe6DeviceCSDySSypGSo8NSNumberCIegggg_SgWOy', symObjAddr: 0x476C, symBinAddr: 0xF54C, symSize: 0x10 }
  - { offsetInCU: 0x28EA, offset: 0x64C55, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFyyScMYccfU0_TA', symObjAddr: 0x4780, symBinAddr: 0xF560, symSize: 0x8 }
  - { offsetInCU: 0x28FE, offset: 0x64C69, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x4788, symBinAddr: 0xF568, symSize: 0x10 }
  - { offsetInCU: 0x2912, offset: 0x64C7D, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe13DeviceManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralF0C_So12CBPeripheralCSDySSypGSo8NSNumberCtFyyScMYccfU0_ySo13UIAlertActionCcfU_TA', symObjAddr: 0x47C8, symBinAddr: 0xF5A8, symSize: 0x8 }
  - { offsetInCU: 0x2AB2, offset: 0x64E1D, size: 0x8, addend: 0x0, symName: '_$sSTsE5first5where7ElementQzSgSbADKXE_tKFSDySS29CapacitorCommunityBluetoothLe6DeviceCG_Tg504$s29def4Le13h71ManagerC07centralF0_11didDiscover17advertisementData4rssiySo09CBCentralM70C_So12CBPeripheralCSDySSypGSo8NSNumberCtFSbSS3key_AA0E0C5valuet_tXEfU_So0T0CTf1cn_nTf4ng_n', symObjAddr: 0x3488, symBinAddr: 0xE268, symSize: 0x294 }
  - { offsetInCU: 0x27, offset: 0x652E3, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe3log_9separator10terminatoryypd_S2StF', symObjAddr: 0x0, symBinAddr: 0xF608, symSize: 0x22C }
  - { offsetInCU: 0x105, offset: 0x653C1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe3log_9separator10terminatoryypd_S2StF', symObjAddr: 0x0, symBinAddr: 0xF608, symSize: 0x22C }
  - { offsetInCU: 0x348, offset: 0x65604, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2A8, symBinAddr: 0xF834, symSize: 0x10 }
  - { offsetInCU: 0x35C, offset: 0x65618, size: 0x8, addend: 0x0, symName: '_$sSi6offset_yp7elementtSgWOb', symObjAddr: 0x2B8, symBinAddr: 0xF844, symSize: 0x48 }
  - { offsetInCU: 0x43, offset: 0x657A8, size: 0x8, addend: 0x0, symName: '_$sSbSSIegyg_SbSSytIegnnr_TR', symObjAddr: 0x0, symBinAddr: 0xF88C, symSize: 0x30 }
  - { offsetInCU: 0x91, offset: 0x657F6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C4loadyyF', symObjAddr: 0x34, symBinAddr: 0xF8C0, symSize: 0x54 }
  - { offsetInCU: 0xF6, offset: 0x6585B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C4loadyyFTo', symObjAddr: 0x88, symBinAddr: 0xF914, symSize: 0x74 }
  - { offsetInCU: 0x199, offset: 0x658FE, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10initializeyySo13CAPPluginCallCF', symObjAddr: 0xFC, symBinAddr: 0xF988, symSize: 0x114 }
  - { offsetInCU: 0x231, offset: 0x65996, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10initializeyySo13CAPPluginCallCFTo', symObjAddr: 0x210, symBinAddr: 0xFA9C, symSize: 0x50 }
  - { offsetInCU: 0x298, offset: 0x659FD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C9isEnabledyySo13CAPPluginCallCF', symObjAddr: 0x260, symBinAddr: 0xFAEC, symSize: 0x1B0 }
  - { offsetInCU: 0x458, offset: 0x65BBD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C9isEnabledyySo13CAPPluginCallCFTo', symObjAddr: 0x410, symBinAddr: 0xFC9C, symSize: 0x50 }
  - { offsetInCU: 0x474, offset: 0x65BD9, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C25startEnabledNotificationsyySo13CAPPluginCallCF', symObjAddr: 0x4B4, symBinAddr: 0xFD40, symSize: 0xE8 }
  - { offsetInCU: 0x55C, offset: 0x65CC1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C25startEnabledNotificationsyySo13CAPPluginCallCFySbcfU_', symObjAddr: 0x59C, symBinAddr: 0xFE28, symSize: 0x130 }
  - { offsetInCU: 0x63C, offset: 0x65DA1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C25startEnabledNotificationsyySo13CAPPluginCallCFTo', symObjAddr: 0x6CC, symBinAddr: 0xFF58, symSize: 0x50 }
  - { offsetInCU: 0x658, offset: 0x65DBD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C24stopEnabledNotificationsyySo13CAPPluginCallCF', symObjAddr: 0x71C, symBinAddr: 0xFFA8, symSize: 0xB4 }
  - { offsetInCU: 0x735, offset: 0x65E9A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C24stopEnabledNotificationsyySo13CAPPluginCallCFTo', symObjAddr: 0x7D0, symBinAddr: 0x1005C, symSize: 0x50 }
  - { offsetInCU: 0x751, offset: 0x65EB6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFyyScMYccfU_', symObjAddr: 0x874, symBinAddr: 0x10100, symSize: 0x228 }
  - { offsetInCU: 0x7A1, offset: 0x65F06, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFyyScMYccfU_ySbcfU_', symObjAddr: 0xA9C, symBinAddr: 0x10328, symSize: 0xFC }
  - { offsetInCU: 0x88F, offset: 0x65FF4, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFTo', symObjAddr: 0xBD4, symBinAddr: 0x10460, symSize: 0x4C }
  - { offsetInCU: 0x8DF, offset: 0x66044, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17setDisplayStringsyySo13CAPPluginCallCF', symObjAddr: 0xC20, symBinAddr: 0x104AC, symSize: 0x404 }
  - { offsetInCU: 0xBC7, offset: 0x6632C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17setDisplayStringsyySo13CAPPluginCallCFTo', symObjAddr: 0x1024, symBinAddr: 0x108B0, symSize: 0x50 }
  - { offsetInCU: 0xBF2, offset: 0x66357, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestDeviceyySo13CAPPluginCallCF', symObjAddr: 0x1074, symBinAddr: 0x10900, symSize: 0x240 }
  - { offsetInCU: 0xD32, offset: 0x66497, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestDeviceyySo13CAPPluginCallCFySb_SStcfU_', symObjAddr: 0x12B4, symBinAddr: 0x10B40, symSize: 0x2C0 }
  - { offsetInCU: 0xEAC, offset: 0x66611, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestDeviceyySo13CAPPluginCallCFyAA0F0C_SDySSypGSo8NSNumberCtcfU0_', symObjAddr: 0x1574, symBinAddr: 0x10E00, symSize: 0x4 }
  - { offsetInCU: 0xF1F, offset: 0x66684, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestDeviceyySo13CAPPluginCallCFTo', symObjAddr: 0x1578, symBinAddr: 0x10E04, symSize: 0x50 }
  - { offsetInCU: 0xF3B, offset: 0x666A0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestLEScanyySo13CAPPluginCallCF', symObjAddr: 0x15C8, symBinAddr: 0x10E54, symSize: 0x248 }
  - { offsetInCU: 0x105C, offset: 0x667C1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestLEScanyySo13CAPPluginCallCFyAA6DeviceC_SDySSypGSo8NSNumberCtcfU0_', symObjAddr: 0x1810, symBinAddr: 0x1109C, symSize: 0x1B8 }
  - { offsetInCU: 0x1168, offset: 0x668CD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestLEScanyySo13CAPPluginCallCFTo', symObjAddr: 0x19C8, symBinAddr: 0x11254, symSize: 0x50 }
  - { offsetInCU: 0x1184, offset: 0x668E9, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10stopLEScanyySo13CAPPluginCallCF', symObjAddr: 0x1A18, symBinAddr: 0x112A4, symSize: 0x9C }
  - { offsetInCU: 0x1221, offset: 0x66986, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10stopLEScanyySo13CAPPluginCallCFTo', symObjAddr: 0x1AB4, symBinAddr: 0x11340, symSize: 0x50 }
  - { offsetInCU: 0x123D, offset: 0x669A2, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10getDevicesyySo13CAPPluginCallCF', symObjAddr: 0x1B04, symBinAddr: 0x11390, symSize: 0x52C }
  - { offsetInCU: 0x1649, offset: 0x66DAE, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10getDevicesyySo13CAPPluginCallCFTo', symObjAddr: 0x2030, symBinAddr: 0x118BC, symSize: 0x50 }
  - { offsetInCU: 0x1665, offset: 0x66DCA, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C19getConnectedDevicesyySo13CAPPluginCallCF', symObjAddr: 0x2080, symBinAddr: 0x1190C, symSize: 0x4B8 }
  - { offsetInCU: 0x1A9F, offset: 0x67204, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C19getConnectedDevicesyySo13CAPPluginCallCFTo', symObjAddr: 0x2768, symBinAddr: 0x11FF4, symSize: 0x50 }
  - { offsetInCU: 0x1BA5, offset: 0x6730A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCF', symObjAddr: 0x27B8, symBinAddr: 0x12044, symSize: 0x2DC }
  - { offsetInCU: 0x1D20, offset: 0x67485, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCFySb_SStcfU0_', symObjAddr: 0x2A94, symBinAddr: 0x12320, symSize: 0x13C }
  - { offsetInCU: 0x1E41, offset: 0x675A6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCFySb_SStcfU1_', symObjAddr: 0x2BD0, symBinAddr: 0x1245C, symSize: 0xF4 }
  - { offsetInCU: 0x1F1F, offset: 0x67684, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCFTo', symObjAddr: 0x2CC4, symBinAddr: 0x12550, symSize: 0x50 }
  - { offsetInCU: 0x1F3B, offset: 0x676A0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10disconnectyySo13CAPPluginCallCF', symObjAddr: 0x2D68, symBinAddr: 0x125F4, symSize: 0x188 }
  - { offsetInCU: 0x204F, offset: 0x677B4, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10disconnectyySo13CAPPluginCallCFTo', symObjAddr: 0x2EF0, symBinAddr: 0x1277C, symSize: 0x50 }
  - { offsetInCU: 0x207A, offset: 0x677DF, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C11getServicesyySo13CAPPluginCallCF', symObjAddr: 0x2F40, symBinAddr: 0x127CC, symSize: 0xBC4 }
  - { offsetInCU: 0x2C8B, offset: 0x683F0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C11getServicesyySo13CAPPluginCallCFTo', symObjAddr: 0x3B04, symBinAddr: 0x13390, symSize: 0x50 }
  - { offsetInCU: 0x2CA7, offset: 0x6840C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C16discoverServicesyySo13CAPPluginCallCF', symObjAddr: 0x3B54, symBinAddr: 0x133E0, symSize: 0x244 }
  - { offsetInCU: 0x2E25, offset: 0x6858A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C16discoverServicesyySo13CAPPluginCallCFTo', symObjAddr: 0x3D98, symBinAddr: 0x13624, symSize: 0x50 }
  - { offsetInCU: 0x2E41, offset: 0x685A6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C6getMtuyySo13CAPPluginCallCF', symObjAddr: 0x3DE8, symBinAddr: 0x13674, symSize: 0x1BC }
  - { offsetInCU: 0x2FDD, offset: 0x68742, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C6getMtuyySo13CAPPluginCallCFTo', symObjAddr: 0x3FA4, symBinAddr: 0x13830, symSize: 0x50 }
  - { offsetInCU: 0x2FF9, offset: 0x6875E, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C8readRssiyySo13CAPPluginCallCF', symObjAddr: 0x4088, symBinAddr: 0x13914, symSize: 0x15C }
  - { offsetInCU: 0x30E9, offset: 0x6884E, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C8readRssiyySo13CAPPluginCallCFTo', symObjAddr: 0x41E4, symBinAddr: 0x13A70, symSize: 0x50 }
  - { offsetInCU: 0x3114, offset: 0x68879, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C4readyySo13CAPPluginCallCF', symObjAddr: 0x4234, symBinAddr: 0x13AC0, symSize: 0x194 }
  - { offsetInCU: 0x3232, offset: 0x68997, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C4readyySo13CAPPluginCallCFTo', symObjAddr: 0x43C8, symBinAddr: 0x13C54, symSize: 0x50 }
  - { offsetInCU: 0x324E, offset: 0x689B3, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C5writeyySo13CAPPluginCallCFTo', symObjAddr: 0x4430, symBinAddr: 0x13CBC, symSize: 0x50 }
  - { offsetInCU: 0x326A, offset: 0x689CF, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C20writeWithoutResponseyySo13CAPPluginCallCFTo', symObjAddr: 0x46F0, symBinAddr: 0x13F7C, symSize: 0x50 }
  - { offsetInCU: 0x3295, offset: 0x689FA, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C14readDescriptoryySo13CAPPluginCallCF', symObjAddr: 0x4740, symBinAddr: 0x13FCC, symSize: 0x1B0 }
  - { offsetInCU: 0x33B3, offset: 0x68B18, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C14readDescriptoryySo13CAPPluginCallCFTo', symObjAddr: 0x4A50, symBinAddr: 0x142DC, symSize: 0x50 }
  - { offsetInCU: 0x33CF, offset: 0x68B34, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15writeDescriptoryySo13CAPPluginCallCF', symObjAddr: 0x4AA0, symBinAddr: 0x1432C, symSize: 0x254 }
  - { offsetInCU: 0x3505, offset: 0x68C6A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15writeDescriptoryySo13CAPPluginCallCFTo', symObjAddr: 0x4CF4, symBinAddr: 0x14580, symSize: 0x50 }
  - { offsetInCU: 0x3521, offset: 0x68C86, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C18startNotificationsyySo13CAPPluginCallCF', symObjAddr: 0x4D44, symBinAddr: 0x145D0, symSize: 0x23C }
  - { offsetInCU: 0x3646, offset: 0x68DAB, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C18startNotificationsyySo13CAPPluginCallCFySb_SStcfU_', symObjAddr: 0x4F80, symBinAddr: 0x1480C, symSize: 0x320 }
  - { offsetInCU: 0x396B, offset: 0x690D0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C18startNotificationsyySo13CAPPluginCallCFTo', symObjAddr: 0x52A0, symBinAddr: 0x14B2C, symSize: 0x50 }
  - { offsetInCU: 0x3987, offset: 0x690EC, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17stopNotificationsyySo13CAPPluginCallCF', symObjAddr: 0x52F0, symBinAddr: 0x14B7C, symSize: 0x1A0 }
  - { offsetInCU: 0x3AA5, offset: 0x6920A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17stopNotificationsyySo13CAPPluginCallCFTo', symObjAddr: 0x54FC, symBinAddr: 0x14D88, symSize: 0x50 }
  - { offsetInCU: 0x3AC1, offset: 0x69226, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17getDisplayStrings33_35AFEEDA0955870F13937D5130BD6AA8LLSDyS2SGyF', symObjAddr: 0x554C, symBinAddr: 0x14DD8, symSize: 0x4C0 }
  - { offsetInCU: 0x3D1C, offset: 0x69481, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C9getDevice33_35AFEEDA0955870F13937D5130BD6AA8LL_15checkConnectionAA0F0CSgSo13CAPPluginCallC_SbtF', symObjAddr: 0x5D6C, symBinAddr: 0x155F8, symSize: 0x24C }
  - { offsetInCU: 0x3E17, offset: 0x6957C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x6334, symBinAddr: 0x15BC0, symSize: 0xB4 }
  - { offsetInCU: 0x3E35, offset: 0x6959A, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x63E8, symBinAddr: 0x15C74, symSize: 0xE0 }
  - { offsetInCU: 0x3EAE, offset: 0x69613, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x64E8, symBinAddr: 0x15D74, symSize: 0x104 }
  - { offsetInCU: 0x3EFD, offset: 0x69662, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CACycfC', symObjAddr: 0x65EC, symBinAddr: 0x15E78, symSize: 0x20 }
  - { offsetInCU: 0x3F1B, offset: 0x69680, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CACycfc', symObjAddr: 0x660C, symBinAddr: 0x15E98, symSize: 0x5C }
  - { offsetInCU: 0x3F56, offset: 0x696BB, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CACycfcTo', symObjAddr: 0x6668, symBinAddr: 0x15EF4, symSize: 0x68 }
  - { offsetInCU: 0x3F91, offset: 0x696F6, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CfD', symObjAddr: 0x66D0, symBinAddr: 0x15F5C, symSize: 0x30 }
  - { offsetInCU: 0x3FBE, offset: 0x69723, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0xA750, symBinAddr: 0x19FDC, symSize: 0x39C }
  - { offsetInCU: 0x4050, offset: 0x697B5, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15getServiceUUIDs33_35AFEEDA0955870F13937D5130BD6AA8LLySaySo6CBUUIDCGSo13CAPPluginCallCFTf4nd_n', symObjAddr: 0xAB50, symBinAddr: 0x1A3DC, symSize: 0x1B4 }
  - { offsetInCU: 0x422F, offset: 0x69994, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C12getBleDevice33_35AFEEDA0955870F13937D5130BD6AA8LLySDySSypGAA0G0CFTf4nd_n', symObjAddr: 0xAD04, symBinAddr: 0x1A590, symSize: 0x270 }
  - { offsetInCU: 0x4450, offset: 0x69BB5, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C19getManufacturerData33_35AFEEDA0955870F13937D5130BD6AA8LL4dataSDyS2SG10Foundation0G0V_tFTf4nd_n', symObjAddr: 0xAF74, symBinAddr: 0x1A800, symSize: 0x304 }
  - { offsetInCU: 0x467B, offset: 0x69DE0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C14getServiceData33_35AFEEDA0955870F13937D5130BD6AA8LL4dataSDyS2SGSDySo6CBUUIDC10Foundation0G0VG_tFTf4nd_n', symObjAddr: 0xB288, symBinAddr: 0x1AB14, symSize: 0x52C }
  - { offsetInCU: 0x4A3C, offset: 0x6A1A1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13getScanResult33_35AFEEDA0955870F13937D5130BD6AA8LLySDySSypGAA6DeviceC_AFSo8NSNumberCtFTf4nnnd_n', symObjAddr: 0xB7C4, symBinAddr: 0x1B050, symSize: 0xA58 }
  - { offsetInCU: 0x5078, offset: 0x6A7DD, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13getProperties33_35AFEEDA0955870F13937D5130BD6AA8LLySDySSSbGSo16CBCharacteristicCFTf4nd_n', symObjAddr: 0xC54C, symBinAddr: 0x1BDD8, symSize: 0x248 }
  - { offsetInCU: 0x5153, offset: 0x6A8B8, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C17getCharacteristic33_35AFEEDA0955870F13937D5130BD6AA8LLySo6CBUUIDC_AGtSgSo13CAPPluginCallCFTf4nd_n', symObjAddr: 0xC794, symBinAddr: 0x1C020, symSize: 0x1F8 }
  - { offsetInCU: 0x51FC, offset: 0x6A961, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13getDescriptor33_35AFEEDA0955870F13937D5130BD6AA8LLySo6CBUUIDC_A2GtSgSo13CAPPluginCallCFTf4nd_n', symObjAddr: 0xCA48, symBinAddr: 0x1C284, symSize: 0x148 }
  - { offsetInCU: 0x54AC, offset: 0x6AC11, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SbIeyBy_TR', symObjAddr: 0xB98, symBinAddr: 0x10424, symSize: 0x3C }
  - { offsetInCU: 0x5C82, offset: 0x6B3E7, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CMa', symObjAddr: 0x64C8, symBinAddr: 0x15D54, symSize: 0x20 }
  - { offsetInCU: 0x5C96, offset: 0x6B3FB, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0CfETo', symObjAddr: 0x6700, symBinAddr: 0x15F8C, symSize: 0x48 }
  - { offsetInCU: 0x5CCC, offset: 0x6B431, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCyq_SgxcisSS_ySb_SStcTg5', symObjAddr: 0x6748, symBinAddr: 0x15FD4, symSize: 0x1C0 }
  - { offsetInCU: 0x5D79, offset: 0x6B4DE, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x6B84, symBinAddr: 0x16410, symSize: 0x64 }
  - { offsetInCU: 0x5DC6, offset: 0x6B52B, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0x6BE8, symBinAddr: 0x16474, symSize: 0x80 }
  - { offsetInCU: 0x5E55, offset: 0x6B5BA, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_8Dispatch0F8WorkItemCTg5', symObjAddr: 0x6C68, symBinAddr: 0x164F4, symSize: 0xF4 }
  - { offsetInCU: 0x5F54, offset: 0x6B6B9, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_ySb_SStcTg5', symObjAddr: 0x6D5C, symBinAddr: 0x165E8, symSize: 0xC8 }
  - { offsetInCU: 0x5FF8, offset: 0x6B75D, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_29CapacitorCommunityBluetoothLe6DeviceCTg5', symObjAddr: 0x6E24, symBinAddr: 0x166B0, symSize: 0xF4 }
  - { offsetInCU: 0x60EC, offset: 0x6B851, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_8Dispatch0F8WorkItemCTg5', symObjAddr: 0x6F18, symBinAddr: 0x167A4, symSize: 0xCC }
  - { offsetInCU: 0x61AE, offset: 0x6B913, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_ySb_SStcTg5', symObjAddr: 0x6FE4, symBinAddr: 0x16870, symSize: 0xDC }
  - { offsetInCU: 0x623C, offset: 0x6B9A1, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_SSTg5', symObjAddr: 0x70C0, symBinAddr: 0x1694C, symSize: 0xDC }
  - { offsetInCU: 0x62BD, offset: 0x6BA22, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ySb_SStcTg5', symObjAddr: 0x7284, symBinAddr: 0x16B10, symSize: 0x54 }
  - { offsetInCU: 0x6336, offset: 0x6BA9B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x72D8, symBinAddr: 0x16B64, symSize: 0x6C }
  - { offsetInCU: 0x63AF, offset: 0x6BB14, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x7344, symBinAddr: 0x16BD0, symSize: 0x54 }
  - { offsetInCU: 0x6422, offset: 0x6BB87, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x7398, symBinAddr: 0x16C24, symSize: 0xE0 }
  - { offsetInCU: 0x646C, offset: 0x6BBD1, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0x7478, symBinAddr: 0x16D04, symSize: 0x174 }
  - { offsetInCU: 0x64C3, offset: 0x6BC28, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_8Dispatch0D8WorkItemCTg5', symObjAddr: 0x75EC, symBinAddr: 0x16E78, symSize: 0x1C4 }
  - { offsetInCU: 0x6544, offset: 0x6BCA9, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ySb_SStcTg5', symObjAddr: 0x77B0, symBinAddr: 0x1703C, symSize: 0x1C8 }
  - { offsetInCU: 0x65C5, offset: 0x6BD2A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_29CapacitorCommunityBluetoothLe6DeviceCTg5', symObjAddr: 0x7978, symBinAddr: 0x17204, symSize: 0x1C4 }
  - { offsetInCU: 0x6646, offset: 0x6BDAB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0x7B3C, symBinAddr: 0x173C8, symSize: 0x1C8 }
  - { offsetInCU: 0x66AB, offset: 0x6BE10, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0x7D04, symBinAddr: 0x17590, symSize: 0x1F4 }
  - { offsetInCU: 0x674D, offset: 0x6BEB2, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_8Dispatch0K8WorkItemCTg5', symObjAddr: 0x7EF8, symBinAddr: 0x17784, symSize: 0x398 }
  - { offsetInCU: 0x6859, offset: 0x6BFBE, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ySb_SStcTg5', symObjAddr: 0x8290, symBinAddr: 0x17B1C, symSize: 0x3AC }
  - { offsetInCU: 0x6965, offset: 0x6C0CA, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_29CapacitorCommunityBluetoothLe6DeviceCTg5', symObjAddr: 0x863C, symBinAddr: 0x17EC8, symSize: 0x398 }
  - { offsetInCU: 0x6A71, offset: 0x6C1D6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0x89D4, symBinAddr: 0x18260, symSize: 0x3AC }
  - { offsetInCU: 0x6B6C, offset: 0x6C2D1, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0x8D80, symBinAddr: 0x1860C, symSize: 0x3A0 }
  - { offsetInCU: 0x6C97, offset: 0x6C3FC, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_8Dispatch0H8WorkItemCTg5', symObjAddr: 0x9120, symBinAddr: 0x189AC, symSize: 0x1E4 }
  - { offsetInCU: 0x6D28, offset: 0x6C48D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ySb_SStcTg5', symObjAddr: 0x9304, symBinAddr: 0x18B90, symSize: 0x1DC }
  - { offsetInCU: 0x6DAE, offset: 0x6C513, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_SSTg5', symObjAddr: 0x94E0, symBinAddr: 0x18D6C, symSize: 0x1DC }
  - { offsetInCU: 0x6EAF, offset: 0x6C614, size: 0x8, addend: 0x0, symName: '_$ss21_arrayConditionalCastySayq_GSgSayxGr0_lF9Capacitor7JSValue_p_SSTg5', symObjAddr: 0x96BC, symBinAddr: 0x18F48, symSize: 0x164 }
  - { offsetInCU: 0x6FF8, offset: 0x6C75D, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSo6CBUUIDC_Tg5', symObjAddr: 0x9820, symBinAddr: 0x190AC, symSize: 0x1C }
  - { offsetInCU: 0x7010, offset: 0x6C775, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x983C, symBinAddr: 0x190C8, symSize: 0x1C }
  - { offsetInCU: 0x7028, offset: 0x6C78D, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSDySSypG_Tg5', symObjAddr: 0x9858, symBinAddr: 0x190E4, symSize: 0x1C }
  - { offsetInCU: 0x70A3, offset: 0x6C808, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo6CBUUIDC_Tg5', symObjAddr: 0x9874, symBinAddr: 0x19100, symSize: 0x150 }
  - { offsetInCU: 0x71F9, offset: 0x6C95E, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x99C4, symBinAddr: 0x19250, symSize: 0x104 }
  - { offsetInCU: 0x7351, offset: 0x6CAB6, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSDySSypG_Tg5', symObjAddr: 0x9AC8, symBinAddr: 0x19354, symSize: 0x124 }
  - { offsetInCU: 0x74BF, offset: 0x6CC24, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF10Foundation4UUIDV_Tg5', symObjAddr: 0x9BEC, symBinAddr: 0x19478, symSize: 0x174 }
  - { offsetInCU: 0x7617, offset: 0x6CD7C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo6CBUUIDC_Tg5', symObjAddr: 0x9D60, symBinAddr: 0x195EC, symSize: 0x16C }
  - { offsetInCU: 0x7794, offset: 0x6CEF9, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSDySSypG_Tg5', symObjAddr: 0x9ECC, symBinAddr: 0x19758, symSize: 0x128 }
  - { offsetInCU: 0x78F5, offset: 0x6D05A, size: 0x8, addend: 0x0, symName: '_$ss32_copyCollectionToContiguousArrayys0dE0Vy7ElementQzGxSlRzlFSS8UTF8ViewV_Tgq5', symObjAddr: 0x9FF4, symBinAddr: 0x19880, symSize: 0x148 }
  - { offsetInCU: 0x7959, offset: 0x6D0BE, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCyq_SgxcisyyYbcfU_SS_ySb_SStcTG5', symObjAddr: 0xA13C, symBinAddr: 0x199C8, symSize: 0xC4 }
  - { offsetInCU: 0x79B5, offset: 0x6D11A, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgq5Tf4nnd_n', symObjAddr: 0xA200, symBinAddr: 0x19A8C, symSize: 0x64 }
  - { offsetInCU: 0x7B14, offset: 0x6D279, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo6CBUUIDC_Tg5Tf4d_n', symObjAddr: 0xAAEC, symBinAddr: 0x1A378, symSize: 0x64 }
  - { offsetInCU: 0x7D0D, offset: 0x6D472, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo6CBUUIDC_Tg5Tf4nnd_n', symObjAddr: 0xC3AC, symBinAddr: 0x1BC38, symSize: 0x80 }
  - { offsetInCU: 0x7D81, offset: 0x6D4E6, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo6CBUUIDC_Tg5Tf4nng_n', symObjAddr: 0xC42C, symBinAddr: 0x1BCB8, symSize: 0x120 }
  - { offsetInCU: 0x7EAB, offset: 0x6D610, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C18startNotificationsyySo13CAPPluginCallCFySb_SStcfU_TA', symObjAddr: 0xC9EC, symBinAddr: 0x1C278, symSize: 0xC }
  - { offsetInCU: 0x7EBF, offset: 0x6D624, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C14readDescriptoryySo13CAPPluginCallCFySb_SStcfU_TA', symObjAddr: 0xCB90, symBinAddr: 0x1C3CC, symSize: 0x18 }
  - { offsetInCU: 0x7ED3, offset: 0x6D638, size: 0x8, addend: 0x0, symName: '_$sxq_q0_r1_lySbSSytIsegnnr_SgWOb', symObjAddr: 0xCC0C, symBinAddr: 0x1C448, symSize: 0x48 }
  - { offsetInCU: 0x7EE7, offset: 0x6D64C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCyq_SgxcisyyYbcfU_SS_ySb_SStcTG5TA', symObjAddr: 0xCC54, symBinAddr: 0x1C490, symSize: 0x10 }
  - { offsetInCU: 0x7EFB, offset: 0x6D660, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xCC64, symBinAddr: 0x1C4A0, symSize: 0x10 }
  - { offsetInCU: 0x7F0F, offset: 0x6D674, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xCC74, symBinAddr: 0x1C4B0, symSize: 0x8 }
  - { offsetInCU: 0x7F23, offset: 0x6D688, size: 0x8, addend: 0x0, symName: '_$sxq_q0_r1_lySbSSytIsegnnr_SgWOy', symObjAddr: 0xCC7C, symBinAddr: 0x1C4B8, symSize: 0x10 }
  - { offsetInCU: 0x7F37, offset: 0x6D69C, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C10disconnectyySo13CAPPluginCallCFySb_SStcfU_TA', symObjAddr: 0xCC8C, symBinAddr: 0x1C4C8, symSize: 0x18 }
  - { offsetInCU: 0x7F4B, offset: 0x6D6B0, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCFySb_SStcfU0_TA', symObjAddr: 0xCD00, symBinAddr: 0x1C53C, symSize: 0x8 }
  - { offsetInCU: 0x7F5F, offset: 0x6D6C4, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C7connectyySo13CAPPluginCallCFySb_SStcfU1_TA', symObjAddr: 0xCD08, symBinAddr: 0x1C544, symSize: 0x8 }
  - { offsetInCU: 0x7F73, offset: 0x6D6D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOc', symObjAddr: 0xCD40, symBinAddr: 0x1C54C, symSize: 0x44 }
  - { offsetInCU: 0x7F87, offset: 0x6D6EC, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestLEScanyySo13CAPPluginCallCFyAA6DeviceC_SDySSypGSo8NSNumberCtcfU0_TA', symObjAddr: 0xCD84, symBinAddr: 0x1C590, symSize: 0x8 }
  - { offsetInCU: 0x7F9B, offset: 0x6D700, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0xCD8C, symBinAddr: 0x1C598, symSize: 0x14 }
  - { offsetInCU: 0x7FAF, offset: 0x6D714, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV29CapacitorCommunityBluetoothLeE11toHexStringSSyFSiSrys5UInt8VGXEfU_TA', symObjAddr: 0xCE70, symBinAddr: 0x1C5AC, symSize: 0x1C }
  - { offsetInCU: 0x7FC3, offset: 0x6D728, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C13requestDeviceyySo13CAPPluginCallCFySb_SStcfU_TA', symObjAddr: 0xCEC0, symBinAddr: 0x1C5FC, symSize: 0xC }
  - { offsetInCU: 0x7FD7, offset: 0x6D73C, size: 0x8, addend: 0x0, symName: '_$sSbIegy_SgWOe', symObjAddr: 0xCECC, symBinAddr: 0x1C608, symSize: 0x10 }
  - { offsetInCU: 0x7FEB, offset: 0x6D750, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C25startEnabledNotificationsyySo13CAPPluginCallCFySbcfU_TA', symObjAddr: 0xCEDC, symBinAddr: 0x1C618, symSize: 0x8 }
  - { offsetInCU: 0x7FFF, offset: 0x6D764, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0xCF90, symBinAddr: 0x1C6CC, symSize: 0x40 }
  - { offsetInCU: 0x8013, offset: 0x6D778, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe0cD0C15openAppSettingsyySo13CAPPluginCallCFyyScMYccfU_ySbcfU_TA', symObjAddr: 0xD0A0, symBinAddr: 0x1C74C, symSize: 0x8 }
  - { offsetInCU: 0x83D8, offset: 0x6DB3D, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFSS_9Capacitor7JSValue_pS2STg5', symObjAddr: 0x5A0C, symBinAddr: 0x15298, symSize: 0x360 }
  - { offsetInCU: 0x850A, offset: 0x6DC6F, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0x5FB8, symBinAddr: 0x15844, symSize: 0x37C }
  - { offsetInCU: 0x8704, offset: 0x6DE69, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ySb_SStcTg5Tf4gd_n', symObjAddr: 0xA264, symBinAddr: 0x19AF0, symSize: 0xFC }
  - { offsetInCU: 0x8830, offset: 0x6DF95, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0xA360, symBinAddr: 0x19BEC, symSize: 0x110 }
  - { offsetInCU: 0x8977, offset: 0x6E0DC, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTg5Tf4gd_n', symObjAddr: 0xA470, symBinAddr: 0x19CFC, symSize: 0x100 }
  - { offsetInCU: 0x8AB2, offset: 0x6E217, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTg5Tf4gd_n', symObjAddr: 0xA570, symBinAddr: 0x19DFC, symSize: 0xFC }
  - { offsetInCU: 0x8BDD, offset: 0x6E342, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SbTg5Tf4gd_n', symObjAddr: 0xA66C, symBinAddr: 0x19EF8, symSize: 0xE4 }
  - { offsetInCU: 0x2B, offset: 0x6EAB9, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCMi', symObjAddr: 0x0, symBinAddr: 0x1C854, symSize: 0x4 }
  - { offsetInCU: 0x43, offset: 0x6EAD1, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCMi', symObjAddr: 0x0, symBinAddr: 0x1C854, symSize: 0x4 }
  - { offsetInCU: 0x57, offset: 0x6EAE5, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCMr', symObjAddr: 0x4, symBinAddr: 0x1C858, symSize: 0x50 }
  - { offsetInCU: 0xA2, offset: 0x6EB30, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCfD', symObjAddr: 0x78, symBinAddr: 0x1C8A8, symSize: 0x2C }
  - { offsetInCU: 0xED, offset: 0x6EB7B, size: 0x8, addend: 0x0, symName: '_$s29CapacitorCommunityBluetoothLe20ThreadSafeDictionaryCMa', symObjAddr: 0xA4, symBinAddr: 0x1C8D4, symSize: 0xC }
  - { offsetInCU: 0x101, offset: 0x6EB8F, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0xB0, symBinAddr: 0x1C8E0, symSize: 0x2C }
...
