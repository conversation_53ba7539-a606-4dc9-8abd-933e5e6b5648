---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorApp.framework/CapacitorApp'
relocations:
  - { offsetInCU: 0x34, offset: 0x589A3, size: 0x8, addend: 0x0, symName: _CapacitorAppVersionString, symObjAddr: 0x0, symBinAddr: 0x9490, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x589D8, size: 0x8, addend: 0x0, symName: _CapacitorAppVersionNumber, symObjAddr: 0x30, symBinAddr: 0x94C0, symSize: 0x0 }
  - { offsetInCU: 0x43, offset: 0x58A31, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC10identifierSSvpfi', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x1C }
  - { offsetInCU: 0x5B, offset: 0x58A49, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC6jsNameSSvpfi', symObjAddr: 0x34, symBinAddr: 0x4034, symSize: 0x10 }
  - { offsetInCU: 0x9F, offset: 0x58A8D, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvpfi', symObjAddr: 0xD8, symBinAddr: 0x40D8, symSize: 0x2CC }
  - { offsetInCU: 0x228, offset: 0x58C16, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvgTo', symObjAddr: 0x3A4, symBinAddr: 0x43A4, symSize: 0x60 }
  - { offsetInCU: 0x263, offset: 0x58C51, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvg', symObjAddr: 0x404, symBinAddr: 0x4404, symSize: 0x10 }
  - { offsetInCU: 0x29E, offset: 0x58C8C, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC4loadyyF', symObjAddr: 0x420, symBinAddr: 0x4420, symSize: 0x644 }
  - { offsetInCU: 0x5A0, offset: 0x58F8E, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC4loadyyFTo', symObjAddr: 0xB08, symBinAddr: 0x4B08, symSize: 0x2C }
  - { offsetInCU: 0x5BC, offset: 0x58FAA, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCfD', symObjAddr: 0xB34, symBinAddr: 0x4B34, symSize: 0x184 }
  - { offsetInCU: 0x711, offset: 0x590FF, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCfDTo', symObjAddr: 0xCB8, symBinAddr: 0x4CB8, symSize: 0x24 }
  - { offsetInCU: 0x72D, offset: 0x5911B, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC15handleUrlOpened12notificationySo14NSNotificationC_tFTo', symObjAddr: 0x10DC, symBinAddr: 0x50DC, symSize: 0x50 }
  - { offsetInCU: 0x773, offset: 0x59161, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC04exitB0yySo13CAPPluginCallCFTo', symObjAddr: 0x1290, symBinAddr: 0x5290, symSize: 0x10 }
  - { offsetInCU: 0x7BD, offset: 0x591AB, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC7getInfoyySo13CAPPluginCallCFTo', symObjAddr: 0x12A0, symBinAddr: 0x52A0, symSize: 0x4C }
  - { offsetInCU: 0x7FE, offset: 0x591EC, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC12getLaunchUrlyySo13CAPPluginCallCFTo', symObjAddr: 0x12EC, symBinAddr: 0x52EC, symSize: 0x4C }
  - { offsetInCU: 0x83F, offset: 0x5922D, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC8getStateyySo13CAPPluginCallCFTo', symObjAddr: 0x1364, symBinAddr: 0x5364, symSize: 0x4C }
  - { offsetInCU: 0x871, offset: 0x5925F, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x13B0, symBinAddr: 0x53B0, symSize: 0xB4 }
  - { offsetInCU: 0x88F, offset: 0x5927D, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x1464, symBinAddr: 0x5464, symSize: 0x3E0 }
  - { offsetInCU: 0x977, offset: 0x59365, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x1844, symBinAddr: 0x5844, symSize: 0x74 }
  - { offsetInCU: 0x993, offset: 0x59381, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCACycfC', symObjAddr: 0x18B8, symBinAddr: 0x58B8, symSize: 0x20 }
  - { offsetInCU: 0x9B1, offset: 0x5939F, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCACycfc', symObjAddr: 0x18D8, symBinAddr: 0x58D8, symSize: 0x368 }
  - { offsetInCU: 0xA6F, offset: 0x5945D, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCACycfcTo', symObjAddr: 0x1C40, symBinAddr: 0x5C40, symSize: 0x20 }
  - { offsetInCU: 0xA8B, offset: 0x59479, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC17makeUrlOpenObjectySDySS0A07JSValue_pGSDySSypSgGFTf4nd_n', symObjAddr: 0x29C0, symBinAddr: 0x69C0, symSize: 0x598 }
  - { offsetInCU: 0xC51, offset: 0x5963F, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC7getInfoyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0x2FF4, symBinAddr: 0x6FF4, symSize: 0x54C }
  - { offsetInCU: 0xDCF, offset: 0x597BD, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC12getLaunchUrlyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0x3540, symBinAddr: 0x7540, symSize: 0x238 }
  - { offsetInCU: 0xED4, offset: 0x598C2, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC8getStateyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0x3778, symBinAddr: 0x7778, symSize: 0x1E0 }
  - { offsetInCU: 0xF48, offset: 0x59936, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC9observers33_ED32B0654111F2B472BA212349C8AF7BLLSaySo8NSObject_pGvpfi', symObjAddr: 0x414, symBinAddr: 0x4414, symSize: 0xC }
  - { offsetInCU: 0xFAD, offset: 0x5999B, size: 0x8, addend: 0x0, symName: '_$s10Foundation12NotificationVIeghn_So14NSNotificationCIeyBhy_TR', symObjAddr: 0xA64, symBinAddr: 0x4A64, symSize: 0xA4 }
  - { offsetInCU: 0x105F, offset: 0x59A4D, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCfETo', symObjAddr: 0xCDC, symBinAddr: 0x4CDC, symSize: 0x60 }
  - { offsetInCU: 0x1107, offset: 0x59AF5, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x1338, symBinAddr: 0x5338, symSize: 0x2C }
  - { offsetInCU: 0x1135, offset: 0x59B23, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo8NSObject_p_Tg5', symObjAddr: 0x1C60, symBinAddr: 0x5C60, symSize: 0x90 }
  - { offsetInCU: 0x1212, offset: 0x59C00, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo8NSObject_p_Tg5', symObjAddr: 0x1CF0, symBinAddr: 0x5CF0, symSize: 0x14C }
  - { offsetInCU: 0x138F, offset: 0x59D7D, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x1E3C, symBinAddr: 0x5E3C, symSize: 0x64 }
  - { offsetInCU: 0x13F2, offset: 0x59DE0, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x1EA0, symBinAddr: 0x5EA0, symSize: 0xE0 }
  - { offsetInCU: 0x1447, offset: 0x59E35, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo8NSObject_p_Tg5', symObjAddr: 0x1F80, symBinAddr: 0x5F80, symSize: 0x1F0 }
  - { offsetInCU: 0x14A4, offset: 0x59E92, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo15CAPPluginMethodC_Tg5Tf4d_n', symObjAddr: 0x2170, symBinAddr: 0x6170, symSize: 0x64 }
  - { offsetInCU: 0x14D1, offset: 0x59EBF, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2200, symBinAddr: 0x6200, symSize: 0x10 }
  - { offsetInCU: 0x14E5, offset: 0x59ED3, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2210, symBinAddr: 0x6210, symSize: 0x8 }
  - { offsetInCU: 0x14F9, offset: 0x59EE7, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginCMa', symObjAddr: 0x2440, symBinAddr: 0x6440, symSize: 0x20 }
  - { offsetInCU: 0x150D, offset: 0x59EFB, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x2460, symBinAddr: 0x6460, symSize: 0x48 }
  - { offsetInCU: 0x1521, offset: 0x59F0F, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x24A8, symBinAddr: 0x64A8, symSize: 0x40 }
  - { offsetInCU: 0x1535, offset: 0x59F23, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo8NSObject_p_Tg5Tf4d_n', symObjAddr: 0x24E8, symBinAddr: 0x64E8, symSize: 0xC }
  - { offsetInCU: 0x1563, offset: 0x59F51, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo8NSObject_p_Tg5Tf4nnd_n', symObjAddr: 0x24F4, symBinAddr: 0x64F4, symSize: 0x80 }
  - { offsetInCU: 0x15ED, offset: 0x59FDB, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo8NSObject_p_Tg5Tf4nng_n', symObjAddr: 0x2574, symBinAddr: 0x6574, symSize: 0x118 }
  - { offsetInCU: 0x181F, offset: 0x5A20D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOc', symObjAddr: 0x2F58, symBinAddr: 0x6F58, symSize: 0x44 }
  - { offsetInCU: 0x1833, offset: 0x5A221, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x2F9C, symBinAddr: 0x6F9C, symSize: 0x10 }
  - { offsetInCU: 0x1847, offset: 0x5A235, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x2FAC, symBinAddr: 0x6FAC, symSize: 0x10 }
  - { offsetInCU: 0x185B, offset: 0x5A249, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x2FBC, symBinAddr: 0x6FBC, symSize: 0x20 }
  - { offsetInCU: 0x186F, offset: 0x5A25D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOb', symObjAddr: 0x2FDC, symBinAddr: 0x6FDC, symSize: 0x18 }
  - { offsetInCU: 0x18D0, offset: 0x5A2BE, size: 0x8, addend: 0x0, symName: '_$s12CapacitorApp0B6PluginC8getStateyySo13CAPPluginCallCFyyScMYccfU_TA', symObjAddr: 0x397C, symBinAddr: 0x797C, symSize: 0x14C }
  - { offsetInCU: 0x19BC, offset: 0x5A3AA, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x3AC8, symBinAddr: 0x7AC8, symSize: 0x48 }
  - { offsetInCU: 0x19D0, offset: 0x5A3BE, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x3B10, symBinAddr: 0x7B10, symSize: 0x4C }
  - { offsetInCU: 0x19E4, offset: 0x5A3D2, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x3B5C, symBinAddr: 0x7B5C, symSize: 0x44 }
  - { offsetInCU: 0x19F8, offset: 0x5A3E6, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0x3C20, symBinAddr: 0x7C20, symSize: 0x3C }
  - { offsetInCU: 0x1AA1, offset: 0x5A48F, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_9Capacitor7JSValue_pSSypTg5', symObjAddr: 0xD40, symBinAddr: 0x4D40, symSize: 0x39C }
  - { offsetInCU: 0x1BF4, offset: 0x5A5E2, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0x268C, symBinAddr: 0x668C, symSize: 0x110 }
  - { offsetInCU: 0x1D1D, offset: 0x5A70B, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypSgTg5Tf4gd_n', symObjAddr: 0x279C, symBinAddr: 0x679C, symSize: 0x110 }
  - { offsetInCU: 0x1E46, offset: 0x5A834, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_9Capacitor7JSValue_pTg5Tf4gd_n', symObjAddr: 0x28AC, symBinAddr: 0x68AC, symSize: 0x114 }
...
