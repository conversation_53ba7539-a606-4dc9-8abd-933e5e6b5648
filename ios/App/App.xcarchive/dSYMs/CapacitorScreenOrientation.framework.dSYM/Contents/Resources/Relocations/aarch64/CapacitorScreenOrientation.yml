---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorScreenOrientation.framework/CapacitorScreenOrientation'
relocations:
  - { offsetInCU: 0x34, offset: 0x582AA, size: 0x8, addend: 0x0, symName: _CapacitorScreenOrientationVersionString, symObjAddr: 0x0, symBinAddr: 0x8B50, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x582DF, size: 0x8, addend: 0x0, symName: _CapacitorScreenOrientationVersionNumber, symObjAddr: 0x38, symBinAddr: 0x8B88, symSize: 0x0 }
  - { offsetInCU: 0x56, offset: 0x5834B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x4000, symSize: 0x8 }
  - { offsetInCU: 0xAA, offset: 0x5839F, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x8, symBinAddr: 0x4008, symSize: 0x40 }
  - { offsetInCU: 0x18D, offset: 0x58482, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x48, symBinAddr: 0x4048, symSize: 0x24 }
  - { offsetInCU: 0x253, offset: 0x58548, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C03setA14ViewControlleryy0A009CAPBridgeeF0CF', symObjAddr: 0xCC, symBinAddr: 0x40CC, symSize: 0x78 }
  - { offsetInCU: 0x308, offset: 0x585FD, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C010getCurrentC4TypeSSyF', symObjAddr: 0x144, symBinAddr: 0x4144, symSize: 0xD4 }
  - { offsetInCU: 0x368, offset: 0x5865D, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C4lock_10completionySS_ys5Error_pSgctF', symObjAddr: 0x218, symBinAddr: 0x4218, symSize: 0x210 }
  - { offsetInCU: 0x3F8, offset: 0x586ED, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C4lock_10completionySS_ys5Error_pSgctFyyScMYccfU_', symObjAddr: 0x428, symBinAddr: 0x4428, symSize: 0x3C4 }
  - { offsetInCU: 0x5FE, offset: 0x588F3, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C6unlock10completionyys5Error_pSgc_tF', symObjAddr: 0xAE8, symBinAddr: 0x4AE8, symSize: 0x1FC }
  - { offsetInCU: 0x67E, offset: 0x58973, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C6unlock10completionyys5Error_pSgc_tFyyScMYccfU_', symObjAddr: 0xCE4, symBinAddr: 0x4CE4, symSize: 0x2F0 }
  - { offsetInCU: 0x785, offset: 0x58A7A, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CACycfC', symObjAddr: 0x100C, symBinAddr: 0x500C, symSize: 0x20 }
  - { offsetInCU: 0x799, offset: 0x58A8E, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CACycfc', symObjAddr: 0x102C, symBinAddr: 0x502C, symSize: 0x50 }
  - { offsetInCU: 0x7D4, offset: 0x58AC9, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CACycfcTo', symObjAddr: 0x109C, symBinAddr: 0x509C, symSize: 0x5C }
  - { offsetInCU: 0x80F, offset: 0x58B04, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CfD', symObjAddr: 0x10F8, symBinAddr: 0x50F8, symSize: 0x30 }
  - { offsetInCU: 0x84D, offset: 0x58B42, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C04fromC9TypeToInt33_590F78E80B0C9FA564D252109B5BBEC7LLySiSSFTf4nd_n', symObjAddr: 0x173C, symBinAddr: 0x573C, symSize: 0x1A8 }
  - { offsetInCU: 0x904, offset: 0x58BF9, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C04fromC10TypeToMask33_590F78E80B0C9FA564D252109B5BBEC7LLySo011UIInterfacecG0VSSFTf4nd_n', symObjAddr: 0x18E4, symBinAddr: 0x58E4, symSize: 0x1B0 }
  - { offsetInCU: 0x9D2, offset: 0x58CC7, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C21supportedOrientations33_590F78E80B0C9FA564D252109B5BBEC7LLSaySiGvpfi', symObjAddr: 0xB8, symBinAddr: 0x40B8, symSize: 0xC }
  - { offsetInCU: 0x9EA, offset: 0x58CDF, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C17capViewController33_590F78E80B0C9FA564D252109B5BBEC7LL0A009CAPBridgeeF0CSgvpfi', symObjAddr: 0xC4, symBinAddr: 0x40C4, symSize: 0x8 }
  - { offsetInCU: 0xA2E, offset: 0x58D23, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C4lock_10completionySS_ys5Error_pSgctFyyScMYccfU_TA', symObjAddr: 0x820, symBinAddr: 0x4820, symSize: 0x10 }
  - { offsetInCU: 0xAC6, offset: 0x58DBB, size: 0x8, addend: 0x0, symName: '_$ss5Error_pIegg_So7NSErrorCIeyBy_TR', symObjAddr: 0x93C, symBinAddr: 0x493C, symSize: 0x50 }
  - { offsetInCU: 0xADE, offset: 0x58DD3, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x98C, symBinAddr: 0x498C, symSize: 0x2C }
  - { offsetInCU: 0xAF6, offset: 0x58DEB, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x9B8, symBinAddr: 0x49B8, symSize: 0x10 }
  - { offsetInCU: 0xB0A, offset: 0x58DFF, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x9C8, symBinAddr: 0x49C8, symSize: 0x8 }
  - { offsetInCU: 0xB1E, offset: 0x58E13, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x9D0, symBinAddr: 0x49D0, symSize: 0x48 }
  - { offsetInCU: 0xB32, offset: 0x58E27, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0xA18, symBinAddr: 0x4A18, symSize: 0x40 }
  - { offsetInCU: 0xB46, offset: 0x58E3B, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0xA58, symBinAddr: 0x4A58, symSize: 0x4C }
  - { offsetInCU: 0xB5A, offset: 0x58E4F, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0xAA4, symBinAddr: 0x4AA4, symSize: 0x44 }
  - { offsetInCU: 0xB6E, offset: 0x58E63, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C6unlock10completionyys5Error_pSgc_tFyyScMYccfU_TA', symObjAddr: 0x1000, symBinAddr: 0x5000, symSize: 0xC }
  - { offsetInCU: 0xB82, offset: 0x58E77, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CMa', symObjAddr: 0x107C, symBinAddr: 0x507C, symSize: 0x20 }
  - { offsetInCU: 0xB96, offset: 0x58E8B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0CfETo', symObjAddr: 0x1128, symBinAddr: 0x5128, symSize: 0x38 }
  - { offsetInCU: 0xBC5, offset: 0x58EBA, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC10identifierSSvpfi', symObjAddr: 0x1160, symBinAddr: 0x5160, symSize: 0x1C }
  - { offsetInCU: 0xBDD, offset: 0x58ED2, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6jsNameSSvpfi', symObjAddr: 0x117C, symBinAddr: 0x517C, symSize: 0x1C }
  - { offsetInCU: 0xC21, offset: 0x58F16, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvpfi', symObjAddr: 0x1198, symBinAddr: 0x5198, symSize: 0x1C8 }
  - { offsetInCU: 0xD2C, offset: 0x59021, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC14implementation33_BBF6BB227F5DBC9D8356328E394BB51BLLAA0bC0Cvpfi', symObjAddr: 0x1360, symBinAddr: 0x5360, symSize: 0x20 }
  - { offsetInCU: 0xD86, offset: 0x5907B, size: 0x8, addend: 0x0, symName: '_$sShyxSh5IndexVyx_GcigSo7UISceneC_Tg5', symObjAddr: 0x1380, symBinAddr: 0x5380, symSize: 0x30C }
  - { offsetInCU: 0xDF7, offset: 0x590EC, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV10startIndexSh0D0Vyx_GvgSo7UISceneC_Tg5', symObjAddr: 0x168C, symBinAddr: 0x568C, symSize: 0xB0 }
  - { offsetInCU: 0xE25, offset: 0x5911A, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo15CAPPluginMethodC_Tg5Tf4d_n', symObjAddr: 0x1A94, symBinAddr: 0x5A94, symSize: 0x64 }
  - { offsetInCU: 0xE52, offset: 0x59147, size: 0x8, addend: 0x0, symName: '_$sSo7UISceneCSo8NSObjectCSH10ObjectiveCWl', symObjAddr: 0x1AF8, symBinAddr: 0x5AF8, symSize: 0x58 }
  - { offsetInCU: 0xE66, offset: 0x5915B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOAEs0D0AAWl', symObjAddr: 0x1B50, symBinAddr: 0x5B50, symSize: 0x44 }
  - { offsetInCU: 0xE7A, offset: 0x5916F, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOe', symObjAddr: 0x1BD0, symBinAddr: 0x5BD0, symSize: 0xC }
  - { offsetInCU: 0xE8E, offset: 0x59183, size: 0x8, addend: 0x0, symName: '_$sSh5IndexV8_VariantOyx__GSHRzlWOy', symObjAddr: 0x1BDC, symBinAddr: 0x5BDC, symSize: 0xC }
  - { offsetInCU: 0xEA2, offset: 0x59197, size: 0x8, addend: 0x0, symName: ___swift_memcpy0_1, symObjAddr: 0x1C30, symBinAddr: 0x5C30, symSize: 0x4 }
  - { offsetInCU: 0xEB6, offset: 0x591AB, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1C34, symBinAddr: 0x5C34, symSize: 0x4 }
  - { offsetInCU: 0xECA, offset: 0x591BF, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOwet', symObjAddr: 0x1C38, symBinAddr: 0x5C38, symSize: 0x50 }
  - { offsetInCU: 0xEDE, offset: 0x591D3, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOwst', symObjAddr: 0x1C88, symBinAddr: 0x5C88, symSize: 0x8C }
  - { offsetInCU: 0xEF2, offset: 0x591E7, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOwug', symObjAddr: 0x1D14, symBinAddr: 0x5D14, symSize: 0x8 }
  - { offsetInCU: 0xF06, offset: 0x591FB, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOwup', symObjAddr: 0x1D1C, symBinAddr: 0x5D1C, symSize: 0x4 }
  - { offsetInCU: 0xF1A, offset: 0x5920F, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOwui', symObjAddr: 0x1D20, symBinAddr: 0x5D20, symSize: 0x4 }
  - { offsetInCU: 0xF2E, offset: 0x59223, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOMa', symObjAddr: 0x1D24, symBinAddr: 0x5D24, symSize: 0x10 }
  - { offsetInCU: 0xF42, offset: 0x59237, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOSHAASQWb', symObjAddr: 0x1D34, symBinAddr: 0x5D34, symSize: 0x4 }
  - { offsetInCU: 0xF56, offset: 0x5924B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOAESQAAWl', symObjAddr: 0x1D38, symBinAddr: 0x5D38, symSize: 0x44 }
  - { offsetInCU: 0xFD3, offset: 0x592C8, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x6C, symBinAddr: 0x406C, symSize: 0x3C }
  - { offsetInCU: 0x106F, offset: 0x59364, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOs0D0AAsAFP7_domainSSvgTW', symObjAddr: 0xA8, symBinAddr: 0x40A8, symSize: 0x4 }
  - { offsetInCU: 0x108B, offset: 0x59380, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOs0D0AAsAFP5_codeSivgTW', symObjAddr: 0xAC, symBinAddr: 0x40AC, symSize: 0x4 }
  - { offsetInCU: 0x10A7, offset: 0x5939C, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOs0D0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0xB0, symBinAddr: 0x40B0, symSize: 0x4 }
  - { offsetInCU: 0x10C3, offset: 0x593B8, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC0C0bC5ErrorOs0D0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0xB4, symBinAddr: 0x40B4, symSize: 0x4 }
  - { offsetInCU: 0x114C, offset: 0x59441, size: 0x8, addend: 0x0, symName: '_$sSlsE5first7ElementQzSgvgShySo7UISceneCG_Tg5', symObjAddr: 0x830, symBinAddr: 0x4830, symSize: 0x10C }
  - { offsetInCU: 0x6D, offset: 0x59820, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvgTo', symObjAddr: 0xAC, symBinAddr: 0x5E44, symSize: 0x60 }
  - { offsetInCU: 0xA8, offset: 0x5985B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvg', symObjAddr: 0x10C, symBinAddr: 0x5EA4, symSize: 0x10 }
  - { offsetInCU: 0xC5, offset: 0x59878, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC4loadyyF', symObjAddr: 0x11C, symBinAddr: 0x5EB4, symSize: 0x164 }
  - { offsetInCU: 0x15D, offset: 0x59910, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC4loadyyFTo', symObjAddr: 0x280, symBinAddr: 0x6018, symSize: 0x2C }
  - { offsetInCU: 0x179, offset: 0x5992C, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCfD', symObjAddr: 0x2AC, symBinAddr: 0x6044, symSize: 0x74 }
  - { offsetInCU: 0x1B6, offset: 0x59969, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCfDTo', symObjAddr: 0x340, symBinAddr: 0x60D8, symSize: 0x98 }
  - { offsetInCU: 0x1EB, offset: 0x5999E, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC11orientationyySo13CAPPluginCallCF', symObjAddr: 0x438, symBinAddr: 0x61D0, symSize: 0x4 }
  - { offsetInCU: 0x20E, offset: 0x599C1, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC11orientationyySo13CAPPluginCallCFTo', symObjAddr: 0x43C, symBinAddr: 0x61D4, symSize: 0x4C }
  - { offsetInCU: 0x240, offset: 0x599F3, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC4lockyySo13CAPPluginCallCF', symObjAddr: 0x488, symBinAddr: 0x6220, symSize: 0x140 }
  - { offsetInCU: 0x296, offset: 0x59A49, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC4lockyySo13CAPPluginCallCFTo', symObjAddr: 0x5C8, symBinAddr: 0x6360, symSize: 0x50 }
  - { offsetInCU: 0x2B2, offset: 0x59A65, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6unlockyySo13CAPPluginCallCF', symObjAddr: 0x618, symBinAddr: 0x63B0, symSize: 0x68 }
  - { offsetInCU: 0x305, offset: 0x59AB8, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6unlockyySo13CAPPluginCallCFTo', symObjAddr: 0x790, symBinAddr: 0x6528, symSize: 0x90 }
  - { offsetInCU: 0x344, offset: 0x59AF7, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC20orientationDidChange33_BBF6BB227F5DBC9D8356328E394BB51BLLyyF', symObjAddr: 0x820, symBinAddr: 0x65B8, symSize: 0x228 }
  - { offsetInCU: 0x475, offset: 0x59C28, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC20orientationDidChange33_BBF6BB227F5DBC9D8356328E394BB51BLLyyFTo', symObjAddr: 0xA48, symBinAddr: 0x67E0, symSize: 0x2C }
  - { offsetInCU: 0x491, offset: 0x59C44, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0xA74, symBinAddr: 0x680C, symSize: 0xB4 }
  - { offsetInCU: 0x4AF, offset: 0x59C62, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0xB28, symBinAddr: 0x68C0, symSize: 0x2F4 }
  - { offsetInCU: 0x5AC, offset: 0x59D5F, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC6bridge8pluginId0F4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0xE1C, symBinAddr: 0x6BB4, symSize: 0x74 }
  - { offsetInCU: 0x5C8, offset: 0x59D7B, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCACycfC', symObjAddr: 0xE90, symBinAddr: 0x6C28, symSize: 0x20 }
  - { offsetInCU: 0x5E6, offset: 0x59D99, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCACycfc', symObjAddr: 0xEB0, symBinAddr: 0x6C48, symSize: 0x27C }
  - { offsetInCU: 0x6B9, offset: 0x59E6C, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCACycfcTo', symObjAddr: 0x112C, symBinAddr: 0x6EC4, symSize: 0x20 }
  - { offsetInCU: 0x6D5, offset: 0x59E88, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC11orientationyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0x138C, symBinAddr: 0x7124, symSize: 0x1C0 }
  - { offsetInCU: 0x8DA, offset: 0x5A08D, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCMa', symObjAddr: 0x320, symBinAddr: 0x60B8, symSize: 0x20 }
  - { offsetInCU: 0x8EE, offset: 0x5A0A1, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginCfETo', symObjAddr: 0x3D8, symBinAddr: 0x6170, symSize: 0x60 }
  - { offsetInCU: 0x9A1, offset: 0x5A154, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x114C, symBinAddr: 0x6EE4, symSize: 0x64 }
  - { offsetInCU: 0xA04, offset: 0x5A1B7, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x11B0, symBinAddr: 0x6F48, symSize: 0xE0 }
  - { offsetInCU: 0xABC, offset: 0x5A26F, size: 0x8, addend: 0x0, symName: '_$s26CapacitorScreenOrientation0bC6PluginC4lockyySo13CAPPluginCallCFys5Error_pSgcfU_TA', symObjAddr: 0x1570, symBinAddr: 0x7308, symSize: 0x18 }
  - { offsetInCU: 0xAD0, offset: 0x5A283, size: 0x8, addend: 0x0, symName: '_$sSS_yptWOc', symObjAddr: 0x15C8, symBinAddr: 0x7320, symSize: 0x48 }
  - { offsetInCU: 0xAE4, offset: 0x5A297, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x1610, symBinAddr: 0x7368, symSize: 0x10 }
  - { offsetInCU: 0xBA3, offset: 0x5A356, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0x1290, symBinAddr: 0x7028, symSize: 0xFC }
...
