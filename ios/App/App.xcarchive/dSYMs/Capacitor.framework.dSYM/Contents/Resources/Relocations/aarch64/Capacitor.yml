---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/Capacitor.framework/Capacitor'
relocations:
  - { offsetInCU: 0x34, offset: 0x5BE39, size: 0x8, addend: 0x0, symName: _CapacitorVersionString, symObjAddr: 0x0, symBinAddr: 0x77790, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x5BE6E, size: 0x8, addend: 0x0, symName: _CapacitorVersionNumber, symObjAddr: 0x28, symBinAddr: 0x777B8, symSize: 0x0 }
  - { offsetInCU: 0x27, offset: 0x5BEAB, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getString:defaultValue:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xC0 }
  - { offsetInCU: 0xD3, offset: 0x5BF57, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getString:defaultValue:]', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0xC0 }
  - { offsetInCU: 0x13A, offset: 0x5BFBE, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getDate:defaultValue:]', symObjAddr: 0xC0, symBinAddr: 0x80C0, symSize: 0x11C }
  - { offsetInCU: 0x1A1, offset: 0x5C025, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getObject:defaultValue:]', symObjAddr: 0x1DC, symBinAddr: 0x81DC, symSize: 0xC0 }
  - { offsetInCU: 0x208, offset: 0x5C08C, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getNumber:defaultValue:]', symObjAddr: 0x29C, symBinAddr: 0x829C, symSize: 0xC0 }
  - { offsetInCU: 0x26F, offset: 0x5C0F3, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall(BridgedJSProtocol) getBool:defaultValue:]', symObjAddr: 0x35C, symBinAddr: 0x835C, symSize: 0x98 }
  - { offsetInCU: 0x27, offset: 0x5C386, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration initWithDescriptor:isDebug:]', symObjAddr: 0x0, symBinAddr: 0x83F4, symSize: 0x360 }
  - { offsetInCU: 0x38B, offset: 0x5C6EA, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration initWithDescriptor:isDebug:]', symObjAddr: 0x0, symBinAddr: 0x83F4, symSize: 0x360 }
  - { offsetInCU: 0x3DE, offset: 0x5C73D, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration initWithConfiguration:andLocation:]', symObjAddr: 0x360, symBinAddr: 0x8754, symSize: 0x2C4 }
  - { offsetInCU: 0x435, offset: 0x5C794, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration updatingAppLocation:]', symObjAddr: 0x624, symBinAddr: 0x8A18, symSize: 0x5C }
  - { offsetInCU: 0x47C, offset: 0x5C7DB, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration appendedUserAgentString]', symObjAddr: 0x680, symBinAddr: 0x8A74, symSize: 0x8 }
  - { offsetInCU: 0x4B3, offset: 0x5C812, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration overridenUserAgentString]', symObjAddr: 0x688, symBinAddr: 0x8A7C, symSize: 0x8 }
  - { offsetInCU: 0x4EA, offset: 0x5C849, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration backgroundColor]', symObjAddr: 0x690, symBinAddr: 0x8A84, symSize: 0x8 }
  - { offsetInCU: 0x521, offset: 0x5C880, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration allowedNavigationHostnames]', symObjAddr: 0x698, symBinAddr: 0x8A8C, symSize: 0x8 }
  - { offsetInCU: 0x558, offset: 0x5C8B7, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration localURL]', symObjAddr: 0x6A0, symBinAddr: 0x8A94, symSize: 0x8 }
  - { offsetInCU: 0x58F, offset: 0x5C8EE, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration serverURL]', symObjAddr: 0x6A8, symBinAddr: 0x8A9C, symSize: 0x8 }
  - { offsetInCU: 0x5C6, offset: 0x5C925, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration errorPath]', symObjAddr: 0x6B0, symBinAddr: 0x8AA4, symSize: 0x8 }
  - { offsetInCU: 0x5FD, offset: 0x5C95C, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration pluginConfigurations]', symObjAddr: 0x6B8, symBinAddr: 0x8AAC, symSize: 0x8 }
  - { offsetInCU: 0x634, offset: 0x5C993, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration loggingEnabled]', symObjAddr: 0x6C0, symBinAddr: 0x8AB4, symSize: 0x8 }
  - { offsetInCU: 0x66B, offset: 0x5C9CA, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration scrollingEnabled]', symObjAddr: 0x6C8, symBinAddr: 0x8ABC, symSize: 0x8 }
  - { offsetInCU: 0x6A2, offset: 0x5CA01, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration zoomingEnabled]', symObjAddr: 0x6D0, symBinAddr: 0x8AC4, symSize: 0x8 }
  - { offsetInCU: 0x6D9, offset: 0x5CA38, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration allowLinkPreviews]', symObjAddr: 0x6D8, symBinAddr: 0x8ACC, symSize: 0x8 }
  - { offsetInCU: 0x710, offset: 0x5CA6F, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration handleApplicationNotifications]', symObjAddr: 0x6E0, symBinAddr: 0x8AD4, symSize: 0x8 }
  - { offsetInCU: 0x747, offset: 0x5CAA6, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration isWebDebuggable]', symObjAddr: 0x6E8, symBinAddr: 0x8ADC, symSize: 0x8 }
  - { offsetInCU: 0x77E, offset: 0x5CADD, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration hasInitialFocus]', symObjAddr: 0x6F0, symBinAddr: 0x8AE4, symSize: 0x8 }
  - { offsetInCU: 0x7B5, offset: 0x5CB14, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration cordovaDeployDisabled]', symObjAddr: 0x6F8, symBinAddr: 0x8AEC, symSize: 0x8 }
  - { offsetInCU: 0x7EC, offset: 0x5CB4B, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration contentInsetAdjustmentBehavior]', symObjAddr: 0x700, symBinAddr: 0x8AF4, symSize: 0x8 }
  - { offsetInCU: 0x823, offset: 0x5CB82, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration appLocation]', symObjAddr: 0x708, symBinAddr: 0x8AFC, symSize: 0x8 }
  - { offsetInCU: 0x85A, offset: 0x5CBB9, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration appStartPath]', symObjAddr: 0x710, symBinAddr: 0x8B04, symSize: 0x8 }
  - { offsetInCU: 0x891, offset: 0x5CBF0, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration limitsNavigationsToAppBoundDomains]', symObjAddr: 0x718, symBinAddr: 0x8B0C, symSize: 0x8 }
  - { offsetInCU: 0x8C8, offset: 0x5CC27, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration preferredContentMode]', symObjAddr: 0x720, symBinAddr: 0x8B14, symSize: 0x8 }
  - { offsetInCU: 0x8FF, offset: 0x5CC5E, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration legacyConfig]', symObjAddr: 0x728, symBinAddr: 0x8B1C, symSize: 0x8 }
  - { offsetInCU: 0x936, offset: 0x5CC95, size: 0x8, addend: 0x0, symName: '-[CAPInstanceConfiguration .cxx_destruct]', symObjAddr: 0x730, symBinAddr: 0x8B24, symSize: 0xA8 }
  - { offsetInCU: 0x27, offset: 0x5CEBD, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor initAsDefault]', symObjAddr: 0x0, symBinAddr: 0x8BCC, symSize: 0x144 }
  - { offsetInCU: 0x41, offset: 0x5CED7, size: 0x8, addend: 0x0, symName: _CAPInstanceDescriptorDefaultScheme, symObjAddr: 0x6C0, symBinAddr: 0x914C8, symSize: 0x0 }
  - { offsetInCU: 0x61, offset: 0x5CEF7, size: 0x8, addend: 0x0, symName: _CAPInstanceDescriptorDefaultHostname, symObjAddr: 0x6C8, symBinAddr: 0x914D0, symSize: 0x0 }
  - { offsetInCU: 0x439, offset: 0x5D2CF, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor initAsDefault]', symObjAddr: 0x0, symBinAddr: 0x8BCC, symSize: 0x144 }
  - { offsetInCU: 0x470, offset: 0x5D306, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor initAtLocation:configuration:cordovaConfiguration:]', symObjAddr: 0x144, symBinAddr: 0x8D10, symSize: 0xBC }
  - { offsetInCU: 0x4D7, offset: 0x5D36D, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor _setDefaultsWithAppLocation:]', symObjAddr: 0x200, symBinAddr: 0x8DCC, symSize: 0x14C }
  - { offsetInCU: 0x51A, offset: 0x5D3B0, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor appendedUserAgentString]', symObjAddr: 0x34C, symBinAddr: 0x8F18, symSize: 0x8 }
  - { offsetInCU: 0x551, offset: 0x5D3E7, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setAppendedUserAgentString:]', symObjAddr: 0x354, symBinAddr: 0x8F20, symSize: 0x8 }
  - { offsetInCU: 0x590, offset: 0x5D426, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor overridenUserAgentString]', symObjAddr: 0x35C, symBinAddr: 0x8F28, symSize: 0x8 }
  - { offsetInCU: 0x5C7, offset: 0x5D45D, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setOverridenUserAgentString:]', symObjAddr: 0x364, symBinAddr: 0x8F30, symSize: 0x8 }
  - { offsetInCU: 0x606, offset: 0x5D49C, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor backgroundColor]', symObjAddr: 0x36C, symBinAddr: 0x8F38, symSize: 0x8 }
  - { offsetInCU: 0x63D, offset: 0x5D4D3, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setBackgroundColor:]', symObjAddr: 0x374, symBinAddr: 0x8F40, symSize: 0xC }
  - { offsetInCU: 0x67E, offset: 0x5D514, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor allowedNavigationHostnames]', symObjAddr: 0x380, symBinAddr: 0x8F4C, symSize: 0x8 }
  - { offsetInCU: 0x6B5, offset: 0x5D54B, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setAllowedNavigationHostnames:]', symObjAddr: 0x388, symBinAddr: 0x8F54, symSize: 0x8 }
  - { offsetInCU: 0x6F4, offset: 0x5D58A, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor urlScheme]', symObjAddr: 0x390, symBinAddr: 0x8F5C, symSize: 0x8 }
  - { offsetInCU: 0x72B, offset: 0x5D5C1, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setUrlScheme:]', symObjAddr: 0x398, symBinAddr: 0x8F64, symSize: 0x8 }
  - { offsetInCU: 0x76A, offset: 0x5D600, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor errorPath]', symObjAddr: 0x3A0, symBinAddr: 0x8F6C, symSize: 0x8 }
  - { offsetInCU: 0x7A1, offset: 0x5D637, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setErrorPath:]', symObjAddr: 0x3A8, symBinAddr: 0x8F74, symSize: 0x8 }
  - { offsetInCU: 0x7E0, offset: 0x5D676, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor urlHostname]', symObjAddr: 0x3B0, symBinAddr: 0x8F7C, symSize: 0x8 }
  - { offsetInCU: 0x817, offset: 0x5D6AD, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setUrlHostname:]', symObjAddr: 0x3B8, symBinAddr: 0x8F84, symSize: 0x8 }
  - { offsetInCU: 0x856, offset: 0x5D6EC, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor serverURL]', symObjAddr: 0x3C0, symBinAddr: 0x8F8C, symSize: 0x8 }
  - { offsetInCU: 0x88D, offset: 0x5D723, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setServerURL:]', symObjAddr: 0x3C8, symBinAddr: 0x8F94, symSize: 0x8 }
  - { offsetInCU: 0x8CC, offset: 0x5D762, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor pluginConfigurations]', symObjAddr: 0x3D0, symBinAddr: 0x8F9C, symSize: 0x8 }
  - { offsetInCU: 0x903, offset: 0x5D799, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setPluginConfigurations:]', symObjAddr: 0x3D8, symBinAddr: 0x8FA4, symSize: 0xC }
  - { offsetInCU: 0x944, offset: 0x5D7DA, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor loggingBehavior]', symObjAddr: 0x3E4, symBinAddr: 0x8FB0, symSize: 0x8 }
  - { offsetInCU: 0x97B, offset: 0x5D811, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setLoggingBehavior:]', symObjAddr: 0x3EC, symBinAddr: 0x8FB8, symSize: 0x8 }
  - { offsetInCU: 0x9B8, offset: 0x5D84E, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor scrollingEnabled]', symObjAddr: 0x3F4, symBinAddr: 0x8FC0, symSize: 0x8 }
  - { offsetInCU: 0x9EF, offset: 0x5D885, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setScrollingEnabled:]', symObjAddr: 0x3FC, symBinAddr: 0x8FC8, symSize: 0x8 }
  - { offsetInCU: 0xA2A, offset: 0x5D8C0, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor zoomingEnabled]', symObjAddr: 0x404, symBinAddr: 0x8FD0, symSize: 0x8 }
  - { offsetInCU: 0xA61, offset: 0x5D8F7, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setZoomingEnabled:]', symObjAddr: 0x40C, symBinAddr: 0x8FD8, symSize: 0x8 }
  - { offsetInCU: 0xA9C, offset: 0x5D932, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor allowLinkPreviews]', symObjAddr: 0x414, symBinAddr: 0x8FE0, symSize: 0x8 }
  - { offsetInCU: 0xAD3, offset: 0x5D969, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setAllowLinkPreviews:]', symObjAddr: 0x41C, symBinAddr: 0x8FE8, symSize: 0x8 }
  - { offsetInCU: 0xB0E, offset: 0x5D9A4, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor handleApplicationNotifications]', symObjAddr: 0x424, symBinAddr: 0x8FF0, symSize: 0x8 }
  - { offsetInCU: 0xB45, offset: 0x5D9DB, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setHandleApplicationNotifications:]', symObjAddr: 0x42C, symBinAddr: 0x8FF8, symSize: 0x8 }
  - { offsetInCU: 0xB80, offset: 0x5DA16, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor isWebDebuggable]', symObjAddr: 0x434, symBinAddr: 0x9000, symSize: 0x8 }
  - { offsetInCU: 0xBB7, offset: 0x5DA4D, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setIsWebDebuggable:]', symObjAddr: 0x43C, symBinAddr: 0x9008, symSize: 0x8 }
  - { offsetInCU: 0xBF2, offset: 0x5DA88, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor hasInitialFocus]', symObjAddr: 0x444, symBinAddr: 0x9010, symSize: 0x8 }
  - { offsetInCU: 0xC29, offset: 0x5DABF, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setHasInitialFocus:]', symObjAddr: 0x44C, symBinAddr: 0x9018, symSize: 0x8 }
  - { offsetInCU: 0xC64, offset: 0x5DAFA, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor contentInsetAdjustmentBehavior]', symObjAddr: 0x454, symBinAddr: 0x9020, symSize: 0x8 }
  - { offsetInCU: 0xC9B, offset: 0x5DB31, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setContentInsetAdjustmentBehavior:]', symObjAddr: 0x45C, symBinAddr: 0x9028, symSize: 0x8 }
  - { offsetInCU: 0xCD8, offset: 0x5DB6E, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor appLocation]', symObjAddr: 0x464, symBinAddr: 0x9030, symSize: 0x8 }
  - { offsetInCU: 0xD0F, offset: 0x5DBA5, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setAppLocation:]', symObjAddr: 0x46C, symBinAddr: 0x9038, symSize: 0x8 }
  - { offsetInCU: 0xD4E, offset: 0x5DBE4, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor appStartPath]', symObjAddr: 0x474, symBinAddr: 0x9040, symSize: 0x8 }
  - { offsetInCU: 0xD85, offset: 0x5DC1B, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setAppStartPath:]', symObjAddr: 0x47C, symBinAddr: 0x9048, symSize: 0x8 }
  - { offsetInCU: 0xDC4, offset: 0x5DC5A, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor limitsNavigationsToAppBoundDomains]', symObjAddr: 0x484, symBinAddr: 0x9050, symSize: 0x8 }
  - { offsetInCU: 0xDFB, offset: 0x5DC91, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setLimitsNavigationsToAppBoundDomains:]', symObjAddr: 0x48C, symBinAddr: 0x9058, symSize: 0x8 }
  - { offsetInCU: 0xE36, offset: 0x5DCCC, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor preferredContentMode]', symObjAddr: 0x494, symBinAddr: 0x9060, symSize: 0x8 }
  - { offsetInCU: 0xE6D, offset: 0x5DD03, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setPreferredContentMode:]', symObjAddr: 0x49C, symBinAddr: 0x9068, symSize: 0x8 }
  - { offsetInCU: 0xEAC, offset: 0x5DD42, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor cordovaConfiguration]', symObjAddr: 0x4A4, symBinAddr: 0x9070, symSize: 0x8 }
  - { offsetInCU: 0xEE3, offset: 0x5DD79, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setCordovaConfiguration:]', symObjAddr: 0x4AC, symBinAddr: 0x9078, symSize: 0x8 }
  - { offsetInCU: 0xF22, offset: 0x5DDB8, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor warnings]', symObjAddr: 0x4B4, symBinAddr: 0x9080, symSize: 0x8 }
  - { offsetInCU: 0xF59, offset: 0x5DDEF, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setWarnings:]', symObjAddr: 0x4BC, symBinAddr: 0x9088, symSize: 0x8 }
  - { offsetInCU: 0xF96, offset: 0x5DE2C, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor instanceType]', symObjAddr: 0x4C4, symBinAddr: 0x9090, symSize: 0x8 }
  - { offsetInCU: 0xFCD, offset: 0x5DE63, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor legacyConfig]', symObjAddr: 0x4CC, symBinAddr: 0x9098, symSize: 0x8 }
  - { offsetInCU: 0x1004, offset: 0x5DE9A, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor setLegacyConfig:]', symObjAddr: 0x4D4, symBinAddr: 0x90A0, symSize: 0xC }
  - { offsetInCU: 0x1045, offset: 0x5DEDB, size: 0x8, addend: 0x0, symName: '-[CAPInstanceDescriptor .cxx_destruct]', symObjAddr: 0x4E0, symBinAddr: 0x90AC, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x5DF84, size: 0x8, addend: 0x0, symName: '-[CAPPlugin initWithBridge:pluginId:pluginName:]', symObjAddr: 0x0, symBinAddr: 0x916C, symSize: 0x110 }
  - { offsetInCU: 0x1F7, offset: 0x5E154, size: 0x8, addend: 0x0, symName: '-[CAPPlugin initWithBridge:pluginId:pluginName:]', symObjAddr: 0x0, symBinAddr: 0x916C, symSize: 0x110 }
  - { offsetInCU: 0x25E, offset: 0x5E1BB, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getId]', symObjAddr: 0x110, symBinAddr: 0x927C, symSize: 0x4 }
  - { offsetInCU: 0x293, offset: 0x5E1F0, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getBool:field:defaultValue:]', symObjAddr: 0x114, symBinAddr: 0x9280, symSize: 0xAC }
  - { offsetInCU: 0x306, offset: 0x5E263, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getString:field:defaultValue:]', symObjAddr: 0x1C0, symBinAddr: 0x932C, symSize: 0x10 }
  - { offsetInCU: 0x369, offset: 0x5E2C6, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getConfigValue:]', symObjAddr: 0x1D0, symBinAddr: 0x933C, symSize: 0xB0 }
  - { offsetInCU: 0x3B0, offset: 0x5E30D, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getConfig]', symObjAddr: 0x280, symBinAddr: 0x93EC, symSize: 0x8C }
  - { offsetInCU: 0x3E7, offset: 0x5E344, size: 0x8, addend: 0x0, symName: '-[CAPPlugin load]', symObjAddr: 0x30C, symBinAddr: 0x9478, symSize: 0x4 }
  - { offsetInCU: 0x416, offset: 0x5E373, size: 0x8, addend: 0x0, symName: '-[CAPPlugin addEventListener:listener:]', symObjAddr: 0x310, symBinAddr: 0x947C, symSize: 0x110 }
  - { offsetInCU: 0x479, offset: 0x5E3D6, size: 0x8, addend: 0x0, symName: '-[CAPPlugin sendRetainedArgumentsForEvent:]', symObjAddr: 0x420, symBinAddr: 0x958C, symSize: 0x174 }
  - { offsetInCU: 0x4EB, offset: 0x5E448, size: 0x8, addend: 0x0, symName: '-[CAPPlugin removeEventListener:listener:]', symObjAddr: 0x594, symBinAddr: 0x9700, symSize: 0xAC }
  - { offsetInCU: 0x55E, offset: 0x5E4BB, size: 0x8, addend: 0x0, symName: '-[CAPPlugin notifyListeners:data:]', symObjAddr: 0x640, symBinAddr: 0x97AC, symSize: 0x8 }
  - { offsetInCU: 0x5AB, offset: 0x5E508, size: 0x8, addend: 0x0, symName: '-[CAPPlugin notifyListeners:data:retainUntilConsumed:]', symObjAddr: 0x648, symBinAddr: 0x97B4, symSize: 0x200 }
  - { offsetInCU: 0x696, offset: 0x5E5F3, size: 0x8, addend: 0x0, symName: '-[CAPPlugin addListener:]', symObjAddr: 0x848, symBinAddr: 0x99B4, symSize: 0x88 }
  - { offsetInCU: 0x6E9, offset: 0x5E646, size: 0x8, addend: 0x0, symName: '-[CAPPlugin removeListener:]', symObjAddr: 0x8D0, symBinAddr: 0x9A3C, symSize: 0x120 }
  - { offsetInCU: 0x75C, offset: 0x5E6B9, size: 0x8, addend: 0x0, symName: '-[CAPPlugin removeAllListeners:]', symObjAddr: 0x9F0, symBinAddr: 0x9B5C, symSize: 0x54 }
  - { offsetInCU: 0x79F, offset: 0x5E6FC, size: 0x8, addend: 0x0, symName: '-[CAPPlugin getListeners:]', symObjAddr: 0xA44, symBinAddr: 0x9BB0, symSize: 0x6C }
  - { offsetInCU: 0x7F6, offset: 0x5E753, size: 0x8, addend: 0x0, symName: '-[CAPPlugin hasListeners:]', symObjAddr: 0xAB0, symBinAddr: 0x9C1C, symSize: 0x90 }
  - { offsetInCU: 0x84D, offset: 0x5E7AA, size: 0x8, addend: 0x0, symName: '-[CAPPlugin checkPermissions:]', symObjAddr: 0xB40, symBinAddr: 0x9CAC, symSize: 0x8 }
  - { offsetInCU: 0x88C, offset: 0x5E7E9, size: 0x8, addend: 0x0, symName: '-[CAPPlugin requestPermissions:]', symObjAddr: 0xB48, symBinAddr: 0x9CB4, symSize: 0x8 }
  - { offsetInCU: 0x8CB, offset: 0x5E828, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setCenteredPopover:]', symObjAddr: 0xB50, symBinAddr: 0x9CBC, symSize: 0x1F4 }
  - { offsetInCU: 0x90E, offset: 0x5E86B, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setCenteredPopover:size:]', symObjAddr: 0xD44, symBinAddr: 0x9EB0, symSize: 0x214 }
  - { offsetInCU: 0x95D, offset: 0x5E8BA, size: 0x8, addend: 0x0, symName: '-[CAPPlugin supportsPopover]', symObjAddr: 0xF58, symBinAddr: 0xA0C4, symSize: 0x8 }
  - { offsetInCU: 0x990, offset: 0x5E8ED, size: 0x8, addend: 0x0, symName: '-[CAPPlugin shouldOverrideLoad:]', symObjAddr: 0xF60, symBinAddr: 0xA0CC, symSize: 0x8 }
  - { offsetInCU: 0x9CF, offset: 0x5E92C, size: 0x8, addend: 0x0, symName: '-[CAPPlugin webView]', symObjAddr: 0xF68, symBinAddr: 0xA0D4, symSize: 0x18 }
  - { offsetInCU: 0xA06, offset: 0x5E963, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setWebView:]', symObjAddr: 0xF80, symBinAddr: 0xA0EC, symSize: 0xC }
  - { offsetInCU: 0xA47, offset: 0x5E9A4, size: 0x8, addend: 0x0, symName: '-[CAPPlugin bridge]', symObjAddr: 0xF8C, symBinAddr: 0xA0F8, symSize: 0x18 }
  - { offsetInCU: 0xA7E, offset: 0x5E9DB, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setBridge:]', symObjAddr: 0xFA4, symBinAddr: 0xA110, symSize: 0xC }
  - { offsetInCU: 0xABF, offset: 0x5EA1C, size: 0x8, addend: 0x0, symName: '-[CAPPlugin pluginId]', symObjAddr: 0xFB0, symBinAddr: 0xA11C, symSize: 0x8 }
  - { offsetInCU: 0xAF6, offset: 0x5EA53, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setPluginId:]', symObjAddr: 0xFB8, symBinAddr: 0xA124, symSize: 0xC }
  - { offsetInCU: 0xB37, offset: 0x5EA94, size: 0x8, addend: 0x0, symName: '-[CAPPlugin pluginName]', symObjAddr: 0xFC4, symBinAddr: 0xA130, symSize: 0x8 }
  - { offsetInCU: 0xB6E, offset: 0x5EACB, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setPluginName:]', symObjAddr: 0xFCC, symBinAddr: 0xA138, symSize: 0xC }
  - { offsetInCU: 0xBAF, offset: 0x5EB0C, size: 0x8, addend: 0x0, symName: '-[CAPPlugin eventListeners]', symObjAddr: 0xFD8, symBinAddr: 0xA144, symSize: 0x8 }
  - { offsetInCU: 0xBE6, offset: 0x5EB43, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setEventListeners:]', symObjAddr: 0xFE0, symBinAddr: 0xA14C, symSize: 0xC }
  - { offsetInCU: 0xC27, offset: 0x5EB84, size: 0x8, addend: 0x0, symName: '-[CAPPlugin retainedEventArguments]', symObjAddr: 0xFEC, symBinAddr: 0xA158, symSize: 0x8 }
  - { offsetInCU: 0xC5E, offset: 0x5EBBB, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setRetainedEventArguments:]', symObjAddr: 0xFF4, symBinAddr: 0xA160, symSize: 0xC }
  - { offsetInCU: 0xC9F, offset: 0x5EBFC, size: 0x8, addend: 0x0, symName: '-[CAPPlugin shouldStringifyDatesInCalls]', symObjAddr: 0x1000, symBinAddr: 0xA16C, symSize: 0x8 }
  - { offsetInCU: 0xCD6, offset: 0x5EC33, size: 0x8, addend: 0x0, symName: '-[CAPPlugin setShouldStringifyDatesInCalls:]', symObjAddr: 0x1008, symBinAddr: 0xA174, symSize: 0x8 }
  - { offsetInCU: 0xD11, offset: 0x5EC6E, size: 0x8, addend: 0x0, symName: '-[CAPPlugin .cxx_destruct]', symObjAddr: 0x1010, symBinAddr: 0xA17C, symSize: 0x58 }
  - { offsetInCU: 0x27, offset: 0x5EEE1, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall initWithCallbackId:options:success:error:]', symObjAddr: 0x0, symBinAddr: 0xA1D4, symSize: 0xB8 }
  - { offsetInCU: 0x1FF, offset: 0x5F0B9, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall initWithCallbackId:options:success:error:]', symObjAddr: 0x0, symBinAddr: 0xA1D4, symSize: 0xB8 }
  - { offsetInCU: 0x276, offset: 0x5F130, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall initWithCallbackId:methodName:options:success:error:]', symObjAddr: 0xB8, symBinAddr: 0xA28C, symSize: 0xCC }
  - { offsetInCU: 0x2FD, offset: 0x5F1B7, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall isSaved]', symObjAddr: 0x184, symBinAddr: 0xA358, symSize: 0x4 }
  - { offsetInCU: 0x332, offset: 0x5F1EC, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setIsSaved:]', symObjAddr: 0x188, symBinAddr: 0xA35C, symSize: 0x4 }
  - { offsetInCU: 0x376, offset: 0x5F230, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall save]', symObjAddr: 0x18C, symBinAddr: 0xA360, symSize: 0x8 }
  - { offsetInCU: 0x3A7, offset: 0x5F261, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall keepAlive]', symObjAddr: 0x194, symBinAddr: 0xA368, symSize: 0x8 }
  - { offsetInCU: 0x3DE, offset: 0x5F298, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setKeepAlive:]', symObjAddr: 0x19C, symBinAddr: 0xA370, symSize: 0x8 }
  - { offsetInCU: 0x419, offset: 0x5F2D3, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall callbackId]', symObjAddr: 0x1A4, symBinAddr: 0xA378, symSize: 0x8 }
  - { offsetInCU: 0x450, offset: 0x5F30A, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setCallbackId:]', symObjAddr: 0x1AC, symBinAddr: 0xA380, symSize: 0xC }
  - { offsetInCU: 0x491, offset: 0x5F34B, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall methodName]', symObjAddr: 0x1B8, symBinAddr: 0xA38C, symSize: 0x8 }
  - { offsetInCU: 0x4C8, offset: 0x5F382, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setMethodName:]', symObjAddr: 0x1C0, symBinAddr: 0xA394, symSize: 0xC }
  - { offsetInCU: 0x509, offset: 0x5F3C3, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall options]', symObjAddr: 0x1CC, symBinAddr: 0xA3A0, symSize: 0x8 }
  - { offsetInCU: 0x540, offset: 0x5F3FA, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setOptions:]', symObjAddr: 0x1D4, symBinAddr: 0xA3A8, symSize: 0xC }
  - { offsetInCU: 0x581, offset: 0x5F43B, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall successHandler]', symObjAddr: 0x1E0, symBinAddr: 0xA3B4, symSize: 0x8 }
  - { offsetInCU: 0x5B8, offset: 0x5F472, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setSuccessHandler:]', symObjAddr: 0x1E8, symBinAddr: 0xA3BC, symSize: 0x8 }
  - { offsetInCU: 0x5F7, offset: 0x5F4B1, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall errorHandler]', symObjAddr: 0x1F0, symBinAddr: 0xA3C4, symSize: 0x8 }
  - { offsetInCU: 0x62E, offset: 0x5F4E8, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall setErrorHandler:]', symObjAddr: 0x1F8, symBinAddr: 0xA3CC, symSize: 0x8 }
  - { offsetInCU: 0x66D, offset: 0x5F527, size: 0x8, addend: 0x0, symName: '-[CAPPluginCall .cxx_destruct]', symObjAddr: 0x200, symBinAddr: 0xA3D4, symSize: 0x54 }
  - { offsetInCU: 0x27, offset: 0x5F599, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument initWithName:nullability:type:]', symObjAddr: 0x0, symBinAddr: 0xA428, symSize: 0x34 }
  - { offsetInCU: 0x3AD, offset: 0x5F91F, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument initWithName:nullability:type:]', symObjAddr: 0x0, symBinAddr: 0xA428, symSize: 0x34 }
  - { offsetInCU: 0x410, offset: 0x5F982, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument name]', symObjAddr: 0x34, symBinAddr: 0xA45C, symSize: 0x8 }
  - { offsetInCU: 0x447, offset: 0x5F9B9, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument setName:]', symObjAddr: 0x3C, symBinAddr: 0xA464, symSize: 0x8 }
  - { offsetInCU: 0x486, offset: 0x5F9F8, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument nullability]', symObjAddr: 0x44, symBinAddr: 0xA46C, symSize: 0x8 }
  - { offsetInCU: 0x4BD, offset: 0x5FA2F, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument setNullability:]', symObjAddr: 0x4C, symBinAddr: 0xA474, symSize: 0x8 }
  - { offsetInCU: 0x4FA, offset: 0x5FA6C, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethodArgument .cxx_destruct]', symObjAddr: 0x54, symBinAddr: 0xA47C, symSize: 0xC }
  - { offsetInCU: 0x52D, offset: 0x5FA9F, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod initWithName:returnType:]', symObjAddr: 0x60, symBinAddr: 0xA488, symSize: 0xA4 }
  - { offsetInCU: 0x5AD, offset: 0x5FB1F, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod initWithSelector:returnType:]', symObjAddr: 0x104, symBinAddr: 0xA52C, symSize: 0xAC }
  - { offsetInCU: 0x63D, offset: 0x5FBAF, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod selector]', symObjAddr: 0x1B0, symBinAddr: 0xA5D8, symSize: 0x8 }
  - { offsetInCU: 0x674, offset: 0x5FBE6, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod setSelector:]', symObjAddr: 0x1B8, symBinAddr: 0xA5E0, symSize: 0x8 }
  - { offsetInCU: 0x6B1, offset: 0x5FC23, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod name]', symObjAddr: 0x1C0, symBinAddr: 0xA5E8, symSize: 0x8 }
  - { offsetInCU: 0x6E8, offset: 0x5FC5A, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod setName:]', symObjAddr: 0x1C8, symBinAddr: 0xA5F0, symSize: 0xC }
  - { offsetInCU: 0x729, offset: 0x5FC9B, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod returnType]', symObjAddr: 0x1D4, symBinAddr: 0xA5FC, symSize: 0x8 }
  - { offsetInCU: 0x760, offset: 0x5FCD2, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod setReturnType:]', symObjAddr: 0x1DC, symBinAddr: 0xA604, symSize: 0xC }
  - { offsetInCU: 0x7A1, offset: 0x5FD13, size: 0x8, addend: 0x0, symName: '-[CAPPluginMethod .cxx_destruct]', symObjAddr: 0x1E8, symBinAddr: 0xA610, symSize: 0x60 }
  - { offsetInCU: 0x27, offset: 0x5FD81, size: 0x8, addend: 0x0, symName: '-[CAPCookiesPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0xA670, symSize: 0x130 }
  - { offsetInCU: 0x66, offset: 0x5FDC0, size: 0x8, addend: 0x0, symName: '-[CAPCookiesPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x0, symBinAddr: 0xA670, symSize: 0x130 }
  - { offsetInCU: 0xA9, offset: 0x5FE03, size: 0x8, addend: 0x0, symName: '-[CAPCookiesPlugin(CAPPluginCategory) identifier]', symObjAddr: 0x130, symBinAddr: 0xA7A0, symSize: 0xC }
  - { offsetInCU: 0xDC, offset: 0x5FE36, size: 0x8, addend: 0x0, symName: '-[CAPCookiesPlugin(CAPPluginCategory) jsName]', symObjAddr: 0x13C, symBinAddr: 0xA7AC, symSize: 0xC }
  - { offsetInCU: 0x10F, offset: 0x5FE69, size: 0x8, addend: 0x0, symName: '-[CAPHttpPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x148, symBinAddr: 0xA7B8, symSize: 0x160 }
  - { offsetInCU: 0x152, offset: 0x5FEAC, size: 0x8, addend: 0x0, symName: '-[CAPHttpPlugin(CAPPluginCategory) identifier]', symObjAddr: 0x2A8, symBinAddr: 0xA918, symSize: 0xC }
  - { offsetInCU: 0x185, offset: 0x5FEDF, size: 0x8, addend: 0x0, symName: '-[CAPHttpPlugin(CAPPluginCategory) jsName]', symObjAddr: 0x2B4, symBinAddr: 0xA924, symSize: 0xC }
  - { offsetInCU: 0x1B8, offset: 0x5FF12, size: 0x8, addend: 0x0, symName: '-[CAPConsolePlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x2C0, symBinAddr: 0xA930, symSize: 0x64 }
  - { offsetInCU: 0x1FB, offset: 0x5FF55, size: 0x8, addend: 0x0, symName: '-[CAPConsolePlugin(CAPPluginCategory) identifier]', symObjAddr: 0x324, symBinAddr: 0xA994, symSize: 0xC }
  - { offsetInCU: 0x22E, offset: 0x5FF88, size: 0x8, addend: 0x0, symName: '-[CAPConsolePlugin(CAPPluginCategory) jsName]', symObjAddr: 0x330, symBinAddr: 0xA9A0, symSize: 0xC }
  - { offsetInCU: 0x261, offset: 0x5FFBB, size: 0x8, addend: 0x0, symName: '-[CAPWebViewPlugin(CAPPluginCategory) pluginMethods]', symObjAddr: 0x33C, symBinAddr: 0xA9AC, symSize: 0x100 }
  - { offsetInCU: 0x2A4, offset: 0x5FFFE, size: 0x8, addend: 0x0, symName: '-[CAPWebViewPlugin(CAPPluginCategory) identifier]', symObjAddr: 0x43C, symBinAddr: 0xAAAC, symSize: 0xC }
  - { offsetInCU: 0x2D7, offset: 0x60031, size: 0x8, addend: 0x0, symName: '-[CAPWebViewPlugin(CAPPluginCategory) jsName]', symObjAddr: 0x448, symBinAddr: 0xAAB8, symSize: 0xC }
  - { offsetInCU: 0x27, offset: 0x60115, size: 0x8, addend: 0x0, symName: '+[UIStatusBarManager(CAPHandleTapAction) load]', symObjAddr: 0x0, symBinAddr: 0xAAC4, symSize: 0x6C }
  - { offsetInCU: 0x35, offset: 0x60123, size: 0x8, addend: 0x0, symName: '+[UIStatusBarManager(CAPHandleTapAction) load]', symObjAddr: 0x0, symBinAddr: 0xAAC4, symSize: 0x6C }
  - { offsetInCU: 0x5B, offset: 0x60149, size: 0x8, addend: 0x0, symName: _load.onceToken, symObjAddr: 0x2C68, symBinAddr: 0xA30E0, symSize: 0x0 }
  - { offsetInCU: 0x192, offset: 0x60280, size: 0x8, addend: 0x0, symName: '___46+[UIStatusBarManager(CAPHandleTapAction) load]_block_invoke', symObjAddr: 0x6C, symBinAddr: 0xAB30, symSize: 0xD4 }
  - { offsetInCU: 0x425, offset: 0x60513, size: 0x8, addend: 0x0, symName: '-[UIStatusBarManager(CAPHandleTapAction) nofity_handleTapAction:]', symObjAddr: 0x140, symBinAddr: 0xAC04, symSize: 0xC0 }
  - { offsetInCU: 0x27, offset: 0x60630, size: 0x8, addend: 0x0, symName: '+[WKWebView(CapacitorAutoFocus) load]', symObjAddr: 0x0, symBinAddr: 0xACC4, symSize: 0x4 }
  - { offsetInCU: 0xBE, offset: 0x606C7, size: 0x8, addend: 0x0, symName: '+[WKWebView(CapacitorAutoFocus) load]', symObjAddr: 0x0, symBinAddr: 0xACC4, symSize: 0x4 }
  - { offsetInCU: 0xDD, offset: 0x60800, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9hexString33_750E6D65F9E101F235B3FD4952DBE776LLySSs16IndexingIteratorVySays5UInt8VGGF', symObjAddr: 0x0, symBinAddr: 0xACC8, symSize: 0x1B8 }
  - { offsetInCU: 0x410, offset: 0x60B33, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV9CapacitorE6sha256SSvg', symObjAddr: 0x1B8, symBinAddr: 0xAE80, symSize: 0x144 }
  - { offsetInCU: 0x4F7, offset: 0x60C1A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV9CapacitorE6sha256SSvgySWXEfU_', symObjAddr: 0x30C, symBinAddr: 0xAFD4, symSize: 0xDC }
  - { offsetInCU: 0x70F, offset: 0x60E32, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDC03getbC0SSyFZ', symObjAddr: 0x3F8, symBinAddr: 0xB0C0, symSize: 0xE4 }
  - { offsetInCU: 0x828, offset: 0x60F4B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDC010regeneratebC0yyFZ', symObjAddr: 0x4DC, symBinAddr: 0xB1A4, symSize: 0x1F8 }
  - { offsetInCU: 0x90D, offset: 0x61030, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDCfd', symObjAddr: 0x6D4, symBinAddr: 0xB39C, symSize: 0x8 }
  - { offsetInCU: 0x93C, offset: 0x6105F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDCfD', symObjAddr: 0x6DC, symBinAddr: 0xB3A4, symSize: 0x10 }
  - { offsetInCU: 0x9A7, offset: 0x610CA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDC06assertbC033_750E6D65F9E101F235B3FD4952DBE776LLyyFZTf4d_n', symObjAddr: 0x4708, symBinAddr: 0xF328, symSize: 0x2E8 }
  - { offsetInCU: 0xC19, offset: 0x6133C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9tmpWindowSo8UIWindowCSgvpfi', symObjAddr: 0x6EC, symBinAddr: 0xB3B4, symSize: 0x8 }
  - { offsetInCU: 0xC31, offset: 0x61354, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14bridgeDelegateAA09CAPBridgeD0_pSgvpfi', symObjAddr: 0x6F4, symBinAddr: 0xB3BC, symSize: 0xC }
  - { offsetInCU: 0xC49, offset: 0x6136C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC10lastPluginSo9CAPPluginCSgvpfi', symObjAddr: 0x700, symBinAddr: 0xB3C8, symSize: 0x8 }
  - { offsetInCU: 0xC61, offset: 0x61384, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7pluginsSDySSSo16CAPBridgedPlugin_So9CAPPluginCXcGvpfi', symObjAddr: 0x708, symBinAddr: 0xB3D0, symSize: 0xC }
  - { offsetInCU: 0xC79, offset: 0x6139C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC20cordovaPluginManagerSo09CDVPluginE0CSgvpfi', symObjAddr: 0x714, symBinAddr: 0xB3DC, symSize: 0x8 }
  - { offsetInCU: 0xCF2, offset: 0x61415, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11storedCallsAA20ConcurrentDictionaryCySo13CAPPluginCallCGvpfi', symObjAddr: 0x71C, symBinAddr: 0xB3E4, symSize: 0x90 }
  - { offsetInCU: 0xD9F, offset: 0x614C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18injectCordovaFiles33_B558F97FDDF7E6D98578814FFB110E95LLSbvpfi', symObjAddr: 0x7AC, symBinAddr: 0xB474, symSize: 0x8 }
  - { offsetInCU: 0xDB7, offset: 0x614DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13cordovaParser33_B558F97FDDF7E6D98578814FFB110E95LLSo09CDVConfigD0CSgvpfi', symObjAddr: 0x7B4, symBinAddr: 0xB47C, symSize: 0x8 }
  - { offsetInCU: 0xDCF, offset: 0x614F2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13dispatchQueueSo03OS_C6_queueCvpfi', symObjAddr: 0x7BC, symBinAddr: 0xB484, symSize: 0x1B4 }
  - { offsetInCU: 0xE27, offset: 0x6154A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9observersSaySo8NSObject_pGvpfi', symObjAddr: 0x970, symBinAddr: 0xB638, symSize: 0xC }
  - { offsetInCU: 0xE3F, offset: 0x61562, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC6configSo24CAPInstanceConfigurationCSgvpfi', symObjAddr: 0x97C, symBinAddr: 0xB644, symSize: 0x8 }
  - { offsetInCU: 0xE57, offset: 0x6157A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC13cookieManagerAA0a6CookieE0CSgvpfi', symObjAddr: 0x984, symBinAddr: 0xB64C, symSize: 0x8 }
  - { offsetInCU: 0xE6F, offset: 0x61592, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC15capacitorBridge33_0151BC8B8398CBA447E765B04F9447A0LLAA0aF0CSgvpfi', symObjAddr: 0x990, symBinAddr: 0xB658, symSize: 0x8 }
  - { offsetInCU: 0xE87, offset: 0x615AA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC03webC0So05WKWebC0CSgvpfi', symObjAddr: 0x998, symBinAddr: 0xB660, symSize: 0x8 }
  - { offsetInCU: 0xE9F, offset: 0x615C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18isStatusBarVisibleSbvpfi', symObjAddr: 0x9A0, symBinAddr: 0xB668, symSize: 0x8 }
  - { offsetInCU: 0xEB7, offset: 0x615DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC14statusBarStyleSo08UIStatusfG0Vvpfi', symObjAddr: 0x9A8, symBinAddr: 0xB670, symSize: 0x8 }
  - { offsetInCU: 0xECF, offset: 0x615F2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18statusBarAnimationSo08UIStatusfG0Vvpfi', symObjAddr: 0x9B0, symBinAddr: 0xB678, symSize: 0x8 }
  - { offsetInCU: 0xEE7, offset: 0x6160A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvpfi', symObjAddr: 0x9B8, symBinAddr: 0xB680, symSize: 0xC }
  - { offsetInCU: 0xEFF, offset: 0x61622, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC29$__lazy_storage_$_isNewBinary33_0151BC8B8398CBA447E765B04F9447A0LLSbSgvpfi', symObjAddr: 0x9C4, symBinAddr: 0xB68C, symSize: 0x8 }
  - { offsetInCU: 0xF17, offset: 0x6163A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6methodSSSgvpfi', symObjAddr: 0x9D0, symBinAddr: 0xB698, symSize: 0xC }
  - { offsetInCU: 0xF2F, offset: 0x61652, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6paramsSDyS2SGSgvpfi', symObjAddr: 0x9DC, symBinAddr: 0xB6A4, symSize: 0x8 }
  - { offsetInCU: 0xF47, offset: 0x6166A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC7requestAA0a3UrlC0CSgvpfi', symObjAddr: 0x9E4, symBinAddr: 0xB6AC, symSize: 0x8 }
  - { offsetInCU: 0xF5F, offset: 0x61682, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04pushB7HandlerAA0bE8Protocol_pSgvpfi', symObjAddr: 0x9EC, symBinAddr: 0xB6B4, symSize: 0x8 }
  - { offsetInCU: 0xF77, offset: 0x6169A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC05localB7HandlerAA0bE8Protocol_pSgvpfi', symObjAddr: 0x9F4, symBinAddr: 0xB6BC, symSize: 0x8 }
  - { offsetInCU: 0xF8F, offset: 0x616B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV8basePathSSvpfi', symObjAddr: 0x9FC, symBinAddr: 0xB6C4, symSize: 0xC }
  - { offsetInCU: 0xFC8, offset: 0x616EB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC9mimeTypesSDyS2SGvpfi', symObjAddr: 0xA48, symBinAddr: 0xB710, symSize: 0x31D8 }
  - { offsetInCU: 0x102A, offset: 0x6174D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC6bridgeAA0A6BridgeCSgvpfi', symObjAddr: 0x3C20, symBinAddr: 0xE8E8, symSize: 0x8 }
  - { offsetInCU: 0x1042, offset: 0x61765, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC17contentControllerSo013WKUserContentG0Cvpfi', symObjAddr: 0x3C28, symBinAddr: 0xE8F0, symSize: 0x24 }
  - { offsetInCU: 0x1085, offset: 0x617A8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC12LoadingStateAC0bcgH0Ovpfi', symObjAddr: 0x3C4C, symBinAddr: 0xE914, symSize: 0x8 }
  - { offsetInCU: 0x109D, offset: 0x617C0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC11handlerName33_F9163AD9166938F8B4F54F28D02AA29FLLSSvpfi', symObjAddr: 0x3C54, symBinAddr: 0xE91C, symSize: 0x14 }
  - { offsetInCU: 0x10E4, offset: 0x61807, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0V15withUnsafeBytesyxxSWKXEKlFyt_Tg5015$s10Foundation4B31V9CapacitorE6sha256SSvgySWXEfU_ACSays5UInt8VGTf1ncn_n', symObjAddr: 0x4500, symBinAddr: 0xF120, symSize: 0xEC }
  - { offsetInCU: 0x112C, offset: 0x6184F, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC15withUnsafeBytes2in5applyxSnySiG_xSWKXEtKlFyt_Tg5015$s10Foundation4B31V9CapacitorE6sha256SSvgySWXEfU_AA0B0VSays5UInt8VGTf1nncn_n', symObjAddr: 0x45EC, symBinAddr: 0xF20C, symSize: 0xCC }
  - { offsetInCU: 0x1157, offset: 0x6187A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOy', symObjAddr: 0x46B8, symBinAddr: 0xF2D8, symSize: 0x50 }
  - { offsetInCU: 0x1176, offset: 0x61899, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_1, symObjAddr: 0x49F0, symBinAddr: 0xF610, symSize: 0x24 }
  - { offsetInCU: 0x118A, offset: 0x618AD, size: 0x8, addend: 0x0, symName: '_$sS2SSysWl', symObjAddr: 0x4A14, symBinAddr: 0xF634, symSize: 0x44 }
  - { offsetInCU: 0x119E, offset: 0x618C1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOe', symObjAddr: 0x4A58, symBinAddr: 0xF678, symSize: 0x14 }
  - { offsetInCU: 0x11B2, offset: 0x618D5, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOWOe', symObjAddr: 0x4A6C, symBinAddr: 0xF68C, symSize: 0x44 }
  - { offsetInCU: 0x121E, offset: 0x61941, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x4B9C, symBinAddr: 0xF7BC, symSize: 0x40 }
  - { offsetInCU: 0x1232, offset: 0x61955, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x4BDC, symBinAddr: 0xF7FC, symSize: 0x3C }
  - { offsetInCU: 0x1246, offset: 0x61969, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x4C18, symBinAddr: 0xF838, symSize: 0x44 }
  - { offsetInCU: 0x1357, offset: 0x61A7A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7AppUUIDCMa', symObjAddr: 0x4F7C, symBinAddr: 0xFB9C, symSize: 0x20 }
  - { offsetInCU: 0x136B, offset: 0x61A8E, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x4FD8, symBinAddr: 0xFBF8, symSize: 0x2C }
  - { offsetInCU: 0x137F, offset: 0x61AA2, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x5044, symBinAddr: 0xFC64, symSize: 0x2C }
  - { offsetInCU: 0x1393, offset: 0x61AB6, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0x5070, symBinAddr: 0xFC90, symSize: 0x2C }
  - { offsetInCU: 0x1507, offset: 0x61C2A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOWOb', symObjAddr: 0x556C, symBinAddr: 0x1018C, symSize: 0x3C }
  - { offsetInCU: 0x16D3, offset: 0x61DF6, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x5B14, symBinAddr: 0x10734, symSize: 0x10 }
  - { offsetInCU: 0x16E7, offset: 0x61E0A, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOb', symObjAddr: 0x5B24, symBinAddr: 0x10744, symSize: 0x48 }
  - { offsetInCU: 0x16FB, offset: 0x61E1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOb', symObjAddr: 0x5BB0, symBinAddr: 0x107D0, symSize: 0x18 }
  - { offsetInCU: 0x170F, offset: 0x61E32, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x5C58, symBinAddr: 0x10878, symSize: 0x2C }
  - { offsetInCU: 0x1723, offset: 0x61E46, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x5C84, symBinAddr: 0x108A4, symSize: 0x2C }
  - { offsetInCU: 0x1737, offset: 0x61E5A, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVSHSCSQWb', symObjAddr: 0x5CB0, symBinAddr: 0x108D0, symSize: 0x2C }
  - { offsetInCU: 0x174B, offset: 0x61E6E, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation26_ObjectiveCBridgeableErrorSCs0F0PWb', symObjAddr: 0x5CDC, symBinAddr: 0x108FC, symSize: 0x2C }
  - { offsetInCU: 0x175F, offset: 0x61E82, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation13CustomNSErrorSCs5ErrorPWb', symObjAddr: 0x5D08, symBinAddr: 0x10928, symSize: 0x2C }
  - { offsetInCU: 0x1773, offset: 0x61E96, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x5D34, symBinAddr: 0x10954, symSize: 0x2C }
  - { offsetInCU: 0x1787, offset: 0x61EAA, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x5D60, symBinAddr: 0x10980, symSize: 0x2C }
  - { offsetInCU: 0x179B, offset: 0x61EBE, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyaSHSCSQWb', symObjAddr: 0x5D8C, symBinAddr: 0x109AC, symSize: 0x2C }
  - { offsetInCU: 0x17AF, offset: 0x61ED2, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSCAC06CustomF0PWb', symObjAddr: 0x5DB8, symBinAddr: 0x109D8, symSize: 0x2C }
  - { offsetInCU: 0x17C3, offset: 0x61EE6, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSCAC26_ObjectiveCBridgeableErrorPWb', symObjAddr: 0x5DE4, symBinAddr: 0x10A04, symSize: 0x2C }
  - { offsetInCU: 0x17D7, offset: 0x61EFA, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSCSHWb', symObjAddr: 0x5E10, symBinAddr: 0x10A30, symSize: 0x2C }
  - { offsetInCU: 0x17EB, offset: 0x61F0E, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSC0B0AcDP_AC06_ErrorB8ProtocolPWT', symObjAddr: 0x5E3C, symBinAddr: 0x10A5C, symSize: 0x2C }
  - { offsetInCU: 0x17FF, offset: 0x61F22, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSC0B0AcDP_SYWT', symObjAddr: 0x5EC0, symBinAddr: 0x10AE0, symSize: 0x2C }
  - { offsetInCU: 0x1813, offset: 0x61F36, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSC0B0AcDP_8RawValueSYs17FixedWidthIntegerPWT', symObjAddr: 0x5EEC, symBinAddr: 0x10B0C, symSize: 0x4 }
  - { offsetInCU: 0x1827, offset: 0x61F4A, size: 0x8, addend: 0x0, symName: '_$sS2is17FixedWidthIntegersWl', symObjAddr: 0x5EF0, symBinAddr: 0x10B10, symSize: 0x44 }
  - { offsetInCU: 0x183B, offset: 0x61F5E, size: 0x8, addend: 0x0, symName: '_$sSo11WKErrorCodeV10Foundation06_ErrorB8ProtocolSCSQWb', symObjAddr: 0x5F34, symBinAddr: 0x10B54, symSize: 0x2C }
  - { offsetInCU: 0x184F, offset: 0x61F72, size: 0x8, addend: 0x0, symName: '_$sSo11WKErrorCodeV10Foundation06_ErrorB8ProtocolSC01_D4TypeAcDP_AC21_BridgedStoredNSErrorPWT', symObjAddr: 0x5F60, symBinAddr: 0x10B80, symSize: 0x2C }
  - { offsetInCU: 0x1863, offset: 0x61F86, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaSHSCSQWb', symObjAddr: 0x5F8C, symBinAddr: 0x10BAC, symSize: 0x2C }
  - { offsetInCU: 0x19E4, offset: 0x62107, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x3C84, symBinAddr: 0xE94C, symSize: 0x14 }
  - { offsetInCU: 0x1A24, offset: 0x62147, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x3C98, symBinAddr: 0xE960, symSize: 0x18 }
  - { offsetInCU: 0x1A64, offset: 0x62187, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromC1C_6resulty01_C5CTypeQz_xSgztFZTW', symObjAddr: 0x3D18, symBinAddr: 0xE99C, symSize: 0x14 }
  - { offsetInCU: 0x1AA4, offset: 0x621C7, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromC1C_6resultSb01_C5CTypeQz_xSgztFZTW', symObjAddr: 0x3D2C, symBinAddr: 0xE9B0, symSize: 0x18 }
  - { offsetInCU: 0x1AE4, offset: 0x62207, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromF1C_6resulty01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x3D6C, symBinAddr: 0xE9F0, symSize: 0x14 }
  - { offsetInCU: 0x1B24, offset: 0x62247, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromF1C_6resultSb01_F5CTypeQz_xSgztFZTW', symObjAddr: 0x3D80, symBinAddr: 0xEA04, symSize: 0x18 }
  - { offsetInCU: 0x1B64, offset: 0x62287, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVSHSCSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x404C, symBinAddr: 0xECD0, symSize: 0x5C }
  - { offsetInCU: 0x1B95, offset: 0x622B8, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVs5ErrorSCsACP9_userInfoyXlSgvgTW', symObjAddr: 0x4238, symBinAddr: 0xEE58, symSize: 0x4 }
  - { offsetInCU: 0x1BC0, offset: 0x622E3, size: 0x8, addend: 0x0, symName: '_$sSo11WKErrorCodeVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x42D4, symBinAddr: 0xEEF4, symSize: 0x14 }
  - { offsetInCU: 0x1C06, offset: 0x62329, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x42F0, symBinAddr: 0xEF10, symSize: 0x84 }
  - { offsetInCU: 0x1C22, offset: 0x62345, size: 0x8, addend: 0x0, symName: '_$sSo16NSURLResourceKeyas35_HasCustomAnyHashableRepresentationSCsACP03_todeF0s0eF0VSgyFTW', symObjAddr: 0x438C, symBinAddr: 0xEFAC, symSize: 0x84 }
  - { offsetInCU: 0x1C3E, offset: 0x62361, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x447C, symBinAddr: 0xF09C, symSize: 0x84 }
  - { offsetInCU: 0x1C93, offset: 0x623B6, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So13CAPPluginCallCTg5Tf4gd_n', symObjAddr: 0x4AB0, symBinAddr: 0xF6D0, symSize: 0xEC }
  - { offsetInCU: 0x1DDA, offset: 0x624FD, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_9Capacitor7JSValue_pTg5Tf4gd_n', symObjAddr: 0x4C5C, symBinAddr: 0xF87C, symSize: 0x114 }
  - { offsetInCU: 0x1F21, offset: 0x62644, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTg5Tf4gd_n', symObjAddr: 0x4D70, symBinAddr: 0xF990, symSize: 0x110 }
  - { offsetInCU: 0x2062, offset: 0x62785, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTg5Tf4gd_n', symObjAddr: 0x4E80, symBinAddr: 0xFAA0, symSize: 0xFC }
  - { offsetInCU: 0x218D, offset: 0x628B0, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_10Foundation3URLVSgTg5Tf4gd_n', symObjAddr: 0x509C, symBinAddr: 0xFCBC, symSize: 0x16C }
  - { offsetInCU: 0x22C4, offset: 0x629E7, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So42UIScrollViewContentInsetAdjustmentBehaviorVTg5Tf4gd_n', symObjAddr: 0x5208, symBinAddr: 0xFE28, symSize: 0xE4 }
  - { offsetInCU: 0x240B, offset: 0x62B2E, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCs17CodingUserInfoKeyV_ypTg5Tf4gd_n', symObjAddr: 0x52EC, symBinAddr: 0xFF0C, symSize: 0x16C }
  - { offsetInCU: 0x2542, offset: 0x62C65, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5Tf4gd_n', symObjAddr: 0x5458, symBinAddr: 0x10078, symSize: 0x114 }
  - { offsetInCU: 0x2689, offset: 0x62DAC, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_10Foundation4DataVTg5Tf4gd_n', symObjAddr: 0x55A8, symBinAddr: 0x101C8, symSize: 0x100 }
  - { offsetInCU: 0x27D0, offset: 0x62EF3, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCTg5Tf4gd_n', symObjAddr: 0x56A8, symBinAddr: 0x102C8, symSize: 0xEC }
  - { offsetInCU: 0x2917, offset: 0x6303A, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCs11AnyHashableV_ypTg5Tf4gd_n', symObjAddr: 0x5794, symBinAddr: 0x103B4, symSize: 0x114 }
  - { offsetInCU: 0x2A5E, offset: 0x63181, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTg5Tf4gd_n', symObjAddr: 0x58A8, symBinAddr: 0x104C8, symSize: 0x100 }
  - { offsetInCU: 0x2BA5, offset: 0x632C8, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SayypGTg5Tf4gd_n', symObjAddr: 0x59A8, symBinAddr: 0x105C8, symSize: 0xEC }
  - { offsetInCU: 0x2E6D, offset: 0x63590, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation13CustomNSErrorSCAcDP05errorB0SivgTW', symObjAddr: 0x3ED4, symBinAddr: 0xEB58, symSize: 0x40 }
  - { offsetInCU: 0x2E89, offset: 0x635AC, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation13CustomNSErrorSCAcDP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x3F14, symBinAddr: 0xEB98, symSize: 0x40 }
  - { offsetInCU: 0x2EA5, offset: 0x635C8, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation26_ObjectiveCBridgeableErrorSCAcDP15_bridgedNSErrorxSgSo0H0Ch_tcfCTW', symObjAddr: 0x3F54, symBinAddr: 0xEBD8, symSize: 0x6C }
  - { offsetInCU: 0x2ED0, offset: 0x635F3, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVSHSCSH9hashValueSivgTW', symObjAddr: 0x3FC0, symBinAddr: 0xEC44, symSize: 0x3C }
  - { offsetInCU: 0x2F01, offset: 0x63624, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVSHSCSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x3FFC, symBinAddr: 0xEC80, symSize: 0x50 }
  - { offsetInCU: 0x2F1D, offset: 0x63640, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVs5ErrorSCsACP7_domainSSvgTW', symObjAddr: 0x41B8, symBinAddr: 0xEDD8, symSize: 0x40 }
  - { offsetInCU: 0x2F39, offset: 0x6365C, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVs5ErrorSCsACP5_codeSivgTW', symObjAddr: 0x41F8, symBinAddr: 0xEE18, symSize: 0x40 }
  - { offsetInCU: 0x2F55, offset: 0x63678, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVs5ErrorSCsACP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x423C, symBinAddr: 0xEE5C, symSize: 0x40 }
  - { offsetInCU: 0x2F71, offset: 0x63694, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeVSQSCSQ2eeoiySbx_xtFZTW', symObjAddr: 0x427C, symBinAddr: 0xEE9C, symSize: 0x58 }
  - { offsetInCU: 0x30A6, offset: 0x637C9, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSCAcDP8_nsErrorSo0F0CvgTW', symObjAddr: 0x3C68, symBinAddr: 0xE930, symSize: 0x8 }
  - { offsetInCU: 0x30D7, offset: 0x637FA, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation21_BridgedStoredNSErrorSCAcDP8_nsErrorxSo0F0C_tcfCTW', symObjAddr: 0x3C70, symBinAddr: 0xE938, symSize: 0x8 }
  - { offsetInCU: 0x3102, offset: 0x63825, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation13CustomNSErrorSCAcDP11errorDomainSSvgZTW', symObjAddr: 0x3EC4, symBinAddr: 0xEB48, symSize: 0x10 }
  - { offsetInCU: 0x3122, offset: 0x63845, size: 0x8, addend: 0x0, symName: '_$sSC11WKErrorCodeLeV10Foundation13CustomNSErrorSCAcDP11errorDomainSSvgZTW', symObjAddr: 0x3EC4, symBinAddr: 0xEB48, symSize: 0x10 }
  - { offsetInCU: 0x3149, offset: 0x6386C, size: 0x8, addend: 0x0, symName: '_$sSo11WKErrorCodeVSYSCSY8rawValuexSg03RawD0Qz_tcfCTW', symObjAddr: 0x40A8, symBinAddr: 0xED2C, symSize: 0x10 }
  - { offsetInCU: 0x3165, offset: 0x63888, size: 0x8, addend: 0x0, symName: '_$sSo11WKErrorCodeVSYSCSY8rawValue03RawD0QzvgTW', symObjAddr: 0x40B8, symBinAddr: 0xED3C, symSize: 0xC }
  - { offsetInCU: 0x27, offset: 0x63A48, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASayAA7JSValue_pGRszlE19replacingNullValuesSayAaD_pSgGyF', symObjAddr: 0x0, symBinAddr: 0x10CB8, symSize: 0x198 }
  - { offsetInCU: 0x81, offset: 0x63AA2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASayAA7JSValue_pGRszlE19replacingNullValuesSayAaD_pSgGyF', symObjAddr: 0x0, symBinAddr: 0x10CB8, symSize: 0x198 }
  - { offsetInCU: 0x27A, offset: 0x63C9B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASayAA7JSValue_pGRszlE23replacingOptionalValuesAEyF', symObjAddr: 0x198, symBinAddr: 0x10E50, symSize: 0x4 }
  - { offsetInCU: 0x2E9, offset: 0x63D0A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASayAA7JSValue_pSgGRszlE23replacingOptionalValuesSayAaD_pGyF', symObjAddr: 0x19C, symBinAddr: 0x10E54, symSize: 0x19C }
  - { offsetInCU: 0x52F, offset: 0x63F50, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_Tg5', symObjAddr: 0x338, symBinAddr: 0x10FF0, symSize: 0x1C }
  - { offsetInCU: 0x547, offset: 0x63F68, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF9Capacitor7JSValue_pSg_Tg5', symObjAddr: 0x354, symBinAddr: 0x1100C, symSize: 0x1C }
  - { offsetInCU: 0x55F, offset: 0x63F80, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOc', symObjAddr: 0x370, symBinAddr: 0x11028, symSize: 0x44 }
  - { offsetInCU: 0x573, offset: 0x63F94, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_1, symObjAddr: 0x3F4, symBinAddr: 0x1106C, symSize: 0x20 }
  - { offsetInCU: 0x587, offset: 0x63FA8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pSgWOb', symObjAddr: 0x414, symBinAddr: 0x1108C, symSize: 0x48 }
  - { offsetInCU: 0x59B, offset: 0x63FBC, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF9Capacitor7JSValue_p_Tg5', symObjAddr: 0x45C, symBinAddr: 0x110D4, symSize: 0x1C }
  - { offsetInCU: 0x5B3, offset: 0x63FD4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pSgWOc', symObjAddr: 0x478, symBinAddr: 0x110F0, symSize: 0x48 }
  - { offsetInCU: 0x5C7, offset: 0x63FE8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pSgWOh', symObjAddr: 0x4C0, symBinAddr: 0x11138, symSize: 0x40 }
  - { offsetInCU: 0x5DB, offset: 0x63FFC, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtF9Capacitor18PluginHeaderMethodV_Tg5', symObjAddr: 0x518, symBinAddr: 0x11178, symSize: 0x2C }
  - { offsetInCU: 0x5F3, offset: 0x64014, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSo12NSHTTPCookieC_Tg5', symObjAddr: 0x544, symBinAddr: 0x111A4, symSize: 0x1C }
  - { offsetInCU: 0x60B, offset: 0x6402C, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSi6offset_Ss7elementt_Tg5', symObjAddr: 0x560, symBinAddr: 0x111C0, symSize: 0x1C }
  - { offsetInCU: 0x623, offset: 0x64044, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFSS_ypt_Tg5', symObjAddr: 0x57C, symBinAddr: 0x111DC, symSize: 0x1C }
  - { offsetInCU: 0x63B, offset: 0x6405C, size: 0x8, addend: 0x0, symName: '_$ss15ContiguousArrayV16_createNewBuffer14bufferIsUnique15minimumCapacity13growForAppendySb_SiSbtFyp_Tg5', symObjAddr: 0x598, symBinAddr: 0x111F8, symSize: 0x30 }
  - { offsetInCU: 0x6B6, offset: 0x640D7, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x5C8, symBinAddr: 0x11228, symSize: 0x104 }
  - { offsetInCU: 0x80E, offset: 0x6422F, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9Capacitor7JSValue_pSg_Tg5', symObjAddr: 0x6CC, symBinAddr: 0x1132C, symSize: 0x138 }
  - { offsetInCU: 0x972, offset: 0x64393, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9Capacitor7JSValue_p_Tg5', symObjAddr: 0x804, symBinAddr: 0x11464, symSize: 0x138 }
  - { offsetInCU: 0xAEC, offset: 0x6450D, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo12NSHTTPCookieC_Tg5', symObjAddr: 0x93C, symBinAddr: 0x1159C, symSize: 0x150 }
  - { offsetInCU: 0xC63, offset: 0x64684, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSi6offset_Ss7elementt_Tg5', symObjAddr: 0xA8C, symBinAddr: 0x116EC, symSize: 0x134 }
  - { offsetInCU: 0xDC6, offset: 0x647E7, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_ypt_Tg5', symObjAddr: 0xBC0, symBinAddr: 0x11820, symSize: 0x138 }
  - { offsetInCU: 0x4F, offset: 0x64BC6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13tmpVCAppeared10Foundation12NotificationVvpZ', symObjAddr: 0x40528, symBinAddr: 0xA6110, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x64BE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13capacitorSiteSSvpZ', symObjAddr: 0x10050, symBinAddr: 0xA1248, symSize: 0x0 }
  - { offsetInCU: 0x83, offset: 0x64BFA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19fileStartIdentifierSSvpZ', symObjAddr: 0x10060, symBinAddr: 0xA1258, symSize: 0x0 }
  - { offsetInCU: 0x9D, offset: 0x64C14, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC30httpInterceptorStartIdentifierSSvpZ', symObjAddr: 0x10070, symBinAddr: 0xA1268, symSize: 0x0 }
  - { offsetInCU: 0xB7, offset: 0x64C2E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC31httpsInterceptorStartIdentifierSSvpZ', symObjAddr: 0x10080, symBinAddr: 0xA1278, symSize: 0x0 }
  - { offsetInCU: 0xD1, offset: 0x64C48, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC23httpInterceptorUrlParamSSvpZ', symObjAddr: 0x40540, symBinAddr: 0xA6128, symSize: 0x0 }
  - { offsetInCU: 0xEB, offset: 0x64C62, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13defaultSchemeSSvpZ', symObjAddr: 0x40550, symBinAddr: 0xA6138, symSize: 0x0 }
  - { offsetInCU: 0x112, offset: 0x64C89, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x11A9C, symSize: 0x8 }
  - { offsetInCU: 0x166, offset: 0x64CDD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOSHAASH9hashValueSivgTW', symObjAddr: 0x8, symBinAddr: 0x11AA4, symSize: 0x40 }
  - { offsetInCU: 0x249, offset: 0x64DC0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x48, symBinAddr: 0x11AE4, symSize: 0x24 }
  - { offsetInCU: 0x2BA, offset: 0x64E31, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0xA8, symBinAddr: 0x11B44, symSize: 0x1C }
  - { offsetInCU: 0x2E5, offset: 0x64E5C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0xC4, symBinAddr: 0x11B60, symSize: 0x28 }
  - { offsetInCU: 0x316, offset: 0x64E8D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAsAGP8intValueSiSgvgTW', symObjAddr: 0xEC, symBinAddr: 0x11B88, symSize: 0xC }
  - { offsetInCU: 0x332, offset: 0x64EA9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0xF8, symBinAddr: 0x11B94, symSize: 0xC }
  - { offsetInCU: 0x34E, offset: 0x64EC5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0xDF78, symBinAddr: 0x1F93C, symSize: 0x84 }
  - { offsetInCU: 0x394, offset: 0x64F0B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV6encode2toys7Encoder_p_tKF', symObjAddr: 0x154, symBinAddr: 0x11BF0, symSize: 0x11C }
  - { offsetInCU: 0x3E9, offset: 0x64F60, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x270, symBinAddr: 0x11D0C, symSize: 0x28 }
  - { offsetInCU: 0x420, offset: 0x64F97, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x298, symBinAddr: 0x11D34, symSize: 0x18 }
  - { offsetInCU: 0x443, offset: 0x64FBA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV4fromACs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0xDFFC, symBinAddr: 0x1F9C0, symSize: 0x144 }
  - { offsetInCU: 0x497, offset: 0x6500E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16isDevEnvironmentSbvgZ', symObjAddr: 0x2B0, symBinAddr: 0x11D4C, symSize: 0x8 }
  - { offsetInCU: 0x4FE, offset: 0x65075, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7webViewSo05WKWebD0CSgvgTo', symObjAddr: 0x2B8, symBinAddr: 0x11D54, symSize: 0xAC }
  - { offsetInCU: 0x547, offset: 0x650BE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7webViewSo05WKWebD0CSgvg', symObjAddr: 0x364, symBinAddr: 0x11E00, symSize: 0x7C }
  - { offsetInCU: 0x59C, offset: 0x65113, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19autoRegisterPluginsSbvgTo', symObjAddr: 0x3E0, symBinAddr: 0x11E7C, symSize: 0x10 }
  - { offsetInCU: 0x5BC, offset: 0x65133, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19autoRegisterPluginsSbvgTo', symObjAddr: 0x3E0, symBinAddr: 0x11E7C, symSize: 0x10 }
  - { offsetInCU: 0x5D7, offset: 0x6514E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19autoRegisterPluginsSbvg', symObjAddr: 0x3F0, symBinAddr: 0x11E8C, symSize: 0x10 }
  - { offsetInCU: 0x5F4, offset: 0x6516B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18notificationRouterAA012NotificationD0CvM', symObjAddr: 0x430, symBinAddr: 0x11ECC, symSize: 0x44 }
  - { offsetInCU: 0x623, offset: 0x6519A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18notificationRouterAA012NotificationD0CvM.resume.0', symObjAddr: 0x474, symBinAddr: 0x11F10, symSize: 0x4 }
  - { offsetInCU: 0x64E, offset: 0x651C5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16isSimEnvironmentSbvgTo', symObjAddr: 0x478, symBinAddr: 0x11F14, symSize: 0x8 }
  - { offsetInCU: 0x66A, offset: 0x651E1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16isSimEnvironmentSbvg', symObjAddr: 0x480, symBinAddr: 0x11F1C, symSize: 0x8 }
  - { offsetInCU: 0x695, offset: 0x6520C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16isDevEnvironmentSbvgTo', symObjAddr: 0x488, symBinAddr: 0x11F24, symSize: 0x8 }
  - { offsetInCU: 0x6B1, offset: 0x65228, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16isDevEnvironmentSbvg', symObjAddr: 0x490, symBinAddr: 0x11F2C, symSize: 0x8 }
  - { offsetInCU: 0x6DC, offset: 0x65253, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18userInterfaceStyleSo06UIUserdE0VvgTo', symObjAddr: 0x498, symBinAddr: 0x11F34, symSize: 0x38 }
  - { offsetInCU: 0x716, offset: 0x6528D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18userInterfaceStyleSo06UIUserdE0Vvg', symObjAddr: 0x4D0, symBinAddr: 0x11F6C, symSize: 0xC0 }
  - { offsetInCU: 0x78B, offset: 0x65302, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvgTo', symObjAddr: 0x590, symBinAddr: 0x1202C, symSize: 0xD4 }
  - { offsetInCU: 0x7EE, offset: 0x65365, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvg', symObjAddr: 0x664, symBinAddr: 0x12100, symSize: 0x9C }
  - { offsetInCU: 0x845, offset: 0x653BC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvsTo', symObjAddr: 0x700, symBinAddr: 0x1219C, symSize: 0x3C }
  - { offsetInCU: 0x861, offset: 0x653D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvs', symObjAddr: 0x73C, symBinAddr: 0x121D8, symSize: 0x234 }
  - { offsetInCU: 0x8C5, offset: 0x6543C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvsyyScMYccfU_', symObjAddr: 0x970, symBinAddr: 0x1240C, symSize: 0xF8 }
  - { offsetInCU: 0x935, offset: 0x654AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvM', symObjAddr: 0xA6C, symBinAddr: 0x12508, symSize: 0xC4 }
  - { offsetInCU: 0x9B3, offset: 0x6552A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvM.resume.0', symObjAddr: 0xB30, symBinAddr: 0x125CC, symSize: 0x2C }
  - { offsetInCU: 0x9FC, offset: 0x65573, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvgTo', symObjAddr: 0xB5C, symBinAddr: 0x125F8, symSize: 0xD4 }
  - { offsetInCU: 0xA5F, offset: 0x655D6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0Vvg', symObjAddr: 0xC30, symBinAddr: 0x126CC, symSize: 0xA0 }
  - { offsetInCU: 0xAB6, offset: 0x6562D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvsTo', symObjAddr: 0xCD0, symBinAddr: 0x1276C, symSize: 0x3C }
  - { offsetInCU: 0xAD4, offset: 0x6564B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvsyyScMYccfU_', symObjAddr: 0xD28, symBinAddr: 0x127C4, symSize: 0xF8 }
  - { offsetInCU: 0xB1A, offset: 0x65691, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvM', symObjAddr: 0xE20, symBinAddr: 0x128BC, symSize: 0xC4 }
  - { offsetInCU: 0xB98, offset: 0x6570F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvM.resume.0', symObjAddr: 0xEE4, symBinAddr: 0x12980, symSize: 0x28 }
  - { offsetInCU: 0xBC3, offset: 0x6573A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvgTo', symObjAddr: 0xF0C, symBinAddr: 0x129A8, symSize: 0x38 }
  - { offsetInCU: 0xBDF, offset: 0x65756, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0Vvg', symObjAddr: 0xF44, symBinAddr: 0x129E0, symSize: 0xD0 }
  - { offsetInCU: 0xC56, offset: 0x657CD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvsTo', symObjAddr: 0x1014, symBinAddr: 0x12AB0, symSize: 0x3C }
  - { offsetInCU: 0xC74, offset: 0x657EB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvsyyScMYccfU_', symObjAddr: 0x1294, symBinAddr: 0x12D30, symSize: 0xF8 }
  - { offsetInCU: 0xCD8, offset: 0x6584F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvM', symObjAddr: 0x138C, symBinAddr: 0x12E28, symSize: 0x104 }
  - { offsetInCU: 0xD76, offset: 0x658ED, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvM.resume.0', symObjAddr: 0x1490, symBinAddr: 0x12F2C, symSize: 0x28 }
  - { offsetInCU: 0xDA1, offset: 0x65918, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13capacitorSiteSSvgZ', symObjAddr: 0x1544, symBinAddr: 0x12FE0, symSize: 0x1C }
  - { offsetInCU: 0xDCC, offset: 0x65943, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19fileStartIdentifierSSvgZ', symObjAddr: 0x156C, symBinAddr: 0x13008, symSize: 0x1C }
  - { offsetInCU: 0xDF7, offset: 0x6596E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC30httpInterceptorStartIdentifierSSvgZ', symObjAddr: 0x1594, symBinAddr: 0x13030, symSize: 0x1C }
  - { offsetInCU: 0xE22, offset: 0x65999, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC31httpsInterceptorStartIdentifierSSvgZ', symObjAddr: 0x15BC, symBinAddr: 0x13058, symSize: 0x1C }
  - { offsetInCU: 0xE77, offset: 0x659EE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14bridgeDelegateAA09CAPBridgeD0_pSgvg', symObjAddr: 0x17F8, symBinAddr: 0x13294, symSize: 0x4C }
  - { offsetInCU: 0xE96, offset: 0x65A0D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14viewControllerSo06UIViewD0CSgvgTo', symObjAddr: 0x1844, symBinAddr: 0x132E0, symSize: 0xAC }
  - { offsetInCU: 0xEDF, offset: 0x65A56, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14viewControllerSo06UIViewD0CSgvg', symObjAddr: 0x18F0, symBinAddr: 0x1338C, symSize: 0x7C }
  - { offsetInCU: 0xF16, offset: 0x65A8D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC6configSo24CAPInstanceConfigurationCvM', symObjAddr: 0x1B84, symBinAddr: 0x1359C, symSize: 0x44 }
  - { offsetInCU: 0xF45, offset: 0x65ABC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC10getWebViewSo05WKWebE0CSgyF', symObjAddr: 0x1C78, symBinAddr: 0x13690, symSize: 0x7C }
  - { offsetInCU: 0xFBA, offset: 0x65B31, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC10getWebViewSo05WKWebE0CSgyFTo', symObjAddr: 0x1CF4, symBinAddr: 0x1370C, symSize: 0xAC }
  - { offsetInCU: 0x1019, offset: 0x65B90, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11isSimulatorSbyF', symObjAddr: 0x1DA0, symBinAddr: 0x137B8, symSize: 0x8 }
  - { offsetInCU: 0x1044, offset: 0x65BBB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11isSimulatorSbyFTo', symObjAddr: 0x1DA8, symBinAddr: 0x137C0, symSize: 0x8 }
  - { offsetInCU: 0x1060, offset: 0x65BD7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9isDevModeSbyF', symObjAddr: 0x1DB0, symBinAddr: 0x137C8, symSize: 0x8 }
  - { offsetInCU: 0x108B, offset: 0x65C02, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9isDevModeSbyFTo', symObjAddr: 0x1DB8, symBinAddr: 0x137D0, symSize: 0x8 }
  - { offsetInCU: 0x10A7, offset: 0x65C1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19getStatusBarVisibleSbyF', symObjAddr: 0x1DC0, symBinAddr: 0x137D8, symSize: 0x9C }
  - { offsetInCU: 0x1134, offset: 0x65CAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19getStatusBarVisibleSbyFTo', symObjAddr: 0x1E5C, symBinAddr: 0x13874, symSize: 0xD4 }
  - { offsetInCU: 0x11AF, offset: 0x65D26, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19setStatusBarVisibleyySbF', symObjAddr: 0x1F30, symBinAddr: 0x13948, symSize: 0x4 }
  - { offsetInCU: 0x11EA, offset: 0x65D61, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC17getStatusBarStyleSo08UIStatuseF0VyF', symObjAddr: 0x1F34, symBinAddr: 0x1394C, symSize: 0xA0 }
  - { offsetInCU: 0x1277, offset: 0x65DEE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC17getStatusBarStyleSo08UIStatuseF0VyFTo', symObjAddr: 0x1FD4, symBinAddr: 0x139EC, symSize: 0xD4 }
  - { offsetInCU: 0x12F2, offset: 0x65E69, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC17setStatusBarStyleyySo08UIStatuseF0VF', symObjAddr: 0x20A8, symBinAddr: 0x13AC0, symSize: 0x4 }
  - { offsetInCU: 0x134B, offset: 0x65EC2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC21getUserInterfaceStyleSo06UIUsereF0VyF', symObjAddr: 0x20AC, symBinAddr: 0x13AC4, symSize: 0xC0 }
  - { offsetInCU: 0x13CA, offset: 0x65F41, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC21getUserInterfaceStyleSo06UIUsereF0VyFTo', symObjAddr: 0x216C, symBinAddr: 0x13B84, symSize: 0x38 }
  - { offsetInCU: 0x1404, offset: 0x65F7B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11getLocalUrlSSyF', symObjAddr: 0x21A4, symBinAddr: 0x13BBC, symSize: 0xDC }
  - { offsetInCU: 0x1461, offset: 0x65FD8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11getLocalUrlSSyFTo', symObjAddr: 0x2280, symBinAddr: 0x13C98, symSize: 0x10C }
  - { offsetInCU: 0x14B4, offset: 0x6602B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC21setStatusBarAnimationyySo08UIStatuseF0VF', symObjAddr: 0x238C, symBinAddr: 0x13DA4, symSize: 0x4 }
  - { offsetInCU: 0x1537, offset: 0x660AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC17setServerBasePathyySSF', symObjAddr: 0x2390, symBinAddr: 0x13DA8, symSize: 0x1FC }
  - { offsetInCU: 0x160D, offset: 0x66184, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfC', symObjAddr: 0x2598, symBinAddr: 0x13FB0, symSize: 0xC0 }
  - { offsetInCU: 0x1657, offset: 0x661CE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfc', symObjAddr: 0x2658, symBinAddr: 0x14070, symSize: 0xB4 }
  - { offsetInCU: 0x16AB, offset: 0x66222, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfcy10Foundation12NotificationVYbcfU_', symObjAddr: 0x270C, symBinAddr: 0x14124, symSize: 0x60 }
  - { offsetInCU: 0x170B, offset: 0x66282, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCfD', symObjAddr: 0x2810, symBinAddr: 0x14228, symSize: 0x1B0 }
  - { offsetInCU: 0x185A, offset: 0x663D1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCfDTo', symObjAddr: 0x29C0, symBinAddr: 0x143D8, symSize: 0x24 }
  - { offsetInCU: 0x18A3, offset: 0x6641A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12exportCoreJS8localUrlySS_tF', symObjAddr: 0x2ACC, symBinAddr: 0x144E4, symSize: 0x154 }
  - { offsetInCU: 0x19D8, offset: 0x6654F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC25setupCordovaCompatibilityyyF', symObjAddr: 0x2C20, symBinAddr: 0x14638, symSize: 0x3AC }
  - { offsetInCU: 0x1C75, offset: 0x667EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC15registerPluginsyyF', symObjAddr: 0x3050, symBinAddr: 0x14A68, symSize: 0x8A0 }
  - { offsetInCU: 0x218A, offset: 0x66D01, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18registerPluginTypeyySo9CAPPluginCmF', symObjAddr: 0x38F0, symBinAddr: 0x15308, symSize: 0x1A8 }
  - { offsetInCU: 0x22B1, offset: 0x66E28, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18registerPluginTypeyySo9CAPPluginCmFTo', symObjAddr: 0x3A98, symBinAddr: 0x154B0, symSize: 0x48 }
  - { offsetInCU: 0x2309, offset: 0x66E80, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC22registerPluginInstanceyySo9CAPPluginCF', symObjAddr: 0x3AE0, symBinAddr: 0x154F8, symSize: 0x40C }
  - { offsetInCU: 0x25DC, offset: 0x67153, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC22registerPluginInstanceyySo9CAPPluginCFTo', symObjAddr: 0x3EEC, symBinAddr: 0x15904, symSize: 0x50 }
  - { offsetInCU: 0x25F8, offset: 0x6716F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC10loadPlugin4typeSo010CAPBridgedD0_So9CAPPluginCXcSgAHm_tF', symObjAddr: 0x3F3C, symBinAddr: 0x15954, symSize: 0x214 }
  - { offsetInCU: 0x27D5, offset: 0x6734C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC6plugin8withNameSo9CAPPluginCSgSS_tF', symObjAddr: 0x4150, symBinAddr: 0x15B68, symSize: 0xC4 }
  - { offsetInCU: 0x2879, offset: 0x673F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC8saveCallyySo09CAPPluginD0CF', symObjAddr: 0x4220, symBinAddr: 0x15C38, symSize: 0x11C }
  - { offsetInCU: 0x2950, offset: 0x674C7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC8saveCallyySo09CAPPluginD0CFTo', symObjAddr: 0x433C, symBinAddr: 0x15D54, symSize: 0x50 }
  - { offsetInCU: 0x296C, offset: 0x674E3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9savedCall6withIDSo09CAPPluginD0CSgSS_tF', symObjAddr: 0x438C, symBinAddr: 0x15DA4, symSize: 0x100 }
  - { offsetInCU: 0x2A62, offset: 0x675D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCall6withIDySS_tF', symObjAddr: 0x45CC, symBinAddr: 0x15FE4, symSize: 0xB0 }
  - { offsetInCU: 0x2B87, offset: 0x676FE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCallyySo09CAPPluginD0CF', symObjAddr: 0x4498, symBinAddr: 0x15EB0, symSize: 0xE4 }
  - { offsetInCU: 0x2CDD, offset: 0x67854, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCallyySo09CAPPluginD0CFTo', symObjAddr: 0x457C, symBinAddr: 0x15F94, symSize: 0x50 }
  - { offsetInCU: 0x2CF9, offset: 0x67870, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCall6withIDySS_tFTo', symObjAddr: 0x467C, symBinAddr: 0x16094, symSize: 0xE0 }
  - { offsetInCU: 0x2E32, offset: 0x679A9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12getSavedCallySo09CAPPluginE0CSgSSF', symObjAddr: 0x475C, symBinAddr: 0x16174, symSize: 0x100 }
  - { offsetInCU: 0x2F2D, offset: 0x67AA4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCall10callbackIdySS_tF', symObjAddr: 0x48D8, symBinAddr: 0x162F0, symSize: 0xB0 }
  - { offsetInCU: 0x3081, offset: 0x67BF8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC11releaseCall10callbackIdySS_tFTo', symObjAddr: 0x4988, symBinAddr: 0x163A0, symSize: 0xE0 }
  - { offsetInCU: 0x3216, offset: 0x67D8D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC22registerCordovaPluginsyyF', symObjAddr: 0x4A68, symBinAddr: 0x16480, symSize: 0x3C0 }
  - { offsetInCU: 0x3400, offset: 0x67F77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC17setupWebDebugging33_B558F97FDDF7E6D98578814FFB110E95LL13configurationySo24CAPInstanceConfigurationC_tF', symObjAddr: 0x4E28, symBinAddr: 0x16840, symSize: 0x168 }
  - { offsetInCU: 0x3564, offset: 0x680DB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tF', symObjAddr: 0x4F90, symBinAddr: 0x169A8, symSize: 0xB6C }
  - { offsetInCU: 0x3FCF, offset: 0x68B46, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_', symObjAddr: 0x5AFC, symBinAddr: 0x17514, symSize: 0x38C }
  - { offsetInCU: 0x4164, offset: 0x68CDB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_yAA19CAPPluginCallResultCSg_So0fG0CSgtcfU_', symObjAddr: 0x5E88, symBinAddr: 0x178A0, symSize: 0x1D4 }
  - { offsetInCU: 0x421F, offset: 0x68D96, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_yAA18CAPPluginCallErrorCSgcfU0_', symObjAddr: 0x605C, symBinAddr: 0x17A74, symSize: 0x178 }
  - { offsetInCU: 0x4346, offset: 0x68EBD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19handleCordovaJSCall4callyAA0E0V_tF', symObjAddr: 0x6204, symBinAddr: 0x17C1C, symSize: 0x5B4 }
  - { offsetInCU: 0x46BF, offset: 0x69236, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4toJs6result4saveyAA16JSResultProtocol_p_SbtFyyScMYccfU_', symObjAddr: 0x67B8, symBinAddr: 0x181D0, symSize: 0x2FC }
  - { offsetInCU: 0x4A04, offset: 0x6957B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9toJsError5erroryAA16JSResultProtocol_p_tFyyScMYccfU_', symObjAddr: 0x6B5C, symBinAddr: 0x18574, symSize: 0x2AC }
  - { offsetInCU: 0x4CD1, offset: 0x69848, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14evalWithPlugin_2jsySo9CAPPluginC_SStF', symObjAddr: 0x6ED0, symBinAddr: 0x188E8, symSize: 0x354 }
  - { offsetInCU: 0x4F3E, offset: 0x69AB5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14evalWithPlugin_2jsySo9CAPPluginC_SStFTo', symObjAddr: 0x7228, symBinAddr: 0x18C40, symSize: 0x7C }
  - { offsetInCU: 0x4F5A, offset: 0x69AD1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4eval2jsySS_tF', symObjAddr: 0x72A4, symBinAddr: 0x18CBC, symSize: 0x228 }
  - { offsetInCU: 0x4FC8, offset: 0x69B3F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14triggerJSEvent9eventName6targetySS_SStF', symObjAddr: 0x77B4, symBinAddr: 0x191CC, symSize: 0xD4 }
  - { offsetInCU: 0x515B, offset: 0x69CD2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14triggerJSEvent9eventName6targetySS_SStFTo', symObjAddr: 0x7888, symBinAddr: 0x192A0, symSize: 0x84 }
  - { offsetInCU: 0x5177, offset: 0x69CEE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14triggerJSEvent9eventName6target4dataySS_S2StF', symObjAddr: 0x790C, symBinAddr: 0x19324, symSize: 0x108 }
  - { offsetInCU: 0x539B, offset: 0x69F12, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC20triggerWindowJSEvent9eventNameySS_tF', symObjAddr: 0x7A20, symBinAddr: 0x19438, symSize: 0x14 }
  - { offsetInCU: 0x53DB, offset: 0x69F52, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC20triggerWindowJSEvent9eventName4dataySS_SStF', symObjAddr: 0x7A48, symBinAddr: 0x19460, symSize: 0x1C }
  - { offsetInCU: 0x542C, offset: 0x69FA3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC22triggerDocumentJSEvent9eventNameySS_tF', symObjAddr: 0x7A78, symBinAddr: 0x19490, symSize: 0x18 }
  - { offsetInCU: 0x546C, offset: 0x69FE3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC22triggerDocumentJSEvent9eventName4dataySS_SStF', symObjAddr: 0x7B1C, symBinAddr: 0x19534, symSize: 0x20 }
  - { offsetInCU: 0x54BD, offset: 0x6A034, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7logToJsyySS_SStF', symObjAddr: 0x7BF4, symBinAddr: 0x1960C, symSize: 0x238 }
  - { offsetInCU: 0x553B, offset: 0x6A0B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7logToJsyySS_SStFyyScMYccfU_', symObjAddr: 0x7E2C, symBinAddr: 0x19844, symSize: 0x19C }
  - { offsetInCU: 0x5723, offset: 0x6A29A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7logToJsyySS_SStFyyScMYccfU_yypSg_s5Error_pSgtcfU_', symObjAddr: 0x7FC8, symBinAddr: 0x199E0, symSize: 0xD0 }
  - { offsetInCU: 0x5836, offset: 0x6A3AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC8localURL07fromWebD010Foundation0D0VSgAI_tF', symObjAddr: 0x8098, symBinAddr: 0x19AB0, symSize: 0x2C8 }
  - { offsetInCU: 0x591D, offset: 0x6A494, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12portablePath12fromLocalURL10Foundation0G0VSgAI_tF', symObjAddr: 0x836C, symBinAddr: 0x19D84, symSize: 0x21C }
  - { offsetInCU: 0x5998, offset: 0x6A50F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13showAlertWith5title7message11buttonTitleySS_S2StF', symObjAddr: 0x86F4, symBinAddr: 0x1A10C, symSize: 0x1CC }
  - { offsetInCU: 0x5A87, offset: 0x6A5FE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9presentVC_8animated10completionySo16UIViewControllerC_SbyycSgtF', symObjAddr: 0x898C, symBinAddr: 0x1A3A4, symSize: 0x2A8 }
  - { offsetInCU: 0x5BE5, offset: 0x6A75C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9presentVC_8animated10completionySo16UIViewControllerC_SbyycSgtFTo', symObjAddr: 0x8C34, symBinAddr: 0x1A64C, symSize: 0xBC }
  - { offsetInCU: 0x5C01, offset: 0x6A778, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9dismissVC8animated10completionySb_yycSgtF', symObjAddr: 0x8CF0, symBinAddr: 0x1A708, symSize: 0x1D0 }
  - { offsetInCU: 0x5CD2, offset: 0x6A849, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9dismissVC8animated10completionySb_yycSgtFTo', symObjAddr: 0x8EC0, symBinAddr: 0x1A8D8, symSize: 0x98 }
  - { offsetInCU: 0x5CEE, offset: 0x6A865, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCACycfC', symObjAddr: 0x8F58, symBinAddr: 0x1A970, symSize: 0x20 }
  - { offsetInCU: 0x5D0C, offset: 0x6A883, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCACycfc', symObjAddr: 0x8F78, symBinAddr: 0x1A990, symSize: 0x2C }
  - { offsetInCU: 0x5D6F, offset: 0x6A8E6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCACycfcTo', symObjAddr: 0x8FA4, symBinAddr: 0x1A9BC, symSize: 0x2C }
  - { offsetInCU: 0x5DD6, offset: 0x6A94D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4toJs6result4saveyAA16JSResultProtocol_p_SbtFTf4enn_nAA0G0V_Tg5', symObjAddr: 0xD8D0, symBinAddr: 0x1F294, symSize: 0x414 }
  - { offsetInCU: 0x5F3B, offset: 0x6AAB2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9toJsError5erroryAA16JSResultProtocol_p_tFTf4en_nAA0gE0V_Tg5', symObjAddr: 0xDCE4, symBinAddr: 0x1F6A8, symSize: 0x294 }
  - { offsetInCU: 0x5FAA, offset: 0x6AB21, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC10fatalErroryys0D0_p_sAE_ptFZTf4nnd_n', symObjAddr: 0xE140, symBinAddr: 0x1FB04, symSize: 0x518 }
  - { offsetInCU: 0x6330, offset: 0x6AEA7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfcTf4nennnnn_nTf4gggggnn_n', symObjAddr: 0xE658, symBinAddr: 0x2001C, symSize: 0x738 }
  - { offsetInCU: 0x65A9, offset: 0x6B120, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13tmpVCAppeared_WZ', symObjAddr: 0x14B8, symBinAddr: 0x12F54, symSize: 0x80 }
  - { offsetInCU: 0x65C3, offset: 0x6B13A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13capacitorSiteSSvau', symObjAddr: 0x1538, symBinAddr: 0x12FD4, symSize: 0xC }
  - { offsetInCU: 0x65E1, offset: 0x6B158, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC19fileStartIdentifierSSvau', symObjAddr: 0x1560, symBinAddr: 0x12FFC, symSize: 0xC }
  - { offsetInCU: 0x65FF, offset: 0x6B176, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC30httpInterceptorStartIdentifierSSvau', symObjAddr: 0x1588, symBinAddr: 0x13024, symSize: 0xC }
  - { offsetInCU: 0x661D, offset: 0x6B194, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC31httpsInterceptorStartIdentifierSSvau', symObjAddr: 0x15B0, symBinAddr: 0x1304C, symSize: 0xC }
  - { offsetInCU: 0x663B, offset: 0x6B1B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC23httpInterceptorUrlParam_WZ', symObjAddr: 0x15D8, symBinAddr: 0x13074, symSize: 0x18 }
  - { offsetInCU: 0x6655, offset: 0x6B1CC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC23httpInterceptorUrlParamSSvau', symObjAddr: 0x15F0, symBinAddr: 0x1308C, symSize: 0x40 }
  - { offsetInCU: 0x6673, offset: 0x6B1EA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13defaultScheme_WZ', symObjAddr: 0x1650, symBinAddr: 0x130EC, symSize: 0x28 }
  - { offsetInCU: 0x668D, offset: 0x6B204, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC13defaultSchemeSSvau', symObjAddr: 0x1678, symBinAddr: 0x13114, symSize: 0x40 }
  - { offsetInCU: 0x66AB, offset: 0x6B222, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14bridgeDelegateAA09CAPBridgeD0_pSgvpACTK', symObjAddr: 0x1740, symBinAddr: 0x131DC, symSize: 0x58 }
  - { offsetInCU: 0x66E4, offset: 0x6B25B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14bridgeDelegateAA09CAPBridgeD0_pSgvpACTk', symObjAddr: 0x1798, symBinAddr: 0x13234, symSize: 0x60 }
  - { offsetInCU: 0x671C, offset: 0x6B293, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC16statusBarVisibleSbvsyyScMYccfU_TA', symObjAddr: 0x19B4, symBinAddr: 0x13450, symSize: 0xC }
  - { offsetInCU: 0x6730, offset: 0x6B2A7, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x19C0, symBinAddr: 0x1345C, symSize: 0x10 }
  - { offsetInCU: 0x6744, offset: 0x6B2BB, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x19D0, symBinAddr: 0x1346C, symSize: 0x8 }
  - { offsetInCU: 0x6758, offset: 0x6B2CF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14statusBarStyleSo08UIStatusdE0VvsyyScMYccfU_TA', symObjAddr: 0x1A5C, symBinAddr: 0x13474, symSize: 0x8 }
  - { offsetInCU: 0x6778, offset: 0x6B2EF, size: 0x8, addend: 0x0, symName: '_$s10Foundation12NotificationVIeghn_So14NSNotificationCIeyBhy_TR', symObjAddr: 0x276C, symBinAddr: 0x14184, symSize: 0xA4 }
  - { offsetInCU: 0x681F, offset: 0x6B396, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCfETo', symObjAddr: 0x29E4, symBinAddr: 0x143FC, symSize: 0xE8 }
  - { offsetInCU: 0x6D13, offset: 0x6B88A, size: 0x8, addend: 0x0, symName: '_$sypSgs5Error_pSgIegng_yXlSgSo7NSErrorCSgIeyByy_TR', symObjAddr: 0x6AB8, symBinAddr: 0x184D0, symSize: 0xA4 }
  - { offsetInCU: 0x6D4C, offset: 0x6B8C3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC18statusBarAnimationSo08UIStatusdE0VvsyyScMYccfU_TA', symObjAddr: 0x8FF4, symBinAddr: 0x1AA0C, symSize: 0x8 }
  - { offsetInCU: 0x6D60, offset: 0x6B8D7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCSgSo0bC0CSgIeggg_AdGIeyByy_TR', symObjAddr: 0x91BC, symBinAddr: 0x1ABD4, symSize: 0x78 }
  - { offsetInCU: 0x6D78, offset: 0x6B8EF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorCSgIegg_ADIeyBy_TR', symObjAddr: 0x9234, symBinAddr: 0x1AC4C, symSize: 0x50 }
  - { offsetInCU: 0x6DA6, offset: 0x6B91D, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x9284, symBinAddr: 0x1AC9C, symSize: 0x64 }
  - { offsetInCU: 0x6DF3, offset: 0x6B96A, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x92E8, symBinAddr: 0x1AD00, symSize: 0x30 }
  - { offsetInCU: 0x6E20, offset: 0x6B997, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFs17CodingUserInfoKeyV_Tg5', symObjAddr: 0x9318, symBinAddr: 0x1AD30, symSize: 0x50 }
  - { offsetInCU: 0x6E38, offset: 0x6B9AF, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0x9368, symBinAddr: 0x1AD80, symSize: 0x80 }
  - { offsetInCU: 0x6F02, offset: 0x6BA79, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_10Foundation4DataVTg5', symObjAddr: 0x93E8, symBinAddr: 0x1AE00, symSize: 0x54 }
  - { offsetInCU: 0x6F95, offset: 0x6BB0C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_ypTg5', symObjAddr: 0x9440, symBinAddr: 0x1AE54, symSize: 0x6C }
  - { offsetInCU: 0x7028, offset: 0x6BB9F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_SSTg5', symObjAddr: 0x94AC, symBinAddr: 0x1AEC0, symSize: 0x54 }
  - { offsetInCU: 0x70AA, offset: 0x6BC21, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_insert2at3key5valueys10_HashTableV6BucketV_xnq_ntFSS_9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0x9500, symBinAddr: 0x1AF14, symSize: 0x70 }
  - { offsetInCU: 0x7132, offset: 0x6BCA9, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x95C0, symBinAddr: 0x1AF84, symSize: 0xE0 }
  - { offsetInCU: 0x717C, offset: 0x6BCF3, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs11AnyHashableV_Tg5', symObjAddr: 0x96A0, symBinAddr: 0x1B064, symSize: 0xC4 }
  - { offsetInCU: 0x71A9, offset: 0x6BD20, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFs17CodingUserInfoKeyV_Tg5', symObjAddr: 0x9764, symBinAddr: 0x1B128, symSize: 0x124 }
  - { offsetInCU: 0x71C1, offset: 0x6BD38, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0x9888, symBinAddr: 0x1B24C, symSize: 0x174 }
  - { offsetInCU: 0x723E, offset: 0x6BDB5, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo8NSObject_p_Tg5', symObjAddr: 0x99FC, symBinAddr: 0x1B3C0, symSize: 0x1F0 }
  - { offsetInCU: 0x729B, offset: 0x6BE12, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo8NSObject_p_Tg5', symObjAddr: 0x9E38, symBinAddr: 0x1B7FC, symSize: 0x90 }
  - { offsetInCU: 0x734C, offset: 0x6BEC3, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_10Foundation4DataVTg5', symObjAddr: 0x9EC8, symBinAddr: 0x1B88C, symSize: 0xC8 }
  - { offsetInCU: 0x73F0, offset: 0x6BF67, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_So16CAPBridgedPlugin_So9CAPPluginCXcTg5', symObjAddr: 0x9F90, symBinAddr: 0x1B954, symSize: 0xF4 }
  - { offsetInCU: 0x7505, offset: 0x6C07C, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_So13CAPPluginCallCTg5', symObjAddr: 0xA084, symBinAddr: 0x1BA48, symSize: 0xF4 }
  - { offsetInCU: 0x75F9, offset: 0x6C170, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_SSTg5', symObjAddr: 0xA178, symBinAddr: 0x1BB3C, symSize: 0xC8 }
  - { offsetInCU: 0x767C, offset: 0x6C1F3, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_9Capacitor07EncodedC033_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0xA240, symBinAddr: 0x1BC04, symSize: 0xC0 }
  - { offsetInCU: 0x7733, offset: 0x6C2AA, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCTg5', symObjAddr: 0xA300, symBinAddr: 0x1BCC4, symSize: 0xF4 }
  - { offsetInCU: 0x7832, offset: 0x6C3A9, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_10Foundation4DataVTg5', symObjAddr: 0xA3F4, symBinAddr: 0x1BDB8, symSize: 0xE0 }
  - { offsetInCU: 0x78E1, offset: 0x6C458, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_So13CAPPluginCallCTg5', symObjAddr: 0xA4D4, symBinAddr: 0x1BE98, symSize: 0xCC }
  - { offsetInCU: 0x798D, offset: 0x6C504, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_SSTg5', symObjAddr: 0xA5A0, symBinAddr: 0x1BF64, symSize: 0xDC }
  - { offsetInCU: 0x7A12, offset: 0x6C589, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_10Foundation4DataVTg5', symObjAddr: 0xA78C, symBinAddr: 0x1C150, symSize: 0x1C0 }
  - { offsetInCU: 0x7A93, offset: 0x6C60A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_So16CAPBridgedPlugin_So9CAPPluginCXcTg5', symObjAddr: 0xA94C, symBinAddr: 0x1C310, symSize: 0x1C4 }
  - { offsetInCU: 0x7B14, offset: 0x6C68B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_So13CAPPluginCallCTg5', symObjAddr: 0xAB10, symBinAddr: 0x1C4D4, symSize: 0x1C4 }
  - { offsetInCU: 0x7BB6, offset: 0x6C72D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_9Capacitor7JSValue_pTg5', symObjAddr: 0xACD4, symBinAddr: 0x1C698, symSize: 0x1F8 }
  - { offsetInCU: 0x7C37, offset: 0x6C7AE, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_ypTg5', symObjAddr: 0xAECC, symBinAddr: 0x1C890, symSize: 0x1F4 }
  - { offsetInCU: 0x7CB8, offset: 0x6C82F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_SSTg5', symObjAddr: 0xB0C0, symBinAddr: 0x1CA84, symSize: 0x1C8 }
  - { offsetInCU: 0x7D1D, offset: 0x6C894, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0xB288, symBinAddr: 0x1CC4C, symSize: 0x1F8 }
  - { offsetInCU: 0x7D9E, offset: 0x6C915, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCTg5', symObjAddr: 0xB480, symBinAddr: 0x1CE44, symSize: 0x1C4 }
  - { offsetInCU: 0x7E40, offset: 0x6C9B7, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFs11AnyHashableV_ypTg5', symObjAddr: 0xB644, symBinAddr: 0x1D008, symSize: 0x1F4 }
  - { offsetInCU: 0x7EDA, offset: 0x6CA51, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_10Foundation4DataVTg5', symObjAddr: 0xB838, symBinAddr: 0x1D1FC, symSize: 0x378 }
  - { offsetInCU: 0x7FE6, offset: 0x6CB5D, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_So16CAPBridgedPlugin_So9CAPPluginCXcTg5', symObjAddr: 0xBBB0, symBinAddr: 0x1D574, symSize: 0x398 }
  - { offsetInCU: 0x80F2, offset: 0x6CC69, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_So13CAPPluginCallCTg5', symObjAddr: 0xBF48, symBinAddr: 0x1D90C, symSize: 0x398 }
  - { offsetInCU: 0x8209, offset: 0x6CD80, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_9Capacitor7JSValue_pTg5', symObjAddr: 0xC2E0, symBinAddr: 0x1DCA4, symSize: 0x3B4 }
  - { offsetInCU: 0x8334, offset: 0x6CEAB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_ypTg5', symObjAddr: 0xC694, symBinAddr: 0x1E058, symSize: 0x3A0 }
  - { offsetInCU: 0x8454, offset: 0x6CFCB, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_SSTg5', symObjAddr: 0xCA34, symBinAddr: 0x1E3F8, symSize: 0x3AC }
  - { offsetInCU: 0x855C, offset: 0x6D0D3, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0xCDE0, symBinAddr: 0x1E7A4, symSize: 0x3B4 }
  - { offsetInCU: 0x867C, offset: 0x6D1F3, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCTg5', symObjAddr: 0xD194, symBinAddr: 0x1EB58, symSize: 0x398 }
  - { offsetInCU: 0x8793, offset: 0x6D30A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFs11AnyHashableV_ypTg5', symObjAddr: 0xD52C, symBinAddr: 0x1EEF0, symSize: 0x3A4 }
  - { offsetInCU: 0x88F5, offset: 0x6D46C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeCMa', symObjAddr: 0xED90, symBinAddr: 0x20754, symSize: 0x20 }
  - { offsetInCU: 0x8909, offset: 0x6D480, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC14evalWithPlugin_2jsySo9CAPPluginC_SStFyyScMYccfU_TA', symObjAddr: 0xEDB4, symBinAddr: 0x20778, symSize: 0x2C }
  - { offsetInCU: 0x891D, offset: 0x6D494, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4eval2jsySS_tFyyScMYccfU_TA', symObjAddr: 0xEE10, symBinAddr: 0x207D4, symSize: 0x2C }
  - { offsetInCU: 0x8931, offset: 0x6D4A8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC7logToJsyySS_SStFyyScMYccfU_TA', symObjAddr: 0xEE70, symBinAddr: 0x20834, symSize: 0x10 }
  - { offsetInCU: 0x8945, offset: 0x6D4BC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOWOd', symObjAddr: 0xEF90, symBinAddr: 0x20954, symSize: 0x3C }
  - { offsetInCU: 0x8959, offset: 0x6D4D0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOWOc', symObjAddr: 0xF008, symBinAddr: 0x20990, symSize: 0x3C }
  - { offsetInCU: 0x896D, offset: 0x6D4E4, size: 0x8, addend: 0x0, symName: ___swift_allocate_value_buffer, symObjAddr: 0xF044, symBinAddr: 0x209CC, symSize: 0x40 }
  - { offsetInCU: 0x8981, offset: 0x6D4F8, size: 0x8, addend: 0x0, symName: ___swift_project_value_buffer, symObjAddr: 0xF084, symBinAddr: 0x20A0C, symSize: 0x18 }
  - { offsetInCU: 0x8995, offset: 0x6D50C, size: 0x8, addend: 0x0, symName: '_$sSDySSypGWOs', symObjAddr: 0xF140, symBinAddr: 0x20A24, symSize: 0x28 }
  - { offsetInCU: 0x89A9, offset: 0x6D520, size: 0x8, addend: 0x0, symName: '_$sypWOc', symObjAddr: 0xF168, symBinAddr: 0x20A4C, symSize: 0x3C }
  - { offsetInCU: 0x89BD, offset: 0x6D534, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVWOr', symObjAddr: 0xF1A4, symBinAddr: 0x20A88, symSize: 0x54 }
  - { offsetInCU: 0x89D1, offset: 0x6D548, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVWOs', symObjAddr: 0xF1F8, symBinAddr: 0x20ADC, symSize: 0x54 }
  - { offsetInCU: 0x89E5, offset: 0x6D55C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_TA', symObjAddr: 0xF298, symBinAddr: 0x20B7C, symSize: 0x10 }
  - { offsetInCU: 0x89F9, offset: 0x6D570, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_yAA19CAPPluginCallResultCSg_So0fG0CSgtcfU_TA', symObjAddr: 0xF2E8, symBinAddr: 0x20BCC, symSize: 0xC }
  - { offsetInCU: 0x8A0D, offset: 0x6D584, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC12handleJSCall4callyAA0D0V_tFyyYbcfU0_yAA18CAPPluginCallErrorCSgcfU0_TA', symObjAddr: 0xF33C, symBinAddr: 0x20C20, symSize: 0xC }
  - { offsetInCU: 0x8A4D, offset: 0x6D5C4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVWOs', symObjAddr: 0xF3D0, symBinAddr: 0x20CB4, symSize: 0x90 }
  - { offsetInCU: 0x8A61, offset: 0x6D5D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVWOr', symObjAddr: 0xF4BC, symBinAddr: 0x20DA0, symSize: 0x94 }
  - { offsetInCU: 0x8A75, offset: 0x6D5EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSResultProtocol_pWOb', symObjAddr: 0xF5C0, symBinAddr: 0x20EA4, symSize: 0x18 }
  - { offsetInCU: 0x8A89, offset: 0x6D600, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC9toJsError5erroryAA16JSResultProtocol_p_tFyyScMYccfU_TA', symObjAddr: 0xF5D8, symBinAddr: 0x20EBC, symSize: 0xC }
  - { offsetInCU: 0x8A9D, offset: 0x6D614, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_0, symObjAddr: 0xF608, symBinAddr: 0x20EC8, symSize: 0x3C }
  - { offsetInCU: 0x8AB1, offset: 0x6D628, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVWOr', symObjAddr: 0xF688, symBinAddr: 0x20F48, symSize: 0x64 }
  - { offsetInCU: 0x8AC5, offset: 0x6D63C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4toJs6result4saveyAA16JSResultProtocol_p_SbtFyyScMYccfU_TA', symObjAddr: 0xF744, symBinAddr: 0x21004, symSize: 0x14 }
  - { offsetInCU: 0x8AD9, offset: 0x6D650, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListVACSeAAWl', symObjAddr: 0xF778, symBinAddr: 0x21038, symSize: 0x44 }
  - { offsetInCU: 0x8AED, offset: 0x6D664, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC25setupCordovaCompatibilityyyFy10Foundation12NotificationVYbcfU_TA', symObjAddr: 0xF7BC, symBinAddr: 0x2107C, symSize: 0x28 }
  - { offsetInCU: 0x8B01, offset: 0x6D678, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC25setupCordovaCompatibilityyyFy10Foundation12NotificationVYbcfU0_TA', symObjAddr: 0xF7E4, symBinAddr: 0x210A4, symSize: 0x28 }
  - { offsetInCU: 0x8B15, offset: 0x6D68C, size: 0x8, addend: 0x0, symName: '_$sIeg_SgWOe', symObjAddr: 0xF80C, symBinAddr: 0x210CC, symSize: 0x10 }
  - { offsetInCU: 0x8B29, offset: 0x6D6A0, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TRTA', symObjAddr: 0xF840, symBinAddr: 0x21100, symSize: 0x8 }
  - { offsetInCU: 0x8B3D, offset: 0x6D6B4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeDelegate_pSgXwWOh', symObjAddr: 0xF848, symBinAddr: 0x21108, symSize: 0x24 }
  - { offsetInCU: 0x8B51, offset: 0x6D6C8, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOc', symObjAddr: 0xF86C, symBinAddr: 0x2112C, symSize: 0x3C }
  - { offsetInCU: 0x8B65, offset: 0x6D6DC, size: 0x8, addend: 0x0, symName: '_$ss11AnyHashableVWOh', symObjAddr: 0xF8A8, symBinAddr: 0x21168, symSize: 0x34 }
  - { offsetInCU: 0x8B79, offset: 0x6D6F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfcy10Foundation12NotificationVYbcfU_TA', symObjAddr: 0xF9D8, symBinAddr: 0x21298, symSize: 0x8 }
  - { offsetInCU: 0x8B8D, offset: 0x6D704, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListVMa', symObjAddr: 0xF9E0, symBinAddr: 0x212A0, symSize: 0x10 }
  - { offsetInCU: 0x8BA1, offset: 0x6D718, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOAFs0D3KeyAAWl', symObjAddr: 0xF9F0, symBinAddr: 0x212B0, symSize: 0x44 }
  - { offsetInCU: 0x8BB5, offset: 0x6D72C, size: 0x8, addend: 0x0, symName: ___swift_memcpy0_1, symObjAddr: 0xFA94, symBinAddr: 0x21354, symSize: 0x4 }
  - { offsetInCU: 0x8BC9, offset: 0x6D740, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0xFA98, symBinAddr: 0x21358, symSize: 0x4 }
  - { offsetInCU: 0x8BDD, offset: 0x6D754, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOwet', symObjAddr: 0xFA9C, symBinAddr: 0x2135C, symSize: 0x50 }
  - { offsetInCU: 0x8BF1, offset: 0x6D768, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOwst', symObjAddr: 0xFAEC, symBinAddr: 0x213AC, symSize: 0x8C }
  - { offsetInCU: 0x8C05, offset: 0x6D77C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOwug', symObjAddr: 0xFB78, symBinAddr: 0x21438, symSize: 0x8 }
  - { offsetInCU: 0x8C19, offset: 0x6D790, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOwup', symObjAddr: 0xFB80, symBinAddr: 0x21440, symSize: 0x4 }
  - { offsetInCU: 0x8C2D, offset: 0x6D7A4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOwui', symObjAddr: 0xFB84, symBinAddr: 0x21444, symSize: 0x4 }
  - { offsetInCU: 0x8C41, offset: 0x6D7B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOMa', symObjAddr: 0xFB88, symBinAddr: 0x21448, symSize: 0x10 }
  - { offsetInCU: 0x8C55, offset: 0x6D7CC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOSHAASQWb', symObjAddr: 0xFB98, symBinAddr: 0x21458, symSize: 0x4 }
  - { offsetInCU: 0x8C69, offset: 0x6D7E0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOAFSQAAWl', symObjAddr: 0xFB9C, symBinAddr: 0x2145C, symSize: 0x44 }
  - { offsetInCU: 0x8C7D, offset: 0x6D7F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0xFBE0, symBinAddr: 0x214A0, symSize: 0x4 }
  - { offsetInCU: 0x8C91, offset: 0x6D808, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0xFBE4, symBinAddr: 0x214A4, symSize: 0x44 }
  - { offsetInCU: 0x8CA5, offset: 0x6D81C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs0D3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0xFC28, symBinAddr: 0x214E8, symSize: 0x4 }
  - { offsetInCU: 0x8CB9, offset: 0x6D830, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0xFC2C, symBinAddr: 0x214EC, symSize: 0x44 }
  - { offsetInCU: 0x8DD3, offset: 0x6D94A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x6C, symBinAddr: 0x11B08, symSize: 0x3C }
  - { offsetInCU: 0x8E75, offset: 0x6D9EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x104, symBinAddr: 0x11BA0, symSize: 0x28 }
  - { offsetInCU: 0x8E91, offset: 0x6DA08, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16RegistrationListV10CodingKeys33_B558F97FDDF7E6D98578814FFB110E95LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x12C, symBinAddr: 0x11BC8, symSize: 0x28 }
  - { offsetInCU: 0x92B3, offset: 0x6DE2A, size: 0x8, addend: 0x0, symName: '_$sSlsE6prefixy11SubSequenceQzSiFSS_Tg5Tf4ng_n', symObjAddr: 0xF348, symBinAddr: 0x20C2C, symSize: 0x88 }
  - { offsetInCU: 0x9674, offset: 0x6E1EB, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC10callbackId10methodName7options7success5errorABSgSSSg_AISDys11AnyHashableVypGSgy9Capacitor0aB6ResultCSg_AHtcSgyAN0aB5ErrorCSgcSgtcfcTO', symObjAddr: 0x8FFC, symBinAddr: 0x1AA14, symSize: 0x1C0 }
  - { offsetInCU: 0x4F, offset: 0x6E600, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tF', symObjAddr: 0x8, symBinAddr: 0x215F8, symSize: 0x4 }
  - { offsetInCU: 0x63, offset: 0x6E614, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tFyyScMYccfU_', symObjAddr: 0xC, symBinAddr: 0x215FC, symSize: 0x84 }
  - { offsetInCU: 0xAE, offset: 0x6E65F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tFyyScMYccfU_ySaySo12NSHTTPCookieCGcfU_', symObjAddr: 0x90, symBinAddr: 0x21680, symSize: 0x114 }
  - { offsetInCU: 0x216, offset: 0x6E7C7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tFTo', symObjAddr: 0x210, symBinAddr: 0x21800, symSize: 0x4C }
  - { offsetInCU: 0x248, offset: 0x6E7F9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverCACycfC', symObjAddr: 0x25C, symBinAddr: 0x2184C, symSize: 0x20 }
  - { offsetInCU: 0x266, offset: 0x6E817, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverCACycfc', symObjAddr: 0x27C, symBinAddr: 0x2186C, symSize: 0x30 }
  - { offsetInCU: 0x2A1, offset: 0x6E852, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverCACycfcTo', symObjAddr: 0x2AC, symBinAddr: 0x2189C, symSize: 0x3C }
  - { offsetInCU: 0x2DC, offset: 0x6E88D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverCfD', symObjAddr: 0x2E8, symBinAddr: 0x218D8, symSize: 0x30 }
  - { offsetInCU: 0x309, offset: 0x6E8BA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tFTf4nd_n', symObjAddr: 0x11D8, symBinAddr: 0x227C8, symSize: 0x200 }
  - { offsetInCU: 0x3FD, offset: 0x6E9AE, size: 0x8, addend: 0x0, symName: '_$sSaySo12NSHTTPCookieCGIegg_So7NSArrayCIeyBy_TR', symObjAddr: 0x1A4, symBinAddr: 0x21794, symSize: 0x6C }
  - { offsetInCU: 0x421, offset: 0x6E9D2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC12getServerUrl10Foundation3URLVSgyF', symObjAddr: 0x318, symBinAddr: 0x21908, symSize: 0x1CC }
  - { offsetInCU: 0x450, offset: 0x6EA01, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC14isUrlSanitized33_9693F7733192184D77E45ECC2FDF6960LLySbSSF', symObjAddr: 0x4E4, symBinAddr: 0x21AD4, symSize: 0x1D8 }
  - { offsetInCU: 0x4CE, offset: 0x6EA7F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC12getServerUrly10Foundation3URLVSgSSSgF', symObjAddr: 0x6BC, symBinAddr: 0x21CAC, symSize: 0x188 }
  - { offsetInCU: 0x5B9, offset: 0x6EB6A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC6encodeyS2SF', symObjAddr: 0x844, symBinAddr: 0x21E34, symSize: 0xC0 }
  - { offsetInCU: 0x609, offset: 0x6EBBA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC6decodeyS2SF', symObjAddr: 0x904, symBinAddr: 0x21EF4, symSize: 0x44 }
  - { offsetInCU: 0x668, offset: 0x6EC19, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC03setB0yySS_SStF', symObjAddr: 0x948, symBinAddr: 0x21F38, symSize: 0x2BC }
  - { offsetInCU: 0x7CE, offset: 0x6ED7F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC03setB0yy10Foundation3URLV_S3SSgAHtF', symObjAddr: 0xC04, symBinAddr: 0x221F4, symSize: 0xC }
  - { offsetInCU: 0x7EA, offset: 0x6ED9B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC15getCookiesAsMapySDyS2SG10Foundation3URLVF', symObjAddr: 0xC10, symBinAddr: 0x22200, symSize: 0x4 }
  - { offsetInCU: 0x806, offset: 0x6EDB7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC10getCookiesSSyF', symObjAddr: 0xC14, symBinAddr: 0x22204, symSize: 0x4F0 }
  - { offsetInCU: 0xD03, offset: 0x6F2B4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC06deleteB0yy10Foundation3URLV_SStF', symObjAddr: 0x1104, symBinAddr: 0x226F4, symSize: 0x4 }
  - { offsetInCU: 0xD4C, offset: 0x6F2FD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC12clearCookiesyy10Foundation3URLVF', symObjAddr: 0x1108, symBinAddr: 0x226F8, symSize: 0x4 }
  - { offsetInCU: 0xD95, offset: 0x6F346, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC15clearAllCookiesyyF', symObjAddr: 0x110C, symBinAddr: 0x226FC, symSize: 0x4 }
  - { offsetInCU: 0xDDE, offset: 0x6F38F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC20syncCookiesToWebViewyyF', symObjAddr: 0x1110, symBinAddr: 0x22700, symSize: 0x4 }
  - { offsetInCU: 0xDF2, offset: 0x6F3A3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerCfd', symObjAddr: 0x1198, symBinAddr: 0x22788, symSize: 0x1C }
  - { offsetInCU: 0xE2D, offset: 0x6F3DE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerCfD', symObjAddr: 0x11B4, symBinAddr: 0x227A4, symSize: 0x24 }
  - { offsetInCU: 0xE78, offset: 0x6F429, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC20syncCookiesToWebViewyyFTf4d_n', symObjAddr: 0x1504, symBinAddr: 0x22A70, symSize: 0x33C }
  - { offsetInCU: 0x100B, offset: 0x6F5BC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC03setB0yy10Foundation3URLV_S3SSgAHtFTf4nnnnnd_n', symObjAddr: 0x1840, symBinAddr: 0x22DAC, symSize: 0x2FC }
  - { offsetInCU: 0x12FE, offset: 0x6F8AF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC15getCookiesAsMapySDyS2SG10Foundation3URLVFTf4nd_n', symObjAddr: 0x1B3C, symBinAddr: 0x230A8, symSize: 0x374 }
  - { offsetInCU: 0x15A7, offset: 0x6FB58, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC06deleteB0yy10Foundation3URLV_SStFTf4nnd_n', symObjAddr: 0x20A0, symBinAddr: 0x235C8, symSize: 0x310 }
  - { offsetInCU: 0x1653, offset: 0x6FC04, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC12clearCookiesyy10Foundation3URLVFTf4nd_n', symObjAddr: 0x23B0, symBinAddr: 0x238D8, symSize: 0x398 }
  - { offsetInCU: 0x17FA, offset: 0x6FDAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC15clearAllCookiesyyFTf4d_n', symObjAddr: 0x2748, symBinAddr: 0x23C70, symSize: 0x37C }
  - { offsetInCU: 0x1AE7, offset: 0x70098, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverCMa', symObjAddr: 0x13D8, symBinAddr: 0x229C8, symSize: 0x20 }
  - { offsetInCU: 0x1AFB, offset: 0x700AC, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOc', symObjAddr: 0x1438, symBinAddr: 0x229E8, symSize: 0x48 }
  - { offsetInCU: 0x1B0F, offset: 0x700C0, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x1480, symBinAddr: 0x22A30, symSize: 0x40 }
  - { offsetInCU: 0x1B9C, offset: 0x7014D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerCMa', symObjAddr: 0x2AC4, symBinAddr: 0x23FEC, symSize: 0x20 }
  - { offsetInCU: 0x1BB0, offset: 0x70161, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x2B40, symBinAddr: 0x24068, symSize: 0x10 }
  - { offsetInCU: 0x1BC4, offset: 0x70175, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x2B50, symBinAddr: 0x24078, symSize: 0x8 }
  - { offsetInCU: 0x1BD8, offset: 0x70189, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x2B58, symBinAddr: 0x24080, symSize: 0x48 }
  - { offsetInCU: 0x1BEC, offset: 0x7019D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC06deleteB0yy10Foundation3URLV_SStFyyScMYccfU0_TA', symObjAddr: 0x2BE0, symBinAddr: 0x24108, symSize: 0x20 }
  - { offsetInCU: 0x1C00, offset: 0x701B1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A13CookieManagerC20syncCookiesToWebViewyyFyyScMYccfU_TA', symObjAddr: 0x2C00, symBinAddr: 0x24128, symSize: 0x20 }
  - { offsetInCU: 0x1C14, offset: 0x701C5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A16WKCookieObserverC16cookiesDidChange2inySo17WKHTTPCookieStoreC_tFyyScMYccfU_TA', symObjAddr: 0x2C20, symBinAddr: 0x24148, symSize: 0x8 }
  - { offsetInCU: 0x1C3C, offset: 0x701ED, size: 0x8, addend: 0x0, symName: '_$ss27_finalizeUninitializedArrayySayxGABnlFSS_SSt_Tg5', symObjAddr: 0x0, symBinAddr: 0x215F0, symSize: 0x4 }
  - { offsetInCU: 0x1C58, offset: 0x70209, size: 0x8, addend: 0x0, symName: '_$ss27_finalizeUninitializedArrayySayxGABnlFSS_So42UIScrollViewContentInsetAdjustmentBehaviorVt_Tg5', symObjAddr: 0x4, symBinAddr: 0x215F4, symSize: 0x4 }
  - { offsetInCU: 0x1D79, offset: 0x7032A, size: 0x8, addend: 0x0, symName: '_$sSTsE5first5where7ElementQzSgSbADKXE_tKFSaySo12NSHTTPCookieCG_Tg5070$s9Capacitor0A13CookieManagerC06deleteB0yy10Foundation3URLV_SStFSbSo12D6CXEfU_SSTf1cn_nTf4ng_n', symObjAddr: 0x1EF4, symBinAddr: 0x2341C, symSize: 0x1AC }
  - { offsetInCU: 0x27, offset: 0x7071D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC4loadyyF', symObjAddr: 0x0, symBinAddr: 0x24190, symSize: 0x94 }
  - { offsetInCU: 0x126, offset: 0x7081C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC4loadyyF', symObjAddr: 0x0, symBinAddr: 0x24190, symSize: 0x94 }
  - { offsetInCU: 0x1B7, offset: 0x708AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC4loadyyFTo', symObjAddr: 0x94, symBinAddr: 0x24224, symSize: 0x2C }
  - { offsetInCU: 0x1F1, offset: 0x708E7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC10getCookiesyySo13CAPPluginCallCF', symObjAddr: 0xC0, symBinAddr: 0x24250, symSize: 0x204 }
  - { offsetInCU: 0x2C2, offset: 0x709B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC10getCookiesyySo13CAPPluginCallCFTo', symObjAddr: 0x2C4, symBinAddr: 0x24454, symSize: 0x50 }
  - { offsetInCU: 0x2DE, offset: 0x709D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC9setCookieyySo13CAPPluginCallCF', symObjAddr: 0x314, symBinAddr: 0x244A4, symSize: 0x420 }
  - { offsetInCU: 0x4C8, offset: 0x70BBE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC9setCookieyySo13CAPPluginCallCFTo', symObjAddr: 0x734, symBinAddr: 0x248C4, symSize: 0x50 }
  - { offsetInCU: 0x4E4, offset: 0x70BDA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC12deleteCookieyySo13CAPPluginCallCF', symObjAddr: 0x784, symBinAddr: 0x24914, symSize: 0x274 }
  - { offsetInCU: 0x5CE, offset: 0x70CC4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC12deleteCookieyySo13CAPPluginCallCFTo', symObjAddr: 0x9F8, symBinAddr: 0x24B88, symSize: 0x50 }
  - { offsetInCU: 0x5EA, offset: 0x70CE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC12clearCookiesyySo13CAPPluginCallCF', symObjAddr: 0xA48, symBinAddr: 0x24BD8, symSize: 0x1B8 }
  - { offsetInCU: 0x6D0, offset: 0x70DC6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC12clearCookiesyySo13CAPPluginCallCFTo', symObjAddr: 0xC00, symBinAddr: 0x24D90, symSize: 0x50 }
  - { offsetInCU: 0x716, offset: 0x70E0C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC15clearAllCookiesyySo13CAPPluginCallCFTo', symObjAddr: 0xC50, symBinAddr: 0x24DE0, symSize: 0x68 }
  - { offsetInCU: 0x79D, offset: 0x70E93, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0xCB8, symBinAddr: 0x24E48, symSize: 0xB4 }
  - { offsetInCU: 0x7BB, offset: 0x70EB1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0xD6C, symBinAddr: 0x24EFC, symSize: 0xC0 }
  - { offsetInCU: 0x834, offset: 0x70F2A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0xE4C, symBinAddr: 0x24FDC, symSize: 0xE4 }
  - { offsetInCU: 0x883, offset: 0x70F79, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCACycfC', symObjAddr: 0xF30, symBinAddr: 0x250C0, symSize: 0x20 }
  - { offsetInCU: 0x8A1, offset: 0x70F97, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCACycfc', symObjAddr: 0xF50, symBinAddr: 0x250E0, symSize: 0x3C }
  - { offsetInCU: 0x8DC, offset: 0x70FD2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCACycfcTo', symObjAddr: 0xF8C, symBinAddr: 0x2511C, symSize: 0x48 }
  - { offsetInCU: 0x917, offset: 0x7100D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCfD', symObjAddr: 0xFD4, symBinAddr: 0x25164, symSize: 0x30 }
  - { offsetInCU: 0x98A, offset: 0x71080, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCMa', symObjAddr: 0xE2C, symBinAddr: 0x24FBC, symSize: 0x20 }
  - { offsetInCU: 0x9AA, offset: 0x710A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPCookiesPluginCfETo', symObjAddr: 0x1004, symBinAddr: 0x25194, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x712B6, size: 0x8, addend: 0x0, symName: '_$sSayxG9Capacitor0A9ExtensionA2bCP9capacitor0A4TypeQzvgTW', symObjAddr: 0x0, symBinAddr: 0x251A4, symSize: 0x14 }
  - { offsetInCU: 0x3F, offset: 0x712CE, size: 0x8, addend: 0x0, symName: '_$sSayxG9Capacitor0A9ExtensionA2bCP9capacitor0A4TypeQzvgTW', symObjAddr: 0x0, symBinAddr: 0x251A4, symSize: 0x14 }
  - { offsetInCU: 0xBC, offset: 0x7134B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A9ExtensionPAAE9capacitorAA0aB11TypeWrapperVyxGvg', symObjAddr: 0x14, symBinAddr: 0x251B8, symSize: 0x80 }
  - { offsetInCU: 0x120, offset: 0x713AF, size: 0x8, addend: 0x0, symName: '_$sSayxG9Capacitor0A9ExtensionA2bCP9capacitor0A4TypeQzmvgZTW', symObjAddr: 0x94, symBinAddr: 0x25238, symSize: 0xC }
  - { offsetInCU: 0x13C, offset: 0x713CB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A9ExtensionPAAE9capacitorAA0aB11TypeWrapperVyxGmvgZ', symObjAddr: 0xA0, symBinAddr: 0x25244, symSize: 0x4 }
  - { offsetInCU: 0x171, offset: 0x71400, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVMi', symObjAddr: 0xBC, symBinAddr: 0x25248, symSize: 0x8 }
  - { offsetInCU: 0x185, offset: 0x71414, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVMr', symObjAddr: 0xC4, symBinAddr: 0x25250, symSize: 0x6C }
  - { offsetInCU: 0x199, offset: 0x71428, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwCP', symObjAddr: 0x130, symBinAddr: 0x252BC, symSize: 0x70 }
  - { offsetInCU: 0x1AD, offset: 0x7143C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwxx', symObjAddr: 0x1A0, symBinAddr: 0x2532C, symSize: 0x10 }
  - { offsetInCU: 0x1C1, offset: 0x71450, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwcp', symObjAddr: 0x1B0, symBinAddr: 0x2533C, symSize: 0x30 }
  - { offsetInCU: 0x1D5, offset: 0x71464, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwca', symObjAddr: 0x1E0, symBinAddr: 0x2536C, symSize: 0x30 }
  - { offsetInCU: 0x1E9, offset: 0x71478, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwtk', symObjAddr: 0x210, symBinAddr: 0x2539C, symSize: 0x30 }
  - { offsetInCU: 0x1FD, offset: 0x7148C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwta', symObjAddr: 0x240, symBinAddr: 0x253CC, symSize: 0x30 }
  - { offsetInCU: 0x211, offset: 0x714A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwet', symObjAddr: 0x270, symBinAddr: 0x253FC, symSize: 0x10C }
  - { offsetInCU: 0x225, offset: 0x714B4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVwst', symObjAddr: 0x37C, symBinAddr: 0x25508, symSize: 0x1B8 }
  - { offsetInCU: 0x239, offset: 0x714C8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVMa', symObjAddr: 0x534, symBinAddr: 0x256C0, symSize: 0xC }
  - { offsetInCU: 0x271, offset: 0x71500, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV9Capacitor0C9ExtensionA2dEP9capacitor0C4TypeQzvgTW', symObjAddr: 0x540, symBinAddr: 0x256CC, symSize: 0xC }
  - { offsetInCU: 0x2CE, offset: 0x7155D, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV9Capacitor0C9ExtensionA2dEP9capacitor0C4TypeQzmvgZTW', symObjAddr: 0x54C, symBinAddr: 0x256D8, symSize: 0xC }
  - { offsetInCU: 0x2EA, offset: 0x71579, size: 0x8, addend: 0x0, symName: '_$sSo7UIColorC9Capacitor0B9ExtensionA2cDP9capacitor0B4TypeQzvgTW', symObjAddr: 0x558, symBinAddr: 0x256E4, symSize: 0xC }
  - { offsetInCU: 0x306, offset: 0x71595, size: 0x8, addend: 0x0, symName: '_$sSo7UIColorC9Capacitor0B9ExtensionA2cDP9capacitor0B4TypeQzmvgZTW', symObjAddr: 0x564, symBinAddr: 0x256F0, symSize: 0xC }
  - { offsetInCU: 0x322, offset: 0x715B1, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9Capacitor0C9ExtensionA2cDP9capacitor0C4TypeQzmvgZTW', symObjAddr: 0x570, symBinAddr: 0x256FC, symSize: 0xC }
  - { offsetInCU: 0x33E, offset: 0x715CD, size: 0x8, addend: 0x0, symName: ___swift_instantiateGenericMetadata, symObjAddr: 0x60C, symBinAddr: 0x25708, symSize: 0x2C }
  - { offsetInCU: 0x8D, offset: 0x717EE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC4httpyySo13CAPPluginCallC_SSSgtF', symObjAddr: 0x0, symBinAddr: 0x25748, symSize: 0x3C0 }
  - { offsetInCU: 0x1B2, offset: 0x71913, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC4httpyySo13CAPPluginCallC_SSSgtFTo', symObjAddr: 0x3C0, symBinAddr: 0x25B08, symSize: 0x8C }
  - { offsetInCU: 0x1F8, offset: 0x71959, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC7requestyySo13CAPPluginCallCFTo', symObjAddr: 0x44C, symBinAddr: 0x25B94, symSize: 0x58 }
  - { offsetInCU: 0x23B, offset: 0x7199C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x568, symBinAddr: 0x25CB0, symSize: 0xB4 }
  - { offsetInCU: 0x259, offset: 0x719BA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x61C, symBinAddr: 0x25D64, symSize: 0xB4 }
  - { offsetInCU: 0x2D2, offset: 0x71A33, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x6F0, symBinAddr: 0x25E38, symSize: 0xD8 }
  - { offsetInCU: 0x329, offset: 0x71A8A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginCACycfC', symObjAddr: 0x7C8, symBinAddr: 0x25F10, symSize: 0x20 }
  - { offsetInCU: 0x347, offset: 0x71AA8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginCACycfc', symObjAddr: 0x7E8, symBinAddr: 0x25F30, symSize: 0x30 }
  - { offsetInCU: 0x382, offset: 0x71AE3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginCACycfcTo', symObjAddr: 0x818, symBinAddr: 0x25F60, symSize: 0x3C }
  - { offsetInCU: 0x3BD, offset: 0x71B1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginCfD', symObjAddr: 0x854, symBinAddr: 0x25F9C, symSize: 0x30 }
  - { offsetInCU: 0x3EB, offset: 0x71B4C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13CAPHttpPluginCMa', symObjAddr: 0x6D0, symBinAddr: 0x25E18, symSize: 0x20 }
  - { offsetInCU: 0xB9, offset: 0x71DB3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7request10Foundation10URLRequestVvg', symObjAddr: 0x198, symBinAddr: 0x2619C, symSize: 0x64 }
  - { offsetInCU: 0xD8, offset: 0x71DD2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7request10Foundation10URLRequestVvs', symObjAddr: 0x1FC, symBinAddr: 0x26200, symSize: 0x88 }
  - { offsetInCU: 0x117, offset: 0x71E11, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7request10Foundation10URLRequestVvM', symObjAddr: 0x284, symBinAddr: 0x26288, symSize: 0x44 }
  - { offsetInCU: 0x136, offset: 0x71E30, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7headersSDyS2SGvg', symObjAddr: 0x2C8, symBinAddr: 0x262CC, symSize: 0x48 }
  - { offsetInCU: 0x155, offset: 0x71E4F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7headersSDyS2SGvs', symObjAddr: 0x310, symBinAddr: 0x26314, symSize: 0x50 }
  - { offsetInCU: 0x194, offset: 0x71E8E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7headersSDyS2SGvM', symObjAddr: 0x360, symBinAddr: 0x26364, symSize: 0x44 }
  - { offsetInCU: 0x1B3, offset: 0x71EAD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC7headersSDyS2SGvM.resume.0', symObjAddr: 0x3A4, symBinAddr: 0x263A8, symSize: 0x4 }
  - { offsetInCU: 0x1ED, offset: 0x71EE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC_6methodAC10Foundation3URLV_SStcfC', symObjAddr: 0x3B8, symBinAddr: 0x263BC, symSize: 0x58 }
  - { offsetInCU: 0x221, offset: 0x71F1B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC_6methodAC10Foundation3URLV_SStcfc', symObjAddr: 0x410, symBinAddr: 0x26414, symSize: 0x30 }
  - { offsetInCU: 0x235, offset: 0x71F2F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getC10DataAsJsony10Foundation0E0VSgAA7JSValue_pKF', symObjAddr: 0x440, symBinAddr: 0x26444, symSize: 0x198 }
  - { offsetInCU: 0x278, offset: 0x71F72, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getc10DataAsFormB7Encodedy10Foundation0E0VSgAA7JSValue_pKF', symObjAddr: 0x5D8, symBinAddr: 0x265DC, symSize: 0x360 }
  - { offsetInCU: 0x358, offset: 0x72052, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getc10DataAsFormB7Encodedy10Foundation0E0VSgAA7JSValue_pKFySSXEfU_', symObjAddr: 0x938, symBinAddr: 0x2693C, symSize: 0x280 }
  - { offsetInCU: 0x4DB, offset: 0x721D5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getc19DataAsMultipartFormE0y10Foundation0E0VAA7JSValue_p_SStKF', symObjAddr: 0xBB8, symBinAddr: 0x26BBC, symSize: 0x364 }
  - { offsetInCU: 0x732, offset: 0x7242C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getc19DataAsMultipartFormE0y10Foundation0E0VAA7JSValue_p_SStKFySS_SStXEfU0_', symObjAddr: 0xF1C, symBinAddr: 0x26F20, symSize: 0x294 }
  - { offsetInCU: 0x96A, offset: 0x72664, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC19overrideContentType33_F1EC701B45DB364BA9C013A5CEB076C6LLyySSF', symObjAddr: 0x11B0, symBinAddr: 0x271B4, symSize: 0x130 }
  - { offsetInCU: 0xAA9, offset: 0x727A3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getC12DataAsStringy10Foundation0E0VAA7JSValue_pKF', symObjAddr: 0x12E0, symBinAddr: 0x272E4, symSize: 0xAC }
  - { offsetInCU: 0xB01, offset: 0x727FB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getC6HeaderyypSgSSF', symObjAddr: 0x138C, symBinAddr: 0x27390, symSize: 0x128 }
  - { offsetInCU: 0xBDC, offset: 0x728D6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getc12DataFromFormE0y10Foundation0E0VSgAA7JSValue_p_SStKF', symObjAddr: 0x14B4, symBinAddr: 0x274B8, symSize: 0x102C }
  - { offsetInCU: 0x1597, offset: 0x73291, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getC4Datay10Foundation0E0VSgAA7JSValue_p_S2SSgtKF', symObjAddr: 0x24E0, symBinAddr: 0x284E4, symSize: 0x538 }
  - { offsetInCU: 0x179C, offset: 0x73496, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03setC7HeadersyySDySSypGFySSXEfU_', symObjAddr: 0x2A90, symBinAddr: 0x28A94, symSize: 0x20C }
  - { offsetInCU: 0x188A, offset: 0x73584, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03setC4BodyyyAA7JSValue_p_SSSgtKF', symObjAddr: 0x2C9C, symBinAddr: 0x28CA0, symSize: 0x228 }
  - { offsetInCU: 0x1988, offset: 0x73682, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC14setContentTypeyySSSgF', symObjAddr: 0x2EC4, symBinAddr: 0x28EC8, symSize: 0x80 }
  - { offsetInCU: 0x19DF, offset: 0x736D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC10setTimeoutyySdF', symObjAddr: 0x2F44, symBinAddr: 0x28F48, symSize: 0x5C }
  - { offsetInCU: 0x1A36, offset: 0x73730, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getbC010Foundation10URLRequestVyF', symObjAddr: 0x2FA0, symBinAddr: 0x28FA4, symSize: 0x64 }
  - { offsetInCU: 0x1A85, offset: 0x7377F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC10urlSession_4task26willPerformHTTPRedirection03newC017completionHandlerySo12NSURLSessionC_So0M4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVyAQSgctF', symObjAddr: 0x3004, symBinAddr: 0x29008, symSize: 0xA0 }
  - { offsetInCU: 0x1B1C, offset: 0x73816, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC10urlSession_4task26willPerformHTTPRedirection03newC017completionHandlerySo12NSURLSessionC_So0M4TaskCSo17NSHTTPURLResponseC10Foundation10URLRequestVyAQSgctFTo', symObjAddr: 0x30A4, symBinAddr: 0x290A8, symSize: 0x1F0 }
  - { offsetInCU: 0x1B86, offset: 0x73880, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC03getB7SessionySo12NSURLSessionCSo13CAPPluginCallCF', symObjAddr: 0x3294, symBinAddr: 0x29298, symSize: 0xD8 }
  - { offsetInCU: 0x1BED, offset: 0x738E7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCACycfC', symObjAddr: 0x34F4, symBinAddr: 0x294F8, symSize: 0x20 }
  - { offsetInCU: 0x1C0B, offset: 0x73905, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCACycfc', symObjAddr: 0x3514, symBinAddr: 0x29518, symSize: 0x2C }
  - { offsetInCU: 0x1C6E, offset: 0x73968, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCACycfcTo', symObjAddr: 0x3540, symBinAddr: 0x29544, symSize: 0x2C }
  - { offsetInCU: 0x1CD5, offset: 0x739CF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCfD', symObjAddr: 0x356C, symBinAddr: 0x29570, symSize: 0x34 }
  - { offsetInCU: 0x1D3D, offset: 0x73A37, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC_6methodAC10Foundation3URLV_SStcfcTf4ngn_n', symObjAddr: 0x4874, symBinAddr: 0x2A878, symSize: 0x4B8 }
  - { offsetInCU: 0x20F4, offset: 0x73DEE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE7getBoolySbSgSSFSo13CAPPluginCallC_Tg5', symObjAddr: 0x336C, symBinAddr: 0x29370, symSize: 0x188 }
  - { offsetInCU: 0x21A6, offset: 0x73EA0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCfETo', symObjAddr: 0x35A0, symBinAddr: 0x295A4, symSize: 0x50 }
  - { offsetInCU: 0x232E, offset: 0x74028, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO22withUnsafeMutableBytesyxxSwKXEKlFs16IndexingIteratorVySS8UTF8ViewVG_Sit_Tg5', symObjAddr: 0x3934, symBinAddr: 0x29938, symSize: 0x30C }
  - { offsetInCU: 0x241B, offset: 0x74115, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0V15withUnsafeBytesyxxSWKXEKlFyt_Tgq5015$s10Foundation4B42VyACxcSTRzs5UInt8V7ElementRtzlufcySWXEfU3_ACTf1ncn_n', symObjAddr: 0x3C50, symBinAddr: 0x29C54, symSize: 0xD4 }
  - { offsetInCU: 0x2485, offset: 0x7417F, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufcAC15_RepresentationOSWXEfU_', symObjAddr: 0x3D24, symBinAddr: 0x29D28, symSize: 0x74 }
  - { offsetInCU: 0x24FA, offset: 0x741F4, size: 0x8, addend: 0x0, symName: '_$s10Foundation13__DataStorageC22withUnsafeMutableBytes2in5applyxSnySiG_xSwKXEtKlFs16IndexingIteratorVySS8UTF8ViewVG_Sit_Tg5', symObjAddr: 0x3D98, symBinAddr: 0x29D9C, symSize: 0xAC }
  - { offsetInCU: 0x2556, offset: 0x74250, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_AI_SitSryAEGXEfU_SS8UTF8ViewV_TG5', symObjAddr: 0x3E44, symBinAddr: 0x29E48, symSize: 0x88 }
  - { offsetInCU: 0x26EB, offset: 0x743E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOAEs0D0AAWl', symObjAddr: 0x4D50, symBinAddr: 0x2AD30, symSize: 0x44 }
  - { offsetInCU: 0x2720, offset: 0x7441A, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV06InlineB0VyAESWcfCTf4nd_n', symObjAddr: 0x4E18, symBinAddr: 0x2AD74, symSize: 0xC4 }
  - { offsetInCU: 0x2796, offset: 0x74490, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV10LargeSliceVyAESWcfCTf4nd_n', symObjAddr: 0x4EDC, symBinAddr: 0x2AE38, symSize: 0x78 }
  - { offsetInCU: 0x27C3, offset: 0x744BD, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV11InlineSliceVyAESWcfCTf4nd_n', symObjAddr: 0x4F54, symBinAddr: 0x2AEB0, symSize: 0x80 }
  - { offsetInCU: 0x2818, offset: 0x74512, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOyAESWcfCTf4nd_n', symObjAddr: 0x4FD4, symBinAddr: 0x2AF30, symSize: 0x68 }
  - { offsetInCU: 0x2869, offset: 0x74563, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationO5countAESi_tcfCTf4nd_n', symObjAddr: 0x503C, symBinAddr: 0x2AF98, symSize: 0x9C }
  - { offsetInCU: 0x29B3, offset: 0x746AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCMa', symObjAddr: 0x5698, symBinAddr: 0x2B530, symSize: 0x3C }
  - { offsetInCU: 0x29C7, offset: 0x746C1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCMU', symObjAddr: 0x56D4, symBinAddr: 0x2B56C, symSize: 0x8 }
  - { offsetInCU: 0x29DB, offset: 0x746D5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestCMr', symObjAddr: 0x56DC, symBinAddr: 0x2B574, symSize: 0x78 }
  - { offsetInCU: 0x29EF, offset: 0x746E9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwCP', symObjAddr: 0x5754, symBinAddr: 0x2B5EC, symSize: 0x2C }
  - { offsetInCU: 0x2A03, offset: 0x746FD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwxx', symObjAddr: 0x5780, symBinAddr: 0x2B618, symSize: 0x8 }
  - { offsetInCU: 0x2A17, offset: 0x74711, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwcp', symObjAddr: 0x5788, symBinAddr: 0x2B620, symSize: 0x2C }
  - { offsetInCU: 0x2A2B, offset: 0x74725, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwca', symObjAddr: 0x57B4, symBinAddr: 0x2B64C, symSize: 0x40 }
  - { offsetInCU: 0x2A3F, offset: 0x74739, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x57F4, symBinAddr: 0x2B68C, symSize: 0xC }
  - { offsetInCU: 0x2A53, offset: 0x7474D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwta', symObjAddr: 0x5800, symBinAddr: 0x2B698, symSize: 0x30 }
  - { offsetInCU: 0x2A67, offset: 0x74761, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwet', symObjAddr: 0x5830, symBinAddr: 0x2B6C8, symSize: 0x5C }
  - { offsetInCU: 0x2A7B, offset: 0x74775, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwst', symObjAddr: 0x588C, symBinAddr: 0x2B724, symSize: 0x50 }
  - { offsetInCU: 0x2A8F, offset: 0x74789, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwug', symObjAddr: 0x58DC, symBinAddr: 0x2B774, symSize: 0x8 }
  - { offsetInCU: 0x2AA3, offset: 0x7479D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwup', symObjAddr: 0x58E4, symBinAddr: 0x2B77C, symSize: 0x4 }
  - { offsetInCU: 0x2AB7, offset: 0x747B1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOwui', symObjAddr: 0x58E8, symBinAddr: 0x2B780, symSize: 0x4 }
  - { offsetInCU: 0x2ACB, offset: 0x747C5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOMa', symObjAddr: 0x58EC, symBinAddr: 0x2B784, symSize: 0x10 }
  - { offsetInCU: 0x2ADF, offset: 0x747D9, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x590C, symBinAddr: 0x2B794, symSize: 0x10 }
  - { offsetInCU: 0x2B29, offset: 0x74823, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_SS8UTF8ViewV_TG5TA', symObjAddr: 0x5978, symBinAddr: 0x2B800, symSize: 0x58 }
  - { offsetInCU: 0x2B7C, offset: 0x74876, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataV15_RepresentationOSgWOe', symObjAddr: 0x59D0, symBinAddr: 0x2B858, symSize: 0x14 }
  - { offsetInCU: 0x2B90, offset: 0x7488A, size: 0x8, addend: 0x0, symName: '_$s10Foundation15ContiguousBytes_pWOb', symObjAddr: 0x59E4, symBinAddr: 0x2B86C, symSize: 0x18 }
  - { offsetInCU: 0x2BA4, offset: 0x7489E, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufc8IteratorQz_SitSwXEfU1_AI_SitSryAEGXEfU_SS8UTF8ViewV_TG5TA', symObjAddr: 0x59FC, symBinAddr: 0x2B884, symSize: 0x18 }
  - { offsetInCU: 0x2BB8, offset: 0x748B2, size: 0x8, addend: 0x0, symName: '_$sSw17withMemoryRebound2to_q_xm_q_SryxGKXEtKr0_lFs5UInt8V_s16IndexingIteratorVySS8UTF8ViewVG_SitTg5Tf4dnn_n', symObjAddr: 0x5A14, symBinAddr: 0x2B89C, symSize: 0x60 }
  - { offsetInCU: 0x2C18, offset: 0x74912, size: 0x8, addend: 0x0, symName: '_$sSTsE7forEachyyy7ElementQzKXEKFSDyS2SG_Tg5095$s9Capacitor0A10UrlRequestC03getc19DataAsMultipartFormE0y10Foundation0E0VAA7JSValue_p_SStKFySS_S6XEfU0_10Foundation0J0VSSTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x26004, symSize: 0x198 }
  - { offsetInCU: 0x2CDE, offset: 0x749D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOs0D0AAsAFP7_domainSSvgTW', symObjAddr: 0x3A8, symBinAddr: 0x263AC, symSize: 0x4 }
  - { offsetInCU: 0x2CFA, offset: 0x749F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOs0D0AAsAFP5_codeSivgTW', symObjAddr: 0x3AC, symBinAddr: 0x263B0, symSize: 0x4 }
  - { offsetInCU: 0x2D16, offset: 0x74A10, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOs0D0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0x3B0, symBinAddr: 0x263B4, symSize: 0x4 }
  - { offsetInCU: 0x2D32, offset: 0x74A2C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A10UrlRequestC0abC5ErrorOs0D0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x3B4, symBinAddr: 0x263B8, symSize: 0x4 }
  - { offsetInCU: 0x2F32, offset: 0x74C2C, size: 0x8, addend: 0x0, symName: '_$sSTsE6reduce4into_qd__qd__n_yqd__z_7ElementQztKXEtKlFSDySS9Capacitor7JSValue_pG_s17_NativeDictionaryVyS2SGTg5051$sSD16compactMapValuesySDyxqd__Gqd__Sgq_KXEKlFys17_fg46Vyxqd__Gz_x3key_q_5valuettKXEfU_SS_9Capacitor7E120_pSSTg5076$s9Capacitor0A10UrlRequestC03getc19DataAsMultipartFormE0y10Foundation0E0VAA7I22_p_SStKFSSSgAaH_pXEfU_Tf3nnpf_nTf1ncn_n', symObjAddr: 0x35F0, symBinAddr: 0x295F4, symSize: 0x344 }
  - { offsetInCU: 0x30F5, offset: 0x74DEF, size: 0x8, addend: 0x0, symName: '_$sSTsE7forEachyyy7ElementQzKXEKFSD4KeysVyS2S_G_Tg556$s9Capacitor0A10UrlRequestC03getC6HeaderyypSgSSFySSXEfU_SDySSypG9Capacitor0phI0CTf1cn_nTf4nng_n', symObjAddr: 0x3ECC, symBinAddr: 0x29ED0, symSize: 0x46C }
  - { offsetInCU: 0x33C2, offset: 0x750BC, size: 0x8, addend: 0x0, symName: '_$sSTsE7forEachyyy7ElementQzKXEKFSD4KeysVyS2S_G_Tg559$s9Capacitor0A10UrlRequestC03setC7HeadersyySDyS2SGFySSXEfU_SDyS2SG9Capacitor0qhI0CTf1cn_nTf4ngg_n', symObjAddr: 0x4338, symBinAddr: 0x2A33C, symSize: 0x3B0 }
  - { offsetInCU: 0x35E5, offset: 0x752DF, size: 0x8, addend: 0x0, symName: '_$sSTsE7forEachyyy7ElementQzKXEKFSD4KeysVySS9Capacitor7JSValue_p_G_Tg5076$s9Capacitor0A10UrlRequestC03getc10DataAsFormB7Encodedy10Foundation0E0VSgAA7F12_pKFySSXEfU_10Foundation13URLComponentsVSDySSAfG_pGTf1cn_nTf4nng_n', symObjAddr: 0x46E8, symBinAddr: 0x2A6EC, symSize: 0x18C }
  - { offsetInCU: 0x37EA, offset: 0x754E4, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVyACxcSTRzs5UInt8V7ElementRtzlufCSS8UTF8ViewV_Tg5Tf4nd_n', symObjAddr: 0x50D8, symBinAddr: 0x2B034, symSize: 0x4EC }
  - { offsetInCU: 0x2B, offset: 0x758B5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC6shared_WZ', symObjAddr: 0x0, symBinAddr: 0x2B944, symSize: 0x30 }
  - { offsetInCU: 0x4F, offset: 0x758D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC6sharedACvpZ', symObjAddr: 0x7C88, symBinAddr: 0xA6158, symSize: 0x0 }
  - { offsetInCU: 0x7A, offset: 0x75904, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCACycfC', symObjAddr: 0x30, symBinAddr: 0x2B974, symSize: 0x20 }
  - { offsetInCU: 0x8E, offset: 0x75918, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC6sharedACvgZ', symObjAddr: 0x90, symBinAddr: 0x2B9D4, symSize: 0x40 }
  - { offsetInCU: 0xEF, offset: 0x75979, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC7lastURL10Foundation0F0VSgvg', symObjAddr: 0x184, symBinAddr: 0x2BAC8, symSize: 0x50 }
  - { offsetInCU: 0x10E, offset: 0x75998, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_4open7optionsSbSo13UIApplicationC_10Foundation3URLVSDySo0H17OpenURLOptionsKeyaypGtF', symObjAddr: 0x25C, symBinAddr: 0x2BB18, symSize: 0xC }
  - { offsetInCU: 0x131, offset: 0x759BB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_4open7optionsSbSo13UIApplicationC_10Foundation3URLVSDySo0H17OpenURLOptionsKeyaypGtFTo', symObjAddr: 0x268, symBinAddr: 0x2BB24, symSize: 0x110 }
  - { offsetInCU: 0x163, offset: 0x759ED, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_8continue18restorationHandlerSbSo13UIApplicationC_So14NSUserActivityCySaySo06UIUserK9Restoring_pGSgctF', symObjAddr: 0x378, symBinAddr: 0x2BC34, symSize: 0x8 }
  - { offsetInCU: 0x186, offset: 0x75A10, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_8continue18restorationHandlerSbSo13UIApplicationC_So14NSUserActivityCySaySo06UIUserK9Restoring_pGSgctFTo', symObjAddr: 0x380, symBinAddr: 0x2BC3C, symSize: 0xA0 }
  - { offsetInCU: 0x1B8, offset: 0x75A42, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCACycfc', symObjAddr: 0x420, symBinAddr: 0x2BCDC, symSize: 0x6C }
  - { offsetInCU: 0x1F5, offset: 0x75A7F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCACycfcTo', symObjAddr: 0x48C, symBinAddr: 0x2BD48, symSize: 0x70 }
  - { offsetInCU: 0x230, offset: 0x75ABA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCfD', symObjAddr: 0x4FC, symBinAddr: 0x2BDB8, symSize: 0x34 }
  - { offsetInCU: 0x25D, offset: 0x75AE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_4open7optionsSbSo13UIApplicationC_10Foundation3URLVSDySo0H17OpenURLOptionsKeyaypGtFTf4dnnn_n', symObjAddr: 0x540, symBinAddr: 0x2BDFC, symSize: 0x2D0 }
  - { offsetInCU: 0x38A, offset: 0x75C14, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC11application_8continue18restorationHandlerSbSo13UIApplicationC_So14NSUserActivityCySaySo06UIUserK9Restoring_pGSgctFTf4dndn_n', symObjAddr: 0x810, symBinAddr: 0x2C0CC, symSize: 0x444 }
  - { offsetInCU: 0x509, offset: 0x75D93, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC6shared_WZ', symObjAddr: 0x0, symBinAddr: 0x2B944, symSize: 0x30 }
  - { offsetInCU: 0x539, offset: 0x75DC3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC6sharedACvau', symObjAddr: 0x50, symBinAddr: 0x2B994, symSize: 0x40 }
  - { offsetInCU: 0x55E, offset: 0x75DE8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyC7lastURL10Foundation0F0VSgvpACTk', symObjAddr: 0xD0, symBinAddr: 0x2BA14, symSize: 0xB4 }
  - { offsetInCU: 0x595, offset: 0x75E1F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCfETo', symObjAddr: 0x530, symBinAddr: 0x2BDEC, symSize: 0x10 }
  - { offsetInCU: 0x680, offset: 0x75F0A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCMa', symObjAddr: 0xC54, symBinAddr: 0x2C510, symSize: 0x3C }
  - { offsetInCU: 0x694, offset: 0x75F1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCMU', symObjAddr: 0xCE8, symBinAddr: 0x2C5A4, symSize: 0x8 }
  - { offsetInCU: 0x6A8, offset: 0x75F32, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24ApplicationDelegateProxyCMr', symObjAddr: 0xCF0, symBinAddr: 0x2C5AC, symSize: 0x6C }
  - { offsetInCU: 0x6BC, offset: 0x75F46, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgMa', symObjAddr: 0xD5C, symBinAddr: 0x2C618, symSize: 0x54 }
  - { offsetInCU: 0x6D0, offset: 0x75F5A, size: 0x8, addend: 0x0, symName: '_$sSo30UIApplicationOpenURLOptionsKeyaABSHSCWl', symObjAddr: 0xDB0, symBinAddr: 0x2C66C, symSize: 0x48 }
  - { offsetInCU: 0x6E4, offset: 0x75F6E, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOf', symObjAddr: 0xE38, symBinAddr: 0x2C6B4, symSize: 0x48 }
  - { offsetInCU: 0x27, offset: 0x761ED, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification_WZ', symObjAddr: 0x0, symBinAddr: 0x2C6FC, symSize: 0x9C }
  - { offsetInCU: 0x4B, offset: 0x76211, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification10Foundation0F0VvpZ', symObjAddr: 0x3B88, symBinAddr: 0xA6168, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x76230, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification_WZ', symObjAddr: 0x0, symBinAddr: 0x2C6FC, symSize: 0x9C }
  - { offsetInCU: 0x92, offset: 0x76258, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification10Foundation0F0Vvau', symObjAddr: 0x9C, symBinAddr: 0x2C798, symSize: 0x48 }
  - { offsetInCU: 0xC3, offset: 0x76289, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification10Foundation0F0VvgZ', symObjAddr: 0xFC, symBinAddr: 0x2C7E0, symSize: 0x70 }
  - { offsetInCU: 0x108, offset: 0x762CE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC27statusBarTappedNotification10Foundation0F0VvgZTo', symObjAddr: 0x16C, symBinAddr: 0x2C850, symSize: 0x5C }
  - { offsetInCU: 0x13F, offset: 0x76305, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC10getLastUrl10Foundation3URLVSgyFZ', symObjAddr: 0x1C8, symBinAddr: 0x2C8AC, symSize: 0x88 }
  - { offsetInCU: 0x18C, offset: 0x76352, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC13handleOpenUrlySb10Foundation3URLV_SDySo013UIApplicationD13URLOptionsKeyaypGtFZ', symObjAddr: 0x2D8, symBinAddr: 0x2C934, symSize: 0x9C }
  - { offsetInCU: 0x1F9, offset: 0x763BF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC22handleContinueActivityySbSo06NSUserE0C_ySaySo06UIUserE9Restoring_pGSgctFZ', symObjAddr: 0x374, symBinAddr: 0x2C9D0, symSize: 0x94 }
  - { offsetInCU: 0x262, offset: 0x76428, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeC21handleAppBecameActiveyySo13UIApplicationCFZ', symObjAddr: 0x408, symBinAddr: 0x2CA64, symSize: 0x4 }
  - { offsetInCU: 0x299, offset: 0x7645F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCACycfC', symObjAddr: 0x40C, symBinAddr: 0x2CA68, symSize: 0x20 }
  - { offsetInCU: 0x2B7, offset: 0x7647D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCACycfc', symObjAddr: 0x42C, symBinAddr: 0x2CA88, symSize: 0x30 }
  - { offsetInCU: 0x2F2, offset: 0x764B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCACycfcTo', symObjAddr: 0x45C, symBinAddr: 0x2CAB8, symSize: 0x3C }
  - { offsetInCU: 0x32D, offset: 0x764F3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCfD', symObjAddr: 0x498, symBinAddr: 0x2CAF4, symSize: 0x30 }
  - { offsetInCU: 0x3B5, offset: 0x7657B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCfETo', symObjAddr: 0x4C8, symBinAddr: 0x2CB24, symSize: 0x4 }
  - { offsetInCU: 0x3E0, offset: 0x765A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9CAPBridgeCMa', symObjAddr: 0x4CC, symBinAddr: 0x2CB28, symSize: 0x20 }
  - { offsetInCU: 0x27, offset: 0x767A5, size: 0x8, addend: 0x0, symName: '_$sSo16CAPBridgedPluginP9CapacitorE9getMethod5namedSo09CAPPluginE0CSgSS_tF', symObjAddr: 0x0, symBinAddr: 0x2CB48, symSize: 0xA0 }
  - { offsetInCU: 0x3F, offset: 0x767BD, size: 0x8, addend: 0x0, symName: '_$sSo16CAPBridgedPluginP9CapacitorE9getMethod5namedSo09CAPPluginE0CSgSS_tF', symObjAddr: 0x0, symBinAddr: 0x2CB48, symSize: 0xA0 }
  - { offsetInCU: 0xCD, offset: 0x7684B, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodCMa', symObjAddr: 0xA0, symBinAddr: 0x2CBE8, symSize: 0x3C }
  - { offsetInCU: 0x1EF, offset: 0x7696D, size: 0x8, addend: 0x0, symName: '_$sSTsE5first5where7ElementQzSgSbADKXE_tKFSaySo15CAPPluginMethodCG_Tg5054$sSo16CAPBridgedPluginP9CapacitorE9getMethod5namedSo09D19E0CSgSS_tFSbAGXEfU_SSTf1cn_nTf4ng_n', symObjAddr: 0xDC, symBinAddr: 0x2CC24, symSize: 0x198 }
  - { offsetInCU: 0x27, offset: 0x76C3A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE11modulePrintyySo9CAPPluginC_ypdtF', symObjAddr: 0x0, symBinAddr: 0x2CDBC, symSize: 0x254 }
  - { offsetInCU: 0x97, offset: 0x76CAA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE11modulePrintyySo9CAPPluginC_ypdtF', symObjAddr: 0x0, symBinAddr: 0x2CDBC, symSize: 0x254 }
  - { offsetInCU: 0x3A1, offset: 0x76FB4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE5alertyySS_S2StF', symObjAddr: 0x254, symBinAddr: 0x2D010, symSize: 0x90 }
  - { offsetInCU: 0x40A, offset: 0x7701D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE19setStatusBarVisibleyySbF', symObjAddr: 0x2E4, symBinAddr: 0x2D0A0, symSize: 0x14 }
  - { offsetInCU: 0x453, offset: 0x77066, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE17setStatusBarStyleyySo08UIStatusfG0VF', symObjAddr: 0x2F8, symBinAddr: 0x2D0B4, symSize: 0x14 }
  - { offsetInCU: 0x49C, offset: 0x770AF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPBridgeProtocolPAAE21setStatusBarAnimationyySo08UIStatusfG0VF', symObjAddr: 0x30C, symBinAddr: 0x2D0C8, symSize: 0x14 }
  - { offsetInCU: 0x4F1, offset: 0x77104, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO21__derived_enum_equalsySbAC_ACtFZ', symObjAddr: 0x320, symBinAddr: 0x2D0DC, symSize: 0x8 }
  - { offsetInCU: 0x52F, offset: 0x77142, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO4hash4intoys6HasherVz_tF', symObjAddr: 0x328, symBinAddr: 0x2D0E4, symSize: 0x24 }
  - { offsetInCU: 0x5C6, offset: 0x771D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO9hashValueSivg', symObjAddr: 0x34C, symBinAddr: 0x2D108, symSize: 0x40 }
  - { offsetInCU: 0x693, offset: 0x772A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x38C, symBinAddr: 0x2D148, symSize: 0x8 }
  - { offsetInCU: 0x6C9, offset: 0x772DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x394, symBinAddr: 0x2D150, symSize: 0x40 }
  - { offsetInCU: 0x7AC, offset: 0x773BF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x3D4, symBinAddr: 0x2D190, symSize: 0x24 }
  - { offsetInCU: 0x834, offset: 0x77447, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO11errorDomainSSvgZ', symObjAddr: 0x48C, symBinAddr: 0x2D248, symSize: 0x4 }
  - { offsetInCU: 0x848, offset: 0x7745B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO9errorCodeSivg', symObjAddr: 0x490, symBinAddr: 0x2D24C, symSize: 0x8 }
  - { offsetInCU: 0x8B6, offset: 0x774C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO13errorUserInfoSDySSypGvg', symObjAddr: 0x498, symBinAddr: 0x2D254, symSize: 0xB8 }
  - { offsetInCU: 0x999, offset: 0x775AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x550, symBinAddr: 0x2D30C, symSize: 0x4 }
  - { offsetInCU: 0x9B9, offset: 0x775CC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation13CustomNSErrorAadEP11errorDomainSSvgZTW', symObjAddr: 0x550, symBinAddr: 0x2D30C, symSize: 0x4 }
  - { offsetInCU: 0x9CB, offset: 0x775DE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation13CustomNSErrorAadEP9errorCodeSivgTW', symObjAddr: 0x554, symBinAddr: 0x2D310, symSize: 0x8 }
  - { offsetInCU: 0x9E7, offset: 0x775FA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation13CustomNSErrorAadEP13errorUserInfoSDySSypGvgTW', symObjAddr: 0x55C, symBinAddr: 0x2D318, symSize: 0x4 }
  - { offsetInCU: 0xA03, offset: 0x77616, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO16errorDescriptionSSSgvg', symObjAddr: 0x560, symBinAddr: 0x2D31C, symSize: 0x4 }
  - { offsetInCU: 0xA26, offset: 0x77639, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AadEP16errorDescriptionSSSgvgTW', symObjAddr: 0x564, symBinAddr: 0x2D320, symSize: 0x4 }
  - { offsetInCU: 0xA46, offset: 0x77659, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AadEP16errorDescriptionSSSgvgTW', symObjAddr: 0x564, symBinAddr: 0x2D320, symSize: 0x4 }
  - { offsetInCU: 0xA58, offset: 0x7766B, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x5B0, symBinAddr: 0x2D330, symSize: 0x20 }
  - { offsetInCU: 0xA6C, offset: 0x7767F, size: 0x8, addend: 0x0, symName: '_$sSaySSGSayxGSKsWl', symObjAddr: 0x610, symBinAddr: 0x2D350, symSize: 0x4C }
  - { offsetInCU: 0xA80, offset: 0x77693, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO11errorDomainSSvgZTf4d_n', symObjAddr: 0x6A0, symBinAddr: 0x2D39C, symSize: 0x24 }
  - { offsetInCU: 0xA9E, offset: 0x776B1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO16errorDescriptionSSSgvgTf4d_n', symObjAddr: 0x6C4, symBinAddr: 0x2D3C0, symSize: 0xB4 }
  - { offsetInCU: 0xAD2, offset: 0x776E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOSHAASQWb', symObjAddr: 0x778, symBinAddr: 0x2D474, symSize: 0x4 }
  - { offsetInCU: 0xAE6, offset: 0x776F9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOACSQAAWl', symObjAddr: 0x77C, symBinAddr: 0x2D478, symSize: 0x44 }
  - { offsetInCU: 0xAFA, offset: 0x7770D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation13CustomNSErrorAAs0C0PWb', symObjAddr: 0x7C0, symBinAddr: 0x2D4BC, symSize: 0x4 }
  - { offsetInCU: 0xB0E, offset: 0x77721, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOACs0C0AAWl', symObjAddr: 0x7C4, symBinAddr: 0x2D4C0, symSize: 0x44 }
  - { offsetInCU: 0xB22, offset: 0x77735, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AAs0C0PWb', symObjAddr: 0x808, symBinAddr: 0x2D504, symSize: 0x4 }
  - { offsetInCU: 0xB36, offset: 0x77749, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOwet', symObjAddr: 0x814, symBinAddr: 0x2D508, symSize: 0x50 }
  - { offsetInCU: 0xB4A, offset: 0x7775D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOwst', symObjAddr: 0x864, symBinAddr: 0x2D558, symSize: 0x8C }
  - { offsetInCU: 0xB5E, offset: 0x77771, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOwug', symObjAddr: 0x8F0, symBinAddr: 0x2D5E4, symSize: 0x8 }
  - { offsetInCU: 0xB72, offset: 0x77785, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOwup', symObjAddr: 0x8F8, symBinAddr: 0x2D5EC, symSize: 0x4 }
  - { offsetInCU: 0xB86, offset: 0x77799, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOwui', symObjAddr: 0x8FC, symBinAddr: 0x2D5F0, symSize: 0x4 }
  - { offsetInCU: 0xB9A, offset: 0x777AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOMa', symObjAddr: 0x900, symBinAddr: 0x2D5F4, symSize: 0x10 }
  - { offsetInCU: 0xBAE, offset: 0x777C1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOAC10Foundation13CustomNSErrorAAWl', symObjAddr: 0x910, symBinAddr: 0x2D604, symSize: 0x44 }
  - { offsetInCU: 0xC4F, offset: 0x77862, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x3F8, symBinAddr: 0x2D1B4, symSize: 0x3C }
  - { offsetInCU: 0xCEB, offset: 0x778FE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOs0C0AAsADP9_userInfoyXlSgvgTW', symObjAddr: 0x484, symBinAddr: 0x2D240, symSize: 0x4 }
  - { offsetInCU: 0xD07, offset: 0x7791A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOs0C0AAsADP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x488, symBinAddr: 0x2D244, symSize: 0x4 }
  - { offsetInCU: 0xDAB, offset: 0x779BE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOs0C0AAsADP7_domainSSvgTW', symObjAddr: 0x434, symBinAddr: 0x2D1F0, symSize: 0x28 }
  - { offsetInCU: 0xDC7, offset: 0x779DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorOs0C0AAsADP5_codeSivgTW', symObjAddr: 0x45C, symBinAddr: 0x2D218, symSize: 0x28 }
  - { offsetInCU: 0xDE3, offset: 0x779F6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AadEP13failureReasonSSSgvgTW', symObjAddr: 0x568, symBinAddr: 0x2D324, symSize: 0x4 }
  - { offsetInCU: 0xDFF, offset: 0x77A12, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AadEP18recoverySuggestionSSSgvgTW', symObjAddr: 0x56C, symBinAddr: 0x2D328, symSize: 0x4 }
  - { offsetInCU: 0xE1B, offset: 0x77A2E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A11BridgeErrorO10Foundation09LocalizedC0AadEP10helpAnchorSSSgvgTW', symObjAddr: 0x570, symBinAddr: 0x2D32C, symSize: 0x4 }
  - { offsetInCU: 0x6D, offset: 0x77C93, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC6bridgeAA0B8Protocol_pSgvg', symObjAddr: 0x0, symBinAddr: 0x2D648, symSize: 0x30 }
  - { offsetInCU: 0xD4, offset: 0x77CFA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC03webC0So05WKWebC0CSgvg', symObjAddr: 0x98, symBinAddr: 0x2D6E0, symSize: 0x50 }
  - { offsetInCU: 0xF3, offset: 0x77D19, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18isStatusBarVisibleSbvg', symObjAddr: 0xE8, symBinAddr: 0x2D730, symSize: 0x44 }
  - { offsetInCU: 0x112, offset: 0x77D38, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18isStatusBarVisibleSbvs', symObjAddr: 0x12C, symBinAddr: 0x2D774, symSize: 0x48 }
  - { offsetInCU: 0x137, offset: 0x77D5D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18isStatusBarVisibleSbvM', symObjAddr: 0x174, symBinAddr: 0x2D7BC, symSize: 0x44 }
  - { offsetInCU: 0x166, offset: 0x77D8C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC14statusBarStyleSo08UIStatusfG0VvM', symObjAddr: 0x1D0, symBinAddr: 0x2D818, symSize: 0x44 }
  - { offsetInCU: 0x195, offset: 0x77DBB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC14statusBarStyleSo08UIStatusfG0VvM.resume.0', symObjAddr: 0x214, symBinAddr: 0x2D85C, symSize: 0x4 }
  - { offsetInCU: 0x1C0, offset: 0x77DE6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18statusBarAnimationSo08UIStatusfG0VvM', symObjAddr: 0x2B4, symBinAddr: 0x2D8FC, symSize: 0x44 }
  - { offsetInCU: 0x20D, offset: 0x77E33, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvgTo', symObjAddr: 0x2F8, symBinAddr: 0x2D940, symSize: 0x68 }
  - { offsetInCU: 0x24A, offset: 0x77E70, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvg', symObjAddr: 0x360, symBinAddr: 0x2D9A8, symSize: 0x48 }
  - { offsetInCU: 0x293, offset: 0x77EB9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvsTo', symObjAddr: 0x3A8, symBinAddr: 0x2D9F0, symSize: 0x64 }
  - { offsetInCU: 0x2D8, offset: 0x77EFE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvs', symObjAddr: 0x40C, symBinAddr: 0x2DA54, symSize: 0x50 }
  - { offsetInCU: 0x301, offset: 0x77F27, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21supportedOrientationsSaySiGvM', symObjAddr: 0x45C, symBinAddr: 0x2DAA4, symSize: 0x44 }
  - { offsetInCU: 0x320, offset: 0x77F46, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11isNewBinarySbvg', symObjAddr: 0x4A0, symBinAddr: 0x2DAE8, symSize: 0x40 }
  - { offsetInCU: 0x34E, offset: 0x77F74, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11isNewBinarySbvgSbyXEfU_', symObjAddr: 0x4E0, symBinAddr: 0x2DB28, symSize: 0x4B4 }
  - { offsetInCU: 0x4DD, offset: 0x78103, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11isNewBinarySbvs', symObjAddr: 0x994, symBinAddr: 0x2DFDC, symSize: 0x10 }
  - { offsetInCU: 0x500, offset: 0x78126, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11isNewBinarySbvM', symObjAddr: 0x9A4, symBinAddr: 0x2DFEC, symSize: 0x38 }
  - { offsetInCU: 0x559, offset: 0x7817F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11isNewBinarySbvM.resume.0', symObjAddr: 0x9DC, symBinAddr: 0x2E024, symSize: 0x18 }
  - { offsetInCU: 0x5E0, offset: 0x78206, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC04loadC0yyF', symObjAddr: 0x9F4, symBinAddr: 0x2E03C, symSize: 0x3F4 }
  - { offsetInCU: 0x751, offset: 0x78377, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC04loadC0yyFTo', symObjAddr: 0x179C, symBinAddr: 0x2EDA4, symSize: 0x2C }
  - { offsetInCU: 0x76D, offset: 0x78393, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11viewDidLoadyyF', symObjAddr: 0x17C8, symBinAddr: 0x2EDD0, symSize: 0x34 }
  - { offsetInCU: 0x78A, offset: 0x783B0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC07loadWebC0yyF', symObjAddr: 0x17FC, symBinAddr: 0x2EE04, symSize: 0x454 }
  - { offsetInCU: 0xA17, offset: 0x7863D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11viewDidLoadyyFTo', symObjAddr: 0x1C50, symBinAddr: 0x2F258, symSize: 0x60 }
  - { offsetInCU: 0xA68, offset: 0x7868E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC13viewDidAppearyySbF', symObjAddr: 0x1CB0, symBinAddr: 0x2F2B8, symSize: 0xC0 }
  - { offsetInCU: 0xB01, offset: 0x78727, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC13viewDidAppearyySbFTo', symObjAddr: 0x1D70, symBinAddr: 0x2F378, symSize: 0x3C }
  - { offsetInCU: 0xB1D, offset: 0x78743, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC27canPerformUnwindSegueAction_4from10withSenderSb10ObjectiveC8SelectorV_So06UIViewD0CyptF', symObjAddr: 0x1DAC, symBinAddr: 0x2F3B4, symSize: 0x8 }
  - { offsetInCU: 0xB6C, offset: 0x78792, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC27canPerformUnwindSegueAction_4from10withSenderSb10ObjectiveC8SelectorV_So06UIViewD0CyptFTo', symObjAddr: 0x1DB4, symBinAddr: 0x2F3BC, symSize: 0x78 }
  - { offsetInCU: 0xB88, offset: 0x787AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC18instanceDescriptorSo011CAPInstanceF0CyF', symObjAddr: 0x1E2C, symBinAddr: 0x2F434, symSize: 0x364 }
  - { offsetInCU: 0xD5C, offset: 0x78982, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC6routerAA6Router_pyF', symObjAddr: 0x2190, symBinAddr: 0x2F798, symSize: 0x20 }
  - { offsetInCU: 0xD87, offset: 0x789AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC03webC13Configuration3forSo05WKWebcF0CSo011CAPInstanceF0C_tF', symObjAddr: 0x21B0, symBinAddr: 0x2F7B8, symSize: 0x360 }
  - { offsetInCU: 0xF77, offset: 0x78B9D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC03webC04with13configurationSo05WKWebC0CSo6CGRectV_So0hC13ConfigurationCtF', symObjAddr: 0x2510, symBinAddr: 0x2FB18, symSize: 0x64 }
  - { offsetInCU: 0xFEC, offset: 0x78C12, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC16capacitorDidLoadyyF', symObjAddr: 0x2574, symBinAddr: 0x2FB7C, symSize: 0x4 }
  - { offsetInCU: 0x106B, offset: 0x78C91, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC20setStatusBarDefaultsyyF', symObjAddr: 0x2578, symBinAddr: 0x2FB80, symSize: 0x338 }
  - { offsetInCU: 0x11BA, offset: 0x78DE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC28setScreenOrientationDefaultsyyF', symObjAddr: 0x28B0, symBinAddr: 0x2FEB8, symSize: 0x5BC }
  - { offsetInCU: 0x1904, offset: 0x7952A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC22prefersStatusBarHiddenSbvgTo', symObjAddr: 0x2E6C, symBinAddr: 0x30474, symSize: 0x4C }
  - { offsetInCU: 0x1961, offset: 0x79587, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC22prefersStatusBarHiddenSbvg', symObjAddr: 0x2EB8, symBinAddr: 0x304C0, symSize: 0x4C }
  - { offsetInCU: 0x199E, offset: 0x795C4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC19setStatusBarVisibleyySbF', symObjAddr: 0x2FB4, symBinAddr: 0x305BC, symSize: 0xF4 }
  - { offsetInCU: 0x19F7, offset: 0x7961D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setStatusBarStyleyySo08UIStatusgH0VF', symObjAddr: 0x30A8, symBinAddr: 0x306B0, symSize: 0xF4 }
  - { offsetInCU: 0x1AA0, offset: 0x796C6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC21setStatusBarAnimationyySo08UIStatusgH0VF', symObjAddr: 0x319C, symBinAddr: 0x307A4, symSize: 0x48 }
  - { offsetInCU: 0x1B07, offset: 0x7972D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC30supportedInterfaceOrientationsSo26UIInterfaceOrientationMaskVvgTo', symObjAddr: 0x31E4, symBinAddr: 0x307EC, symSize: 0x20 }
  - { offsetInCU: 0x1B23, offset: 0x79749, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC30supportedInterfaceOrientationsSo26UIInterfaceOrientationMaskVvg', symObjAddr: 0x3204, symBinAddr: 0x3080C, symSize: 0xA4 }
  - { offsetInCU: 0x1B8B, offset: 0x797B1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfC', symObjAddr: 0x33CC, symBinAddr: 0x309D4, symSize: 0x7C }
  - { offsetInCU: 0x1BA9, offset: 0x797CF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x3448, symBinAddr: 0x30A50, symSize: 0xEC }
  - { offsetInCU: 0x1BF4, offset: 0x7981A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x3534, symBinAddr: 0x30B3C, symSize: 0x60 }
  - { offsetInCU: 0x1C10, offset: 0x79836, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC5coderACSgSo7NSCoderC_tcfC', symObjAddr: 0x3594, symBinAddr: 0x30B9C, symSize: 0x44 }
  - { offsetInCU: 0x1C2E, offset: 0x79854, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x35D8, symBinAddr: 0x30BE0, symSize: 0xB4 }
  - { offsetInCU: 0x1C6B, offset: 0x79891, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x368C, symBinAddr: 0x30C94, symSize: 0x30 }
  - { offsetInCU: 0x1C87, offset: 0x798AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerCfD', symObjAddr: 0x36BC, symBinAddr: 0x30CC4, symSize: 0x30 }
  - { offsetInCU: 0x1CB5, offset: 0x798DB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC03webC0So05WKWebC0CSgvpACTk', symObjAddr: 0x30, symBinAddr: 0x2D678, symSize: 0x68 }
  - { offsetInCU: 0x1F31, offset: 0x79B57, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfcTf4nennnnn_nAA0nS10ControllerC_Tg5Tf4gggggnn_n', symObjAddr: 0x5484, symBinAddr: 0x32A8C, symSize: 0x770 }
  - { offsetInCU: 0x21B9, offset: 0x79DDF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC010prepareWebC033_0151BC8B8398CBA447E765B04F9447A0LL4with12assetHandler010delegationP0ySo24CAPInstanceConfigurationC_AA0fc5AssetP0CAA0fc10DelegationP0CtF', symObjAddr: 0xE28, symBinAddr: 0x2E430, symSize: 0x528 }
  - { offsetInCU: 0x22BB, offset: 0x79EE1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC19updateBinaryVersion33_0151BC8B8398CBA447E765B04F9447A0LLyyF', symObjAddr: 0x1350, symBinAddr: 0x2E958, symSize: 0x44C }
  - { offsetInCU: 0x26B3, offset: 0x7A2D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerCfETo', symObjAddr: 0x36EC, symBinAddr: 0x30CF4, symSize: 0x48 }
  - { offsetInCU: 0x26E2, offset: 0x7A308, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17getServerBasePathSSyF', symObjAddr: 0x3734, symBinAddr: 0x30D3C, symSize: 0xF4 }
  - { offsetInCU: 0x2750, offset: 0x7A376, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17getServerBasePathSSyFTo', symObjAddr: 0x3828, symBinAddr: 0x30E30, symSize: 0x5C }
  - { offsetInCU: 0x276C, offset: 0x7A392, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setServerBasePath4pathySS_tF', symObjAddr: 0x3884, symBinAddr: 0x30E8C, symSize: 0x274 }
  - { offsetInCU: 0x2810, offset: 0x7A436, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setServerBasePath4pathySS_tFyyScMYccfU_', symObjAddr: 0x3AF8, symBinAddr: 0x31100, symSize: 0x1C4 }
  - { offsetInCU: 0x288F, offset: 0x7A4B5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setServerBasePath4pathySS_tFTo', symObjAddr: 0x3CBC, symBinAddr: 0x312C4, symSize: 0x58 }
  - { offsetInCU: 0x28AB, offset: 0x7A4D1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC14printLoadError33_0151BC8B8398CBA447E765B04F9447A0LLyyF', symObjAddr: 0x3D14, symBinAddr: 0x3131C, symSize: 0x314 }
  - { offsetInCU: 0x2BAD, offset: 0x7A7D3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC010bridgedWebC0So05WKWebC0CSgvg', symObjAddr: 0x4028, symBinAddr: 0x31630, symSize: 0x50 }
  - { offsetInCU: 0x2BEB, offset: 0x7A811, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC07bridgedcD0So06UIViewD0CSgvg', symObjAddr: 0x4078, symBinAddr: 0x31680, symSize: 0x1C }
  - { offsetInCU: 0x2C28, offset: 0x7A84E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerCAA0B8DelegateA2aDP010bridgedWebC0So05WKWebC0CSgvgTW', symObjAddr: 0x4094, symBinAddr: 0x3169C, symSize: 0x50 }
  - { offsetInCU: 0x2CA3, offset: 0x7A8C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerCAA0B8DelegateA2aDP07bridgedcD0So06UIViewD0CSgvgTW', symObjAddr: 0x40E4, symBinAddr: 0x316EC, symSize: 0x1C }
  - { offsetInCU: 0x2D58, offset: 0x7A97E, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFs5UInt8V_Tg5', symObjAddr: 0x4100, symBinAddr: 0x31708, symSize: 0xE8 }
  - { offsetInCU: 0x2EF0, offset: 0x7AB16, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLO_Tg5', symObjAddr: 0x41E8, symBinAddr: 0x317F0, symSize: 0x104 }
  - { offsetInCU: 0x3078, offset: 0x7AC9E, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSS_Tg5', symObjAddr: 0x42EC, symBinAddr: 0x318F4, symSize: 0x104 }
  - { offsetInCU: 0x3200, offset: 0x7AE26, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9Capacitor18PluginHeaderMethodV_Tg5', symObjAddr: 0x43F0, symBinAddr: 0x319F8, symSize: 0x104 }
  - { offsetInCU: 0x3393, offset: 0x7AFB9, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFyXlXp_Tg5', symObjAddr: 0x44F4, symBinAddr: 0x31AFC, symSize: 0xFC }
  - { offsetInCU: 0x3536, offset: 0x7B15C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo8NSObject_p_Tg5', symObjAddr: 0x45F0, symBinAddr: 0x31BF8, symSize: 0x14C }
  - { offsetInCU: 0x3716, offset: 0x7B33C, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF10Foundation12URLQueryItemV_Tg5', symObjAddr: 0x4750, symBinAddr: 0x31D58, symSize: 0x174 }
  - { offsetInCU: 0x38C6, offset: 0x7B4EC, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSi_Tg5', symObjAddr: 0x48C4, symBinAddr: 0x31ECC, symSize: 0xFC }
  - { offsetInCU: 0x3A5E, offset: 0x7B684, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSs_Tg5', symObjAddr: 0x49C0, symBinAddr: 0x31FC8, symSize: 0x104 }
  - { offsetInCU: 0x3BFC, offset: 0x7B822, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSi6offset_Ss7elementt_Tg5', symObjAddr: 0x4AC4, symBinAddr: 0x320CC, symSize: 0x138 }
  - { offsetInCU: 0x3DB5, offset: 0x7B9DB, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtF9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLO_Tg5', symObjAddr: 0x4D44, symBinAddr: 0x3234C, symSize: 0x114 }
  - { offsetInCU: 0x3ECF, offset: 0x7BAF5, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo8NSObject_p_Tg5Tf4d_n', symObjAddr: 0x4E58, symBinAddr: 0x32460, symSize: 0xC }
  - { offsetInCU: 0x3EFD, offset: 0x7BB23, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo8NSObject_p_Tg5Tf4nnd_n', symObjAddr: 0x4E64, symBinAddr: 0x3246C, symSize: 0x80 }
  - { offsetInCU: 0x3F92, offset: 0x7BBB8, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo8NSObject_p_Tg5Tf4nng_n', symObjAddr: 0x4EE4, symBinAddr: 0x324EC, symSize: 0x118 }
  - { offsetInCU: 0x4085, offset: 0x7BCAB, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo12NSHTTPCookieC_Tg5Tf4d_n', symObjAddr: 0x4FFC, symBinAddr: 0x32604, symSize: 0x64 }
  - { offsetInCU: 0x40B2, offset: 0x7BCD8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC11logWarnings33_0151BC8B8398CBA447E765B04F9447A0LL3forySo21CAPInstanceDescriptorC_tFTf4nd_n', symObjAddr: 0x5060, symBinAddr: 0x32668, symSize: 0x424 }
  - { offsetInCU: 0x4488, offset: 0x7C0AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerCMa', symObjAddr: 0x5BF4, symBinAddr: 0x331FC, symSize: 0x20 }
  - { offsetInCU: 0x449C, offset: 0x7C0C2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5CD4, symBinAddr: 0x3327C, symSize: 0x10 }
  - { offsetInCU: 0x44B0, offset: 0x7C0D6, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5CE4, symBinAddr: 0x3328C, symSize: 0x8 }
  - { offsetInCU: 0x44C4, offset: 0x7C0EA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setStatusBarStyleyySo08UIStatusgH0VFyycfU_TA', symObjAddr: 0x5CEC, symBinAddr: 0x33294, symSize: 0x10 }
  - { offsetInCU: 0x44ED, offset: 0x7C113, size: 0x8, addend: 0x0, symName: '_$s9Capacitor23CAPBridgeViewControllerC17setServerBasePath4pathySS_tFyyScMYccfU_TA', symObjAddr: 0x5D28, symBinAddr: 0x332D0, symSize: 0x8 }
  - { offsetInCU: 0x4501, offset: 0x7C127, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6BridgeC4with8delegate20cordovaConfiguration12assetHandler010delegationH019autoRegisterPluginsACSo011CAPInstanceF0C_AA17CAPBridgeDelegate_pSo15CDVConfigParserCAA012WebViewAssetH0CAA0rs10DelegationH0CSbtcfcy10Foundation12NotificationVYbcfU_TA', symObjAddr: 0x5EA4, symBinAddr: 0x333D0, symSize: 0x8 }
  - { offsetInCU: 0x468F, offset: 0x7C2B5, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySiG_Tg5', symObjAddr: 0x32A8, symBinAddr: 0x308B0, symSize: 0x60 }
  - { offsetInCU: 0x46C7, offset: 0x7C2ED, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySiG_Tg5', symObjAddr: 0x32A8, symBinAddr: 0x308B0, symSize: 0x60 }
  - { offsetInCU: 0x46DB, offset: 0x7C301, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySiG_Tg5', symObjAddr: 0x32A8, symBinAddr: 0x308B0, symSize: 0x60 }
  - { offsetInCU: 0x46EF, offset: 0x7C315, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySiG_Tg5', symObjAddr: 0x32A8, symBinAddr: 0x308B0, symSize: 0x60 }
  - { offsetInCU: 0x4703, offset: 0x7C329, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySiG_Tg5', symObjAddr: 0x32A8, symBinAddr: 0x308B0, symSize: 0x60 }
  - { offsetInCU: 0x47CE, offset: 0x7C3F4, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE8containsySbABFSaySSG_Tg5', symObjAddr: 0x3308, symBinAddr: 0x30910, symSize: 0xC4 }
  - { offsetInCU: 0x27, offset: 0x7CB53, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerC15getPortablePath4host3uriSSSgSS_10Foundation3URLVSgtFZ', symObjAddr: 0x0, symBinAddr: 0x33440, symSize: 0x204 }
  - { offsetInCU: 0x4B, offset: 0x7CB77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerC15getPortablePath4host3uriSSSgSS_10Foundation3URLVSgtFZ', symObjAddr: 0x0, symBinAddr: 0x33440, symSize: 0x204 }
  - { offsetInCU: 0xF6, offset: 0x7CC22, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerCACycfC', symObjAddr: 0x310, symBinAddr: 0x33644, symSize: 0x20 }
  - { offsetInCU: 0x114, offset: 0x7CC40, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerCACycfc', symObjAddr: 0x330, symBinAddr: 0x33664, symSize: 0x30 }
  - { offsetInCU: 0x14F, offset: 0x7CC7B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerCACycfcTo', symObjAddr: 0x380, symBinAddr: 0x336B4, symSize: 0x3C }
  - { offsetInCU: 0x18A, offset: 0x7CCB6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerCfD', symObjAddr: 0x3BC, symBinAddr: 0x336F0, symSize: 0x30 }
  - { offsetInCU: 0x1B8, offset: 0x7CCE4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14CAPFileManagerCMa', symObjAddr: 0x360, symBinAddr: 0x33694, symSize: 0x20 }
  - { offsetInCU: 0x43, offset: 0x7CE92, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE12errorPathURL10Foundation0F0VSgvgTo', symObjAddr: 0x220, symBinAddr: 0x33940, symSize: 0xCC }
  - { offsetInCU: 0x5F, offset: 0x7CEAE, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE12errorPathURL10Foundation0F0VSgvg', symObjAddr: 0x2EC, symBinAddr: 0x33A0C, symSize: 0x144 }
  - { offsetInCU: 0x108, offset: 0x7CF57, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE20getPluginConfigValueyypSgSS_SStF', symObjAddr: 0x430, symBinAddr: 0x33B50, symSize: 0x19C }
  - { offsetInCU: 0x2B0, offset: 0x7D0FF, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE20getPluginConfigValueyypSgSS_SStFTo', symObjAddr: 0xE50, symBinAddr: 0x344B0, symSize: 0x124 }
  - { offsetInCU: 0x31D, offset: 0x7D16C, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE15getPluginConfigyAC0eF0CSSF', symObjAddr: 0xF74, symBinAddr: 0x345D4, symSize: 0x1AC }
  - { offsetInCU: 0x3F8, offset: 0x7D247, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE15getPluginConfigyAC0eF0CSSFTo', symObjAddr: 0x1120, symBinAddr: 0x34780, symSize: 0x64 }
  - { offsetInCU: 0x47B, offset: 0x7D2CA, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE21shouldAllowNavigation2toSbSS_tF', symObjAddr: 0x1184, symBinAddr: 0x347E4, symSize: 0x138 }
  - { offsetInCU: 0x5AB, offset: 0x7D3FA, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE21shouldAllowNavigation2toSbSS_tFTo', symObjAddr: 0x12BC, symBinAddr: 0x3491C, symSize: 0x64 }
  - { offsetInCU: 0x5C7, offset: 0x7D416, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE8getValueyypSgSSF', symObjAddr: 0x1320, symBinAddr: 0x34980, symSize: 0x150 }
  - { offsetInCU: 0x626, offset: 0x7D475, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE8getValueyypSgSSFTo', symObjAddr: 0x1470, symBinAddr: 0x34AD0, symSize: 0x100 }
  - { offsetInCU: 0x642, offset: 0x7D491, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE9getStringySSSgSSF', symObjAddr: 0x1570, symBinAddr: 0x34BD0, symSize: 0x150 }
  - { offsetInCU: 0x6A1, offset: 0x7D4F0, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE9getStringySSSgSSFTo', symObjAddr: 0x16C0, symBinAddr: 0x34D20, symSize: 0x8C }
  - { offsetInCU: 0x86A, offset: 0x7D6B9, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKFSS_Tg5', symObjAddr: 0x18B4, symBinAddr: 0x34F14, symSize: 0x418 }
  - { offsetInCU: 0xC1B, offset: 0x7DA6A, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyFSs_Tg5', symObjAddr: 0x1CCC, symBinAddr: 0x3532C, symSize: 0x14 }
  - { offsetInCU: 0xC53, offset: 0x7DAA2, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNewAByxGyFSi6offset_Ss7elementt_Tg5', symObjAddr: 0x1CE0, symBinAddr: 0x35340, symSize: 0x14 }
  - { offsetInCU: 0xC80, offset: 0x7DACF, size: 0x8, addend: 0x0, symName: '_$sSlsE5split9maxSplits25omittingEmptySubsequences14whereSeparatorSay11SubSequenceQzGSi_S2b7ElementQzKXEtKF17appendSubsequenceL_3endSb5IndexQz_tSlRzlFSS_Tg5', symObjAddr: 0x1CF4, symBinAddr: 0x35354, symSize: 0x10C }
  - { offsetInCU: 0xE73, offset: 0x7DCC2, size: 0x8, addend: 0x0, symName: '_$ss30_copySequenceToContiguousArrayys0dE0Vy7ElementQzGxSTRzlFs010EnumeratedB0VySaySsGG_Tg5', symObjAddr: 0x1E00, symBinAddr: 0x35460, symSize: 0x1C0 }
  - { offsetInCU: 0x1106, offset: 0x7DF55, size: 0x8, addend: 0x0, symName: '_$sSo24CAPInstanceConfigurationC9CapacitorE8doesHost33_B57F4D0DE97AE9C2AEF6A73FAB0F46C7LL_5matchSbSS_SStFTf4nnd_n', symObjAddr: 0x20CC, symBinAddr: 0x3572C, symSize: 0x3F4 }
  - { offsetInCU: 0x162C, offset: 0x7E47B, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFs11AnyHashableV_ypSS9Capacitor7JSValue_pTg5', symObjAddr: 0x5CC, symBinAddr: 0x33CEC, symSize: 0x420 }
  - { offsetInCU: 0x177F, offset: 0x7E5CE, size: 0x8, addend: 0x0, symName: '_$ss30_dictionaryDownCastConditionalySDyq0_q1_GSgSDyxq_GSHRzSHR0_r2_lFs11AnyHashableV_ypS2STg5', symObjAddr: 0xA30, symBinAddr: 0x3410C, symSize: 0x3A4 }
  - { offsetInCU: 0x192D, offset: 0x7E77C, size: 0x8, addend: 0x0, symName: '_$sSTsE8reversedSay7ElementQzGyFs18EnumeratedSequenceVySaySsGG_Tg5', symObjAddr: 0x174C, symBinAddr: 0x34DAC, symSize: 0x168 }
  - { offsetInCU: 0x1B0C, offset: 0x7E95B, size: 0x8, addend: 0x0, symName: '_$sSasSQRzlE2eeoiySbSayxG_ABtFZSs_Tg5Tf4nnd_n', symObjAddr: 0x1FC0, symBinAddr: 0x35620, symSize: 0x10C }
  - { offsetInCU: 0x4F, offset: 0x7ECF6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO6schemeSSvpZ', symObjAddr: 0xC8A8, symBinAddr: 0xA6180, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x7ED10, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO8hostnameSSvpZ', symObjAddr: 0xC8B8, symBinAddr: 0xA6190, symSize: 0x0 }
  - { offsetInCU: 0x82, offset: 0x7ED29, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO6schemeSSvau', symObjAddr: 0x7C, symBinAddr: 0x35CD4, symSize: 0x40 }
  - { offsetInCU: 0x96, offset: 0x7ED3D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO6scheme_WZ', symObjAddr: 0xBC, symBinAddr: 0x35D14, symSize: 0x28 }
  - { offsetInCU: 0xB0, offset: 0x7ED57, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO8hostname_WZ', symObjAddr: 0x104, symBinAddr: 0x35D5C, symSize: 0x28 }
  - { offsetInCU: 0xCA, offset: 0x7ED71, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsO8hostnameSSvau', symObjAddr: 0x12C, symBinAddr: 0x35D84, symSize: 0x40 }
  - { offsetInCU: 0x1AE, offset: 0x7EE55, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE19_parseConfiguration2at07cordovaE0y10Foundation3URLVSg_AJtF', symObjAddr: 0x1DC, symBinAddr: 0x35E34, symSize: 0x1DA4 }
  - { offsetInCU: 0xDC8, offset: 0x7FA6F, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE19_parseConfiguration2at07cordovaE0y10Foundation3URLVSg_AJtFTo', symObjAddr: 0x1F80, symBinAddr: 0x37BD8, symSize: 0x174 }
  - { offsetInCU: 0xDE4, offset: 0x7FA8B, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE21cordovaDeployDisabledSbvgTo', symObjAddr: 0x20F4, symBinAddr: 0x37D4C, symSize: 0x38 }
  - { offsetInCU: 0xE00, offset: 0x7FAA7, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE21cordovaDeployDisabledSbvg', symObjAddr: 0x212C, symBinAddr: 0x37D84, symSize: 0x19C }
  - { offsetInCU: 0xE44, offset: 0x7FAEB, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCMa', symObjAddr: 0x2308, symBinAddr: 0x37F20, symSize: 0x3C }
  - { offsetInCU: 0xE7A, offset: 0x7FB21, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE9normalizeyyF', symObjAddr: 0x2344, symBinAddr: 0x37F5C, symSize: 0x700 }
  - { offsetInCU: 0xF79, offset: 0x7FC20, size: 0x8, addend: 0x0, symName: '_$sSo21CAPInstanceDescriptorC9CapacitorE9normalizeyyFTo', symObjAddr: 0x2A44, symBinAddr: 0x3865C, symSize: 0x2C }
  - { offsetInCU: 0xF95, offset: 0x7FC3C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor26InstanceDescriptorDefaultsOMa', symObjAddr: 0x2B34, symBinAddr: 0x38708, symSize: 0x10 }
  - { offsetInCU: 0xFB4, offset: 0x7FC5B, size: 0x8, addend: 0x0, symName: '_$sSo26CAPInstanceLoggingBehaviorV9CapacitorE8behavior33_8A70532DEC43102D9B8E29134CB87F82LL4fromABSgSS_tFZTf4nd_n', symObjAddr: 0x2B8C, symBinAddr: 0x38718, symSize: 0x158 }
  - { offsetInCU: 0x1051, offset: 0x7FCF8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo7UIColorCRbzlE5color7fromHexAESgSS_tFZAE_Tg5Tf4nd_n', symObjAddr: 0x2CE4, symBinAddr: 0x38870, symSize: 0x2B0 }
  - { offsetInCU: 0x1175, offset: 0x7FE1C, size: 0x8, addend: 0x0, symName: '_$sSDyq_SgxcigSS_So42UIScrollViewContentInsetAdjustmentBehaviorVTg5', symObjAddr: 0x0, symBinAddr: 0x35C58, symSize: 0x7C }
  - { offsetInCU: 0x27, offset: 0x80262, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x0, symBinAddr: 0x38B20, symSize: 0xB4 }
  - { offsetInCU: 0x4B, offset: 0x80286, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x0, symBinAddr: 0x38B20, symSize: 0xB4 }
  - { offsetInCU: 0x69, offset: 0x802A4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0xB4, symBinAddr: 0x38BD4, symSize: 0xB4 }
  - { offsetInCU: 0xE2, offset: 0x8031D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x188, symBinAddr: 0x38CA8, symSize: 0xD8 }
  - { offsetInCU: 0x139, offset: 0x80374, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginCACycfC', symObjAddr: 0x260, symBinAddr: 0x38D80, symSize: 0x20 }
  - { offsetInCU: 0x157, offset: 0x80392, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginCACycfc', symObjAddr: 0x280, symBinAddr: 0x38DA0, symSize: 0x30 }
  - { offsetInCU: 0x192, offset: 0x803CD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginCACycfcTo', symObjAddr: 0x2B0, symBinAddr: 0x38DD0, symSize: 0x3C }
  - { offsetInCU: 0x1CD, offset: 0x80408, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginCfD', symObjAddr: 0x2EC, symBinAddr: 0x38E0C, symSize: 0x30 }
  - { offsetInCU: 0x1FB, offset: 0x80436, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17CAPInstancePluginCMa', symObjAddr: 0x168, symBinAddr: 0x38C88, symSize: 0x20 }
  - { offsetInCU: 0x27, offset: 0x80568, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC5print_9separator10terminatoryypd_S2StFZ', symObjAddr: 0x0, symBinAddr: 0x38E3C, symSize: 0x4 }
  - { offsetInCU: 0x4B, offset: 0x8058C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvpZ', symObjAddr: 0x500, symBinAddr: 0xA1898, symSize: 0x0 }
  - { offsetInCU: 0x65, offset: 0x805A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC5print_9separator10terminatoryypd_S2StFZ', symObjAddr: 0x0, symBinAddr: 0x38E3C, symSize: 0x4 }
  - { offsetInCU: 0x81, offset: 0x805C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvgZ', symObjAddr: 0x10, symBinAddr: 0x38E4C, symSize: 0x40 }
  - { offsetInCU: 0x9D, offset: 0x805DE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvsZ', symObjAddr: 0x50, symBinAddr: 0x38E8C, symSize: 0x44 }
  - { offsetInCU: 0xB9, offset: 0x805FA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvMZ', symObjAddr: 0x94, symBinAddr: 0x38ED0, symSize: 0x40 }
  - { offsetInCU: 0xD5, offset: 0x80616, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvMZ.resume.0', symObjAddr: 0xD4, symBinAddr: 0x38F10, symSize: 0x4 }
  - { offsetInCU: 0xF1, offset: 0x80632, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogCfd', symObjAddr: 0xD8, symBinAddr: 0x38F14, symSize: 0x8 }
  - { offsetInCU: 0x120, offset: 0x80661, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogCfD', symObjAddr: 0xE0, symBinAddr: 0x38F1C, symSize: 0x10 }
  - { offsetInCU: 0x14F, offset: 0x80690, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC5print_9separator10terminatoryypd_S2StFZTf4nnnd_n', symObjAddr: 0xF0, symBinAddr: 0x38F2C, symSize: 0x2C0 }
  - { offsetInCU: 0x371, offset: 0x808B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogC13enableLoggingSbvau', symObjAddr: 0x4, symBinAddr: 0x38E40, symSize: 0xC }
  - { offsetInCU: 0x44A, offset: 0x8098B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6CAPLogCMa', symObjAddr: 0x3B0, symBinAddr: 0x391EC, symSize: 0x20 }
  - { offsetInCU: 0x45E, offset: 0x8099F, size: 0x8, addend: 0x0, symName: '_$sSi6offset_yp7elementtSgWOb', symObjAddr: 0x41C, symBinAddr: 0x3920C, symSize: 0x48 }
  - { offsetInCU: 0x4B, offset: 0x80BC2, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE16capacitorOpenURLABvpZ', symObjAddr: 0x5C40, symBinAddr: 0xA61A0, symSize: 0x0 }
  - { offsetInCU: 0x65, offset: 0x80BDC, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE26capacitorOpenUniversalLinkABvpZ', symObjAddr: 0x5C48, symBinAddr: 0xA61A8, symSize: 0x0 }
  - { offsetInCU: 0x7F, offset: 0x80BF6, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE25capacitorContinueActivityABvpZ', symObjAddr: 0x5C50, symBinAddr: 0xA61B0, symSize: 0x0 }
  - { offsetInCU: 0x99, offset: 0x80C10, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE42capacitorDidRegisterForRemoteNotificationsABvpZ', symObjAddr: 0x5C58, symBinAddr: 0xA61B8, symSize: 0x0 }
  - { offsetInCU: 0xB3, offset: 0x80C2A, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE48capacitorDidFailToRegisterForRemoteNotificationsABvpZ', symObjAddr: 0x5C60, symBinAddr: 0xA61C0, symSize: 0x0 }
  - { offsetInCU: 0xCD, offset: 0x80C44, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE40capacitorDecidePolicyForNavigationActionABvpZ', symObjAddr: 0x5C68, symBinAddr: 0xA61C8, symSize: 0x0 }
  - { offsetInCU: 0xE7, offset: 0x80C5E, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE24capacitorStatusBarTappedABvpZ', symObjAddr: 0x5C70, symBinAddr: 0xA61D0, symSize: 0x0 }
  - { offsetInCU: 0x101, offset: 0x80C78, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE16capacitorOpenURLSo0A4NameavpZ', symObjAddr: 0x5C78, symBinAddr: 0xA61D8, symSize: 0x0 }
  - { offsetInCU: 0x11B, offset: 0x80C92, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE26capacitorOpenUniversalLinkSo0A4NameavpZ', symObjAddr: 0x5C80, symBinAddr: 0xA61E0, symSize: 0x0 }
  - { offsetInCU: 0x135, offset: 0x80CAC, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE25capacitorContinueActivitySo0A4NameavpZ', symObjAddr: 0x5C88, symBinAddr: 0xA61E8, symSize: 0x0 }
  - { offsetInCU: 0x14F, offset: 0x80CC6, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE42capacitorDidRegisterForRemoteNotificationsSo0A4NameavpZ', symObjAddr: 0x5C90, symBinAddr: 0xA61F0, symSize: 0x0 }
  - { offsetInCU: 0x169, offset: 0x80CE0, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE48capacitorDidFailToRegisterForRemoteNotificationsSo0A4NameavpZ', symObjAddr: 0x5C98, symBinAddr: 0xA61F8, symSize: 0x0 }
  - { offsetInCU: 0x183, offset: 0x80CFA, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE40capacitorDecidePolicyForNavigationActionSo0A4NameavpZ', symObjAddr: 0x5CA0, symBinAddr: 0xA6200, symSize: 0x0 }
  - { offsetInCU: 0x19D, offset: 0x80D14, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE24capacitorStatusBarTappedSo0A4NameavpZ', symObjAddr: 0x5CA8, symBinAddr: 0xA6208, symSize: 0x0 }
  - { offsetInCU: 0x1AB, offset: 0x80D22, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE16capacitorOpenURLABvau', symObjAddr: 0x0, symBinAddr: 0x39278, symSize: 0x40 }
  - { offsetInCU: 0x1BF, offset: 0x80D36, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE26capacitorOpenUniversalLinkABvau', symObjAddr: 0x40, symBinAddr: 0x392B8, symSize: 0x40 }
  - { offsetInCU: 0x1D3, offset: 0x80D4A, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE24capacitorStatusBarTappedABvau', symObjAddr: 0x80, symBinAddr: 0x392F8, symSize: 0x40 }
  - { offsetInCU: 0x1F1, offset: 0x80D68, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE16capacitorOpenURL_WZ', symObjAddr: 0xC0, symBinAddr: 0x39338, symSize: 0x34 }
  - { offsetInCU: 0x20B, offset: 0x80D82, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE26capacitorOpenUniversalLink_WZ', symObjAddr: 0x110, symBinAddr: 0x39388, symSize: 0x34 }
  - { offsetInCU: 0x225, offset: 0x80D9C, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE25capacitorContinueActivity_WZ', symObjAddr: 0x160, symBinAddr: 0x393D8, symSize: 0x34 }
  - { offsetInCU: 0x23F, offset: 0x80DB6, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE25capacitorContinueActivityABvau', symObjAddr: 0x194, symBinAddr: 0x3940C, symSize: 0x40 }
  - { offsetInCU: 0x253, offset: 0x80DCA, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE42capacitorDidRegisterForRemoteNotifications_WZ', symObjAddr: 0x1F0, symBinAddr: 0x39468, symSize: 0x34 }
  - { offsetInCU: 0x26D, offset: 0x80DE4, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE42capacitorDidRegisterForRemoteNotificationsABvau', symObjAddr: 0x224, symBinAddr: 0x3949C, symSize: 0x40 }
  - { offsetInCU: 0x281, offset: 0x80DF8, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE48capacitorDidFailToRegisterForRemoteNotifications_WZ', symObjAddr: 0x280, symBinAddr: 0x394F8, symSize: 0x34 }
  - { offsetInCU: 0x29B, offset: 0x80E12, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE48capacitorDidFailToRegisterForRemoteNotificationsABvau', symObjAddr: 0x2B4, symBinAddr: 0x3952C, symSize: 0x40 }
  - { offsetInCU: 0x2AF, offset: 0x80E26, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE40capacitorDecidePolicyForNavigationAction_WZ', symObjAddr: 0x310, symBinAddr: 0x39588, symSize: 0x34 }
  - { offsetInCU: 0x2C9, offset: 0x80E40, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE40capacitorDecidePolicyForNavigationActionABvau', symObjAddr: 0x344, symBinAddr: 0x395BC, symSize: 0x40 }
  - { offsetInCU: 0x2DD, offset: 0x80E54, size: 0x8, addend: 0x0, symName: '_$sSo18NSNotificationNamea9CapacitorE24capacitorStatusBarTapped_WZ', symObjAddr: 0x3A0, symBinAddr: 0x39618, symSize: 0x34 }
  - { offsetInCU: 0x2F7, offset: 0x80E6E, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE16capacitorOpenURLSo0A4Nameavau', symObjAddr: 0x44C, symBinAddr: 0x396C4, symSize: 0x40 }
  - { offsetInCU: 0x315, offset: 0x80E8C, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE26capacitorOpenUniversalLinkSo0A4Nameavau', symObjAddr: 0x4E8, symBinAddr: 0x39760, symSize: 0x40 }
  - { offsetInCU: 0x333, offset: 0x80EAA, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE25capacitorContinueActivitySo0A4Nameavau', symObjAddr: 0x584, symBinAddr: 0x397FC, symSize: 0x40 }
  - { offsetInCU: 0x351, offset: 0x80EC8, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE42capacitorDidRegisterForRemoteNotificationsSo0A4Nameavau', symObjAddr: 0x620, symBinAddr: 0x39898, symSize: 0x40 }
  - { offsetInCU: 0x36F, offset: 0x80EE6, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE48capacitorDidFailToRegisterForRemoteNotificationsSo0A4Nameavau', symObjAddr: 0x6BC, symBinAddr: 0x39934, symSize: 0x40 }
  - { offsetInCU: 0x38D, offset: 0x80F04, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE40capacitorDecidePolicyForNavigationActionSo0A4Nameavau', symObjAddr: 0x758, symBinAddr: 0x399D0, symSize: 0x40 }
  - { offsetInCU: 0x3AB, offset: 0x80F22, size: 0x8, addend: 0x0, symName: '_$sSo14NSNotificationC9CapacitorE24capacitorStatusBarTappedSo0A4Nameavau', symObjAddr: 0x838, symBinAddr: 0x39AB0, symSize: 0x40 }
  - { offsetInCU: 0x43B, offset: 0x80FB2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsO4nameSSyF', symObjAddr: 0x924, symBinAddr: 0x39B9C, symSize: 0x194 }
  - { offsetInCU: 0x4B8, offset: 0x8102F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsO8rawValueACSgSi_tcfC', symObjAddr: 0xAB8, symBinAddr: 0x39D30, symSize: 0x14 }
  - { offsetInCU: 0x4D7, offset: 0x8104E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsO8rawValueSivg', symObjAddr: 0xACC, symBinAddr: 0x39D44, symSize: 0x4 }
  - { offsetInCU: 0x51A, offset: 0x81091, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSYAASY8rawValuexSg03RawD0Qz_tcfCTW', symObjAddr: 0xBD8, symBinAddr: 0x39E50, symSize: 0x20 }
  - { offsetInCU: 0x54B, offset: 0x810C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSYAASY8rawValue03RawD0QzvgTW', symObjAddr: 0xBF8, symBinAddr: 0x39E70, symSize: 0xC }
  - { offsetInCU: 0x573, offset: 0x810EA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSHAASQWb', symObjAddr: 0xAE4, symBinAddr: 0x39D5C, symSize: 0x4 }
  - { offsetInCU: 0x587, offset: 0x810FE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOACSQAAWl', symObjAddr: 0xAE8, symBinAddr: 0x39D60, symSize: 0x44 }
  - { offsetInCU: 0x5B1, offset: 0x81128, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOMa', symObjAddr: 0xC04, symBinAddr: 0x39E7C, symSize: 0x10 }
  - { offsetInCU: 0x630, offset: 0x811A7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0xAD0, symBinAddr: 0x39D48, symSize: 0x14 }
  - { offsetInCU: 0x6C1, offset: 0x81238, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSHAASH9hashValueSivgTW', symObjAddr: 0xB2C, symBinAddr: 0x39DA4, symSize: 0x44 }
  - { offsetInCU: 0x770, offset: 0x812E7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xB70, symBinAddr: 0x39DE8, symSize: 0x28 }
  - { offsetInCU: 0x7C3, offset: 0x8133A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPNotificationsOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xB98, symBinAddr: 0x39E10, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x814BC, size: 0x8, addend: 0x0, symName: '_$sSo16CAPBridgedPluginP9CapacitorSo9CAPPluginCRbzrlE4load2onyAC17CAPBridgeProtocol_p_tF', symObjAddr: 0x0, symBinAddr: 0x39E8C, symSize: 0x1A8 }
  - { offsetInCU: 0x3F, offset: 0x814D4, size: 0x8, addend: 0x0, symName: '_$sSo16CAPBridgedPluginP9CapacitorSo9CAPPluginCRbzrlE4load2onyAC17CAPBridgeProtocol_p_tF', symObjAddr: 0x0, symBinAddr: 0x39E8C, symSize: 0x1A8 }
  - { offsetInCU: 0x8C, offset: 0x81521, size: 0x8, addend: 0x0, symName: '_$sSo19NSMutableDictionaryCMa', symObjAddr: 0x1A8, symBinAddr: 0x3A034, symSize: 0x3C }
  - { offsetInCU: 0x4B, offset: 0x8168D, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvpZ', symObjAddr: 0x7C28, symBinAddr: 0xA6210, symSize: 0x0 }
  - { offsetInCU: 0x59, offset: 0x8169B, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE22jsObjectRepresentationSDySSAC7JSValue_pGvg', symObjAddr: 0x0, symBinAddr: 0x3A070, symSize: 0x94 }
  - { offsetInCU: 0xBC, offset: 0x816FE, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSValueContainerA2cDP15jsDateFormatterSo09NSISO8601gH0CvgZTW', symObjAddr: 0x94, symBinAddr: 0x3A104, symSize: 0x68 }
  - { offsetInCU: 0xFB, offset: 0x8173D, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvgZ', symObjAddr: 0xFC, symBinAddr: 0x3A16C, symSize: 0x68 }
  - { offsetInCU: 0x140, offset: 0x81782, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSValueContainerA2cDP22jsObjectRepresentationSDySSAC0D0_pGvgTW', symObjAddr: 0x164, symBinAddr: 0x3A1D4, symSize: 0x94 }
  - { offsetInCU: 0x19E, offset: 0x817E0, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE24dictionaryRepresentationSo12NSDictionaryCvgTo', symObjAddr: 0x1F8, symBinAddr: 0x3A268, symSize: 0x4C }
  - { offsetInCU: 0x1DE, offset: 0x81820, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE24dictionaryRepresentationSo12NSDictionaryCvg', symObjAddr: 0x244, symBinAddr: 0x3A2B4, symSize: 0x30 }
  - { offsetInCU: 0x21F, offset: 0x81861, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatter_WZ', symObjAddr: 0x274, symBinAddr: 0x3A2E4, symSize: 0x30 }
  - { offsetInCU: 0x27A, offset: 0x818BC, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0Cvau', symObjAddr: 0x2A4, symBinAddr: 0x3A314, symSize: 0x40 }
  - { offsetInCU: 0x28E, offset: 0x818D0, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvgZTo', symObjAddr: 0x2E4, symBinAddr: 0x3A354, symSize: 0x6C }
  - { offsetInCU: 0x2C5, offset: 0x81907, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvsZ', symObjAddr: 0x350, symBinAddr: 0x3A3C0, symSize: 0x74 }
  - { offsetInCU: 0x320, offset: 0x81962, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvsZTo', symObjAddr: 0x3C4, symBinAddr: 0x3A434, symSize: 0x7C }
  - { offsetInCU: 0x361, offset: 0x819A3, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvMZ', symObjAddr: 0x440, symBinAddr: 0x3A4B0, symSize: 0x6C }
  - { offsetInCU: 0x398, offset: 0x819DA, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE15jsDateFormatterSo09NSISO8601eF0CvMZ.resume.0', symObjAddr: 0x4AC, symBinAddr: 0x3A51C, symSize: 0x4 }
  - { offsetInCU: 0x3CE, offset: 0x81A10, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE9hasOptionySbSSF', symObjAddr: 0x4B0, symBinAddr: 0x3A520, symSize: 0x170 }
  - { offsetInCU: 0x46F, offset: 0x81AB1, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE9hasOptionySbSSFTo', symObjAddr: 0x620, symBinAddr: 0x3A690, symSize: 0x64 }
  - { offsetInCU: 0x4F9, offset: 0x81B3B, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE7resolveyyF', symObjAddr: 0x684, symBinAddr: 0x3A6F4, symSize: 0xA8 }
  - { offsetInCU: 0x596, offset: 0x81BD8, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE7resolveyyFTo', symObjAddr: 0x72C, symBinAddr: 0x3A79C, symSize: 0xB4 }
  - { offsetInCU: 0x621, offset: 0x81C63, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE7resolveyySDySSypGF', symObjAddr: 0x7E0, symBinAddr: 0x3A850, symSize: 0xB8 }
  - { offsetInCU: 0x6DB, offset: 0x81D1D, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE7resolveyySDySSypGFTo', symObjAddr: 0x898, symBinAddr: 0x3A908, symSize: 0xFC }
  - { offsetInCU: 0x7AA, offset: 0x81DEC, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE6rejectyySS_SSSgs5Error_pSgSDySSypGSgtF', symObjAddr: 0x994, symBinAddr: 0x3AA04, symSize: 0x100 }
  - { offsetInCU: 0x88C, offset: 0x81ECE, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE6rejectyySS_SSSgs5Error_pSgSDySSypGSgtFTo', symObjAddr: 0xA94, symBinAddr: 0x3AB04, symSize: 0x19C }
  - { offsetInCU: 0x936, offset: 0x81F78, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE7resolve4with7encoder28messageForRejectionFromErroryx_AC14JSValueEncoderCSSs0K0_pXEtSERzlF', symObjAddr: 0xFE0, symBinAddr: 0x3B050, symSize: 0x124 }
  - { offsetInCU: 0x9F4, offset: 0x82036, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9CapacitorE6decode_7decoderxxm_AC14JSValueDecoderCtKSeRzlF', symObjAddr: 0x1104, symBinAddr: 0x3B174, symSize: 0x118 }
  - { offsetInCU: 0xA6C, offset: 0x820AE, size: 0x8, addend: 0x0, symName: '_$sypSgWOh', symObjAddr: 0x1250, symBinAddr: 0x3B28C, symSize: 0x40 }
  - { offsetInCU: 0xA80, offset: 0x820C2, size: 0x8, addend: 0x0, symName: '_$sSo6NSNullCMa', symObjAddr: 0x12E0, symBinAddr: 0x3B2CC, symSize: 0x3C }
  - { offsetInCU: 0x27, offset: 0x823F9, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeO8rawValueAESgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x3B308, symSize: 0x5C }
  - { offsetInCU: 0x74, offset: 0x82446, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeO8rawValueAESgSS_tcfC', symObjAddr: 0x0, symBinAddr: 0x3B308, symSize: 0x5C }
  - { offsetInCU: 0xBD, offset: 0x8248F, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeO8rawValueSSvg', symObjAddr: 0x9C, symBinAddr: 0x3B364, symSize: 0x20 }
  - { offsetInCU: 0xEB, offset: 0x824BD, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSYACSY8rawValuexSg03RawG0Qz_tcfCTW', symObjAddr: 0xE0, symBinAddr: 0x3B3A8, symSize: 0x64 }
  - { offsetInCU: 0x151, offset: 0x82523, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSYACSY8rawValue03RawG0QzvgTW', symObjAddr: 0x144, symBinAddr: 0x3B40C, symSize: 0x28 }
  - { offsetInCU: 0x188, offset: 0x8255A, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE_10returnTypeAB10ObjectiveC8SelectorV_AbCE06ReturnE0OtcfC', symObjAddr: 0x16C, symBinAddr: 0x3B434, symSize: 0x98 }
  - { offsetInCU: 0x20A, offset: 0x825DC, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSHACSQWb', symObjAddr: 0x204, symBinAddr: 0x3B4CC, symSize: 0x4 }
  - { offsetInCU: 0x21E, offset: 0x825F0, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOAESQACWl', symObjAddr: 0x208, symBinAddr: 0x3B4D0, symSize: 0x44 }
  - { offsetInCU: 0x232, offset: 0x82604, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x24C, symBinAddr: 0x3B514, symSize: 0xC }
  - { offsetInCU: 0x246, offset: 0x82618, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOwet', symObjAddr: 0x25C, symBinAddr: 0x3B520, symSize: 0x90 }
  - { offsetInCU: 0x25A, offset: 0x8262C, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOwst', symObjAddr: 0x2EC, symBinAddr: 0x3B5B0, symSize: 0xBC }
  - { offsetInCU: 0x26E, offset: 0x82640, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOwug', symObjAddr: 0x3A8, symBinAddr: 0x3B66C, symSize: 0x8 }
  - { offsetInCU: 0x282, offset: 0x82654, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOwup', symObjAddr: 0x3B0, symBinAddr: 0x3B674, symSize: 0x4 }
  - { offsetInCU: 0x296, offset: 0x82668, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOwui', symObjAddr: 0x3B4, symBinAddr: 0x3B678, symSize: 0x8 }
  - { offsetInCU: 0x2AA, offset: 0x8267C, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOMa', symObjAddr: 0x3BC, symBinAddr: 0x3B680, symSize: 0x10 }
  - { offsetInCU: 0x2D8, offset: 0x826AA, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSQACSQ2eeoiySbx_xtFZTW', symObjAddr: 0xBC, symBinAddr: 0x3B384, symSize: 0xC }
  - { offsetInCU: 0x2F4, offset: 0x826C6, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSHACSH9hashValueSivgTW', symObjAddr: 0xC8, symBinAddr: 0x3B390, symSize: 0x8 }
  - { offsetInCU: 0x310, offset: 0x826E2, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSHACSH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xD0, symBinAddr: 0x3B398, symSize: 0x8 }
  - { offsetInCU: 0x32C, offset: 0x826FE, size: 0x8, addend: 0x0, symName: '_$sSo15CAPPluginMethodC9CapacitorE10ReturnTypeOSHACSH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0xD8, symBinAddr: 0x3B3A0, symSize: 0x8 }
  - { offsetInCU: 0x27, offset: 0x82855, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC3logyySo13CAPPluginCallCF', symObjAddr: 0x0, symBinAddr: 0x3B690, symSize: 0x4 }
  - { offsetInCU: 0x4B, offset: 0x82879, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC3logyySo13CAPPluginCallCF', symObjAddr: 0x0, symBinAddr: 0x3B690, symSize: 0x4 }
  - { offsetInCU: 0x6E, offset: 0x8289C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC3logyySo13CAPPluginCallCFTo', symObjAddr: 0x4, symBinAddr: 0x3B694, symSize: 0x4C }
  - { offsetInCU: 0xA0, offset: 0x828CE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x50, symBinAddr: 0x3B6E0, symSize: 0xB4 }
  - { offsetInCU: 0xBE, offset: 0x828EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x104, symBinAddr: 0x3B794, symSize: 0xB4 }
  - { offsetInCU: 0x137, offset: 0x82965, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC6bridge8pluginId0E4NameAcA17CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x1B8, symBinAddr: 0x3B848, symSize: 0xD8 }
  - { offsetInCU: 0x18E, offset: 0x829BC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginCACycfC', symObjAddr: 0x290, symBinAddr: 0x3B920, symSize: 0x20 }
  - { offsetInCU: 0x1AC, offset: 0x829DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginCACycfc', symObjAddr: 0x2B0, symBinAddr: 0x3B940, symSize: 0x30 }
  - { offsetInCU: 0x1E7, offset: 0x82A15, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginCACycfcTo', symObjAddr: 0x2E0, symBinAddr: 0x3B970, symSize: 0x3C }
  - { offsetInCU: 0x222, offset: 0x82A50, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginCfD', symObjAddr: 0x31C, symBinAddr: 0x3B9AC, symSize: 0x30 }
  - { offsetInCU: 0x24F, offset: 0x82A7D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginC3logyySo13CAPPluginCallCFTf4nd_n', symObjAddr: 0x34C, symBinAddr: 0x3B9DC, symSize: 0x188 }
  - { offsetInCU: 0x4FE, offset: 0x82D2C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPConsolePluginCMa', symObjAddr: 0x4D4, symBinAddr: 0x3BB64, symSize: 0x20 }
  - { offsetInCU: 0x2B, offset: 0x82EBB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAA10Foundation4DataVRszlE4data015base64EncodedOrF3UrlAFSgSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x3BB84, symSize: 0x27C }
  - { offsetInCU: 0x6D, offset: 0x82EFD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAA10Foundation4DataVRszlE4data015base64EncodedOrF3UrlAFSgSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x3BB84, symSize: 0x27C }
  - { offsetInCU: 0x171, offset: 0x83001, size: 0x8, addend: 0x0, symName: '_$sSo6NSDataC10contentsOf7optionsAB10Foundation3URLV_So0A14ReadingOptionsVtKcfcTO', symObjAddr: 0x498, symBinAddr: 0x3BF58, symSize: 0x120 }
  - { offsetInCU: 0x1C5, offset: 0x83055, size: 0x8, addend: 0x0, symName: '_$sSTsSQ7ElementRpzrlE6starts4withSbqd___tSTRd__AAQyd__ABRSlFSS_SSTg5', symObjAddr: 0x2BC, symBinAddr: 0x3BE00, symSize: 0x158 }
  - { offsetInCU: 0x4F, offset: 0x8326F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO7defaultACvpZ', symObjAddr: 0x1DC78, symBinAddr: 0xA6218, symSize: 0x0 }
  - { offsetInCU: 0x92, offset: 0x832B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO7defaultACvgZ', symObjAddr: 0x2BD8, symBinAddr: 0x3EC50, symSize: 0x40 }
  - { offsetInCU: 0xF3, offset: 0x83313, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO6stringACSSSg_tcfC', symObjAddr: 0x2C18, symBinAddr: 0x3EC90, symSize: 0xEC }
  - { offsetInCU: 0x1DC, offset: 0x833FC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO8rawValueACSgSS_tcfC', symObjAddr: 0x2D04, symBinAddr: 0x3ED7C, symSize: 0x5C }
  - { offsetInCU: 0x225, offset: 0x83445, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO8rawValueSSvg', symObjAddr: 0x2D60, symBinAddr: 0x3EDD8, symSize: 0x20 }
  - { offsetInCU: 0x244, offset: 0x83464, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSYAASY8rawValuexSg03RawE0Qz_tcfCTW', symObjAddr: 0x2DA4, symBinAddr: 0x3EE1C, symSize: 0x64 }
  - { offsetInCU: 0x2AA, offset: 0x834CA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSYAASY8rawValue03RawE0QzvgTW', symObjAddr: 0x2E08, symBinAddr: 0x3EE80, symSize: 0x28 }
  - { offsetInCU: 0x5A1, offset: 0x837C1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE9getStringySSSgSSFSo13CAPPluginCallC_Tg5', symObjAddr: 0x1760, symBinAddr: 0x3D7D8, symSize: 0x18C }
  - { offsetInCU: 0x732, offset: 0x83952, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC3url10Foundation3URLVSgvg', symObjAddr: 0x2E30, symBinAddr: 0x3EEA8, symSize: 0x58 }
  - { offsetInCU: 0x74E, offset: 0x8396E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC3url10Foundation3URLVSgvs', symObjAddr: 0x2E88, symBinAddr: 0x3EF00, symSize: 0x68 }
  - { offsetInCU: 0x76A, offset: 0x8398A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC3url10Foundation3URLVSgvM', symObjAddr: 0x2EF0, symBinAddr: 0x3EF68, symSize: 0x44 }
  - { offsetInCU: 0x786, offset: 0x839A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC3url10Foundation3URLVSgvM.resume.0', symObjAddr: 0x2F34, symBinAddr: 0x3EFAC, symSize: 0x4 }
  - { offsetInCU: 0x7A2, offset: 0x839C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6methodSSSgvg', symObjAddr: 0x2F38, symBinAddr: 0x3EFB0, symSize: 0x54 }
  - { offsetInCU: 0x7BE, offset: 0x839DE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6methodSSSgvs', symObjAddr: 0x2F8C, symBinAddr: 0x3F004, symSize: 0x5C }
  - { offsetInCU: 0x7DA, offset: 0x839FA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6methodSSSgvM', symObjAddr: 0x2FE8, symBinAddr: 0x3F060, symSize: 0x44 }
  - { offsetInCU: 0x7F6, offset: 0x83A16, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6paramsSDyS2SGSgvg', symObjAddr: 0x302C, symBinAddr: 0x3F0A4, symSize: 0x48 }
  - { offsetInCU: 0x812, offset: 0x83A32, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6paramsSDyS2SGSgvs', symObjAddr: 0x3074, symBinAddr: 0x3F0EC, symSize: 0x50 }
  - { offsetInCU: 0x82E, offset: 0x83A4E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6paramsSDyS2SGSgvM', symObjAddr: 0x30C4, symBinAddr: 0x3F13C, symSize: 0x44 }
  - { offsetInCU: 0x84A, offset: 0x83A6A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC7requestAA0a3UrlC0CSgvg', symObjAddr: 0x3108, symBinAddr: 0x3F180, symSize: 0x50 }
  - { offsetInCU: 0x866, offset: 0x83A86, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC7requestAA0a3UrlC0CSgvs', symObjAddr: 0x3158, symBinAddr: 0x3F1D0, symSize: 0x50 }
  - { offsetInCU: 0x882, offset: 0x83AA2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC7requestAA0a3UrlC0CSgvM', symObjAddr: 0x31A8, symBinAddr: 0x3F220, symSize: 0x44 }
  - { offsetInCU: 0x89E, offset: 0x83ABE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCAEycfC', symObjAddr: 0x31EC, symBinAddr: 0x3F264, symSize: 0x88 }
  - { offsetInCU: 0x8D1, offset: 0x83AF1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCAEycfc', symObjAddr: 0x3274, symBinAddr: 0x3F2EC, symSize: 0x74 }
  - { offsetInCU: 0x8F0, offset: 0x83B10, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6setUrlyAESSKF', symObjAddr: 0x32E8, symBinAddr: 0x3F360, symSize: 0x24 }
  - { offsetInCU: 0x904, offset: 0x83B24, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC9setMethodyAESSF', symObjAddr: 0x330C, symBinAddr: 0x3F384, symSize: 0x78 }
  - { offsetInCU: 0x92D, offset: 0x83B4D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC12setUrlParamsyAESDySSypGF', symObjAddr: 0x3384, symBinAddr: 0x3F3FC, symSize: 0x9D8 }
  - { offsetInCU: 0xD89, offset: 0x83FA9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC14openConnectionAEyF', symObjAddr: 0x401C, symBinAddr: 0x40094, symSize: 0x150 }
  - { offsetInCU: 0xE05, offset: 0x84025, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC5buildAA0a3UrlC0CyF', symObjAddr: 0x416C, symBinAddr: 0x401E4, symSize: 0x24 }
  - { offsetInCU: 0xE37, offset: 0x84057, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCfd', symObjAddr: 0x4190, symBinAddr: 0x40208, symSize: 0x60 }
  - { offsetInCU: 0xE72, offset: 0x84092, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCfD', symObjAddr: 0x41F0, symBinAddr: 0x40268, symSize: 0x6C }
  - { offsetInCU: 0xEB5, offset: 0x840D5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderC6setUrlyAESSKFTf4nn_g', symObjAddr: 0x4FCC, symBinAddr: 0x41000, symSize: 0x27C }
  - { offsetInCU: 0xF3C, offset: 0x8415C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC7requestyySo13CAPPluginCallC_SSSgSo24CAPInstanceConfigurationCSgtKFZ', symObjAddr: 0x18EC, symBinAddr: 0x3D964, symSize: 0xD64 }
  - { offsetInCU: 0x136D, offset: 0x8458D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC7requestyySo13CAPPluginCallC_SSSgSo24CAPInstanceConfigurationCSgtKFZy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU_', symObjAddr: 0x4264, symBinAddr: 0x402DC, symSize: 0x2F0 }
  - { offsetInCU: 0x14BA, offset: 0x846DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC22setCookiesFromResponseyySo17NSHTTPURLResponseC_So24CAPInstanceConfigurationCSgtFZ', symObjAddr: 0x425C, symBinAddr: 0x402D4, symSize: 0x4 }
  - { offsetInCU: 0x14CE, offset: 0x846EE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC13buildResponse__12responseTypeSDySSypG10Foundation4DataVSg_So17NSHTTPURLResponseCAA0fH0OtFZ', symObjAddr: 0x4260, symBinAddr: 0x402D8, symSize: 0x4 }
  - { offsetInCU: 0x1500, offset: 0x84720, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerCfd', symObjAddr: 0x4620, symBinAddr: 0x40698, symSize: 0x8 }
  - { offsetInCU: 0x152F, offset: 0x8474F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerCfD', symObjAddr: 0x4628, symBinAddr: 0x406A0, symSize: 0x10 }
  - { offsetInCU: 0x155E, offset: 0x8477E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC22setCookiesFromResponseyySo17NSHTTPURLResponseC_So24CAPInstanceConfigurationCSgtFZTf4nnd_n', symObjAddr: 0x5408, symBinAddr: 0x4131C, symSize: 0x3C8 }
  - { offsetInCU: 0x18D0, offset: 0x84AF0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC13buildResponse__12responseTypeSDySSypG10Foundation4DataVSg_So17NSHTTPURLResponseCAA0fH0OtFZTf4nnnd_n', symObjAddr: 0x57D0, symBinAddr: 0x416E4, symSize: 0xCE8 }
  - { offsetInCU: 0x1FEF, offset: 0x8520F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE9getDoubleySdSgSSFSo13CAPPluginCallC_Tg5', symObjAddr: 0x2868, symBinAddr: 0x3E8E0, symSize: 0x190 }
  - { offsetInCU: 0x208B, offset: 0x852AB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE9getObjectySDySSAA0B0_pGSgSSFSo13CAPPluginCallC_Tg5', symObjAddr: 0x29F8, symBinAddr: 0x3EA70, symSize: 0x190 }
  - { offsetInCU: 0x2127, offset: 0x85347, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO7default_WZ', symObjAddr: 0x2B88, symBinAddr: 0x3EC00, symSize: 0x10 }
  - { offsetInCU: 0x2141, offset: 0x85361, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeO7defaultACvau', symObjAddr: 0x2B98, symBinAddr: 0x3EC10, symSize: 0x40 }
  - { offsetInCU: 0x2468, offset: 0x85688, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgSo13NSURLResponseCSgs5Error_pSgIeghggg_So6NSDataCSgAGSo7NSErrorCSgIeyBhyyy_TR', symObjAddr: 0x4554, symBinAddr: 0x405CC, symSize: 0xCC }
  - { offsetInCU: 0x2480, offset: 0x856A0, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tgq5', symObjAddr: 0x4638, symBinAddr: 0x406B0, symSize: 0x64 }
  - { offsetInCU: 0x2498, offset: 0x856B8, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tgq5', symObjAddr: 0x469C, symBinAddr: 0x40714, symSize: 0x144 }
  - { offsetInCU: 0x2560, offset: 0x85780, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFSS_ypSaySS_yptGTg5012$sSD20uniquegh33ValuesSDyxq_Gqd__n_tcSTRd__x_q_t7i31Rtd__lufcq_q__q_tKXEfU_SS_ypSayU8_yptGTg5Tf1nncn_n', symObjAddr: 0x47E0, symBinAddr: 0x40858, symSize: 0x3D8 }
  - { offsetInCU: 0x26F2, offset: 0x85912, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV10startIndexSD0D0Vyxq__Gvgs11AnyHashableV_ypTg5', symObjAddr: 0x4BB8, symBinAddr: 0x40C30, symSize: 0xB0 }
  - { offsetInCU: 0x274C, offset: 0x8596C, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_ypTgq5Tf4gd_n', symObjAddr: 0x4DF4, symBinAddr: 0x40E6C, symSize: 0x110 }
  - { offsetInCU: 0x27B5, offset: 0x859D5, size: 0x8, addend: 0x0, symName: '_$s10Foundation8URLErrorVAcA21_BridgedStoredNSErrorAAWl', symObjAddr: 0x4F04, symBinAddr: 0x40F7C, symSize: 0x48 }
  - { offsetInCU: 0x27C9, offset: 0x859E9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCMa', symObjAddr: 0x4F90, symBinAddr: 0x40FC4, symSize: 0x3C }
  - { offsetInCU: 0x27DD, offset: 0x859FD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC7requestyySo13CAPPluginCallC_SSSgSo24CAPInstanceConfigurationCSgtKFZy10Foundation4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU_TA', symObjAddr: 0x52F8, symBinAddr: 0x412B8, symSize: 0x2C }
  - { offsetInCU: 0x27F1, offset: 0x85A11, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5324, symBinAddr: 0x412E4, symSize: 0x10 }
  - { offsetInCU: 0x2805, offset: 0x85A25, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5334, symBinAddr: 0x412F4, symSize: 0x8 }
  - { offsetInCU: 0x294F, offset: 0x85B6F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSHAASQWb', symObjAddr: 0x6518, symBinAddr: 0x4242C, symSize: 0x4 }
  - { offsetInCU: 0x2963, offset: 0x85B83, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOACSQAAWl', symObjAddr: 0x651C, symBinAddr: 0x42430, symSize: 0x44 }
  - { offsetInCU: 0x2977, offset: 0x85B97, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOwet', symObjAddr: 0x6570, symBinAddr: 0x42474, symSize: 0x90 }
  - { offsetInCU: 0x298B, offset: 0x85BAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOwst', symObjAddr: 0x6600, symBinAddr: 0x42504, symSize: 0xBC }
  - { offsetInCU: 0x299F, offset: 0x85BBF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOwug', symObjAddr: 0x66BC, symBinAddr: 0x425C0, symSize: 0x8 }
  - { offsetInCU: 0x29B3, offset: 0x85BD3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOwup', symObjAddr: 0x66C4, symBinAddr: 0x425C8, symSize: 0x4 }
  - { offsetInCU: 0x29C7, offset: 0x85BE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOwui', symObjAddr: 0x66C8, symBinAddr: 0x425CC, symSize: 0x8 }
  - { offsetInCU: 0x29DB, offset: 0x85BFB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOMa', symObjAddr: 0x66D0, symBinAddr: 0x425D4, symSize: 0x10 }
  - { offsetInCU: 0x29EF, offset: 0x85C0F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerCMa', symObjAddr: 0x66E0, symBinAddr: 0x425E4, symSize: 0x20 }
  - { offsetInCU: 0x2A03, offset: 0x85C23, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCMU', symObjAddr: 0x6700, symBinAddr: 0x42604, symSize: 0x8 }
  - { offsetInCU: 0x2A17, offset: 0x85C37, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18HttpRequestHandlerC0abC7BuilderCMr', symObjAddr: 0x6708, symBinAddr: 0x4260C, symSize: 0x80 }
  - { offsetInCU: 0x2A8C, offset: 0x85CAC, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSDys11AnyHashableVypG_SS_yptTg505$ss11cd160VypSSyps5Error_pIgnnorzo_AB3key_yp5valuetSS_yptsAC_pIegnrzo_TR091$s9Capacitor25lowerCaseHeaderDictionary33_EE15C01EA65F5710520693E9E7846AECLLySDySSypGSDys11ab14F27GFSS_yptAF_W5XEfU_Tf3nnpf_nTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x3C078, symSize: 0x2FC }
  - { offsetInCU: 0x2D2E, offset: 0x85F4E, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_yps11AnyHashableVypTg5', symObjAddr: 0x2FC, symBinAddr: 0x3C374, symSize: 0x408 }
  - { offsetInCU: 0x2E76, offset: 0x86096, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_9Capacitor7JSValue_ps11AnyHashableVypTg5', symObjAddr: 0x704, symBinAddr: 0x3C77C, symSize: 0x43C }
  - { offsetInCU: 0x2FCA, offset: 0x861EA, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0xB40, symBinAddr: 0x3CBB8, symSize: 0x37C }
  - { offsetInCU: 0x3111, offset: 0x86331, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_9Capacitor7JSValue_pSSypTg5', symObjAddr: 0xEBC, symBinAddr: 0x3CF34, symSize: 0x39C }
  - { offsetInCU: 0x326D, offset: 0x8648D, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_SayypGSSypTg5', symObjAddr: 0x1258, symBinAddr: 0x3D2D0, symSize: 0x37C }
  - { offsetInCU: 0x33C3, offset: 0x865E3, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlFSo15CAPPluginMethodC9CapacitorE10ReturnTypeO_Tgq5', symObjAddr: 0x15D4, symBinAddr: 0x3D64C, symSize: 0x84 }
  - { offsetInCU: 0x3452, offset: 0x86672, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF9Capacitor12ResponseTypeO_Tgq5', symObjAddr: 0x1658, symBinAddr: 0x3D6D0, symSize: 0x84 }
  - { offsetInCU: 0x34E1, offset: 0x86701, size: 0x8, addend: 0x0, symName: '_$ss2eeoiySbx_xtSYRzSQ8RawValueRpzlF9Capacitor12ResponseTypeO_Tg5', symObjAddr: 0x16DC, symBinAddr: 0x3D754, symSize: 0x84 }
  - { offsetInCU: 0x363A, offset: 0x8685A, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE04hashB0SivgSo15CAPPluginMethodC9CapacitorE10ReturnTypeO_Tgq5', symObjAddr: 0x2650, symBinAddr: 0x3E6C8, symSize: 0x68 }
  - { offsetInCU: 0x3714, offset: 0x86934, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE04hashB0Sivg9Capacitor12ResponseTypeO_Tgq5', symObjAddr: 0x26B8, symBinAddr: 0x3E730, symSize: 0x68 }
  - { offsetInCU: 0x37B2, offset: 0x869D2, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tFSo15CAPPluginMethodC9CapacitorE10ReturnTypeO_Tgq5', symObjAddr: 0x2720, symBinAddr: 0x3E798, symSize: 0x40 }
  - { offsetInCU: 0x380F, offset: 0x86A2F, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE4hash4intoys6HasherVz_tF9Capacitor12ResponseTypeO_Tgq5', symObjAddr: 0x2760, symBinAddr: 0x3E7D8, symSize: 0x40 }
  - { offsetInCU: 0x386C, offset: 0x86A8C, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE08_rawHashB04seedS2i_tF9Capacitor12ResponseTypeO_Tgq5', symObjAddr: 0x27A0, symBinAddr: 0x3E818, symSize: 0x64 }
  - { offsetInCU: 0x38E8, offset: 0x86B08, size: 0x8, addend: 0x0, symName: '_$sSYsSHRzSH8RawValueSYRpzrlE08_rawHashB04seedS2i_tFSo15CAPPluginMethodC9CapacitorE10ReturnTypeO_Tgq5', symObjAddr: 0x2804, symBinAddr: 0x3E87C, symSize: 0x64 }
  - { offsetInCU: 0x396A, offset: 0x86B8A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x2D80, symBinAddr: 0x3EDF8, symSize: 0xC }
  - { offsetInCU: 0x3986, offset: 0x86BA6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSHAASH9hashValueSivgTW', symObjAddr: 0x2D8C, symBinAddr: 0x3EE04, symSize: 0x8 }
  - { offsetInCU: 0x39A2, offset: 0x86BC2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x2D94, symBinAddr: 0x3EE0C, symSize: 0x8 }
  - { offsetInCU: 0x39B6, offset: 0x86BD6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12ResponseTypeOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x2D9C, symBinAddr: 0x3EE14, symSize: 0x8 }
  - { offsetInCU: 0x3A67, offset: 0x86C87, size: 0x8, addend: 0x0, symName: '_$sSa6append10contentsOfyqd__n_t7ElementQyd__RszSTRd__lF9Capacitor18PluginHeaderMethodV_SayAGGTg5', symObjAddr: 0x3D5C, symBinAddr: 0x3FDD4, symSize: 0xDC }
  - { offsetInCU: 0x3C78, offset: 0x86E98, size: 0x8, addend: 0x0, symName: '_$sSa6append10contentsOfyqd__n_t7ElementQyd__RszSTRd__lF10Foundation12URLQueryItemV_SayAGGTg5', symObjAddr: 0x3E38, symBinAddr: 0x3FEB0, symSize: 0xF4 }
  - { offsetInCU: 0x3E88, offset: 0x870A8, size: 0x8, addend: 0x0, symName: '_$sSa6append10contentsOfyqd__n_t7ElementQyd__RszSTRd__lF9Capacitor7JSValue_p_SayAeF_pGTg5', symObjAddr: 0x3F2C, symBinAddr: 0x3FFA4, symSize: 0xF0 }
  - { offsetInCU: 0x40D4, offset: 0x872F4, size: 0x8, addend: 0x0, symName: '_$sSTsE7forEachyyy7ElementQzKXEKFSD4KeysVySSyp_G_Tg560$s9Capacitor0A10UrlRequestC03setC7HeadersyySDySSypGFySSXEfU_SDySSypG9Capacitor0qhI0CTf1cn_nTf4ngg_n', symObjAddr: 0x4C68, symBinAddr: 0x40CE0, symSize: 0x18C }
  - { offsetInCU: 0x2B, offset: 0x877EA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSDateCfd', symObjAddr: 0x0, symBinAddr: 0x4275C, symSize: 0x8 }
  - { offsetInCU: 0x4F, offset: 0x8780E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSDateCfd', symObjAddr: 0x0, symBinAddr: 0x4275C, symSize: 0x8 }
  - { offsetInCU: 0x7E, offset: 0x8783D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSDateCfD', symObjAddr: 0x8, symBinAddr: 0x42764, symSize: 0x10 }
  - { offsetInCU: 0xAE, offset: 0x8786D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSDateCMa', symObjAddr: 0x18, symBinAddr: 0x42774, symSize: 0x20 }
  - { offsetInCU: 0x152, offset: 0x87911, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultV11jsonPayloadSSyF', symObjAddr: 0x38, symBinAddr: 0x42794, symSize: 0x3A4 }
  - { offsetInCU: 0x4AA, offset: 0x87C69, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVAA0B8ProtocolA2aDP11jsonPayloadSSyFTW', symObjAddr: 0x460, symBinAddr: 0x42BBC, symSize: 0x38 }
  - { offsetInCU: 0x4D8, offset: 0x87C97, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVAA0B8ProtocolA2aDP10callbackIDSSvgTW', symObjAddr: 0x3DC, symBinAddr: 0x42B38, symSize: 0x2C }
  - { offsetInCU: 0x51B, offset: 0x87CDA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVAA0B8ProtocolA2aDP8pluginIDSSvgTW', symObjAddr: 0x408, symBinAddr: 0x42B64, symSize: 0x2C }
  - { offsetInCU: 0x55E, offset: 0x87D1D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVAA0B8ProtocolA2aDP10methodNameSSvgTW', symObjAddr: 0x434, symBinAddr: 0x42B90, symSize: 0x2C }
  - { offsetInCU: 0x60A, offset: 0x87DC9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorV11jsonPayloadSSyF', symObjAddr: 0x498, symBinAddr: 0x42BF4, symSize: 0x620 }
  - { offsetInCU: 0xAEF, offset: 0x882AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVAA0B8ProtocolA2aDP11jsonPayloadSSyFTW', symObjAddr: 0xB3C, symBinAddr: 0x43298, symSize: 0x48 }
  - { offsetInCU: 0xB1D, offset: 0x882DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVAA0B8ProtocolA2aDP10callbackIDSSvgTW', symObjAddr: 0xAB8, symBinAddr: 0x43214, symSize: 0x2C }
  - { offsetInCU: 0xB60, offset: 0x8831F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVAA0B8ProtocolA2aDP8pluginIDSSvgTW', symObjAddr: 0xAE4, symBinAddr: 0x43240, symSize: 0x2C }
  - { offsetInCU: 0xBA3, offset: 0x88362, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVAA0B8ProtocolA2aDP10methodNameSSvgTW', symObjAddr: 0xB10, symBinAddr: 0x4326C, symSize: 0x2C }
  - { offsetInCU: 0xBD5, offset: 0x88394, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorV4call0dC0AcA6JSCallV_AA013CAPPluginCallC0CtcfCTf4gnd_n', symObjAddr: 0xB84, symBinAddr: 0x432E0, symSize: 0x27C }
  - { offsetInCU: 0xC39, offset: 0x883F8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOSgWOb', symObjAddr: 0xEA8, symBinAddr: 0x4355C, symSize: 0x48 }
  - { offsetInCU: 0xC4D, offset: 0x8840C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwxx', symObjAddr: 0xF30, symBinAddr: 0x435A4, symSize: 0x40 }
  - { offsetInCU: 0xC61, offset: 0x88420, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwcp', symObjAddr: 0xF70, symBinAddr: 0x435E4, symSize: 0x74 }
  - { offsetInCU: 0xC75, offset: 0x88434, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwca', symObjAddr: 0xFE4, symBinAddr: 0x43658, symSize: 0xBC }
  - { offsetInCU: 0xC89, offset: 0x88448, size: 0x8, addend: 0x0, symName: ___swift_memcpy64_8, symObjAddr: 0x10A0, symBinAddr: 0x43714, symSize: 0x14 }
  - { offsetInCU: 0xC9D, offset: 0x8845C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwta', symObjAddr: 0x10B4, symBinAddr: 0x43728, symSize: 0x74 }
  - { offsetInCU: 0xCB1, offset: 0x88470, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwet', symObjAddr: 0x1128, symBinAddr: 0x4379C, symSize: 0x48 }
  - { offsetInCU: 0xCC5, offset: 0x88484, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVwst', symObjAddr: 0x1170, symBinAddr: 0x437E4, symSize: 0x50 }
  - { offsetInCU: 0xCD9, offset: 0x88498, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSResultVMa', symObjAddr: 0x11C0, symBinAddr: 0x43834, symSize: 0x10 }
  - { offsetInCU: 0xCED, offset: 0x884AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwCP', symObjAddr: 0x11D0, symBinAddr: 0x43844, symSize: 0x30 }
  - { offsetInCU: 0xD01, offset: 0x884C0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwxx', symObjAddr: 0x1200, symBinAddr: 0x43874, symSize: 0x58 }
  - { offsetInCU: 0xD15, offset: 0x884D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwcp', symObjAddr: 0x1258, symBinAddr: 0x438CC, symSize: 0xAC }
  - { offsetInCU: 0xD29, offset: 0x884E8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwca', symObjAddr: 0x1304, symBinAddr: 0x43978, symSize: 0x11C }
  - { offsetInCU: 0xD3D, offset: 0x884FC, size: 0x8, addend: 0x0, symName: ___swift_memcpy112_8, symObjAddr: 0x1420, symBinAddr: 0x43A94, symSize: 0x24 }
  - { offsetInCU: 0xD51, offset: 0x88510, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwta', symObjAddr: 0x1444, symBinAddr: 0x43AB8, symSize: 0xA4 }
  - { offsetInCU: 0xD65, offset: 0x88524, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwet', symObjAddr: 0x14E8, symBinAddr: 0x43B5C, symSize: 0x48 }
  - { offsetInCU: 0xD79, offset: 0x88538, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVwst', symObjAddr: 0x1530, symBinAddr: 0x43BA4, symSize: 0x5C }
  - { offsetInCU: 0xD8D, offset: 0x8854C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13JSResultErrorVMa', symObjAddr: 0x158C, symBinAddr: 0x43C00, symSize: 0x10 }
  - { offsetInCU: 0xDA1, offset: 0x88560, size: 0x8, addend: 0x0, symName: '_$sSSWOr', symObjAddr: 0x159C, symBinAddr: 0x43C10, symSize: 0x28 }
  - { offsetInCU: 0xDB5, offset: 0x88574, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwxx', symObjAddr: 0x1658, symBinAddr: 0x43C5C, symSize: 0x38 }
  - { offsetInCU: 0xDC9, offset: 0x88588, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwcp', symObjAddr: 0x1690, symBinAddr: 0x43C94, symSize: 0x64 }
  - { offsetInCU: 0xDDD, offset: 0x8859C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwca', symObjAddr: 0x16F4, symBinAddr: 0x43CF8, symSize: 0xA4 }
  - { offsetInCU: 0xDF1, offset: 0x885B0, size: 0x8, addend: 0x0, symName: ___swift_memcpy56_8, symObjAddr: 0x1798, symBinAddr: 0x43D9C, symSize: 0x1C }
  - { offsetInCU: 0xE05, offset: 0x885C4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwta', symObjAddr: 0x17B4, symBinAddr: 0x43DB8, symSize: 0x64 }
  - { offsetInCU: 0xE19, offset: 0x885D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwet', symObjAddr: 0x1818, symBinAddr: 0x43E1C, symSize: 0x48 }
  - { offsetInCU: 0xE2D, offset: 0x885EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVwst', symObjAddr: 0x1860, symBinAddr: 0x43E64, symSize: 0x4C }
  - { offsetInCU: 0xE41, offset: 0x88600, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6JSCallVMa', symObjAddr: 0x18AC, symBinAddr: 0x43EB0, symSize: 0x10 }
  - { offsetInCU: 0x4F, offset: 0x88851, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC24catchallOptionsParameterSSvpZ', symObjAddr: 0x142B8, symBinAddr: 0xA6240, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x8886B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC17callbackParameterSSvpZ', symObjAddr: 0x142C8, symBinAddr: 0xA6250, symSize: 0x0 }
  - { offsetInCU: 0xDB, offset: 0x888DD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC14exportBridgeJS21userContentControllerySo06WKUsergH0C_tKFZ', symObjAddr: 0x0, symBinAddr: 0x43EC8, symSize: 0x3C8 }
  - { offsetInCU: 0x250, offset: 0x88A52, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC22exportCordovaPluginsJS21userContentControllerySo06WKUserhI0C_tKFZ', symObjAddr: 0x3C8, symBinAddr: 0x44290, symSize: 0x210 }
  - { offsetInCU: 0x2AC, offset: 0x88AAE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC20injectFilesForFolder6folder21userContentControllery10Foundation3URLV_So06WKUseriJ0CtFZ', symObjAddr: 0xB94, symBinAddr: 0x44A5C, symSize: 0x544 }
  - { offsetInCU: 0x58B, offset: 0x88D8D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportCfD', symObjAddr: 0x10D8, symBinAddr: 0x44FA0, symSize: 0x10 }
  - { offsetInCU: 0x5BA, offset: 0x88DBC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC06exportA8GlobalJS21userContentController7isDebug14loggingEnabled8localUrlySo06WKUsergH0C_S2bSStKFZTf4nnnnd_n', symObjAddr: 0x1188, symBinAddr: 0x44FD0, symSize: 0x1B8 }
  - { offsetInCU: 0x7F1, offset: 0x88FF3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC10injectFile7fileURL21userContentControllery10Foundation0F0V_So06WKUserhI0CtKFZTf4nnd_n', symObjAddr: 0x1340, symBinAddr: 0x45188, symSize: 0x1B0 }
  - { offsetInCU: 0x933, offset: 0x89135, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC14generateMethod33_35B4C07ABE18CB14DB9A3F920E64F657LL15pluginClassName6methodS2S_So09CAPPluginD0CtFZTf4nnd_n', symObjAddr: 0x14F0, symBinAddr: 0x45338, symSize: 0x880 }
  - { offsetInCU: 0x14D1, offset: 0x89CD3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC24createPluginHeaderMethod33_35B4C07ABE18CB14DB9A3F920E64F657LL6methodAA0deF0VSo09CAPPluginF0C_tFZTf4nd_n', symObjAddr: 0x1D70, symBinAddr: 0x45BB8, symSize: 0xF0 }
  - { offsetInCU: 0x158F, offset: 0x89D91, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC18createPluginHeader33_35B4C07ABE18CB14DB9A3F920E64F657LL3forAA0dE0VSgSo010CAPBridgedD0_So9CAPPluginCXc_tFZTf4nd_n', symObjAddr: 0x1E60, symBinAddr: 0x45CA8, symSize: 0x2F0 }
  - { offsetInCU: 0x185E, offset: 0x8A060, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC8exportJS3for2inySo16CAPBridgedPlugin_So9CAPPluginCXc_So23WKUserContentControllerCtFZTf4nnd_n', symObjAddr: 0x2150, symBinAddr: 0x45F98, symSize: 0x68C }
  - { offsetInCU: 0x1F7E, offset: 0x8A780, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC15exportCordovaJS21userContentControllerySo06WKUsergH0C_tKFZTf4nd_n', symObjAddr: 0x27DC, symBinAddr: 0x46624, symSize: 0x66C }
  - { offsetInCU: 0x21CD, offset: 0x8A9CF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLO11stringValueSSvg', symObjAddr: 0x5D8, symBinAddr: 0x444A0, symSize: 0x2C }
  - { offsetInCU: 0x21F4, offset: 0x8A9F6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0E3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0x8D0, symBinAddr: 0x44798, symSize: 0x8 }
  - { offsetInCU: 0x221F, offset: 0x8AA21, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0E3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0x8D8, symBinAddr: 0x447A0, symSize: 0x24 }
  - { offsetInCU: 0x2250, offset: 0x8AA52, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0E3KeyAAsAGP8intValueSiSgvgTW', symObjAddr: 0x8FC, symBinAddr: 0x447C4, symSize: 0xC }
  - { offsetInCU: 0x226C, offset: 0x8AA6E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0x3C8C, symBinAddr: 0x4792C, symSize: 0xD0 }
  - { offsetInCU: 0x22B2, offset: 0x8AAB4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV6encode2toys7Encoder_p_tKF', symObjAddr: 0x604, symBinAddr: 0x444CC, symSize: 0x128 }
  - { offsetInCU: 0x2307, offset: 0x8AB09, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0x958, symBinAddr: 0x44820, symSize: 0x2C }
  - { offsetInCU: 0x233E, offset: 0x8AB40, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0x984, symBinAddr: 0x4484C, symSize: 0x1C }
  - { offsetInCU: 0x2361, offset: 0x8AB63, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV4fromACs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0x3678, symBinAddr: 0x47348, symSize: 0x1B0 }
  - { offsetInCU: 0x23B1, offset: 0x8ABB3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLO11stringValueSSvg', symObjAddr: 0x72C, symBinAddr: 0x445F4, symSize: 0x30 }
  - { offsetInCU: 0x23FC, offset: 0x8ABFE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x9A0, symBinAddr: 0x44868, symSize: 0x18 }
  - { offsetInCU: 0x2464, offset: 0x8AC66, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0xA00, symBinAddr: 0x448C8, symSize: 0x28 }
  - { offsetInCU: 0x24DE, offset: 0x8ACE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAsAGP11stringValueSSvgTW', symObjAddr: 0xA6C, symBinAddr: 0x44934, symSize: 0x8 }
  - { offsetInCU: 0x2509, offset: 0x8AD0B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAsAGP11stringValuexSgSS_tcfCTW', symObjAddr: 0xA74, symBinAddr: 0x4493C, symSize: 0x24 }
  - { offsetInCU: 0x253A, offset: 0x8AD3C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAsAGP8intValueSiSgvgTW', symObjAddr: 0xA98, symBinAddr: 0x44960, symSize: 0xC }
  - { offsetInCU: 0x2556, offset: 0x8AD58, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAsAGP8intValuexSgSi_tcfCTW', symObjAddr: 0xAA4, symBinAddr: 0x4496C, symSize: 0xC }
  - { offsetInCU: 0x2572, offset: 0x8AD74, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLO11stringValueAFSgSS_tcfCTf4nd_n', symObjAddr: 0x3D5C, symBinAddr: 0x479FC, symSize: 0xD8 }
  - { offsetInCU: 0x25B8, offset: 0x8ADBA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV6encode2toys7Encoder_p_tKF', symObjAddr: 0x75C, symBinAddr: 0x44624, symSize: 0x16C }
  - { offsetInCU: 0x260D, offset: 0x8AE0F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVSeAASe4fromxs7Decoder_p_tKcfCTW', symObjAddr: 0xB00, symBinAddr: 0x449C8, symSize: 0x2C }
  - { offsetInCU: 0x2644, offset: 0x8AE46, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVSEAASE6encode2toys7Encoder_p_tKFTW', symObjAddr: 0xB2C, symBinAddr: 0x449F4, symSize: 0x1C }
  - { offsetInCU: 0x2667, offset: 0x8AE69, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV4fromACs7Decoder_p_tKcfCTf4nd_n', symObjAddr: 0x3498, symBinAddr: 0x47168, symSize: 0x1E0 }
  - { offsetInCU: 0x26AF, offset: 0x8AEB1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC24catchallOptionsParameter_WZ', symObjAddr: 0xB48, symBinAddr: 0x44A10, symSize: 0x24 }
  - { offsetInCU: 0x26C9, offset: 0x8AECB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportC17callbackParameter_WZ', symObjAddr: 0xB6C, symBinAddr: 0x44A34, symSize: 0x28 }
  - { offsetInCU: 0x2730, offset: 0x8AF32, size: 0x8, addend: 0x0, symName: '_$s9Capacitor8JSExportCMa', symObjAddr: 0x10E8, symBinAddr: 0x44FB0, symSize: 0x20 }
  - { offsetInCU: 0x294A, offset: 0x8B14C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVACSEAAWl', symObjAddr: 0x2F0C, symBinAddr: 0x46C90, symSize: 0x44 }
  - { offsetInCU: 0x295E, offset: 0x8B160, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVSgWOe', symObjAddr: 0x2F50, symBinAddr: 0x46CD4, symSize: 0x30 }
  - { offsetInCU: 0x2972, offset: 0x8B174, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwCP', symObjAddr: 0x3010, symBinAddr: 0x46D04, symSize: 0x30 }
  - { offsetInCU: 0x2986, offset: 0x8B188, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwxx', symObjAddr: 0x3040, symBinAddr: 0x46D34, symSize: 0x28 }
  - { offsetInCU: 0x299A, offset: 0x8B19C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwcp', symObjAddr: 0x3068, symBinAddr: 0x46D5C, symSize: 0x3C }
  - { offsetInCU: 0x29AE, offset: 0x8B1B0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwca', symObjAddr: 0x30A4, symBinAddr: 0x46D98, symSize: 0x6C }
  - { offsetInCU: 0x29C2, offset: 0x8B1C4, size: 0x8, addend: 0x0, symName: ___swift_memcpy32_8, symObjAddr: 0x3110, symBinAddr: 0x46E04, symSize: 0xC }
  - { offsetInCU: 0x29D6, offset: 0x8B1D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwta', symObjAddr: 0x311C, symBinAddr: 0x46E10, symSize: 0x44 }
  - { offsetInCU: 0x29EA, offset: 0x8B1EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwet', symObjAddr: 0x3160, symBinAddr: 0x46E54, symSize: 0x48 }
  - { offsetInCU: 0x29FE, offset: 0x8B200, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVwst', symObjAddr: 0x31A8, symBinAddr: 0x46E9C, symSize: 0x40 }
  - { offsetInCU: 0x2A12, offset: 0x8B214, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVMa', symObjAddr: 0x31E8, symBinAddr: 0x46EDC, symSize: 0x10 }
  - { offsetInCU: 0x2A26, offset: 0x8B228, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwCP', symObjAddr: 0x31F8, symBinAddr: 0x46EEC, symSize: 0x3C }
  - { offsetInCU: 0x2A3A, offset: 0x8B23C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwxx', symObjAddr: 0x3234, symBinAddr: 0x46F28, symSize: 0x28 }
  - { offsetInCU: 0x2A4E, offset: 0x8B250, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwcp', symObjAddr: 0x325C, symBinAddr: 0x46F50, symSize: 0x3C }
  - { offsetInCU: 0x2A62, offset: 0x8B264, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwca', symObjAddr: 0x3298, symBinAddr: 0x46F8C, symSize: 0x64 }
  - { offsetInCU: 0x2A76, offset: 0x8B278, size: 0x8, addend: 0x0, symName: ___swift_memcpy24_8, symObjAddr: 0x32FC, symBinAddr: 0x46FF0, symSize: 0x14 }
  - { offsetInCU: 0x2A8A, offset: 0x8B28C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwta', symObjAddr: 0x3310, symBinAddr: 0x47004, symSize: 0x44 }
  - { offsetInCU: 0x2A9E, offset: 0x8B2A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwet', symObjAddr: 0x3354, symBinAddr: 0x47048, symSize: 0x48 }
  - { offsetInCU: 0x2AB2, offset: 0x8B2B4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVwst', symObjAddr: 0x339C, symBinAddr: 0x47090, symSize: 0x40 }
  - { offsetInCU: 0x2AC6, offset: 0x8B2C8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderVMa', symObjAddr: 0x33DC, symBinAddr: 0x470D0, symSize: 0x10 }
  - { offsetInCU: 0x2ADA, offset: 0x8B2DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs0D3KeyAAWl', symObjAddr: 0x3410, symBinAddr: 0x470E0, symSize: 0x44 }
  - { offsetInCU: 0x2AEE, offset: 0x8B2F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVACSEAAWl', symObjAddr: 0x3454, symBinAddr: 0x47124, symSize: 0x44 }
  - { offsetInCU: 0x2B02, offset: 0x8B304, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs0E3KeyAAWl', symObjAddr: 0x3828, symBinAddr: 0x474F8, symSize: 0x44 }
  - { offsetInCU: 0x2B16, offset: 0x8B318, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodVACSeAAWl', symObjAddr: 0x38F0, symBinAddr: 0x475A0, symSize: 0x44 }
  - { offsetInCU: 0x2B2A, offset: 0x8B32C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOwup', symObjAddr: 0x394C, symBinAddr: 0x475EC, symSize: 0x4 }
  - { offsetInCU: 0x2B3E, offset: 0x8B340, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOMa', symObjAddr: 0x3950, symBinAddr: 0x475F0, symSize: 0x10 }
  - { offsetInCU: 0x2B52, offset: 0x8B354, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOwug', symObjAddr: 0x3AB4, symBinAddr: 0x47754, symSize: 0x8 }
  - { offsetInCU: 0x2B66, offset: 0x8B368, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOwup', symObjAddr: 0x3ABC, symBinAddr: 0x4775C, symSize: 0x4 }
  - { offsetInCU: 0x2B7A, offset: 0x8B37C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOwui', symObjAddr: 0x3AC0, symBinAddr: 0x47760, symSize: 0xC }
  - { offsetInCU: 0x2B8E, offset: 0x8B390, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOMa', symObjAddr: 0x3ACC, symBinAddr: 0x4776C, symSize: 0x10 }
  - { offsetInCU: 0x2BA2, offset: 0x8B3A4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOSHAASQWb', symObjAddr: 0x3ADC, symBinAddr: 0x4777C, symSize: 0x4 }
  - { offsetInCU: 0x2BB6, offset: 0x8B3B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFSQAAWl', symObjAddr: 0x3AE0, symBinAddr: 0x47780, symSize: 0x44 }
  - { offsetInCU: 0x2BCA, offset: 0x8B3CC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOSHAASQWb', symObjAddr: 0x3B24, symBinAddr: 0x477C4, symSize: 0x4 }
  - { offsetInCU: 0x2BDE, offset: 0x8B3E0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFSQAAWl', symObjAddr: 0x3B28, symBinAddr: 0x477C8, symSize: 0x44 }
  - { offsetInCU: 0x2BF2, offset: 0x8B3F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0E3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3B6C, symBinAddr: 0x4780C, symSize: 0x4 }
  - { offsetInCU: 0x2C06, offset: 0x8B408, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3B70, symBinAddr: 0x47810, symSize: 0x44 }
  - { offsetInCU: 0x2C1A, offset: 0x8B41C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0E3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3BB4, symBinAddr: 0x47854, symSize: 0x4 }
  - { offsetInCU: 0x2C2E, offset: 0x8B430, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3BB8, symBinAddr: 0x47858, symSize: 0x44 }
  - { offsetInCU: 0x2C42, offset: 0x8B444, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x3BFC, symBinAddr: 0x4789C, symSize: 0x4 }
  - { offsetInCU: 0x2C56, offset: 0x8B458, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs28CustomDebugStringConvertibleAAWl', symObjAddr: 0x3C00, symBinAddr: 0x478A0, symSize: 0x44 }
  - { offsetInCU: 0x2C6A, offset: 0x8B46C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs0D3KeyAAs23CustomStringConvertiblePWb', symObjAddr: 0x3C44, symBinAddr: 0x478E4, symSize: 0x4 }
  - { offsetInCU: 0x2C7E, offset: 0x8B480, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOAFs23CustomStringConvertibleAAWl', symObjAddr: 0x3C48, symBinAddr: 0x478E8, symSize: 0x44 }
  - { offsetInCU: 0x2CC6, offset: 0x8B4C8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0x908, symBinAddr: 0x447D0, symSize: 0x28 }
  - { offsetInCU: 0x2CE2, offset: 0x8B4E4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18PluginHeaderMethodV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0x930, symBinAddr: 0x447F8, symSize: 0x28 }
  - { offsetInCU: 0x2D04, offset: 0x8B506, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs28CustomDebugStringConvertibleAAsAGP16debugDescriptionSSvgTW', symObjAddr: 0xAB0, symBinAddr: 0x44978, symSize: 0x28 }
  - { offsetInCU: 0x2D20, offset: 0x8B522, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginHeaderV10CodingKeys33_35B4C07ABE18CB14DB9A3F920E64F657LLOs23CustomStringConvertibleAAsAGP11descriptionSSvgTW', symObjAddr: 0xAD8, symBinAddr: 0x449A0, symSize: 0x28 }
  - { offsetInCU: 0x4F, offset: 0x8B975, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19dateStringFormatter33_883E0F63E6283FEB7FEBC7F75A4BFDE5LLSo013NSISO8601DateD0Cvp', symObjAddr: 0x23A8, symBinAddr: 0xA1D90, symSize: 0x0 }
  - { offsetInCU: 0x5D, offset: 0x8B983, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSTypesO26coerceDictionaryToJSObject_24formattingDatesAsStringsSDySSAA7JSValue_pGSgSDys11AnyHashableVypGSg_SbtFZ', symObjAddr: 0x0, symBinAddr: 0x47AF8, symSize: 0x4 }
  - { offsetInCU: 0x84, offset: 0x8B9AA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE9getStringySSSgSSF', symObjAddr: 0x4, symBinAddr: 0x47AFC, symSize: 0x114 }
  - { offsetInCU: 0xF3, offset: 0x8BA19, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17JSStringContainerPAAE9getStringyS2S_SStF', symObjAddr: 0x118, symBinAddr: 0x47C10, symSize: 0x58 }
  - { offsetInCU: 0x150, offset: 0x8BA76, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE7getBoolySbSgSSF', symObjAddr: 0x170, symBinAddr: 0x47C68, symSize: 0x110 }
  - { offsetInCU: 0x1BF, offset: 0x8BAE5, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSStringContainerA2cDP9getStringyS2S_SStFTW', symObjAddr: 0x280, symBinAddr: 0x47D78, symSize: 0x4 }
  - { offsetInCU: 0x1DB, offset: 0x8BB01, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSStringContainerA2cDP9getStringySSSgSSFTW', symObjAddr: 0x284, symBinAddr: 0x47D7C, symSize: 0xC }
  - { offsetInCU: 0x1F7, offset: 0x8BB1D, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor15JSBoolContainerA2cDP7getBoolySbSS_SbtFTW', symObjAddr: 0x290, symBinAddr: 0x47D88, symSize: 0x4 }
  - { offsetInCU: 0x213, offset: 0x8BB39, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15JSBoolContainerPAAE7getBoolySbSS_SbtF', symObjAddr: 0x294, symBinAddr: 0x47D8C, symSize: 0x3C }
  - { offsetInCU: 0x270, offset: 0x8BB96, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor15JSBoolContainerA2cDP7getBoolySbSgSSFTW', symObjAddr: 0x2D0, symBinAddr: 0x47DC8, symSize: 0xC }
  - { offsetInCU: 0x28C, offset: 0x8BBB2, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor14JSIntContainerA2cDP6getIntySiSS_SitFTW', symObjAddr: 0x2DC, symBinAddr: 0x47DD4, symSize: 0x4 }
  - { offsetInCU: 0x2A8, offset: 0x8BBCE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSIntContainerPAAE6getIntySiSS_SitF', symObjAddr: 0x2E0, symBinAddr: 0x47DD8, symSize: 0x34 }
  - { offsetInCU: 0x305, offset: 0x8BC2B, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor14JSIntContainerA2cDP6getIntySiSgSSFTW', symObjAddr: 0x314, symBinAddr: 0x47E0C, symSize: 0x20 }
  - { offsetInCU: 0x321, offset: 0x8BC47, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE6getIntySiSgSSF', symObjAddr: 0x334, symBinAddr: 0x47E2C, symSize: 0x118 }
  - { offsetInCU: 0x390, offset: 0x8BCB6, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSFloatContainerA2cDP8getFloatySfSS_SftFTW', symObjAddr: 0x44C, symBinAddr: 0x47F44, symSize: 0x4 }
  - { offsetInCU: 0x3AC, offset: 0x8BCD2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSFloatContainerPAAE8getFloatySfSS_SftF', symObjAddr: 0x450, symBinAddr: 0x47F48, symSize: 0x30 }
  - { offsetInCU: 0x408, offset: 0x8BD2E, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSFloatContainerA2cDP8getFloatySfSgSSFTW', symObjAddr: 0x480, symBinAddr: 0x47F78, symSize: 0x30 }
  - { offsetInCU: 0x424, offset: 0x8BD4A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE8getFloatySfSgSSF', symObjAddr: 0x4B0, symBinAddr: 0x47FA8, symSize: 0x21C }
  - { offsetInCU: 0x4CA, offset: 0x8BDF0, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSDoubleContainerA2cDP9getDoubleySdSS_SdtFTW', symObjAddr: 0x6CC, symBinAddr: 0x481C4, symSize: 0x4 }
  - { offsetInCU: 0x4E6, offset: 0x8BE0C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17JSDoubleContainerPAAE9getDoubleySdSS_SdtF', symObjAddr: 0x6D0, symBinAddr: 0x481C8, symSize: 0x30 }
  - { offsetInCU: 0x542, offset: 0x8BE68, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSDoubleContainerA2cDP9getDoubleySdSgSSFTW', symObjAddr: 0x700, symBinAddr: 0x481F8, symSize: 0x20 }
  - { offsetInCU: 0x55E, offset: 0x8BE84, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE9getDoubleySdSgSSF', symObjAddr: 0x720, symBinAddr: 0x48218, symSize: 0x118 }
  - { offsetInCU: 0x5CD, offset: 0x8BEF3, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor15JSDateContainerA2cDP7getDatey10Foundation0G0VSS_AItFTW', symObjAddr: 0x838, symBinAddr: 0x48330, symSize: 0x4 }
  - { offsetInCU: 0x5E9, offset: 0x8BF0F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15JSDateContainerPAAE7getDatey10Foundation0E0VSS_AGtF', symObjAddr: 0x83C, symBinAddr: 0x48334, symSize: 0x170 }
  - { offsetInCU: 0x646, offset: 0x8BF6C, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor15JSDateContainerA2cDP7getDatey10Foundation0G0VSgSSFTW', symObjAddr: 0x9AC, symBinAddr: 0x484A4, symSize: 0xC }
  - { offsetInCU: 0x662, offset: 0x8BF88, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE7getDatey10Foundation0E0VSgSSF', symObjAddr: 0x9B8, symBinAddr: 0x484B0, symSize: 0x348 }
  - { offsetInCU: 0x70A, offset: 0x8C030, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSArrayContainerA2cDP8getArrayySayAC7JSValue_pGSS_AHtFTW', symObjAddr: 0xD00, symBinAddr: 0x487F8, symSize: 0x4 }
  - { offsetInCU: 0x726, offset: 0x8C04C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSArrayContainerPAAE8getArrayySayAA7JSValue_pGSS_AFtF', symObjAddr: 0xD04, symBinAddr: 0x487FC, symSize: 0x40 }
  - { offsetInCU: 0x783, offset: 0x8C0A9, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSArrayContainerA2cDP8getArrayySayqd__GSgSS_qd__mtlFTW', symObjAddr: 0xD44, symBinAddr: 0x4883C, symSize: 0x10 }
  - { offsetInCU: 0x79F, offset: 0x8C0C5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSArrayContainerPAAE8getArrayySayqd__GSgSS_qd__mtlF', symObjAddr: 0xD54, symBinAddr: 0x4884C, symSize: 0x60 }
  - { offsetInCU: 0x806, offset: 0x8C12C, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor16JSArrayContainerA2cDP8getArrayySayAC7JSValue_pGSgSSFTW', symObjAddr: 0xDB4, symBinAddr: 0x488AC, symSize: 0xC }
  - { offsetInCU: 0x822, offset: 0x8C148, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSObjectContainerA2cDP9getObjectySDySSAC7JSValue_pGSS_AHtFTW', symObjAddr: 0xDCC, symBinAddr: 0x488C4, symSize: 0x4 }
  - { offsetInCU: 0x83E, offset: 0x8C164, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17JSObjectContainerPAAE9getObjectySDySSAA7JSValue_pGSS_AFtF', symObjAddr: 0xDD0, symBinAddr: 0x488C8, symSize: 0x40 }
  - { offsetInCU: 0x89B, offset: 0x8C1C1, size: 0x8, addend: 0x0, symName: '_$sSo13CAPPluginCallC9Capacitor17JSObjectContainerA2cDP9getObjectySDySSAC7JSValue_pGSgSSFTW', symObjAddr: 0xE10, symBinAddr: 0x48908, symSize: 0xC }
  - { offsetInCU: 0x8B7, offset: 0x8C1DD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE8getValueyAA0B0_pSgSSF', symObjAddr: 0xF40, symBinAddr: 0x48A38, symSize: 0xA4 }
  - { offsetInCU: 0x926, offset: 0x8C24C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE6getAnyyypSgSSF', symObjAddr: 0xFE4, symBinAddr: 0x48ADC, symSize: 0x90 }
  - { offsetInCU: 0x973, offset: 0x8C299, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16JSValueContainerPAAE6decode_3for4withqd__qd__m_SSAA0B7DecoderCtKSeRd__lF', symObjAddr: 0x1074, symBinAddr: 0x48B6C, symSize: 0x184 }
  - { offsetInCU: 0xA2B, offset: 0x8C351, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSTypesO26coerceDictionaryToJSObject_24formattingDatesAsStringsSDySSAA7JSValue_pGSgSo12NSDictionaryCSg_SbtFZ', symObjAddr: 0x11F8, symBinAddr: 0x48CF0, symSize: 0xE0 }
  - { offsetInCU: 0xA93, offset: 0x8C3B9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15coerceToJSValue33_883E0F63E6283FEB7FEBC7F75A4BFDE5LL_15formattingDatesAA0D0_pSgypSg_SbtF', symObjAddr: 0x12D8, symBinAddr: 0x48DD0, symSize: 0xB7C }
  - { offsetInCU: 0x13AB, offset: 0x8CCD1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSTypesO20coerceArrayToJSArray_24formattingDatesAsStringsSayAA7JSValue_pGSgSayypGSg_SbtFZ', symObjAddr: 0x1E54, symBinAddr: 0x4994C, symSize: 0x18C }
  - { offsetInCU: 0x1613, offset: 0x8CF39, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19dateStringFormatter33_883E0F63E6283FEB7FEBC7F75A4BFDE5LL_WZ', symObjAddr: 0x1FE0, symBinAddr: 0x49AD8, symSize: 0x30 }
  - { offsetInCU: 0x1658, offset: 0x8CF7E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSTypesO26coerceDictionaryToJSObject_24formattingDatesAsStringsSDySSAA7JSValue_pGSgSDys11AnyHashableVypGSg_SbtFZTf4nnd_n', symObjAddr: 0x2010, symBinAddr: 0x49B08, symSize: 0xD4 }
  - { offsetInCU: 0x1697, offset: 0x8CFBD, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DateVSgWOb', symObjAddr: 0x2168, symBinAddr: 0x49BDC, symSize: 0x48 }
  - { offsetInCU: 0x16AB, offset: 0x8CFD1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSTypesOMa', symObjAddr: 0x2284, symBinAddr: 0x49C80, symSize: 0x10 }
  - { offsetInCU: 0x16BF, offset: 0x8CFE5, size: 0x8, addend: 0x0, symName: '_$s10Foundation25NSFastEnumerationIteratorVACStAAWl', symObjAddr: 0x2320, symBinAddr: 0x49D0C, symSize: 0x48 }
  - { offsetInCU: 0xD4, offset: 0x8D717, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy04dataeF0018nonConformingFloateF0AC10Foundation11JSONDecoderC04DateeF0O_AI04DataeF0OAC03NonijeF0OtcfC', symObjAddr: 0x0, symBinAddr: 0x49D90, symSize: 0x114 }
  - { offsetInCU: 0x13E, offset: 0x8D781, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC6decode_4fromxxm_AA0B0_ptKSeRzlF', symObjAddr: 0x114, symBinAddr: 0x49EA4, symSize: 0x144 }
  - { offsetInCU: 0x211, offset: 0x8D854, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy04dataeF0018nonConformingFloateF0AC10Foundation11JSONDecoderC04DateeF0O_AI04DataeF0OAC03NonijeF0Otcfc', symObjAddr: 0x258, symBinAddr: 0x49FE8, symSize: 0xFC }
  - { offsetInCU: 0x263, offset: 0x8D8A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy10Foundation11JSONDecoderC04DateeF0Ovg', symObjAddr: 0x41C, symBinAddr: 0x4A1AC, symSize: 0x74 }
  - { offsetInCU: 0x282, offset: 0x8D8C5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy10Foundation11JSONDecoderC04DateeF0Ovs', symObjAddr: 0x490, symBinAddr: 0x4A220, symSize: 0x98 }
  - { offsetInCU: 0x2C9, offset: 0x8D90C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy10Foundation11JSONDecoderC04DateeF0OvM', symObjAddr: 0x528, symBinAddr: 0x4A2B8, symSize: 0xCC }
  - { offsetInCU: 0x327, offset: 0x8D96A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dateDecodingStrategy10Foundation11JSONDecoderC04DateeF0OvM.resume.0', symObjAddr: 0x5F4, symBinAddr: 0x4A384, symSize: 0x124 }
  - { offsetInCU: 0x35C, offset: 0x8D99F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dataDecodingStrategy10Foundation11JSONDecoderC04DataeF0Ovg', symObjAddr: 0x718, symBinAddr: 0x4A4A8, symSize: 0x64 }
  - { offsetInCU: 0x37B, offset: 0x8D9BE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dataDecodingStrategy10Foundation11JSONDecoderC04DataeF0Ovs', symObjAddr: 0x77C, symBinAddr: 0x4A50C, symSize: 0x88 }
  - { offsetInCU: 0x3C2, offset: 0x8DA05, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dataDecodingStrategy10Foundation11JSONDecoderC04DataeF0OvM', symObjAddr: 0x804, symBinAddr: 0x4A594, symSize: 0xBC }
  - { offsetInCU: 0x420, offset: 0x8DA63, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC20dataDecodingStrategy10Foundation11JSONDecoderC04DataeF0OvM.resume.0', symObjAddr: 0x8C0, symBinAddr: 0x4A650, symSize: 0x118 }
  - { offsetInCU: 0x455, offset: 0x8DA98, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34nonConformingFloatDecodingStrategyAC03NonefgH0Ovg', symObjAddr: 0x9D8, symBinAddr: 0x4A768, symSize: 0xA0 }
  - { offsetInCU: 0x474, offset: 0x8DAB7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34nonConformingFloatDecodingStrategyAC03NonefgH0Ovs', symObjAddr: 0xA78, symBinAddr: 0x4A808, symSize: 0x9C }
  - { offsetInCU: 0x4BB, offset: 0x8DAFE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34nonConformingFloatDecodingStrategyAC03NonefgH0OvM', symObjAddr: 0xB14, symBinAddr: 0x4A8A4, symSize: 0x94 }
  - { offsetInCU: 0x519, offset: 0x8DB5C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34nonConformingFloatDecodingStrategyAC03NonefgH0OvM.resume.0', symObjAddr: 0xBA8, symBinAddr: 0x4A938, symSize: 0xC4 }
  - { offsetInCU: 0x55E, offset: 0x8DBA1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderCfd', symObjAddr: 0xE38, symBinAddr: 0x4ABC8, symSize: 0x24 }
  - { offsetInCU: 0x599, offset: 0x8DBDC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderCfD', symObjAddr: 0xE5C, symBinAddr: 0x4ABEC, symSize: 0x30 }
  - { offsetInCU: 0x5E4, offset: 0x8DC27, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Combine08TopLevelC0AadEP6decode_4fromqd__qd__m_5InputQztKSeRd__lFTW', symObjAddr: 0xE8C, symBinAddr: 0x4AC1C, symSize: 0x20 }
  - { offsetInCU: 0x67F, offset: 0x8DCC2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCfD', symObjAddr: 0xEAC, symBinAddr: 0x4AC3C, symSize: 0x50 }
  - { offsetInCU: 0x6D1, offset: 0x8DD14, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0C0AAsAEP10codingPathSays9CodingKey_pGvgTW', symObjAddr: 0x2840, symBinAddr: 0x4C5D0, symSize: 0xC }
  - { offsetInCU: 0x749, offset: 0x8DD8C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCfd', symObjAddr: 0x28AC, symBinAddr: 0x4C63C, symSize: 0x44 }
  - { offsetInCU: 0x776, offset: 0x8DDB9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCfD', symObjAddr: 0x28F0, symBinAddr: 0x4C680, symSize: 0x20 }
  - { offsetInCU: 0x7C1, offset: 0x8DE04, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP10codingPathSays9CodingKey_pGvgTW', symObjAddr: 0x4664, symBinAddr: 0x4E3F4, symSize: 0xC }
  - { offsetInCU: 0x802, offset: 0x8DE45, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP7allKeysSay3KeyQzGvgTW', symObjAddr: 0x4670, symBinAddr: 0x4E400, symSize: 0xC }
  - { offsetInCU: 0x834, offset: 0x8DE77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC4data10codingPath8userInfo7optionsADyxGSDySSAA7JSValue_pG_Says9CodingKey_pGSDys0s4UserpT0VypGAA0R7DecoderC7OptionsACLLVtcfcTf4gggnn_n', symObjAddr: 0x68F8, symBinAddr: 0x50688, symSize: 0x100 }
  - { offsetInCU: 0x976, offset: 0x8DFB9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCfD', symObjAddr: 0x4D3C, symBinAddr: 0x4EACC, symSize: 0x48 }
  - { offsetInCU: 0x9CB, offset: 0x8E00E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP12currentIndexSivgTW', symObjAddr: 0x5610, symBinAddr: 0x4F3A0, symSize: 0xC }
  - { offsetInCU: 0xA91, offset: 0x8E0D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCfD', symObjAddr: 0x5B18, symBinAddr: 0x4F8A8, symSize: 0x48 }
  - { offsetInCU: 0xAE6, offset: 0x8E129, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP10codingPathSays9CodingKey_pGvgTW', symObjAddr: 0x6598, symBinAddr: 0x50328, symSize: 0xC }
  - { offsetInCU: 0xB22, offset: 0x8E165, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOWOb', symObjAddr: 0x368, symBinAddr: 0x4A0F8, symSize: 0x18 }
  - { offsetInCU: 0xB3A, offset: 0x8E17D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOWOb', symObjAddr: 0x368, symBinAddr: 0x4A0F8, symSize: 0x18 }
  - { offsetInCU: 0xB4B, offset: 0x8E18E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVWOb', symObjAddr: 0x380, symBinAddr: 0x4A110, symSize: 0x44 }
  - { offsetInCU: 0xB5F, offset: 0x8E1A2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVWOc', symObjAddr: 0x3C4, symBinAddr: 0x4A154, symSize: 0x44 }
  - { offsetInCU: 0xB73, offset: 0x8E1B6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC10decodeData2asxxm_tKSeRzlF', symObjAddr: 0xC6C, symBinAddr: 0x4A9FC, symSize: 0x1CC }
  - { offsetInCU: 0xBC2, offset: 0x8E205, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC9container7keyedBys22KeyedDecodingContainerVyxGxm_tKs9CodingKeyRzlF', symObjAddr: 0xEFC, symBinAddr: 0x4AC8C, symSize: 0x208 }
  - { offsetInCU: 0xC72, offset: 0x8E2B5, size: 0x8, addend: 0x0, symName: '_$ss13DecodingErrorO9CapacitorE12typeMismatch_2on10codingPathABypXp_AC7JSValue_pSays9CodingKey_pGtFZ', symObjAddr: 0x1104, symBinAddr: 0x4AE94, symSize: 0x138 }
  - { offsetInCU: 0xD65, offset: 0x8E3A8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC16unkeyedContainers015UnkeyedDecodingM0_pyKF', symObjAddr: 0x123C, symBinAddr: 0x4AFCC, symSize: 0x1E8 }
  - { offsetInCU: 0xDF7, offset: 0x8E43A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC20singleValueContainers06Singlem8DecodingN0_pyKF', symObjAddr: 0x1424, symBinAddr: 0x4B1B4, symSize: 0x130 }
  - { offsetInCU: 0xECF, offset: 0x8E512, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC10decodeDate10Foundation0M0VyKF', symObjAddr: 0x1554, symBinAddr: 0x4B2E4, symSize: 0xBC8 }
  - { offsetInCU: 0x120A, offset: 0x8E84D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC9decodeUrl10Foundation3URLVyKF', symObjAddr: 0x211C, symBinAddr: 0x4BEAC, symSize: 0x230 }
  - { offsetInCU: 0x130E, offset: 0x8E951, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLC10decodeData10Foundation0M0VyKF', symObjAddr: 0x234C, symBinAddr: 0x4C0DC, symSize: 0x4F4 }
  - { offsetInCU: 0x1494, offset: 0x8EAD7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0C0AAsAEP9container7keyedBys22KeyedDecodingContainerVyqd__Gqd__m_tKs9CodingKeyRd__lFTW', symObjAddr: 0x284C, symBinAddr: 0x4C5DC, symSize: 0x20 }
  - { offsetInCU: 0x14B7, offset: 0x8EAFA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0C0AAsAEP16unkeyedContainers015UnkeyedDecodingM0_pyKFTW', symObjAddr: 0x286C, symBinAddr: 0x4C5FC, symSize: 0x20 }
  - { offsetInCU: 0x14DA, offset: 0x8EB1D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0C0AAsAEP20singleValueContainers06Singlem8DecodingN0_pyKFTW', symObjAddr: 0x288C, symBinAddr: 0x4C61C, symSize: 0x20 }
  - { offsetInCU: 0x14FD, offset: 0x8EB40, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8containsySbxF', symObjAddr: 0x2910, symBinAddr: 0x4C6A0, symSize: 0x94 }
  - { offsetInCU: 0x1549, offset: 0x8EB8C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8containsySbxFSbxXEfU_', symObjAddr: 0x29A4, symBinAddr: 0x4C734, symSize: 0xBC }
  - { offsetInCU: 0x15D9, offset: 0x8EC1C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC9decodeNil6forKeySbx_tKF', symObjAddr: 0x2A60, symBinAddr: 0x4C7F0, symSize: 0x2B0 }
  - { offsetInCU: 0x16C5, offset: 0x8ED08, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decode_6forKeyqd__qd__m_xtKSeRd__lF', symObjAddr: 0x2D10, symBinAddr: 0x4CAA0, symSize: 0x3E0 }
  - { offsetInCU: 0x18D4, offset: 0x8EF17, size: 0x8, addend: 0x0, symName: '_$ss13DecodingErrorO9CapacitorE11keyNotFound_2on10codingPathABs9CodingKey_p_AC7JSValue_pSaysAG_pGtFZ', symObjAddr: 0x30F0, symBinAddr: 0x4CE80, symSize: 0x168 }
  - { offsetInCU: 0x1A09, offset: 0x8F04C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC013nestedUnkeyedC06forKeys0m8DecodingC0_px_tKF', symObjAddr: 0x3258, symBinAddr: 0x4CFE8, symSize: 0x4D4 }
  - { offsetInCU: 0x1BD2, offset: 0x8F215, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC06nestedC07keyedBy6forKeys0b8DecodingC0Vyqd__Gqd__m_xtKs06CodingP0Rd__lF', symObjAddr: 0x372C, symBinAddr: 0x4D4BC, symSize: 0x4FC }
  - { offsetInCU: 0x1DAB, offset: 0x8F3EE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyO8rawValueAFyx_GSgSS_tcfC', symObjAddr: 0x3C28, symBinAddr: 0x4D9B8, symSize: 0x58 }
  - { offsetInCU: 0x1DE4, offset: 0x8F427, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyO11stringValueAFyx_GSgSS_tcfC', symObjAddr: 0x3C80, symBinAddr: 0x4DA10, symSize: 0x18 }
  - { offsetInCU: 0x1E1D, offset: 0x8F460, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSYAASY8rawValuexSg03RawO0Qz_tcfCTW', symObjAddr: 0x3DD8, symBinAddr: 0x4DB68, symSize: 0x38 }
  - { offsetInCU: 0x1E48, offset: 0x8F48B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSYAASY8rawValue03RawO0QzvgTW', symObjAddr: 0x3E10, symBinAddr: 0x4DBA0, symSize: 0x24 }
  - { offsetInCU: 0x1E97, offset: 0x8F4DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x3E34, symBinAddr: 0x4DBC4, symSize: 0x4 }
  - { offsetInCU: 0x1EB7, offset: 0x8F4FA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x3E34, symBinAddr: 0x4DBC4, symSize: 0x4 }
  - { offsetInCU: 0x1ECB, offset: 0x8F50E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x3E34, symBinAddr: 0x4DBC4, symSize: 0x4 }
  - { offsetInCU: 0x1EDF, offset: 0x8F522, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x3E34, symBinAddr: 0x4DBC4, symSize: 0x4 }
  - { offsetInCU: 0x1EF2, offset: 0x8F535, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValuexSgSS_tcfCTW', symObjAddr: 0x3E38, symBinAddr: 0x4DBC8, symSize: 0x30 }
  - { offsetInCU: 0x1F0E, offset: 0x8F551, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP8intValueSiSgvgTW', symObjAddr: 0x3E68, symBinAddr: 0x4DBF8, symSize: 0xC }
  - { offsetInCU: 0x1F39, offset: 0x8F57C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAsAHP8intValuexSgSi_tcfCTW', symObjAddr: 0x3E74, symBinAddr: 0x4DC04, symSize: 0x2C }
  - { offsetInCU: 0x1F6A, offset: 0x8F5AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyO8rawValueSSvgTf4d_n', symObjAddr: 0x68E4, symBinAddr: 0x50674, symSize: 0x14 }
  - { offsetInCU: 0x1F86, offset: 0x8F5C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyO8intValueAFyx_GSgSi_tcfCTf4dd_n', symObjAddr: 0x7C5C, symBinAddr: 0x5187C, symSize: 0x8 }
  - { offsetInCU: 0x1FBD, offset: 0x8F600, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC12superDecoders0M0_pyKF', symObjAddr: 0x3F08, symBinAddr: 0x4DC98, symSize: 0x35C }
  - { offsetInCU: 0x217E, offset: 0x8F7C1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC12superDecoder6forKeys0M0_px_tKF', symObjAddr: 0x4264, symBinAddr: 0x4DFF4, symSize: 0x400 }
  - { offsetInCU: 0x2334, offset: 0x8F977, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP8containsySb3KeyQzFTW', symObjAddr: 0x467C, symBinAddr: 0x4E40C, symSize: 0x24 }
  - { offsetInCU: 0x2350, offset: 0x8F993, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP9decodeNil6forKeySb0Q0Qz_tKFTW', symObjAddr: 0x46A0, symBinAddr: 0x4E430, symSize: 0x24 }
  - { offsetInCU: 0x2373, offset: 0x8F9B6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP6decode_6forKeyS2bm_0P0QztKFTW', symObjAddr: 0x46C4, symBinAddr: 0x4E454, symSize: 0x4C }
  - { offsetInCU: 0x2396, offset: 0x8F9D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP6decode_6forKeyS2Sm_0P0QztKFTW', symObjAddr: 0x4710, symBinAddr: 0x4E4A0, symSize: 0x50 }
  - { offsetInCU: 0x23B9, offset: 0x8F9FC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP6decode_6forKeyS2dm_0P0QztKFTW', symObjAddr: 0x4760, symBinAddr: 0x4E4F0, symSize: 0x48 }
  - { offsetInCU: 0x23DC, offset: 0x8FA1F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP6decode_6forKeyS2fm_0P0QztKFTW', symObjAddr: 0x47A8, symBinAddr: 0x4E538, symSize: 0x48 }
  - { offsetInCU: 0x23FF, offset: 0x8FA42, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP6decode_6forKeyqd__qd__m_0P0QztKSeRd__lFTW', symObjAddr: 0x4A58, symBinAddr: 0x4E7E8, symSize: 0x20 }
  - { offsetInCU: 0x2422, offset: 0x8FA65, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP06nestedC07keyedBy6forKeys0blC0Vyqd__Gqd__m_0R0QztKs06CodingR0Rd__lFTW', symObjAddr: 0x4CBC, symBinAddr: 0x4EA4C, symSize: 0x20 }
  - { offsetInCU: 0x2445, offset: 0x8FA88, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP013nestedUnkeyedC06forKeys0olC0_p0Q0Qz_tKFTW', symObjAddr: 0x4CDC, symBinAddr: 0x4EA6C, symSize: 0x20 }
  - { offsetInCU: 0x2468, offset: 0x8FAAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP12superDecoders0O0_pyKFTW', symObjAddr: 0x4CFC, symBinAddr: 0x4EA8C, symSize: 0x20 }
  - { offsetInCU: 0x248B, offset: 0x8FACE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP12superDecoder6forKeys0O0_p0Q0Qz_tKFTW', symObjAddr: 0x4D1C, symBinAddr: 0x4EAAC, symSize: 0x20 }
  - { offsetInCU: 0x24C4, offset: 0x8FB07, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC9decodeNilSbyKF', symObjAddr: 0x4D84, symBinAddr: 0x4EB14, symSize: 0xC4 }
  - { offsetInCU: 0x2584, offset: 0x8FBC7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlF', symObjAddr: 0x4E48, symBinAddr: 0x4EBD8, symSize: 0x17C }
  - { offsetInCU: 0x26F1, offset: 0x8FD34, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC06nestedbC0s0b8DecodingC0_pyKF', symObjAddr: 0x4FC4, symBinAddr: 0x4ED54, symSize: 0x248 }
  - { offsetInCU: 0x287B, offset: 0x8FEBE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC06nestedC07keyedBys013KeyedDecodingC0VyxGxm_tKs9CodingKeyRzlF', symObjAddr: 0x520C, symBinAddr: 0x4EF9C, symSize: 0x274 }
  - { offsetInCU: 0x2A16, offset: 0x90059, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC12superDecoders0M0_pyKF', symObjAddr: 0x5480, symBinAddr: 0x4F210, symSize: 0x160 }
  - { offsetInCU: 0x2B6A, offset: 0x901AD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP5countSiSgvgTW', symObjAddr: 0x55E0, symBinAddr: 0x4F370, symSize: 0x14 }
  - { offsetInCU: 0x2C08, offset: 0x9024B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP7isAtEndSbvgTW', symObjAddr: 0x55F4, symBinAddr: 0x4F384, symSize: 0x1C }
  - { offsetInCU: 0x2C86, offset: 0x902C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP9decodeNilSbyKFTW', symObjAddr: 0x561C, symBinAddr: 0x4F3AC, symSize: 0x24 }
  - { offsetInCU: 0x2CBF, offset: 0x90302, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2bmKFTW', symObjAddr: 0x5640, symBinAddr: 0x4F3D0, symSize: 0x24 }
  - { offsetInCU: 0x2D0E, offset: 0x90351, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2SmKFTW', symObjAddr: 0x5664, symBinAddr: 0x4F3F4, symSize: 0x20 }
  - { offsetInCU: 0x2D5D, offset: 0x903A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2dmKFTW', symObjAddr: 0x5684, symBinAddr: 0x4F414, symSize: 0x20 }
  - { offsetInCU: 0x2DAC, offset: 0x903EF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2fmKFTW', symObjAddr: 0x56A4, symBinAddr: 0x4F434, symSize: 0x20 }
  - { offsetInCU: 0x2DFB, offset: 0x9043E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2imKFTW', symObjAddr: 0x56C4, symBinAddr: 0x4F454, symSize: 0x28 }
  - { offsetInCU: 0x2E4A, offset: 0x9048D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys4Int8VAImKFTW', symObjAddr: 0x56EC, symBinAddr: 0x4F47C, symSize: 0x28 }
  - { offsetInCU: 0x2E99, offset: 0x904DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys5Int16VAImKFTW', symObjAddr: 0x5714, symBinAddr: 0x4F4A4, symSize: 0x28 }
  - { offsetInCU: 0x2EE8, offset: 0x9052B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys5Int32VAImKFTW', symObjAddr: 0x573C, symBinAddr: 0x4F4CC, symSize: 0x28 }
  - { offsetInCU: 0x2F37, offset: 0x9057A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys5Int64VAImKFTW', symObjAddr: 0x5764, symBinAddr: 0x4F4F4, symSize: 0x28 }
  - { offsetInCU: 0x2F86, offset: 0x905C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyS2umKFTW', symObjAddr: 0x578C, symBinAddr: 0x4F51C, symSize: 0x28 }
  - { offsetInCU: 0x2FD5, offset: 0x90618, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys5UInt8VAImKFTW', symObjAddr: 0x57B4, symBinAddr: 0x4F544, symSize: 0x28 }
  - { offsetInCU: 0x3024, offset: 0x90667, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys6UInt16VAImKFTW', symObjAddr: 0x57DC, symBinAddr: 0x4F56C, symSize: 0x28 }
  - { offsetInCU: 0x3073, offset: 0x906B6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys6UInt32VAImKFTW', symObjAddr: 0x5804, symBinAddr: 0x4F594, symSize: 0x28 }
  - { offsetInCU: 0x30C2, offset: 0x90705, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeys6UInt64VAImKFTW', symObjAddr: 0x582C, symBinAddr: 0x4F5BC, symSize: 0x28 }
  - { offsetInCU: 0x30FB, offset: 0x9073E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP6decodeyqd__qd__mKSeRd__lFTW', symObjAddr: 0x5854, symBinAddr: 0x4F5E4, symSize: 0x20 }
  - { offsetInCU: 0x311E, offset: 0x90761, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP06nestedC07keyedBys05KeyedlC0Vyqd__Gqd__m_tKs9CodingKeyRd__lFTW', symObjAddr: 0x5AB8, symBinAddr: 0x4F848, symSize: 0x20 }
  - { offsetInCU: 0x3141, offset: 0x90784, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP06nestedbC0sAE_pyKFTW', symObjAddr: 0x5AD8, symBinAddr: 0x4F868, symSize: 0x20 }
  - { offsetInCU: 0x3164, offset: 0x907A7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP12superDecoders0N0_pyKFTW', symObjAddr: 0x5AF8, symBinAddr: 0x4F888, symSize: 0x20 }
  - { offsetInCU: 0x3187, offset: 0x907CA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC4cast2toxxm_tKlFSd_Tg5', symObjAddr: 0x5B60, symBinAddr: 0x4F8F0, symSize: 0xE0 }
  - { offsetInCU: 0x31CF, offset: 0x90812, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC4cast2toxxm_tKlFSf_Tg5', symObjAddr: 0x5C40, symBinAddr: 0x4F9D0, symSize: 0xE0 }
  - { offsetInCU: 0x322D, offset: 0x90870, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC9castFloat2toxxm_tKSFRzlFSd_Tg5', symObjAddr: 0x5D20, symBinAddr: 0x4FAB0, symSize: 0x3AC }
  - { offsetInCU: 0x33E5, offset: 0x90A28, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC9castFloat2toxxm_tKSFRzlFSf_Tg5', symObjAddr: 0x60CC, symBinAddr: 0x4FE5C, symSize: 0x3AC }
  - { offsetInCU: 0x3587, offset: 0x90BCA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlF', symObjAddr: 0x6478, symBinAddr: 0x50208, symSize: 0x120 }
  - { offsetInCU: 0x3673, offset: 0x90CB6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP9decodeNilSbyFTW', symObjAddr: 0x65A4, symBinAddr: 0x50334, symSize: 0x84 }
  - { offsetInCU: 0x36C7, offset: 0x90D0A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2bmKFTW', symObjAddr: 0x6628, symBinAddr: 0x503B8, symSize: 0x24 }
  - { offsetInCU: 0x3716, offset: 0x90D59, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2SmKFTW', symObjAddr: 0x664C, symBinAddr: 0x503DC, symSize: 0x20 }
  - { offsetInCU: 0x3782, offset: 0x90DC5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2dmKFTW', symObjAddr: 0x666C, symBinAddr: 0x503FC, symSize: 0x28 }
  - { offsetInCU: 0x37FE, offset: 0x90E41, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2fmKFTW', symObjAddr: 0x6694, symBinAddr: 0x50424, symSize: 0x28 }
  - { offsetInCU: 0x385D, offset: 0x90EA0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2imKFTW', symObjAddr: 0x66BC, symBinAddr: 0x5044C, symSize: 0x28 }
  - { offsetInCU: 0x38AC, offset: 0x90EEF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys4Int8VAImKFTW', symObjAddr: 0x66E4, symBinAddr: 0x50474, symSize: 0x28 }
  - { offsetInCU: 0x38FB, offset: 0x90F3E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys5Int16VAImKFTW', symObjAddr: 0x670C, symBinAddr: 0x5049C, symSize: 0x28 }
  - { offsetInCU: 0x394A, offset: 0x90F8D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys5Int32VAImKFTW', symObjAddr: 0x6734, symBinAddr: 0x504C4, symSize: 0x28 }
  - { offsetInCU: 0x3999, offset: 0x90FDC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys5Int64VAImKFTW', symObjAddr: 0x675C, symBinAddr: 0x504EC, symSize: 0x28 }
  - { offsetInCU: 0x39E8, offset: 0x9102B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyS2umKFTW', symObjAddr: 0x6784, symBinAddr: 0x50514, symSize: 0x28 }
  - { offsetInCU: 0x3A37, offset: 0x9107A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys5UInt8VAImKFTW', symObjAddr: 0x67AC, symBinAddr: 0x5053C, symSize: 0x28 }
  - { offsetInCU: 0x3A86, offset: 0x910C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys6UInt16VAImKFTW', symObjAddr: 0x67D4, symBinAddr: 0x50564, symSize: 0x28 }
  - { offsetInCU: 0x3AD5, offset: 0x91118, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys6UInt32VAImKFTW', symObjAddr: 0x67FC, symBinAddr: 0x5058C, symSize: 0x28 }
  - { offsetInCU: 0x3B24, offset: 0x91167, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeys6UInt64VAImKFTW', symObjAddr: 0x6824, symBinAddr: 0x505B4, symSize: 0x28 }
  - { offsetInCU: 0x3B5D, offset: 0x911A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0bc8DecodingD0AAsAEP6decodeyqd__qd__mKSeRd__lFTW', symObjAddr: 0x684C, symBinAddr: 0x505DC, symSize: 0x20 }
  - { offsetInCU: 0x3BA1, offset: 0x911E4, size: 0x8, addend: 0x0, symName: '_$sSa37_appendElementAssumeUniqueAndCapacity_03newB0ySi_xntFs9CodingKey_p_Tg5Tf4nen_n', symObjAddr: 0x686C, symBinAddr: 0x505FC, symSize: 0x78 }
  - { offsetInCU: 0x3C05, offset: 0x91248, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVWOh', symObjAddr: 0x6A18, symBinAddr: 0x50788, symSize: 0x3C }
  - { offsetInCU: 0x3C19, offset: 0x9125C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOWOy', symObjAddr: 0x6A54, symBinAddr: 0x507C4, symSize: 0x40 }
  - { offsetInCU: 0x3C2D, offset: 0x91270, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOWOe', symObjAddr: 0x6A94, symBinAddr: 0x50804, symSize: 0x40 }
  - { offsetInCU: 0x3C41, offset: 0x91284, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderCMU', symObjAddr: 0x6AD4, symBinAddr: 0x50844, symSize: 0x8 }
  - { offsetInCU: 0x3C55, offset: 0x91298, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderCMr', symObjAddr: 0x6AF0, symBinAddr: 0x50860, symSize: 0x6C }
  - { offsetInCU: 0x3C69, offset: 0x912AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwCP', symObjAddr: 0x6B5C, symBinAddr: 0x508CC, symSize: 0x30 }
  - { offsetInCU: 0x3C7D, offset: 0x912C0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwxx', symObjAddr: 0x6B8C, symBinAddr: 0x508FC, symSize: 0x48 }
  - { offsetInCU: 0x3C91, offset: 0x912D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwcp', symObjAddr: 0x6BD4, symBinAddr: 0x50944, symSize: 0x78 }
  - { offsetInCU: 0x3CA5, offset: 0x912E8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwca', symObjAddr: 0x6C4C, symBinAddr: 0x509BC, symSize: 0x128 }
  - { offsetInCU: 0x3CB9, offset: 0x912FC, size: 0x8, addend: 0x0, symName: ___swift_memcpy48_8, symObjAddr: 0x6D74, symBinAddr: 0x50AE4, symSize: 0x14 }
  - { offsetInCU: 0x3CCD, offset: 0x91310, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwta', symObjAddr: 0x6D88, symBinAddr: 0x50AF8, symSize: 0x98 }
  - { offsetInCU: 0x3CE1, offset: 0x91324, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwet', symObjAddr: 0x6E20, symBinAddr: 0x50B90, symSize: 0x54 }
  - { offsetInCU: 0x3CF5, offset: 0x91338, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwst', symObjAddr: 0x6E74, symBinAddr: 0x50BE4, symSize: 0x58 }
  - { offsetInCU: 0x3D09, offset: 0x9134C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwug', symObjAddr: 0x6ECC, symBinAddr: 0x50C3C, symSize: 0x18 }
  - { offsetInCU: 0x3D1D, offset: 0x91360, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwup', symObjAddr: 0x6EE4, symBinAddr: 0x50C54, symSize: 0x4 }
  - { offsetInCU: 0x3D31, offset: 0x91374, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOwui', symObjAddr: 0x6EE8, symBinAddr: 0x50C58, symSize: 0x30 }
  - { offsetInCU: 0x3D45, offset: 0x91388, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC34NonConformingFloatDecodingStrategyOMa', symObjAddr: 0x6F18, symBinAddr: 0x50C88, symSize: 0x10 }
  - { offsetInCU: 0x3D59, offset: 0x9139C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCMU', symObjAddr: 0x6F28, symBinAddr: 0x50C98, symSize: 0x8 }
  - { offsetInCU: 0x3D6D, offset: 0x913B0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueDecoder33_DDEFC033B005E1F1FC949B68024B9ED4LLCMr', symObjAddr: 0x6F30, symBinAddr: 0x50CA0, symSize: 0x84 }
  - { offsetInCU: 0x3D81, offset: 0x913C4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMi', symObjAddr: 0x6FB4, symBinAddr: 0x50D24, symSize: 0x4 }
  - { offsetInCU: 0x3D95, offset: 0x913D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMr', symObjAddr: 0x6FB8, symBinAddr: 0x50D28, symSize: 0x80 }
  - { offsetInCU: 0x3DA9, offset: 0x913EC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMa', symObjAddr: 0x7038, symBinAddr: 0x50DA8, symSize: 0xC }
  - { offsetInCU: 0x3DBD, offset: 0x91400, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMU', symObjAddr: 0x7044, symBinAddr: 0x50DB4, symSize: 0x8 }
  - { offsetInCU: 0x3DD1, offset: 0x91414, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMr', symObjAddr: 0x7060, symBinAddr: 0x50DD0, symSize: 0x8C }
  - { offsetInCU: 0x3DE5, offset: 0x91428, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMU', symObjAddr: 0x70EC, symBinAddr: 0x50E5C, symSize: 0x8 }
  - { offsetInCU: 0x3DF9, offset: 0x9143C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCMr', symObjAddr: 0x713C, symBinAddr: 0x50EAC, symSize: 0x88 }
  - { offsetInCU: 0x3E0D, offset: 0x91450, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwCP', symObjAddr: 0x71C4, symBinAddr: 0x50F34, symSize: 0x108 }
  - { offsetInCU: 0x3E21, offset: 0x91464, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwxx', symObjAddr: 0x72CC, symBinAddr: 0x5103C, symSize: 0x9C }
  - { offsetInCU: 0x3E35, offset: 0x91478, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwcp', symObjAddr: 0x7368, symBinAddr: 0x510D8, symSize: 0xDC }
  - { offsetInCU: 0x3E49, offset: 0x9148C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwca', symObjAddr: 0x7444, symBinAddr: 0x511B4, symSize: 0x18C }
  - { offsetInCU: 0x3E5D, offset: 0x914A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwtk', symObjAddr: 0x7610, symBinAddr: 0x51340, symSize: 0xA0 }
  - { offsetInCU: 0x3E71, offset: 0x914B4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwta', symObjAddr: 0x76B0, symBinAddr: 0x513E0, symSize: 0xF8 }
  - { offsetInCU: 0x3E85, offset: 0x914C8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwet', symObjAddr: 0x77A8, symBinAddr: 0x514D8, symSize: 0xC }
  - { offsetInCU: 0x3E99, offset: 0x914DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVwst', symObjAddr: 0x786C, symBinAddr: 0x5159C, symSize: 0xC }
  - { offsetInCU: 0x3EAD, offset: 0x914F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueDecoderC7Options33_DDEFC033B005E1F1FC949B68024B9ED4LLVMr', symObjAddr: 0x7920, symBinAddr: 0x51650, symSize: 0x8C }
  - { offsetInCU: 0x3EC1, offset: 0x91504, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAA3KeysAFP_s06CodingN0PWT', symObjAddr: 0x79AC, symBinAddr: 0x516DC, symSize: 0x8 }
  - { offsetInCU: 0x3ED5, offset: 0x91518, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC4data10codingPath8userInfo7optionsADyxGSDySSAA7JSValue_pG_Says9CodingKey_pGSDys0s4UserpT0VypGAA0R7DecoderC7OptionsACLLVtcfcxSgSScfu_TA', symObjAddr: 0x79F4, symBinAddr: 0x51724, symSize: 0x60 }
  - { offsetInCU: 0x3F1B, offset: 0x9155E, size: 0x8, addend: 0x0, symName: '_$sSD4KeysVySS9Capacitor7JSValue_p_GAByxq__GSTsWl', symObjAddr: 0x7A54, symBinAddr: 0x51784, symSize: 0x4C }
  - { offsetInCU: 0x3F2F, offset: 0x91572, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0x7AE4, symBinAddr: 0x517D0, symSize: 0x3C }
  - { offsetInCU: 0x3F43, offset: 0x91586, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOMa', symObjAddr: 0x7BA0, symBinAddr: 0x51850, symSize: 0xC }
  - { offsetInCU: 0x3F57, offset: 0x9159A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8containsySbxFSbxXEfU_TA', symObjAddr: 0x7C3C, symBinAddr: 0x5185C, symSize: 0x20 }
  - { offsetInCU: 0x3FA1, offset: 0x915E4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlFSb_Tg5Tf4dn_n', symObjAddr: 0x7C64, symBinAddr: 0x51884, symSize: 0x178 }
  - { offsetInCU: 0x4127, offset: 0x9176A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlFSS_Tg5Tf4dn_n', symObjAddr: 0x7DDC, symBinAddr: 0x519FC, symSize: 0x174 }
  - { offsetInCU: 0x42AD, offset: 0x918F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlFSd_Tg5Tf4dn_n', symObjAddr: 0x7F50, symBinAddr: 0x51B70, symSize: 0x174 }
  - { offsetInCU: 0x4433, offset: 0x91A76, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyxxmKSeRzlFSf_Tg5Tf4dn_n', symObjAddr: 0x80C4, symBinAddr: 0x51CE4, symSize: 0x174 }
  - { offsetInCU: 0x45B6, offset: 0x91BF9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyS2bmKFTf4dn_n', symObjAddr: 0x8818, symBinAddr: 0x52438, symSize: 0xE8 }
  - { offsetInCU: 0x4643, offset: 0x91C86, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC6decodeyS2SmKFTf4dn_n', symObjAddr: 0x8900, symBinAddr: 0x52520, symSize: 0xE0 }
  - { offsetInCU: 0x469D, offset: 0x91CE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOMi', symObjAddr: 0x8DF0, symBinAddr: 0x529E4, symSize: 0x8 }
  - { offsetInCU: 0x46B1, offset: 0x91CF4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOwet', symObjAddr: 0x8E00, symBinAddr: 0x529EC, symSize: 0x50 }
  - { offsetInCU: 0x46C5, offset: 0x91D08, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOwst', symObjAddr: 0x8E50, symBinAddr: 0x52A3C, symSize: 0x8C }
  - { offsetInCU: 0x46D9, offset: 0x91D1C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOwug', symObjAddr: 0x8EDC, symBinAddr: 0x52AC8, symSize: 0x8 }
  - { offsetInCU: 0x46ED, offset: 0x91D30, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOwup', symObjAddr: 0x8EE4, symBinAddr: 0x52AD0, symSize: 0x4 }
  - { offsetInCU: 0x4701, offset: 0x91D44, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOwui', symObjAddr: 0x8EE8, symBinAddr: 0x52AD4, symSize: 0x4 }
  - { offsetInCU: 0x4715, offset: 0x91D58, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSHAASQWb', symObjAddr: 0x8EEC, symBinAddr: 0x52AD8, symSize: 0x10 }
  - { offsetInCU: 0x4729, offset: 0x91D6C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAs28CustomDebugStringConvertiblePWb', symObjAddr: 0x8EFC, symBinAddr: 0x52AE8, symSize: 0x10 }
  - { offsetInCU: 0x473D, offset: 0x91D80, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs06CodingM0AAs23CustomStringConvertiblePWb', symObjAddr: 0x8F0C, symBinAddr: 0x52AF8, symSize: 0x10 }
  - { offsetInCU: 0x48B4, offset: 0x91EF7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x3C98, symBinAddr: 0x4DA28, symSize: 0x54 }
  - { offsetInCU: 0x48D0, offset: 0x91F13, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSHAASH9hashValueSivgTW', symObjAddr: 0x3CEC, symBinAddr: 0x4DA7C, symSize: 0x4C }
  - { offsetInCU: 0x48EC, offset: 0x91F2F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x3D38, symBinAddr: 0x4DAC8, symSize: 0x50 }
  - { offsetInCU: 0x4908, offset: 0x91F4B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_GSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x3D88, symBinAddr: 0x4DB18, symSize: 0x50 }
  - { offsetInCU: 0x492A, offset: 0x91F6D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs28CustomDebugStringConvertibleAAsAHP16debugDescriptionSSvgTW', symObjAddr: 0x3EA0, symBinAddr: 0x4DC30, symSize: 0x34 }
  - { offsetInCU: 0x4946, offset: 0x91F89, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLC8SuperKeyOyx_Gs23CustomStringConvertibleAAsAHP11descriptionSSvgTW', symObjAddr: 0x3ED4, symBinAddr: 0x4DC64, symSize: 0x34 }
  - { offsetInCU: 0x4968, offset: 0x91FAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeySbSgSbm_0R0QztKFTW', symObjAddr: 0x4A78, symBinAddr: 0x4E808, symSize: 0x14 }
  - { offsetInCU: 0x498B, offset: 0x91FCE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeySSSgSSm_0R0QztKFTW', symObjAddr: 0x4A8C, symBinAddr: 0x4E81C, symSize: 0x14 }
  - { offsetInCU: 0x49AE, offset: 0x91FF1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeySdSgSdm_0R0QztKFTW', symObjAddr: 0x4AA0, symBinAddr: 0x4E830, symSize: 0x20 }
  - { offsetInCU: 0x49D1, offset: 0x92014, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeySiSgSim_0R0QztKFTW', symObjAddr: 0x4ADC, symBinAddr: 0x4E86C, symSize: 0x20 }
  - { offsetInCU: 0x49F4, offset: 0x92037, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeys5Int64VSgAKm_0R0QztKFTW', symObjAddr: 0x4B50, symBinAddr: 0x4E8E0, symSize: 0x20 }
  - { offsetInCU: 0x4A17, offset: 0x9205A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeySuSgSum_0R0QztKFTW', symObjAddr: 0x4B70, symBinAddr: 0x4E900, symSize: 0x20 }
  - { offsetInCU: 0x4A3A, offset: 0x9207D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeys6UInt64VSgAKm_0R0QztKFTW', symObjAddr: 0x4C74, symBinAddr: 0x4EA04, symSize: 0x20 }
  - { offsetInCU: 0x4A5D, offset: 0x920A0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCyxGs0b8DecodingC8ProtocolAAsAFP15decodeIfPresent_6forKeyqd__Sgqd__m_0R0QztKSeRd__lFTW', symObjAddr: 0x4C94, symBinAddr: 0x4EA24, symSize: 0x28 }
  - { offsetInCU: 0x4AE3, offset: 0x92126, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentySbSgSbmKFTW', symObjAddr: 0x5874, symBinAddr: 0x4F604, symSize: 0x14 }
  - { offsetInCU: 0x4B06, offset: 0x92149, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentySSSgSSmKFTW', symObjAddr: 0x5888, symBinAddr: 0x4F618, symSize: 0x14 }
  - { offsetInCU: 0x4B29, offset: 0x9216C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentySdSgSdmKFTW', symObjAddr: 0x589C, symBinAddr: 0x4F62C, symSize: 0x20 }
  - { offsetInCU: 0x4B4C, offset: 0x9218F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentySiSgSimKFTW', symObjAddr: 0x58D8, symBinAddr: 0x4F668, symSize: 0x20 }
  - { offsetInCU: 0x4B6F, offset: 0x921B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentys5Int64VSgAImKFTW', symObjAddr: 0x594C, symBinAddr: 0x4F6DC, symSize: 0x20 }
  - { offsetInCU: 0x4B92, offset: 0x921D5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentySuSgSumKFTW', symObjAddr: 0x596C, symBinAddr: 0x4F6FC, symSize: 0x20 }
  - { offsetInCU: 0x4BB5, offset: 0x921F8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentys6UInt64VSgAImKFTW', symObjAddr: 0x5A70, symBinAddr: 0x4F800, symSize: 0x20 }
  - { offsetInCU: 0x4BD8, offset: 0x9221B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_DDEFC033B005E1F1FC949B68024B9ED4LLCs0b8DecodingC0AAsAEP15decodeIfPresentyqd__Sgqd__mKSeRd__lFTW', symObjAddr: 0x5A90, symBinAddr: 0x4F820, symSize: 0x28 }
  - { offsetInCU: 0x172, offset: 0x9278B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCfD', symObjAddr: 0x2608, symBinAddr: 0x5508C, symSize: 0x58 }
  - { offsetInCU: 0x1B5, offset: 0x927CE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCAA0B17EncodingContainerACLLA2aeCLLP4dataAA0B0_pSgvgTW', symObjAddr: 0x2660, symBinAddr: 0x550E4, symSize: 0x40 }
  - { offsetInCU: 0x1F6, offset: 0x9280F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0C0AAsAEP8userInfoSDys010CodingUserM3KeyVypGvgTW', symObjAddr: 0x3098, symBinAddr: 0x55B1C, symSize: 0x14 }
  - { offsetInCU: 0x23E, offset: 0x92857, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC5arraySayAA7JSValue_pGSgvg', symObjAddr: 0x26A0, symBinAddr: 0x55124, symSize: 0x240 }
  - { offsetInCU: 0x654, offset: 0x92C6D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCfD', symObjAddr: 0x5280, symBinAddr: 0x57D04, symSize: 0x50 }
  - { offsetInCU: 0x706, offset: 0x92D1F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyAdA0cD0ACLLCyxGcs9CodingKeyRzlufC', symObjAddr: 0x2C24, symBinAddr: 0x556A8, symSize: 0x38 }
  - { offsetInCU: 0x760, offset: 0x92D79, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCfD', symObjAddr: 0x5170, symBinAddr: 0x57BF4, symSize: 0x24 }
  - { offsetInCU: 0x7AE, offset: 0x92DC7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCAA015JSValueEncodingD0ACLLA2aeCLLP4dataAA0M0_pSgvgTW', symObjAddr: 0x5194, symBinAddr: 0x57C18, symSize: 0x78 }
  - { offsetInCU: 0x80D, offset: 0x92E26, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLO4dataAA7JSValue_pSgvg', symObjAddr: 0x1FE0, symBinAddr: 0x54A64, symSize: 0xE4 }
  - { offsetInCU: 0x8A7, offset: 0x92EC0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOAA07JSValuebC0ACLLA2aeCLLP4dataAA0L0_pSgvgTW', symObjAddr: 0x20C4, symBinAddr: 0x54B48, symSize: 0x8 }
  - { offsetInCU: 0x9EE, offset: 0x93007, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCfD', symObjAddr: 0x6C08, symBinAddr: 0x5968C, symSize: 0x50 }
  - { offsetInCU: 0xA43, offset: 0x9305C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP10codingPathSays9CodingKey_pGvgTW', symObjAddr: 0x7574, symBinAddr: 0x59FF8, symSize: 0xC }
  - { offsetInCU: 0xA85, offset: 0x9309E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCAA015JSValueEncodingD0ACLLA2aeCLLP4dataAA0M0_pSgvgTW', symObjAddr: 0x7AD0, symBinAddr: 0x5A554, symSize: 0x54 }
  - { offsetInCU: 0xB18, offset: 0x93131, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC10codingPath8userInfo7optionsADyxGSays9CodingKey_pG_SDys0q4UseroR0VypGAA14JSValueEncoderC7OptionsACLLVtcfC', symObjAddr: 0x2BB4, symBinAddr: 0x55638, symSize: 0x70 }
  - { offsetInCU: 0xB6E, offset: 0x93187, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6objectSDySSAA7JSValue_pGSgvg', symObjAddr: 0x310C, symBinAddr: 0x55B90, symSize: 0x67C }
  - { offsetInCU: 0xFA2, offset: 0x935BB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCfd', symObjAddr: 0x3788, symBinAddr: 0x5620C, symSize: 0x44 }
  - { offsetInCU: 0xFD1, offset: 0x935EA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCfD', symObjAddr: 0x37CC, symBinAddr: 0x56250, symSize: 0x20 }
  - { offsetInCU: 0x100F, offset: 0x93628, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP10codingPathSays9CodingKey_pGvgTW', symObjAddr: 0x4A44, symBinAddr: 0x574C8, symSize: 0xC }
  - { offsetInCU: 0x10C1, offset: 0x936DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyO21__derived_enum_equalsySbAE_AEtFZ', symObjAddr: 0x4F0, symBinAddr: 0x53000, symSize: 0x10 }
  - { offsetInCU: 0x10E9, offset: 0x93702, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyO4hash4intoys6HasherVz_tF', symObjAddr: 0x500, symBinAddr: 0x53010, symSize: 0x24 }
  - { offsetInCU: 0x117F, offset: 0x93798, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyO9hashValueSivg', symObjAddr: 0x524, symBinAddr: 0x53034, symSize: 0x44 }
  - { offsetInCU: 0x1279, offset: 0x93892, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x568, symBinAddr: 0x53078, symSize: 0x18 }
  - { offsetInCU: 0x12D7, offset: 0x938F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOSHAASH9hashValueSivgTW', symObjAddr: 0x580, symBinAddr: 0x53090, symSize: 0x44 }
  - { offsetInCU: 0x13BA, offset: 0x939D3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x5C4, symBinAddr: 0x530D4, symSize: 0x28 }
  - { offsetInCU: 0x144B, offset: 0x93A64, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyO21__derived_enum_equalsySbAE_AEtFZ', symObjAddr: 0x62C, symBinAddr: 0x5313C, symSize: 0x4 }
  - { offsetInCU: 0x146E, offset: 0x93A87, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x630, symBinAddr: 0x53140, symSize: 0x48 }
  - { offsetInCU: 0x149F, offset: 0x93AB8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyO21__derived_enum_equalsySbAE_AEtFZTf4nnd_n', symObjAddr: 0x8AD8, symBinAddr: 0x5B55C, symSize: 0x15C }
  - { offsetInCU: 0x1517, offset: 0x93B30, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategy04dateeF004dataeF0018nonConformingFloatE7StategyA2C08OptionaleF0O_10Foundation11JSONEncoderC04DateeF0OAL04DataeF0OAC03NonjkeF0OtcfC', symObjAddr: 0x1D8, symBinAddr: 0x52CE8, symSize: 0x12C }
  - { offsetInCU: 0x1581, offset: 0x93B9A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC14encodeJSObjectySDySSAA0B0_pGxKSERzlF', symObjAddr: 0x304, symBinAddr: 0x52E14, symSize: 0x1EC }
  - { offsetInCU: 0x1628, offset: 0x93C41, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategyAC08OptionaleF0Ovg', symObjAddr: 0x678, symBinAddr: 0x53188, symSize: 0x44 }
  - { offsetInCU: 0x1647, offset: 0x93C60, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategyAC08OptionaleF0Ovs', symObjAddr: 0x6BC, symBinAddr: 0x531CC, symSize: 0x50 }
  - { offsetInCU: 0x168A, offset: 0x93CA3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategyAC08OptionaleF0OvM', symObjAddr: 0x70C, symBinAddr: 0x5321C, symSize: 0x6C }
  - { offsetInCU: 0x16E8, offset: 0x93D01, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategyAC08OptionaleF0OvM.resume.0', symObjAddr: 0x778, symBinAddr: 0x53288, symSize: 0x14 }
  - { offsetInCU: 0x172D, offset: 0x93D46, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dateEncodingStrategy10Foundation11JSONEncoderC04DateeF0Ovg', symObjAddr: 0x78C, symBinAddr: 0x5329C, symSize: 0x74 }
  - { offsetInCU: 0x174C, offset: 0x93D65, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dateEncodingStrategy10Foundation11JSONEncoderC04DateeF0Ovs', symObjAddr: 0x800, symBinAddr: 0x53310, symSize: 0x98 }
  - { offsetInCU: 0x1793, offset: 0x93DAC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dateEncodingStrategy10Foundation11JSONEncoderC04DateeF0OvM', symObjAddr: 0x898, symBinAddr: 0x533A8, symSize: 0xCC }
  - { offsetInCU: 0x17F1, offset: 0x93E0A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dateEncodingStrategy10Foundation11JSONEncoderC04DateeF0OvM.resume.0', symObjAddr: 0x964, symBinAddr: 0x53474, symSize: 0x124 }
  - { offsetInCU: 0x1826, offset: 0x93E3F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dataEncodingStrategy10Foundation11JSONEncoderC04DataeF0Ovg', symObjAddr: 0xA88, symBinAddr: 0x53598, symSize: 0x74 }
  - { offsetInCU: 0x1845, offset: 0x93E5E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dataEncodingStrategy10Foundation11JSONEncoderC04DataeF0Ovs', symObjAddr: 0xAFC, symBinAddr: 0x5360C, symSize: 0x98 }
  - { offsetInCU: 0x188C, offset: 0x93EA5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dataEncodingStrategy10Foundation11JSONEncoderC04DataeF0OvM', symObjAddr: 0xB94, symBinAddr: 0x536A4, symSize: 0xCC }
  - { offsetInCU: 0x18EA, offset: 0x93F03, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC20dataEncodingStrategy10Foundation11JSONEncoderC04DataeF0OvM.resume.0', symObjAddr: 0xC60, symBinAddr: 0x53770, symSize: 0x124 }
  - { offsetInCU: 0x191F, offset: 0x93F38, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34nonConformingFloatEncodingStrategyAC03NonefgH0Ovg', symObjAddr: 0xD84, symBinAddr: 0x53894, symSize: 0xA0 }
  - { offsetInCU: 0x193E, offset: 0x93F57, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34nonConformingFloatEncodingStrategyAC03NonefgH0Ovs', symObjAddr: 0xE24, symBinAddr: 0x53934, symSize: 0x9C }
  - { offsetInCU: 0x1985, offset: 0x93F9E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34nonConformingFloatEncodingStrategyAC03NonefgH0OvM', symObjAddr: 0xEC0, symBinAddr: 0x539D0, symSize: 0x94 }
  - { offsetInCU: 0x19E3, offset: 0x93FFC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34nonConformingFloatEncodingStrategyAC03NonefgH0OvM.resume.0', symObjAddr: 0xF54, symBinAddr: 0x53A64, symSize: 0xC4 }
  - { offsetInCU: 0x1A28, offset: 0x94041, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24optionalEncodingStrategy04dateeF004dataeF0018nonConformingFloatE7StategyA2C08OptionaleF0O_10Foundation11JSONEncoderC04DateeF0OAL04DataeF0OAC03NonjkeF0Otcfc', symObjAddr: 0x1018, symBinAddr: 0x53B28, symSize: 0x114 }
  - { offsetInCU: 0x1A80, offset: 0x94099, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC6encodeyAA0B0_pxKSERzlF', symObjAddr: 0x119C, symBinAddr: 0x53CAC, symSize: 0x2E4 }
  - { offsetInCU: 0x1B8D, offset: 0x941A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderCfd', symObjAddr: 0x1F6C, symBinAddr: 0x549F0, symSize: 0x24 }
  - { offsetInCU: 0x1BC8, offset: 0x941E1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderCfD', symObjAddr: 0x1F90, symBinAddr: 0x54A14, symSize: 0x30 }
  - { offsetInCU: 0x1C13, offset: 0x9422C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Combine08TopLevelC0AadEP6encodey6OutputQzqd__KSERd__lFTW', symObjAddr: 0x1FC0, symBinAddr: 0x54A44, symSize: 0x20 }
  - { offsetInCU: 0x1C6A, offset: 0x94283, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOWOb', symObjAddr: 0x1140, symBinAddr: 0x53C50, symSize: 0x18 }
  - { offsetInCU: 0x1C82, offset: 0x9429B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOWOb', symObjAddr: 0x1140, symBinAddr: 0x53C50, symSize: 0x18 }
  - { offsetInCU: 0x1C93, offset: 0x942AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVWOb', symObjAddr: 0x1158, symBinAddr: 0x53C68, symSize: 0x44 }
  - { offsetInCU: 0x1CA7, offset: 0x942C0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLC13encodeGenericyyxKSERzlF', symObjAddr: 0x150C, symBinAddr: 0x53F90, symSize: 0xA60 }
  - { offsetInCU: 0x1E67, offset: 0x94480, size: 0x8, addend: 0x0, symName: '_$sSa9CapacitorAA17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLORszlE4dataACLLAA7JSValue_pSgvg', symObjAddr: 0x20CC, symBinAddr: 0x54B50, symSize: 0x53C }
  - { offsetInCU: 0x21ED, offset: 0x94806, size: 0x8, addend: 0x0, symName: '_$sSayxG9Capacitor24JSValueEncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLA2B0cD0ADLLORszlAbcDLLP4dataAB0B0_pSgvgTW', symObjAddr: 0x28E0, symBinAddr: 0x55364, symSize: 0x8 }
  - { offsetInCU: 0x22CA, offset: 0x948E3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLC9container7keyedBys22KeyedEncodingContainerVyxGxm_ts9CodingKeyRzlF', symObjAddr: 0x28E8, symBinAddr: 0x5536C, symSize: 0x2CC }
  - { offsetInCU: 0x2664, offset: 0x94C7D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLC16unkeyedContainers015UnkeyedEncodingM0_pyF', symObjAddr: 0x2C5C, symBinAddr: 0x556E0, symSize: 0x298 }
  - { offsetInCU: 0x29DA, offset: 0x94FF3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLC20singleValueContainers06Singlem8EncodingN0_pyF', symObjAddr: 0x2EF4, symBinAddr: 0x55978, symSize: 0x1A4 }
  - { offsetInCU: 0x2BF3, offset: 0x9520C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0C0AAsAEP9container7keyedBys22KeyedEncodingContainerVyqd__Gqd__m_ts9CodingKeyRd__lFTW', symObjAddr: 0x30AC, symBinAddr: 0x55B30, symSize: 0x20 }
  - { offsetInCU: 0x2C0F, offset: 0x95228, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0C0AAsAEP16unkeyedContainers015UnkeyedEncodingM0_pyFTW', symObjAddr: 0x30CC, symBinAddr: 0x55B50, symSize: 0x20 }
  - { offsetInCU: 0x2C2B, offset: 0x95244, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0C0AAsAEP20singleValueContainers06Singlem8EncodingN0_pyFTW', symObjAddr: 0x30EC, symBinAddr: 0x55B70, symSize: 0x20 }
  - { offsetInCU: 0x2D18, offset: 0x95331, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6insert_3foryAA7JSValue_p_xtF', symObjAddr: 0x37EC, symBinAddr: 0x56270, symSize: 0x54 }
  - { offsetInCU: 0x2DBB, offset: 0x953D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6insert_3foryAA12EncodedValueACLLO_qd__ts9CodingKeyRd__lF', symObjAddr: 0x3840, symBinAddr: 0x562C4, symSize: 0x1CC }
  - { offsetInCU: 0x2EEA, offset: 0x95503, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC9encodeNil6forKeyyx_tKF', symObjAddr: 0x3A0C, symBinAddr: 0x56490, symSize: 0x8C }
  - { offsetInCU: 0x2F5B, offset: 0x95574, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encode_6forKeyyqd___xtKSERd__lF', symObjAddr: 0x3A98, symBinAddr: 0x5651C, symSize: 0x188 }
  - { offsetInCU: 0x303B, offset: 0x95654, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC16_encodeIfPresent_6forKeyyqd__Sg_xtKSERd__lF', symObjAddr: 0x3C20, symBinAddr: 0x566A4, symSize: 0x21C }
  - { offsetInCU: 0x30D2, offset: 0x956EB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC15encodeIfPresent_6forKeyyqd__Sg_xtKSERd__lF', symObjAddr: 0x3E3C, symBinAddr: 0x568C0, symSize: 0x14 }
  - { offsetInCU: 0x3137, offset: 0x95750, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC15encodeIfPresent_6forKeyySbSg_xtKF', symObjAddr: 0x3E50, symBinAddr: 0x568D4, symSize: 0x34 }
  - { offsetInCU: 0x318E, offset: 0x957A7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC15encodeIfPresent_6forKeyySSSg_xtKF', symObjAddr: 0x3E84, symBinAddr: 0x56908, symSize: 0x3C }
  - { offsetInCU: 0x323D, offset: 0x95856, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC06nestedC07keyedBy6forKeys0b8EncodingC0Vyqd__Gqd__m_xts06CodingP0Rd__lF', symObjAddr: 0x3F7C, symBinAddr: 0x56A00, symSize: 0x27C }
  - { offsetInCU: 0x339F, offset: 0x959B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC013nestedUnkeyedC06forKeys0m8EncodingC0_px_tF', symObjAddr: 0x41F8, symBinAddr: 0x56C7C, symSize: 0x278 }
  - { offsetInCU: 0x3514, offset: 0x95B2D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyO8rawValueAFyx_GSgSS_tcfC', symObjAddr: 0x4470, symBinAddr: 0x56EF4, symSize: 0x58 }
  - { offsetInCU: 0x354D, offset: 0x95B66, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyO11stringValueAFyx_GSgSS_tcfC', symObjAddr: 0x44C8, symBinAddr: 0x56F4C, symSize: 0x18 }
  - { offsetInCU: 0x3586, offset: 0x95B9F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSYAASY8rawValuexSg03RawO0Qz_tcfCTW', symObjAddr: 0x4620, symBinAddr: 0x570A4, symSize: 0x38 }
  - { offsetInCU: 0x35B1, offset: 0x95BCA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSYAASY8rawValue03RawO0QzvgTW', symObjAddr: 0x4658, symBinAddr: 0x570DC, symSize: 0x24 }
  - { offsetInCU: 0x3600, offset: 0x95C19, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x467C, symBinAddr: 0x57100, symSize: 0x4 }
  - { offsetInCU: 0x3620, offset: 0x95C39, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x467C, symBinAddr: 0x57100, symSize: 0x4 }
  - { offsetInCU: 0x3634, offset: 0x95C4D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x467C, symBinAddr: 0x57100, symSize: 0x4 }
  - { offsetInCU: 0x3648, offset: 0x95C61, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValueSSvgTW', symObjAddr: 0x467C, symBinAddr: 0x57100, symSize: 0x4 }
  - { offsetInCU: 0x365B, offset: 0x95C74, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP11stringValuexSgSS_tcfCTW', symObjAddr: 0x4680, symBinAddr: 0x57104, symSize: 0x30 }
  - { offsetInCU: 0x3677, offset: 0x95C90, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP8intValueSiSgvgTW', symObjAddr: 0x46B0, symBinAddr: 0x57134, symSize: 0xC }
  - { offsetInCU: 0x36A2, offset: 0x95CBB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAsAHP8intValuexSgSi_tcfCTW', symObjAddr: 0x46BC, symBinAddr: 0x57140, symSize: 0x2C }
  - { offsetInCU: 0x36D3, offset: 0x95CEC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyO8rawValueSSvgTf4d_n', symObjAddr: 0x8D0C, symBinAddr: 0x5B790, symSize: 0x14 }
  - { offsetInCU: 0x36EF, offset: 0x95D08, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyO8intValueAFyx_GSgSi_tcfCTf4dd_n', symObjAddr: 0xA6D4, symBinAddr: 0x5CFA4, symSize: 0x8 }
  - { offsetInCU: 0x371B, offset: 0x95D34, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC12superEncoders0M0_pyF', symObjAddr: 0x4750, symBinAddr: 0x571D4, symSize: 0x184 }
  - { offsetInCU: 0x37C5, offset: 0x95DDE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC12superEncoder6forKeys0M0_px_tF', symObjAddr: 0x48D4, symBinAddr: 0x57358, symSize: 0x170 }
  - { offsetInCU: 0x3880, offset: 0x95E99, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP9encodeNil6forKeyy0Q0Qz_tKFTW', symObjAddr: 0x4A50, symBinAddr: 0x574D4, symSize: 0x20 }
  - { offsetInCU: 0x38A3, offset: 0x95EBC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyySb_0P0QztKFTW', symObjAddr: 0x4A70, symBinAddr: 0x574F4, symSize: 0x38 }
  - { offsetInCU: 0x38C6, offset: 0x95EDF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyySS_0P0QztKFTW', symObjAddr: 0x4AA8, symBinAddr: 0x5752C, symSize: 0x6C }
  - { offsetInCU: 0x38E9, offset: 0x95F02, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyySd_0P0QztKFTW', symObjAddr: 0x4B14, symBinAddr: 0x57598, symSize: 0x3C }
  - { offsetInCU: 0x390C, offset: 0x95F25, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyySf_0P0QztKFTW', symObjAddr: 0x4B50, symBinAddr: 0x575D4, symSize: 0x3C }
  - { offsetInCU: 0x392F, offset: 0x95F48, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyys4Int8V_0P0QztKFTW', symObjAddr: 0x4BB0, symBinAddr: 0x57634, symSize: 0x38 }
  - { offsetInCU: 0x3952, offset: 0x95F6B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyys5UInt8V_0P0QztKFTW', symObjAddr: 0x4C78, symBinAddr: 0x576FC, symSize: 0x38 }
  - { offsetInCU: 0x3975, offset: 0x95F8E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP6encode_6forKeyyqd___0P0QztKSERd__lFTW', symObjAddr: 0x4DAC, symBinAddr: 0x57830, symSize: 0x20 }
  - { offsetInCU: 0x3998, offset: 0x95FB1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyySbSg_0R0QztKFTW', symObjAddr: 0x4DF4, symBinAddr: 0x57878, symSize: 0x20 }
  - { offsetInCU: 0x39BB, offset: 0x95FD4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyySSSg_0R0QztKFTW', symObjAddr: 0x4E14, symBinAddr: 0x57898, symSize: 0x20 }
  - { offsetInCU: 0x39DE, offset: 0x95FF7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyySdSg_0R0QztKFTW', symObjAddr: 0x4E34, symBinAddr: 0x578B8, symSize: 0x34 }
  - { offsetInCU: 0x3A01, offset: 0x9601A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyySiSg_0R0QztKFTW', symObjAddr: 0x4E8C, symBinAddr: 0x57910, symSize: 0x34 }
  - { offsetInCU: 0x3A24, offset: 0x9603D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyys5Int64VSg_0R0QztKFTW', symObjAddr: 0x4F60, symBinAddr: 0x579E4, symSize: 0x34 }
  - { offsetInCU: 0x3A47, offset: 0x96060, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyySuSg_0R0QztKFTW', symObjAddr: 0x4F94, symBinAddr: 0x57A18, symSize: 0x34 }
  - { offsetInCU: 0x3A6A, offset: 0x96083, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyys6UInt64VSg_0R0QztKFTW', symObjAddr: 0x509C, symBinAddr: 0x57B20, symSize: 0x34 }
  - { offsetInCU: 0x3A8D, offset: 0x960A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP15encodeIfPresent_6forKeyyqd__Sg_0R0QztKSERd__lFTW', symObjAddr: 0x50D0, symBinAddr: 0x57B54, symSize: 0x20 }
  - { offsetInCU: 0x3AB0, offset: 0x960C9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP06nestedC07keyedBy6forKeys0blC0Vyqd__Gqd__m_0R0Qzts06CodingR0Rd__lFTW', symObjAddr: 0x50F0, symBinAddr: 0x57B74, symSize: 0x20 }
  - { offsetInCU: 0x3ACC, offset: 0x960E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP013nestedUnkeyedC06forKeys0olC0_p0Q0Qz_tFTW', symObjAddr: 0x5110, symBinAddr: 0x57B94, symSize: 0x20 }
  - { offsetInCU: 0x3AE8, offset: 0x96101, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP12superEncoders0O0_pyFTW', symObjAddr: 0x5130, symBinAddr: 0x57BB4, symSize: 0x20 }
  - { offsetInCU: 0x3B04, offset: 0x9611D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP12superEncoder6forKeys0O0_p0Q0Qz_tFTW', symObjAddr: 0x5150, symBinAddr: 0x57BD4, symSize: 0x20 }
  - { offsetInCU: 0x3B20, offset: 0x96139, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC4dataAA7JSValue_pSgvg', symObjAddr: 0x520C, symBinAddr: 0x57C90, symSize: 0x54 }
  - { offsetInCU: 0x3B51, offset: 0x9616A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGAA015JSValueEncodingC0ACLLA2afCLLP4dataAA0L0_pSgvgTW', symObjAddr: 0x5260, symBinAddr: 0x57CE4, symSize: 0x20 }
  - { offsetInCU: 0x3BF1, offset: 0x9620A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6appendyyAA12EncodedValueACLLOF', symObjAddr: 0x52D0, symBinAddr: 0x57D54, symSize: 0x130 }
  - { offsetInCU: 0x3DEC, offset: 0x96405, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlFSb_Tg5', symObjAddr: 0x5400, symBinAddr: 0x57E84, symSize: 0x188 }
  - { offsetInCU: 0x3F17, offset: 0x96530, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlFSS_Tg5', symObjAddr: 0x5588, symBinAddr: 0x5800C, symSize: 0x1B0 }
  - { offsetInCU: 0x403C, offset: 0x96655, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlFSd_Tg5', symObjAddr: 0x5738, symBinAddr: 0x581BC, symSize: 0x190 }
  - { offsetInCU: 0x4168, offset: 0x96781, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlFSf_Tg5', symObjAddr: 0x58C8, symBinAddr: 0x5834C, symSize: 0x190 }
  - { offsetInCU: 0x424F, offset: 0x96868, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlF', symObjAddr: 0x6078, symBinAddr: 0x58AFC, symSize: 0x16C }
  - { offsetInCU: 0x4310, offset: 0x96929, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC06nestedbC0s0b8EncodingC0_pyF', symObjAddr: 0x61E4, symBinAddr: 0x58C68, symSize: 0x13C }
  - { offsetInCU: 0x4398, offset: 0x969B1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC06nestedC07keyedBys013KeyedEncodingC0VyxGxm_ts9CodingKeyRzlF', symObjAddr: 0x6320, symBinAddr: 0x58DA4, symSize: 0x13C }
  - { offsetInCU: 0x4400, offset: 0x96A19, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC12superEncoders0M0_pyF', symObjAddr: 0x645C, symBinAddr: 0x58EE0, symSize: 0x150 }
  - { offsetInCU: 0x44BC, offset: 0x96AD5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP5countSivgTW', symObjAddr: 0x65AC, symBinAddr: 0x59030, symSize: 0x38 }
  - { offsetInCU: 0x459A, offset: 0x96BB3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP9encodeNilyyKFTW', symObjAddr: 0x65E4, symBinAddr: 0x59068, symSize: 0xB4 }
  - { offsetInCU: 0x4649, offset: 0x96C62, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySbKFTW', symObjAddr: 0x6698, symBinAddr: 0x5911C, symSize: 0x20 }
  - { offsetInCU: 0x466C, offset: 0x96C85, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySSKFTW', symObjAddr: 0x66B8, symBinAddr: 0x5913C, symSize: 0x20 }
  - { offsetInCU: 0x468F, offset: 0x96CA8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySdKFTW', symObjAddr: 0x66D8, symBinAddr: 0x5915C, symSize: 0x20 }
  - { offsetInCU: 0x46B2, offset: 0x96CCB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySfKFTW', symObjAddr: 0x66F8, symBinAddr: 0x5917C, symSize: 0x20 }
  - { offsetInCU: 0x46D5, offset: 0x96CEE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySiKFTW', symObjAddr: 0x6718, symBinAddr: 0x5919C, symSize: 0x28 }
  - { offsetInCU: 0x46F8, offset: 0x96D11, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys4Int8VKFTW', symObjAddr: 0x6740, symBinAddr: 0x591C4, symSize: 0x28 }
  - { offsetInCU: 0x471B, offset: 0x96D34, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys5Int16VKFTW', symObjAddr: 0x6768, symBinAddr: 0x591EC, symSize: 0x28 }
  - { offsetInCU: 0x473E, offset: 0x96D57, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys5Int32VKFTW', symObjAddr: 0x6790, symBinAddr: 0x59214, symSize: 0x28 }
  - { offsetInCU: 0x4761, offset: 0x96D7A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys5Int64VKFTW', symObjAddr: 0x67B8, symBinAddr: 0x5923C, symSize: 0x28 }
  - { offsetInCU: 0x4784, offset: 0x96D9D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyySuKFTW', symObjAddr: 0x67E0, symBinAddr: 0x59264, symSize: 0x28 }
  - { offsetInCU: 0x47A7, offset: 0x96DC0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys5UInt8VKFTW', symObjAddr: 0x6808, symBinAddr: 0x5928C, symSize: 0x28 }
  - { offsetInCU: 0x47CA, offset: 0x96DE3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys6UInt16VKFTW', symObjAddr: 0x6830, symBinAddr: 0x592B4, symSize: 0x28 }
  - { offsetInCU: 0x47ED, offset: 0x96E06, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys6UInt32VKFTW', symObjAddr: 0x6858, symBinAddr: 0x592DC, symSize: 0x28 }
  - { offsetInCU: 0x4810, offset: 0x96E29, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyys6UInt64VKFTW', symObjAddr: 0x6880, symBinAddr: 0x59304, symSize: 0x28 }
  - { offsetInCU: 0x4833, offset: 0x96E4C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encodeyyqd__KSERd__lFTW', symObjAddr: 0x68A8, symBinAddr: 0x5932C, symSize: 0x20 }
  - { offsetInCU: 0x4856, offset: 0x96E6F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP06nestedC07keyedBys05KeyedlC0Vyqd__Gqd__m_ts9CodingKeyRd__lFTW', symObjAddr: 0x6B50, symBinAddr: 0x595D4, symSize: 0x20 }
  - { offsetInCU: 0x4872, offset: 0x96E8B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP06nestedbC0sAE_pyFTW', symObjAddr: 0x6B70, symBinAddr: 0x595F4, symSize: 0x20 }
  - { offsetInCU: 0x488E, offset: 0x96EA7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP12superEncoders0N0_pyFTW', symObjAddr: 0x6B90, symBinAddr: 0x59614, symSize: 0x20 }
  - { offsetInCU: 0x48AA, offset: 0x96EC3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCAA015JSValueEncodingC0ACLLA2aeCLLP4dataAA0L0_pSgvgTW', symObjAddr: 0x6BB0, symBinAddr: 0x59634, symSize: 0x58 }
  - { offsetInCU: 0x4914, offset: 0x96F2D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC11encodeFloatyyxKSFRzlFSd_Tg5', symObjAddr: 0x6C58, symBinAddr: 0x596DC, symSize: 0x3D0 }
  - { offsetInCU: 0x4A82, offset: 0x9709B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC11encodeFloatyyxKSFRzlFSf_Tg5', symObjAddr: 0x7028, symBinAddr: 0x59AAC, symSize: 0x3D0 }
  - { offsetInCU: 0x4BC4, offset: 0x971DD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC6encodeyyxKSERzlF', symObjAddr: 0x73F8, symBinAddr: 0x59E7C, symSize: 0x17C }
  - { offsetInCU: 0x4CC1, offset: 0x972DA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP9encodeNilyyKFTW', symObjAddr: 0x7580, symBinAddr: 0x5A004, symSize: 0xA4 }
  - { offsetInCU: 0x4D61, offset: 0x9737A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP6encodeyySbKFTW', symObjAddr: 0x7624, symBinAddr: 0x5A0A8, symSize: 0x70 }
  - { offsetInCU: 0x4DD8, offset: 0x973F1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP6encodeyySSKFTW', symObjAddr: 0x7694, symBinAddr: 0x5A118, symSize: 0x84 }
  - { offsetInCU: 0x4E51, offset: 0x9746A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP6encodeyySdKFTW', symObjAddr: 0x7718, symBinAddr: 0x5A19C, symSize: 0x20 }
  - { offsetInCU: 0x4ECF, offset: 0x974E8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP6encodeyySfKFTW', symObjAddr: 0x7738, symBinAddr: 0x5A1BC, symSize: 0x20 }
  - { offsetInCU: 0x4F1A, offset: 0x97533, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0bc8EncodingD0AAsAEP6encodeyyqd__KSERd__lFTW', symObjAddr: 0x7AB0, symBinAddr: 0x5A534, symSize: 0x20 }
  - { offsetInCU: 0x4F7F, offset: 0x97598, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_10Foundation4DataVTg5', symObjAddr: 0x7B24, symBinAddr: 0x5A5A8, symSize: 0x1E4 }
  - { offsetInCU: 0x5026, offset: 0x9763F, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_So13CAPPluginCallCTg5', symObjAddr: 0x7D08, symBinAddr: 0x5A78C, symSize: 0x204 }
  - { offsetInCU: 0x50B7, offset: 0x976D0, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_9Capacitor7JSValue_pTg5', symObjAddr: 0x7F0C, symBinAddr: 0x5A990, symSize: 0x1F4 }
  - { offsetInCU: 0x5153, offset: 0x9776C, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_ypTg5', symObjAddr: 0x8100, symBinAddr: 0x5AB84, symSize: 0x1E4 }
  - { offsetInCU: 0x51EF, offset: 0x97808, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_SSTg5', symObjAddr: 0x82E4, symBinAddr: 0x5AD68, symSize: 0x1DC }
  - { offsetInCU: 0x5277, offset: 0x97890, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0x84C0, symBinAddr: 0x5AF44, symSize: 0x1FC }
  - { offsetInCU: 0x5372, offset: 0x9798B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFSS_9Capacitor7JSValue_ps15LazyMapSequenceVySDySSAhI_pGSS_AhI_ptGTg505$sSa9j76AA17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLORszlE4dataACLLAA7K28_pSgvgAaF_pAaF_p_AaF_ptXEfU_Tf1nncn_n', symObjAddr: 0x86BC, symBinAddr: 0x5B140, symSize: 0x3DC }
  - { offsetInCU: 0x5585, offset: 0x97B9E, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_9Capacitor7JSValue_pTg5081$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_9Capacitor7D5_pTG5Tf3nnpf_n', symObjAddr: 0x8A98, symBinAddr: 0x5B51C, symSize: 0x40 }
  - { offsetInCU: 0x55D2, offset: 0x97BEB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOWOy', symObjAddr: 0x8C34, symBinAddr: 0x5B6B8, symSize: 0x40 }
  - { offsetInCU: 0x55E6, offset: 0x97BFF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOWOe', symObjAddr: 0x8C74, symBinAddr: 0x5B6F8, symSize: 0x40 }
  - { offsetInCU: 0x55FA, offset: 0x97C13, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVWOc', symObjAddr: 0x8CB4, symBinAddr: 0x5B738, symSize: 0x44 }
  - { offsetInCU: 0x560E, offset: 0x97C27, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVWOh', symObjAddr: 0x8D20, symBinAddr: 0x5B7A4, symSize: 0x3C }
  - { offsetInCU: 0x5622, offset: 0x97C3B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOSHAASQWb', symObjAddr: 0x8D5C, symBinAddr: 0x5B7E0, symSize: 0x4 }
  - { offsetInCU: 0x5636, offset: 0x97C4F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOAESQAAWl', symObjAddr: 0x8D60, symBinAddr: 0x5B7E4, symSize: 0x44 }
  - { offsetInCU: 0x564A, offset: 0x97C63, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderCMU', symObjAddr: 0x8DA4, symBinAddr: 0x5B828, symSize: 0x8 }
  - { offsetInCU: 0x565E, offset: 0x97C77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderCMr', symObjAddr: 0x8DC0, symBinAddr: 0x5B844, symSize: 0x6C }
  - { offsetInCU: 0x5672, offset: 0x97C8B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOwet', symObjAddr: 0x8E3C, symBinAddr: 0x5B8B0, symSize: 0x90 }
  - { offsetInCU: 0x5686, offset: 0x97C9F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOwst', symObjAddr: 0x8ECC, symBinAddr: 0x5B940, symSize: 0xBC }
  - { offsetInCU: 0x569A, offset: 0x97CB3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOwug', symObjAddr: 0x8F88, symBinAddr: 0x5B9FC, symSize: 0x8 }
  - { offsetInCU: 0x56AE, offset: 0x97CC7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOwup', symObjAddr: 0x8F90, symBinAddr: 0x5BA04, symSize: 0x4 }
  - { offsetInCU: 0x56C2, offset: 0x97CDB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOwui', symObjAddr: 0x8F94, symBinAddr: 0x5BA08, symSize: 0xC }
  - { offsetInCU: 0x56D6, offset: 0x97CEF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOMa', symObjAddr: 0x8FA0, symBinAddr: 0x5BA14, symSize: 0x10 }
  - { offsetInCU: 0x56EA, offset: 0x97D03, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwxx', symObjAddr: 0x8FB0, symBinAddr: 0x5BA24, symSize: 0x48 }
  - { offsetInCU: 0x56FE, offset: 0x97D17, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwcp', symObjAddr: 0x8FF8, symBinAddr: 0x5BA6C, symSize: 0x78 }
  - { offsetInCU: 0x5712, offset: 0x97D2B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwca', symObjAddr: 0x9070, symBinAddr: 0x5BAE4, symSize: 0x128 }
  - { offsetInCU: 0x5726, offset: 0x97D3F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwta', symObjAddr: 0x91AC, symBinAddr: 0x5BC0C, symSize: 0x98 }
  - { offsetInCU: 0x573A, offset: 0x97D53, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwet', symObjAddr: 0x9244, symBinAddr: 0x5BCA4, symSize: 0x54 }
  - { offsetInCU: 0x574E, offset: 0x97D67, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwst', symObjAddr: 0x9298, symBinAddr: 0x5BCF8, symSize: 0x58 }
  - { offsetInCU: 0x5762, offset: 0x97D7B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwug', symObjAddr: 0x92F0, symBinAddr: 0x5BD50, symSize: 0x18 }
  - { offsetInCU: 0x5776, offset: 0x97D8F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwup', symObjAddr: 0x9308, symBinAddr: 0x5BD68, symSize: 0x4 }
  - { offsetInCU: 0x578A, offset: 0x97DA3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOwui', symObjAddr: 0x930C, symBinAddr: 0x5BD6C, symSize: 0x30 }
  - { offsetInCU: 0x579E, offset: 0x97DB7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC34NonConformingFloatEncodingStrategyOMa', symObjAddr: 0x933C, symBinAddr: 0x5BD9C, symSize: 0x10 }
  - { offsetInCU: 0x57B2, offset: 0x97DCB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCMU', symObjAddr: 0x934C, symBinAddr: 0x5BDAC, symSize: 0x8 }
  - { offsetInCU: 0x57C6, offset: 0x97DDF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor15_JSValueEncoder33_762F18EA3FB6340CB5CC34BC6E29A243LLCMr', symObjAddr: 0x9354, symBinAddr: 0x5BDB4, symSize: 0x80 }
  - { offsetInCU: 0x57DA, offset: 0x97DF3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMi', symObjAddr: 0x93D4, symBinAddr: 0x5BE34, symSize: 0x4 }
  - { offsetInCU: 0x57EE, offset: 0x97E07, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMr', symObjAddr: 0x93D8, symBinAddr: 0x5BE38, symSize: 0x84 }
  - { offsetInCU: 0x5802, offset: 0x97E1B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMa', symObjAddr: 0x945C, symBinAddr: 0x5BEBC, symSize: 0xC }
  - { offsetInCU: 0x5816, offset: 0x97E2F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMa', symObjAddr: 0x9468, symBinAddr: 0x5BEC8, symSize: 0x20 }
  - { offsetInCU: 0x582A, offset: 0x97E43, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMU', symObjAddr: 0x9488, symBinAddr: 0x5BEE8, symSize: 0x8 }
  - { offsetInCU: 0x583E, offset: 0x97E57, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMr', symObjAddr: 0x94A4, symBinAddr: 0x5BF04, symSize: 0x84 }
  - { offsetInCU: 0x5852, offset: 0x97E6B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMU', symObjAddr: 0x9528, symBinAddr: 0x5BF88, symSize: 0x8 }
  - { offsetInCU: 0x5866, offset: 0x97E7F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20SingleValueContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCMr', symObjAddr: 0x9578, symBinAddr: 0x5BFD8, symSize: 0x88 }
  - { offsetInCU: 0x587A, offset: 0x97E93, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwCP', symObjAddr: 0x9600, symBinAddr: 0x5C060, symSize: 0x30 }
  - { offsetInCU: 0x588E, offset: 0x97EA7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwxx', symObjAddr: 0x9630, symBinAddr: 0x5C090, symSize: 0x4 }
  - { offsetInCU: 0x58A2, offset: 0x97EBB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwcp', symObjAddr: 0x9654, symBinAddr: 0x5C094, symSize: 0x6C }
  - { offsetInCU: 0x58B6, offset: 0x97ECF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwca', symObjAddr: 0x96C0, symBinAddr: 0x5C100, symSize: 0x9C }
  - { offsetInCU: 0x58CA, offset: 0x97EE3, size: 0x8, addend: 0x0, symName: ___swift_memcpy41_8, symObjAddr: 0x975C, symBinAddr: 0x5C19C, symSize: 0x14 }
  - { offsetInCU: 0x58DE, offset: 0x97EF7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwta', symObjAddr: 0x9770, symBinAddr: 0x5C1B0, symSize: 0x64 }
  - { offsetInCU: 0x58F2, offset: 0x97F0B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwet', symObjAddr: 0x97D4, symBinAddr: 0x5C214, symSize: 0x3C }
  - { offsetInCU: 0x5906, offset: 0x97F1F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwst', symObjAddr: 0x9810, symBinAddr: 0x5C250, symSize: 0x4C }
  - { offsetInCU: 0x591A, offset: 0x97F33, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwug', symObjAddr: 0x985C, symBinAddr: 0x5C29C, symSize: 0x1C }
  - { offsetInCU: 0x592E, offset: 0x97F47, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwup', symObjAddr: 0x9878, symBinAddr: 0x5C2B8, symSize: 0x4 }
  - { offsetInCU: 0x5942, offset: 0x97F5B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOwui', symObjAddr: 0x987C, symBinAddr: 0x5C2BC, symSize: 0x30 }
  - { offsetInCU: 0x5956, offset: 0x97F6F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOMa', symObjAddr: 0x98AC, symBinAddr: 0x5C2EC, symSize: 0x10 }
  - { offsetInCU: 0x596A, offset: 0x97F83, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwCP', symObjAddr: 0x98BC, symBinAddr: 0x5C2FC, symSize: 0x30 }
  - { offsetInCU: 0x597E, offset: 0x97F97, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwxx', symObjAddr: 0x98EC, symBinAddr: 0x5C32C, symSize: 0xC }
  - { offsetInCU: 0x5992, offset: 0x97FAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwcp', symObjAddr: 0x98F8, symBinAddr: 0x5C338, symSize: 0x30 }
  - { offsetInCU: 0x59A6, offset: 0x97FBF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwca', symObjAddr: 0x9928, symBinAddr: 0x5C368, symSize: 0x40 }
  - { offsetInCU: 0x59BA, offset: 0x97FD3, size: 0x8, addend: 0x0, symName: ___swift_memcpy8_8, symObjAddr: 0x9968, symBinAddr: 0x5C3A8, symSize: 0xC }
  - { offsetInCU: 0x59CE, offset: 0x97FE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwta', symObjAddr: 0x9974, symBinAddr: 0x5C3B4, symSize: 0x34 }
  - { offsetInCU: 0x59E2, offset: 0x97FFB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwet', symObjAddr: 0x99A8, symBinAddr: 0x5C3E8, symSize: 0x58 }
  - { offsetInCU: 0x59F6, offset: 0x9800F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwst', symObjAddr: 0x9A00, symBinAddr: 0x5C440, symSize: 0x54 }
  - { offsetInCU: 0x5A0A, offset: 0x98023, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwug', symObjAddr: 0x9A54, symBinAddr: 0x5C494, symSize: 0xC }
  - { offsetInCU: 0x5A1E, offset: 0x98037, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwup', symObjAddr: 0x9A60, symBinAddr: 0x5C4A0, symSize: 0x10 }
  - { offsetInCU: 0x5A32, offset: 0x9804B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOwui', symObjAddr: 0x9A70, symBinAddr: 0x5C4B0, symSize: 0x14 }
  - { offsetInCU: 0x5A46, offset: 0x9805F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17EncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLOMa', symObjAddr: 0x9A84, symBinAddr: 0x5C4C4, symSize: 0x10 }
  - { offsetInCU: 0x5A5A, offset: 0x98073, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwCP', symObjAddr: 0x9A94, symBinAddr: 0x5C4D4, symSize: 0x11C }
  - { offsetInCU: 0x5A6E, offset: 0x98087, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwxx', symObjAddr: 0x9BB0, symBinAddr: 0x5C5F0, symSize: 0xA4 }
  - { offsetInCU: 0x5A82, offset: 0x9809B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwcp', symObjAddr: 0x9C54, symBinAddr: 0x5C694, symSize: 0xF0 }
  - { offsetInCU: 0x5A96, offset: 0x980AF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwca', symObjAddr: 0x9D44, symBinAddr: 0x5C784, symSize: 0x1A0 }
  - { offsetInCU: 0x5AAA, offset: 0x980C3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwtk', symObjAddr: 0x9EE4, symBinAddr: 0x5C924, symSize: 0xB4 }
  - { offsetInCU: 0x5ABE, offset: 0x980D7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwta', symObjAddr: 0x9F98, symBinAddr: 0x5C9D8, symSize: 0x10C }
  - { offsetInCU: 0x5AD2, offset: 0x980EB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwet', symObjAddr: 0xA0A4, symBinAddr: 0x5CAE4, symSize: 0xC }
  - { offsetInCU: 0x5AE6, offset: 0x980FF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVwst', symObjAddr: 0xA164, symBinAddr: 0x5CBA4, symSize: 0xC }
  - { offsetInCU: 0x5AFA, offset: 0x98113, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC7Options33_762F18EA3FB6340CB5CC34BC6E29A243LLVMr', symObjAddr: 0xA214, symBinAddr: 0x5CC54, symSize: 0x98 }
  - { offsetInCU: 0x5B0E, offset: 0x98127, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAA3KeysAFP_s06CodingN0PWT', symObjAddr: 0xA2AC, symBinAddr: 0x5CCEC, symSize: 0x8 }
  - { offsetInCU: 0x5B22, offset: 0x9813B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24JSValueEncodingContainer33_762F18EA3FB6340CB5CC34BC6E29A243LL_pWOb', symObjAddr: 0xA2F4, symBinAddr: 0x5CD34, symSize: 0x18 }
  - { offsetInCU: 0x5B3D, offset: 0x98156, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12EncodedValue33_762F18EA3FB6340CB5CC34BC6E29A243LLOWOh', symObjAddr: 0xA30C, symBinAddr: 0x5CD4C, symSize: 0x24 }
  - { offsetInCU: 0x5B6D, offset: 0x98186, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOMa', symObjAddr: 0xA37C, symBinAddr: 0x5CD70, symSize: 0xC }
  - { offsetInCU: 0x5B81, offset: 0x9819A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pSgWOd', symObjAddr: 0xA3F0, symBinAddr: 0x5CD7C, symSize: 0x48 }
  - { offsetInCU: 0x5B95, offset: 0x981AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyAdA0cD0ACLLCyxGcs9CodingKeyRzlufcSDySSAA7JSValue_pGSgycfU_TA', symObjAddr: 0xA438, symBinAddr: 0x5CDC4, symSize: 0x4 }
  - { offsetInCU: 0x5BAD, offset: 0x981C6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17AnyKeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyAdA0cD0ACLLCyxGcs9CodingKeyRzlufcSDySSAA7JSValue_pGSgycfU_TA', symObjAddr: 0xA438, symBinAddr: 0x5CDC4, symSize: 0x4 }
  - { offsetInCU: 0x5BC7, offset: 0x981E0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOMi', symObjAddr: 0xA5AC, symBinAddr: 0x5CE80, symSize: 0x8 }
  - { offsetInCU: 0x5BDB, offset: 0x981F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOwet', symObjAddr: 0xA5B8, symBinAddr: 0x5CE88, symSize: 0x50 }
  - { offsetInCU: 0x5BEF, offset: 0x98208, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOwst', symObjAddr: 0xA608, symBinAddr: 0x5CED8, symSize: 0x8C }
  - { offsetInCU: 0x5C03, offset: 0x9821C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOwug', symObjAddr: 0xA694, symBinAddr: 0x5CF64, symSize: 0x8 }
  - { offsetInCU: 0x5C17, offset: 0x98230, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOwup', symObjAddr: 0xA69C, symBinAddr: 0x5CF6C, symSize: 0x4 }
  - { offsetInCU: 0x5C2B, offset: 0x98244, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOwui', symObjAddr: 0xA6A0, symBinAddr: 0x5CF70, symSize: 0x4 }
  - { offsetInCU: 0x5C3F, offset: 0x98258, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSHAASQWb', symObjAddr: 0xA6A4, symBinAddr: 0x5CF74, symSize: 0x10 }
  - { offsetInCU: 0x5C53, offset: 0x9826C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAs28CustomDebugStringConvertiblePWb', symObjAddr: 0xA6B4, symBinAddr: 0x5CF84, symSize: 0x10 }
  - { offsetInCU: 0x5C67, offset: 0x98280, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs06CodingM0AAs23CustomStringConvertiblePWb', symObjAddr: 0xA6C4, symBinAddr: 0x5CF94, symSize: 0x10 }
  - { offsetInCU: 0x5CA4, offset: 0x982BD, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_ypTg5', symObjAddr: 0x0, symBinAddr: 0x52B10, symSize: 0xE4 }
  - { offsetInCU: 0x5D51, offset: 0x9836A, size: 0x8, addend: 0x0, symName: '_$sSD11removeValue6forKeyq_Sgx_tFSS_9Capacitor07EncodedB033_762F18EA3FB6340CB5CC34BC6E29A243LLOTg5', symObjAddr: 0xE4, symBinAddr: 0x52BF4, symSize: 0xF4 }
  - { offsetInCU: 0x5E25, offset: 0x9843E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14JSValueEncoderC24OptionalEncodingStrategyOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x5EC, symBinAddr: 0x530FC, symSize: 0x40 }
  - { offsetInCU: 0x60E1, offset: 0x986FA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x44E0, symBinAddr: 0x56F64, symSize: 0x54 }
  - { offsetInCU: 0x60FD, offset: 0x98716, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSHAASH9hashValueSivgTW', symObjAddr: 0x4534, symBinAddr: 0x56FB8, symSize: 0x4C }
  - { offsetInCU: 0x6119, offset: 0x98732, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x4580, symBinAddr: 0x57004, symSize: 0x50 }
  - { offsetInCU: 0x6135, offset: 0x9874E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_GSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x45D0, symBinAddr: 0x57054, symSize: 0x50 }
  - { offsetInCU: 0x6157, offset: 0x98770, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs28CustomDebugStringConvertibleAAsAHP16debugDescriptionSSvgTW', symObjAddr: 0x46E8, symBinAddr: 0x5716C, symSize: 0x34 }
  - { offsetInCU: 0x6173, offset: 0x9878C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLC8SuperKeyOyx_Gs23CustomStringConvertibleAAsAHP11descriptionSSvgTW', symObjAddr: 0x471C, symBinAddr: 0x571A0, symSize: 0x34 }
  - { offsetInCU: 0x6195, offset: 0x987AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor14KeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCyxGs0b8EncodingC8ProtocolAAsAFP17encodeConditional_6forKeyyqd___0Q0QztKRld__CSERd__lFTW', symObjAddr: 0x4DCC, symBinAddr: 0x57850, symSize: 0x28 }
  - { offsetInCU: 0x61F1, offset: 0x9880A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP17encodeConditionalyyqd__KRld__CSERd__lFTW', symObjAddr: 0x68C8, symBinAddr: 0x5934C, symSize: 0x28 }
  - { offsetInCU: 0x6214, offset: 0x9882D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__Sb7ElementRtd__lFTW', symObjAddr: 0x68F0, symBinAddr: 0x59374, symSize: 0x28 }
  - { offsetInCU: 0x6237, offset: 0x98850, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__SS7ElementRtd__lFTW', symObjAddr: 0x6918, symBinAddr: 0x5939C, symSize: 0x28 }
  - { offsetInCU: 0x625A, offset: 0x98873, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__Sd7ElementRtd__lFTW', symObjAddr: 0x6940, symBinAddr: 0x593C4, symSize: 0x28 }
  - { offsetInCU: 0x627D, offset: 0x98896, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__Sf7ElementRtd__lFTW', symObjAddr: 0x6968, symBinAddr: 0x593EC, symSize: 0x28 }
  - { offsetInCU: 0x62A0, offset: 0x988B9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__Si7ElementRtd__lFTW', symObjAddr: 0x6990, symBinAddr: 0x59414, symSize: 0x28 }
  - { offsetInCU: 0x62C3, offset: 0x988DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s4Int8V7ElementRtd__lFTW', symObjAddr: 0x69B8, symBinAddr: 0x5943C, symSize: 0x28 }
  - { offsetInCU: 0x62E6, offset: 0x988FF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s5Int16V7ElementRtd__lFTW', symObjAddr: 0x69E0, symBinAddr: 0x59464, symSize: 0x28 }
  - { offsetInCU: 0x6309, offset: 0x98922, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s5Int32V7ElementRtd__lFTW', symObjAddr: 0x6A08, symBinAddr: 0x5948C, symSize: 0x28 }
  - { offsetInCU: 0x632C, offset: 0x98945, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s5Int64V7ElementRtd__lFTW', symObjAddr: 0x6A30, symBinAddr: 0x594B4, symSize: 0x28 }
  - { offsetInCU: 0x634F, offset: 0x98968, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__Su7ElementRtd__lFTW', symObjAddr: 0x6A58, symBinAddr: 0x594DC, symSize: 0x28 }
  - { offsetInCU: 0x6372, offset: 0x9898B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s5UInt8V7ElementRtd__lFTW', symObjAddr: 0x6A80, symBinAddr: 0x59504, symSize: 0x28 }
  - { offsetInCU: 0x6395, offset: 0x989AE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s6UInt16V7ElementRtd__lFTW', symObjAddr: 0x6AA8, symBinAddr: 0x5952C, symSize: 0x28 }
  - { offsetInCU: 0x63B8, offset: 0x989D1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s6UInt32V7ElementRtd__lFTW', symObjAddr: 0x6AD0, symBinAddr: 0x59554, symSize: 0x28 }
  - { offsetInCU: 0x63DB, offset: 0x989F4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__s6UInt64V7ElementRtd__lFTW', symObjAddr: 0x6AF8, symBinAddr: 0x5957C, symSize: 0x28 }
  - { offsetInCU: 0x63FE, offset: 0x98A17, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16UnkeyedContainer33_762F18EA3FB6340CB5CC34BC6E29A243LLCs0b8EncodingC0AAsAEP6encode10contentsOfyqd___tKSTRd__SE7ElementRpd__lFTW', symObjAddr: 0x6B20, symBinAddr: 0x595A4, symSize: 0x30 }
  - { offsetInCU: 0x135, offset: 0x99118, size: 0x8, addend: 0x0, symName: '_$sSD9CapacitorSSRszAA7JSValue_pRs_rlE7keyPathAaB_pSgAA03KeyD0V_tcig', symObjAddr: 0x0, symBinAddr: 0x5CFB8, symSize: 0x25C }
  - { offsetInCU: 0x446, offset: 0x99429, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathV13stringLiteralACSS_tcfC', symObjAddr: 0x25C, symBinAddr: 0x5D214, symSize: 0x30 }
  - { offsetInCU: 0x4C5, offset: 0x994A8, size: 0x8, addend: 0x0, symName: '_$ss20_ArrayBufferProtocolPsE15replaceSubrange_4with10elementsOfySnySiG_Siqd__ntSlRd__7ElementQyd__AGRtzlFs01_aB0VySSG_s15EmptyCollectionVySSGTg5Tf4nndn_n', symObjAddr: 0x2DC, symBinAddr: 0x5D294, symSize: 0x18C }
  - { offsetInCU: 0x5F7, offset: 0x995DA, size: 0x8, addend: 0x0, symName: '_$sSa15replaceSubrange_4withySnySiG_qd__nt7ElementQyd__RszSlRd__lFSS_s15EmptyCollectionVySSGTg5Tf4ndn_n', symObjAddr: 0x468, symBinAddr: 0x5D420, symSize: 0xC0 }
  - { offsetInCU: 0x6FA, offset: 0x996DD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVs26ExpressibleByStringLiteralAAs0de23ExtendedGraphemeClusterG0PWb', symObjAddr: 0x6A8, symBinAddr: 0x5D534, symSize: 0x4 }
  - { offsetInCU: 0x70E, offset: 0x996F1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVACs43ExpressibleByExtendedGraphemeClusterLiteralAAWl', symObjAddr: 0x6AC, symBinAddr: 0x5D538, symSize: 0x44 }
  - { offsetInCU: 0x722, offset: 0x99705, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVs26ExpressibleByStringLiteralAA0fG4TypesADP_s01_de7BuiltinfG0PWT', symObjAddr: 0x6F0, symBinAddr: 0x5D57C, symSize: 0xC }
  - { offsetInCU: 0x736, offset: 0x99719, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVs43ExpressibleByExtendedGraphemeClusterLiteralAAs0de13UnicodeScalarI0PWb', symObjAddr: 0x6FC, symBinAddr: 0x5D588, symSize: 0x4 }
  - { offsetInCU: 0x74A, offset: 0x9972D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVACs33ExpressibleByUnicodeScalarLiteralAAWl', symObjAddr: 0x700, symBinAddr: 0x5D58C, symSize: 0x44 }
  - { offsetInCU: 0x75E, offset: 0x99741, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVs43ExpressibleByExtendedGraphemeClusterLiteralAA0fghI4TypesADP_s01_de7BuiltinfghI0PWT', symObjAddr: 0x744, symBinAddr: 0x5D5D0, symSize: 0xC }
  - { offsetInCU: 0x772, offset: 0x99755, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVs33ExpressibleByUnicodeScalarLiteralAA0fgH4TypesADP_s01_de7BuiltinfgH0PWT', symObjAddr: 0x750, symBinAddr: 0x5D5DC, symSize: 0xC }
  - { offsetInCU: 0x786, offset: 0x99769, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7KeyPathVMa', symObjAddr: 0x75C, symBinAddr: 0x5D5E8, symSize: 0x10 }
  - { offsetInCU: 0x2B, offset: 0x99999, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC8standardACvau', symObjAddr: 0x0, symBinAddr: 0x5D600, symSize: 0x40 }
  - { offsetInCU: 0x4F, offset: 0x999BD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC8standardACvpZ', symObjAddr: 0xDD68, symBinAddr: 0xA6260, symSize: 0x0 }
  - { offsetInCU: 0x93, offset: 0x99A01, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7backendAcA0bcD7Backend_p_tcfC', symObjAddr: 0x40, symBinAddr: 0x5D640, symSize: 0x40 }
  - { offsetInCU: 0xD9, offset: 0x99A47, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7backendAcA0bcD7Backend_p_tcfc', symObjAddr: 0x80, symBinAddr: 0x5D680, symSize: 0x1C }
  - { offsetInCU: 0x172, offset: 0x99AE0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC4typeA2C7BackendO_tcfC', symObjAddr: 0x9C, symBinAddr: 0x5D69C, symSize: 0x160 }
  - { offsetInCU: 0x2C3, offset: 0x99C31, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC4typeA2C7BackendO_tcfc', symObjAddr: 0x1FC, symBinAddr: 0x5D7FC, symSize: 0x140 }
  - { offsetInCU: 0x419, offset: 0x99D87, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC9suiteNameACSS_tcfC', symObjAddr: 0x33C, symBinAddr: 0x5D93C, symSize: 0x70 }
  - { offsetInCU: 0x4A2, offset: 0x99E10, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC3get_2asxSgSS_xmtKSeRzlF', symObjAddr: 0x3AC, symBinAddr: 0x5D9AC, symSize: 0x94 }
  - { offsetInCU: 0x4DF, offset: 0x99E4D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC3set_5valueySS_xtKSERzlF', symObjAddr: 0x440, symBinAddr: 0x5DA40, symSize: 0x8C }
  - { offsetInCU: 0x51C, offset: 0x99E8A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC6deleteyySSKF', symObjAddr: 0x4CC, symBinAddr: 0x5DACC, symSize: 0x64 }
  - { offsetInCU: 0x58B, offset: 0x99EF9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC_2asxSgSS_xmtcSeRzSERzluig', symObjAddr: 0x530, symBinAddr: 0x5DB30, symSize: 0xCC }
  - { offsetInCU: 0x694, offset: 0x9A002, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC_2asxSgSS_xmtcSeRzSERzluis', symObjAddr: 0x5FC, symBinAddr: 0x5DBFC, symSize: 0x1D0 }
  - { offsetInCU: 0x779, offset: 0x9A0E7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC_2asxSgSS_xmtcSeRzSERzluiM', symObjAddr: 0x7CC, symBinAddr: 0x5DDCC, symSize: 0xD0 }
  - { offsetInCU: 0x795, offset: 0x9A103, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC_2asxSgSS_xmtcSeRzSERzluiM.resume.0', symObjAddr: 0x89C, symBinAddr: 0x5DE9C, symSize: 0xDC }
  - { offsetInCU: 0x7B1, offset: 0x9A11F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC8standardACvgZ', symObjAddr: 0x9F8, symBinAddr: 0x5DFF8, symSize: 0x40 }
  - { offsetInCU: 0x7DA, offset: 0x9A148, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreCfd', symObjAddr: 0xA38, symBinAddr: 0x5E038, symSize: 0x1C }
  - { offsetInCU: 0x815, offset: 0x9A183, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreCfD', symObjAddr: 0xA54, symBinAddr: 0x5E054, symSize: 0x24 }
  - { offsetInCU: 0x86D, offset: 0x9A1DB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC9instancesAA20ConcurrentDictionaryCyADGvpZ', symObjAddr: 0x2438, symBinAddr: 0xA28D0, symSize: 0x0 }
  - { offsetInCU: 0x887, offset: 0x9A1F5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC8standardACvau', symObjAddr: 0x0, symBinAddr: 0x5D600, symSize: 0x40 }
  - { offsetInCU: 0x8C8, offset: 0x9A236, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC3get_2asxSgSS_xmtKSeRzlF', symObjAddr: 0xA78, symBinAddr: 0x5E078, symSize: 0x584 }
  - { offsetInCU: 0xABE, offset: 0x9A42C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC3set_5valueySS_xtKSERzlF', symObjAddr: 0xFFC, symBinAddr: 0x5E5FC, symSize: 0x1BC }
  - { offsetInCU: 0xBD8, offset: 0x9A546, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC6deleteyySSKF', symObjAddr: 0x11B8, symBinAddr: 0x5E7B8, symSize: 0x1EC }
  - { offsetInCU: 0xCEC, offset: 0x9A65A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCfD', symObjAddr: 0x1438, symBinAddr: 0x5EA38, symSize: 0x68 }
  - { offsetInCU: 0xD31, offset: 0x9A69F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueC7BackendA2aEP3get_2asqd__SgSS_qd__mtKSeRd__lFTW', symObjAddr: 0x14A0, symBinAddr: 0x5EAA0, symSize: 0x20 }
  - { offsetInCU: 0xD54, offset: 0x9A6C2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueC7BackendA2aEP3set_5valueySS_qd__tKSERd__lFTW', symObjAddr: 0x14C0, symBinAddr: 0x5EAC0, symSize: 0x20 }
  - { offsetInCU: 0xD77, offset: 0x9A6E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueC7BackendA2aEP6deleteyySSKFTW', symObjAddr: 0x14E0, symBinAddr: 0x5EAE0, symSize: 0x20 }
  - { offsetInCU: 0xDD5, offset: 0x9A743, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC4with4nameADSS_tFZTf4nd_n', symObjAddr: 0x193C, symBinAddr: 0x5EF18, symSize: 0x634 }
  - { offsetInCU: 0x1097, offset: 0x9AA05, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC3get_2asxSgSS_xmtKSeRzlF', symObjAddr: 0x1500, symBinAddr: 0x5EB00, symSize: 0x188 }
  - { offsetInCU: 0x1184, offset: 0x9AAF2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC3set_5valueySS_xtKSERzlF', symObjAddr: 0x1688, symBinAddr: 0x5EC88, symSize: 0x118 }
  - { offsetInCU: 0x1280, offset: 0x9ABEE, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC6deleteyySSF', symObjAddr: 0x17A0, symBinAddr: 0x5EDA0, symSize: 0xB0 }
  - { offsetInCU: 0x136A, offset: 0x9ACD8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCfD', symObjAddr: 0x1850, symBinAddr: 0x5EE50, symSize: 0x34 }
  - { offsetInCU: 0x13AD, offset: 0x9AD1B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueD7BackendA2aEP3get_2asqd__SgSS_qd__mtKSeRd__lFTW', symObjAddr: 0x1884, symBinAddr: 0x5EE84, symSize: 0x20 }
  - { offsetInCU: 0x13D0, offset: 0x9AD3E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueD7BackendA2aEP3set_5valueySS_qd__tKSERd__lFTW', symObjAddr: 0x18A4, symBinAddr: 0x5EEA4, symSize: 0x20 }
  - { offsetInCU: 0x13F3, offset: 0x9AD61, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCAA08KeyValueD7BackendA2aEP6deleteyySSKFTW', symObjAddr: 0x18C4, symBinAddr: 0x5EEC4, symSize: 0x28 }
  - { offsetInCU: 0x1595, offset: 0x9AF03, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20ConcurrentDictionaryCfD', symObjAddr: 0x1910, symBinAddr: 0x5EEEC, symSize: 0x2C }
  - { offsetInCU: 0x16A6, offset: 0x9B014, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC8standard_WZ', symObjAddr: 0x978, symBinAddr: 0x5DF78, symSize: 0x68 }
  - { offsetInCU: 0x1723, offset: 0x9B091, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20KeyValueStoreBackend_pWOb', symObjAddr: 0x9E0, symBinAddr: 0x5DFE0, symSize: 0x18 }
  - { offsetInCU: 0x1777, offset: 0x9B0E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLC9instances_WZ', symObjAddr: 0x13A4, symBinAddr: 0x5E9A4, symSize: 0x94 }
  - { offsetInCU: 0x185F, offset: 0x9B1CD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCMa', symObjAddr: 0x1F70, symBinAddr: 0x5F54C, symSize: 0x3C }
  - { offsetInCU: 0x1873, offset: 0x9B1E1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13InMemoryStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCMa', symObjAddr: 0x1FAC, symBinAddr: 0x5F588, symSize: 0x20 }
  - { offsetInCU: 0x1887, offset: 0x9B1F5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreCMa', symObjAddr: 0x200C, symBinAddr: 0x5F5A8, symSize: 0x20 }
  - { offsetInCU: 0x189B, offset: 0x9B209, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwCP', symObjAddr: 0x2070, symBinAddr: 0x5F5C8, symSize: 0x2C }
  - { offsetInCU: 0x18AF, offset: 0x9B21D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwxx', symObjAddr: 0x209C, symBinAddr: 0x5F5F4, symSize: 0x8 }
  - { offsetInCU: 0x18C3, offset: 0x9B231, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwcp', symObjAddr: 0x20A4, symBinAddr: 0x5F5FC, symSize: 0x2C }
  - { offsetInCU: 0x18D7, offset: 0x9B245, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwca', symObjAddr: 0x20D0, symBinAddr: 0x5F628, symSize: 0x40 }
  - { offsetInCU: 0x18EB, offset: 0x9B259, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwta', symObjAddr: 0x211C, symBinAddr: 0x5F668, symSize: 0x30 }
  - { offsetInCU: 0x18FF, offset: 0x9B26D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwet', symObjAddr: 0x214C, symBinAddr: 0x5F698, symSize: 0x50 }
  - { offsetInCU: 0x1913, offset: 0x9B281, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwst', symObjAddr: 0x219C, symBinAddr: 0x5F6E8, symSize: 0x54 }
  - { offsetInCU: 0x1927, offset: 0x9B295, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwug', symObjAddr: 0x21F0, symBinAddr: 0x5F73C, symSize: 0x18 }
  - { offsetInCU: 0x193B, offset: 0x9B2A9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwup', symObjAddr: 0x2208, symBinAddr: 0x5F754, symSize: 0x4 }
  - { offsetInCU: 0x194F, offset: 0x9B2BD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOwui', symObjAddr: 0x220C, symBinAddr: 0x5F758, symSize: 0x20 }
  - { offsetInCU: 0x1963, offset: 0x9B2D1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor13KeyValueStoreC7BackendOMa', symObjAddr: 0x222C, symBinAddr: 0x5F778, symSize: 0x10 }
  - { offsetInCU: 0x1977, offset: 0x9B2E5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCMU', symObjAddr: 0x223C, symBinAddr: 0x5F788, symSize: 0x8 }
  - { offsetInCU: 0x198B, offset: 0x9B2F9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor9FileStore33_C037C347CF1CD06D719C8EAD67CE5F9DLLCMr', symObjAddr: 0x2244, symBinAddr: 0x5F790, symSize: 0x80 }
  - { offsetInCU: 0x199F, offset: 0x9B30D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20ConcurrentDictionaryCMi', symObjAddr: 0x22C4, symBinAddr: 0x5F810, symSize: 0x4 }
  - { offsetInCU: 0x19B3, offset: 0x9B321, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20ConcurrentDictionaryCMr', symObjAddr: 0x22C8, symBinAddr: 0x5F814, symSize: 0x50 }
  - { offsetInCU: 0x19C7, offset: 0x9B335, size: 0x8, addend: 0x0, symName: '_$s9Capacitor20ConcurrentDictionaryCMa', symObjAddr: 0x2318, symBinAddr: 0x5F864, symSize: 0xC }
  - { offsetInCU: 0x4F, offset: 0x9B718, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCACycfC', symObjAddr: 0x0, symBinAddr: 0x5F870, symSize: 0x20 }
  - { offsetInCU: 0x6D, offset: 0x9B736, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC30handleApplicationNotificationsSbvs', symObjAddr: 0x20, symBinAddr: 0x5F890, symSize: 0xA4 }
  - { offsetInCU: 0xC1, offset: 0x9B78A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04pushB7HandlerAA0bE8Protocol_pSgvM', symObjAddr: 0x138, symBinAddr: 0x5F968, symSize: 0x70 }
  - { offsetInCU: 0xF0, offset: 0x9B7B9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC05localB7HandlerAA0bE8Protocol_pSgvM', symObjAddr: 0x3A8, symBinAddr: 0x5FBD8, symSize: 0x70 }
  - { offsetInCU: 0x15B, offset: 0x9B824, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04userB6Center_11willPresent21withCompletionHandlerySo06UNUserbE0C_So14UNNotificationCySo0L19PresentationOptionsVctF', symObjAddr: 0x478, symBinAddr: 0x5FCA8, symSize: 0x11C }
  - { offsetInCU: 0x1F2, offset: 0x9B8BB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04userB6Center_10didReceive21withCompletionHandlerySo06UNUserbE0C_So22UNNotificationResponseCyyctF', symObjAddr: 0x5A0, symBinAddr: 0x5FDD0, symSize: 0x130 }
  - { offsetInCU: 0x289, offset: 0x9B952, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCACycfc', symObjAddr: 0x784, symBinAddr: 0x5FFB4, symSize: 0x58 }
  - { offsetInCU: 0x2C4, offset: 0x9B98D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCACycfcTo', symObjAddr: 0x7DC, symBinAddr: 0x6000C, symSize: 0x64 }
  - { offsetInCU: 0x2FF, offset: 0x9B9C8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCfD', symObjAddr: 0x840, symBinAddr: 0x60070, symSize: 0x30 }
  - { offsetInCU: 0x32C, offset: 0x9B9F5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04userB6Center_11willPresent21withCompletionHandlerySo06UNUserbE0C_So14UNNotificationCySo0L19PresentationOptionsVctF06$sSo33lmN16VIeyBy_ABIegy_TRALIeyBy_Tf1nncn_nTf4dnng_n', symObjAddr: 0x904, symBinAddr: 0x60134, symSize: 0x11C }
  - { offsetInCU: 0x3BA, offset: 0x9BA83, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterC04userB6Center_10didReceive21withCompletionHandlerySo06UNUserbE0C_So22UNNotificationResponseCyyctF13$sIeyB_Ieg_TRIeyB_Tf1nncn_nTf4dnng_n', symObjAddr: 0xA20, symBinAddr: 0x60250, symSize: 0x130 }
  - { offsetInCU: 0x449, offset: 0x9BB12, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCfETo', symObjAddr: 0x870, symBinAddr: 0x600A0, symSize: 0x38 }
  - { offsetInCU: 0x478, offset: 0x9BB41, size: 0x8, addend: 0x0, symName: '_$sSo25UNPushNotificationTriggerCMa', symObjAddr: 0x8A8, symBinAddr: 0x600D8, symSize: 0x3C }
  - { offsetInCU: 0x48C, offset: 0x9BB55, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18NotificationRouterCMa', symObjAddr: 0x8E4, symBinAddr: 0x60114, symSize: 0x20 }
  - { offsetInCU: 0x4B6, offset: 0x9BB7F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor27NotificationHandlerProtocol_pSgXwWOh', symObjAddr: 0xB50, symBinAddr: 0x60380, symSize: 0x24 }
  - { offsetInCU: 0x4F, offset: 0x9BD6B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO9formatter031_F806BC7C3B9E74076D863322397580I0LLSo22NSISO8601DateFormatterCvpZ', symObjAddr: 0x2010, symBinAddr: 0xA2C48, symSize: 0x0 }
  - { offsetInCU: 0xC0, offset: 0x9BDDC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCyACSDySSypGSgcfC', symObjAddr: 0x40, symBinAddr: 0x603E4, symSize: 0x4C }
  - { offsetInCU: 0x107, offset: 0x9BE23, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultC10resultDataAA06PlugincD0OSgvg', symObjAddr: 0x4E8, symBinAddr: 0x6088C, symSize: 0x10 }
  - { offsetInCU: 0x134, offset: 0x9BE50, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCyACSDySSypGSgcfc', symObjAddr: 0x504, symBinAddr: 0x608A8, symSize: 0x3C }
  - { offsetInCU: 0x15B, offset: 0x9BE77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCyACSDySSypGSgcfcTo', symObjAddr: 0x540, symBinAddr: 0x608E4, symSize: 0x7C }
  - { offsetInCU: 0x18D, offset: 0x9BEA9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCACycfc', symObjAddr: 0x5C8, symBinAddr: 0x6096C, symSize: 0x2C }
  - { offsetInCU: 0x1F0, offset: 0x9BF0C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCACycfcTo', symObjAddr: 0x5F4, symBinAddr: 0x60998, symSize: 0x2C }
  - { offsetInCU: 0x285, offset: 0x9BFA1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7message4code5error4dataACSS_SSSgs0D0_pSgSDySSypGSgtcfC', symObjAddr: 0x8C, symBinAddr: 0x60430, symSize: 0x98 }
  - { offsetInCU: 0x2D7, offset: 0x9BFF3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7messageSSvgTo', symObjAddr: 0x63C, symBinAddr: 0x609E0, symSize: 0x4C }
  - { offsetInCU: 0x312, offset: 0x9C02E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7messageSSvg', symObjAddr: 0x688, symBinAddr: 0x60A2C, symSize: 0x38 }
  - { offsetInCU: 0x34F, offset: 0x9C06B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC4codeSSSgvgTo', symObjAddr: 0x6C0, symBinAddr: 0x60A64, symSize: 0x5C }
  - { offsetInCU: 0x382, offset: 0x9C09E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC4codeSSSgvg', symObjAddr: 0x71C, symBinAddr: 0x60AC0, symSize: 0x38 }
  - { offsetInCU: 0x3BF, offset: 0x9C0DB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC5errors0D0_pSgvgTo', symObjAddr: 0x754, symBinAddr: 0x60AF8, symSize: 0x50 }
  - { offsetInCU: 0x3FA, offset: 0x9C116, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC5errors0D0_pSgvg', symObjAddr: 0x7A4, symBinAddr: 0x60B48, symSize: 0x30 }
  - { offsetInCU: 0x419, offset: 0x9C135, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC10resultDataAA06PluginC6ResultOSgvg', symObjAddr: 0x7D4, symBinAddr: 0x60B78, symSize: 0x10 }
  - { offsetInCU: 0x446, offset: 0x9C162, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7message4code5error4dataACSS_SSSgs0D0_pSgSDySSypGSgtcfc', symObjAddr: 0x854, symBinAddr: 0x60BF8, symSize: 0x64 }
  - { offsetInCU: 0x45A, offset: 0x9C176, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7message4code5error4dataACSS_SSSgs0D0_pSgSDySSypGSgtcfcTo', symObjAddr: 0x8B8, symBinAddr: 0x60C5C, symSize: 0xEC }
  - { offsetInCU: 0x48C, offset: 0x9C1A8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorCACycfc', symObjAddr: 0x9E0, symBinAddr: 0x60D84, symSize: 0x2C }
  - { offsetInCU: 0x4EF, offset: 0x9C20B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorCACycfcTo', symObjAddr: 0xA0C, symBinAddr: 0x60DB0, symSize: 0x2C }
  - { offsetInCU: 0x556, offset: 0x9C272, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorC7message4code5error4dataACSS_SSSgs0D0_pSgSDySSypGSgtcfcTf4ggggn_n', symObjAddr: 0x1864, symBinAddr: 0x61C08, symSize: 0x17C }
  - { offsetInCU: 0x6E9, offset: 0x9C405, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18jsonRepresentation15includingFieldsSSSgSDySSypGSg_tKF', symObjAddr: 0x124, symBinAddr: 0x604C8, symSize: 0x2DC }
  - { offsetInCU: 0x804, offset: 0x9C520, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x400, symBinAddr: 0x607A4, symSize: 0x8 }
  - { offsetInCU: 0x858, offset: 0x9C574, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOSHAASH9hashValueSivgTW', symObjAddr: 0x408, symBinAddr: 0x607AC, symSize: 0x40 }
  - { offsetInCU: 0x93B, offset: 0x9C657, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x448, symBinAddr: 0x607EC, symSize: 0x24 }
  - { offsetInCU: 0xA82, offset: 0x9C79E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO9formatter031_F806BC7C3B9E74076D863322397580I0LL_WZ', symObjAddr: 0x4B8, symBinAddr: 0x6085C, symSize: 0x30 }
  - { offsetInCU: 0xADD, offset: 0x9C7F9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCfETo', symObjAddr: 0x62C, symBinAddr: 0x609D0, symSize: 0x10 }
  - { offsetInCU: 0xB0C, offset: 0x9C828, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorCfETo', symObjAddr: 0xA74, symBinAddr: 0x60E18, symSize: 0x60 }
  - { offsetInCU: 0xBCA, offset: 0x9C8E6, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFSS_yps15LazyMapSequenceVySDySSypGSS_yptGTg599$s9Capacitor16PluginCallResultO18jsonRepresentation15includingFieldsSSSgSDySSypGSg_tKFypyp_yptXEfU_Tf1nncn_n', symObjAddr: 0xAD4, symBinAddr: 0x60E78, symSize: 0x264 }
  - { offsetInCU: 0xD27, offset: 0x9CA43, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRSS_ypTg575$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_SS_ypTG5Tf3nnpf_n', symObjAddr: 0xD38, symBinAddr: 0x610DC, symSize: 0x40 }
  - { offsetInCU: 0xDAB, offset: 0x9CAC7, size: 0x8, addend: 0x0, symName: '_$ss15LazyMapSequenceV8IteratorV4nextq_SgyFSDySSypG_SS_yptTg5', symObjAddr: 0xD78, symBinAddr: 0x6111C, symSize: 0x138 }
  - { offsetInCU: 0xE4E, offset: 0x9CB6A, size: 0x8, addend: 0x0, symName: '_$sSq3mapyqd__Sgqd__xKXEKlFSS3key_yp5valuet_SS_yptTg5', symObjAddr: 0xEB0, symBinAddr: 0x61254, symSize: 0xAC }
  - { offsetInCU: 0xE7E, offset: 0x9CB9A, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV9mapValuesyAByxqd__Gqd__q_KXEKlFSS_ypypTg5111$s9Capacitor16PluginCallResultO7prepare031_F806BC7C3B9E74076D863322397580I0LL10dictionarySDySSypGAG_tFypypXEfU_9Capacitor0ghI0OTf1cn_nTf4ng_n', symObjAddr: 0xF5C, symBinAddr: 0x61300, symSize: 0x520 }
  - { offsetInCU: 0x10A3, offset: 0x9CDBF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19CAPPluginCallResultCMa', symObjAddr: 0x19E0, symBinAddr: 0x61D84, symSize: 0x20 }
  - { offsetInCU: 0x10B7, offset: 0x9CDD3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor18CAPPluginCallErrorCMa', symObjAddr: 0x1A00, symBinAddr: 0x61DA4, symSize: 0x20 }
  - { offsetInCU: 0x10CB, offset: 0x9CDE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwCP', symObjAddr: 0x1A20, symBinAddr: 0x61DC4, symSize: 0x2C }
  - { offsetInCU: 0x10DF, offset: 0x9CDFB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwxx', symObjAddr: 0x1A4C, symBinAddr: 0x61DF0, symSize: 0x8 }
  - { offsetInCU: 0x10F3, offset: 0x9CE0F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwcp', symObjAddr: 0x1A54, symBinAddr: 0x61DF8, symSize: 0x2C }
  - { offsetInCU: 0x1107, offset: 0x9CE23, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwca', symObjAddr: 0x1A80, symBinAddr: 0x61E24, symSize: 0x38 }
  - { offsetInCU: 0x111B, offset: 0x9CE37, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwta', symObjAddr: 0x1AC4, symBinAddr: 0x61E5C, symSize: 0x30 }
  - { offsetInCU: 0x112F, offset: 0x9CE4B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwet', symObjAddr: 0x1AF4, symBinAddr: 0x61E8C, symSize: 0x48 }
  - { offsetInCU: 0x1143, offset: 0x9CE5F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwst', symObjAddr: 0x1B3C, symBinAddr: 0x61ED4, symSize: 0x3C }
  - { offsetInCU: 0x1157, offset: 0x9CE73, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwug', symObjAddr: 0x1B78, symBinAddr: 0x61F10, symSize: 0x8 }
  - { offsetInCU: 0x116B, offset: 0x9CE87, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwup', symObjAddr: 0x1B80, symBinAddr: 0x61F18, symSize: 0x4 }
  - { offsetInCU: 0x117F, offset: 0x9CE9B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOwui', symObjAddr: 0x1B84, symBinAddr: 0x61F1C, symSize: 0x4 }
  - { offsetInCU: 0x1193, offset: 0x9CEAF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultOMa', symObjAddr: 0x1B88, symBinAddr: 0x61F20, symSize: 0x10 }
  - { offsetInCU: 0x11A7, offset: 0x9CEC3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOAEs0F0AAWl', symObjAddr: 0x1B98, symBinAddr: 0x61F30, symSize: 0x44 }
  - { offsetInCU: 0x11BB, offset: 0x9CED7, size: 0x8, addend: 0x0, symName: '_$sSS3key_yp5valuetSgWOc', symObjAddr: 0x1CDC, symBinAddr: 0x61F74, symSize: 0x48 }
  - { offsetInCU: 0x11CF, offset: 0x9CEEB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOwet', symObjAddr: 0x1D68, symBinAddr: 0x61FF8, symSize: 0x50 }
  - { offsetInCU: 0x11E3, offset: 0x9CEFF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOwst', symObjAddr: 0x1DB8, symBinAddr: 0x62048, symSize: 0x8C }
  - { offsetInCU: 0x11F7, offset: 0x9CF13, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOwug', symObjAddr: 0x1E44, symBinAddr: 0x620D4, symSize: 0x8 }
  - { offsetInCU: 0x120B, offset: 0x9CF27, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOwup', symObjAddr: 0x1E4C, symBinAddr: 0x620DC, symSize: 0x4 }
  - { offsetInCU: 0x121F, offset: 0x9CF3B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOwui', symObjAddr: 0x1E50, symBinAddr: 0x620E0, symSize: 0x4 }
  - { offsetInCU: 0x1233, offset: 0x9CF4F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOMa', symObjAddr: 0x1E54, symBinAddr: 0x620E4, symSize: 0x10 }
  - { offsetInCU: 0x1247, offset: 0x9CF63, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOSHAASQWb', symObjAddr: 0x1E64, symBinAddr: 0x620F4, symSize: 0x4 }
  - { offsetInCU: 0x125B, offset: 0x9CF77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOAESQAAWl', symObjAddr: 0x1E68, symBinAddr: 0x620F8, symSize: 0x44 }
  - { offsetInCU: 0x12A6, offset: 0x9CFC2, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSayypG_ypTg5103$s9Capacitor16PluginCallResultO7prepare031_F806BC7C3B9E74076D863322397580I0LL5arraySayypGAG_tFypypXEfU_9Capacitor0efG0OTf1cn_n', symObjAddr: 0x0, symBinAddr: 0x603A4, symSize: 0x40 }
  - { offsetInCU: 0x1343, offset: 0x9D05F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x46C, symBinAddr: 0x60810, symSize: 0x3C }
  - { offsetInCU: 0x13DF, offset: 0x9D0FB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOs0F0AAsAFP7_domainSSvgTW', symObjAddr: 0x4A8, symBinAddr: 0x6084C, symSize: 0x4 }
  - { offsetInCU: 0x13FB, offset: 0x9D117, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOs0F0AAsAFP5_codeSivgTW', symObjAddr: 0x4AC, symBinAddr: 0x60850, symSize: 0x4 }
  - { offsetInCU: 0x1417, offset: 0x9D133, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOs0F0AAsAFP9_userInfoyXlSgvgTW', symObjAddr: 0x4B0, symBinAddr: 0x60854, symSize: 0x4 }
  - { offsetInCU: 0x1433, offset: 0x9D14F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16PluginCallResultO18SerializationErrorOs0F0AAsAFP19_getEmbeddedNSErroryXlSgyFTW', symObjAddr: 0x4B4, symBinAddr: 0x60858, symSize: 0x4 }
  - { offsetInCU: 0x14D6, offset: 0x9D1F2, size: 0x8, addend: 0x0, symName: '_$sSlsE3mapySayqd__Gqd__7ElementQzKXEKlFSayypG_ypTg5103$s9Capacitor16PluginCallResultO7prepare031_F806BC7C3B9E74076D863322397580I0LL5arraySayypGAG_tFypypXEfU_9Capacitor0efG0OTf1cn_nTf4ng_n', symObjAddr: 0x147C, symBinAddr: 0x61820, symSize: 0x3E8 }
  - { offsetInCU: 0x2B, offset: 0x9D5F0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC9getStringySSSgSS_AEtF', symObjAddr: 0x0, symBinAddr: 0x62144, symSize: 0xF8 }
  - { offsetInCU: 0x6D, offset: 0x9D632, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC9getStringySSSgSS_AEtF', symObjAddr: 0x0, symBinAddr: 0x62144, symSize: 0xF8 }
  - { offsetInCU: 0xF2, offset: 0x9D6B7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC9getStringySSSgSS_AEtFTo', symObjAddr: 0x1BC, symBinAddr: 0x6223C, symSize: 0xC4 }
  - { offsetInCU: 0x10E, offset: 0x9D6D3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC10getBooleanySbSS_SbtF', symObjAddr: 0x280, symBinAddr: 0x62300, symSize: 0xE0 }
  - { offsetInCU: 0x193, offset: 0x9D758, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC10getBooleanySbSS_SbtFTo', symObjAddr: 0x360, symBinAddr: 0x623E0, symSize: 0x74 }
  - { offsetInCU: 0x1AF, offset: 0x9D774, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC6getIntySiSS_SitF', symObjAddr: 0x3D4, symBinAddr: 0x62454, symSize: 0xE0 }
  - { offsetInCU: 0x234, offset: 0x9D7F9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC6getIntySiSS_SitFTo', symObjAddr: 0x4B4, symBinAddr: 0x62534, symSize: 0x74 }
  - { offsetInCU: 0x250, offset: 0x9D815, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC8getArrayySayAA7JSValue_pGSgSS_AGtF', symObjAddr: 0x528, symBinAddr: 0x625A8, symSize: 0xF4 }
  - { offsetInCU: 0x2D5, offset: 0x9D89A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC9getObjectySDySSAA7JSValue_pGSgSSF', symObjAddr: 0x61C, symBinAddr: 0x6269C, symSize: 0xEC }
  - { offsetInCU: 0x34A, offset: 0x9D90F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC7isEmptySbyF', symObjAddr: 0x708, symBinAddr: 0x62788, symSize: 0x1C }
  - { offsetInCU: 0x36A, offset: 0x9D92F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC7isEmptySbyF', symObjAddr: 0x708, symBinAddr: 0x62788, symSize: 0x1C }
  - { offsetInCU: 0x3CD, offset: 0x9D992, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC7isEmptySbyFTo', symObjAddr: 0x724, symBinAddr: 0x627A4, symSize: 0x1C }
  - { offsetInCU: 0x3ED, offset: 0x9D9B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC7isEmptySbyFTo', symObjAddr: 0x724, symBinAddr: 0x627A4, symSize: 0x1C }
  - { offsetInCU: 0x40A, offset: 0x9D9CF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC7isEmptySbyFTo', symObjAddr: 0x724, symBinAddr: 0x627A4, symSize: 0x1C }
  - { offsetInCU: 0x450, offset: 0x9DA15, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC03getC4JSONSDySSAA7JSValue_pGyF', symObjAddr: 0x740, symBinAddr: 0x627C0, symSize: 0x10 }
  - { offsetInCU: 0x480, offset: 0x9DA45, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigC03getC4JSONSDySSAA7JSValue_pGyF', symObjAddr: 0x740, symBinAddr: 0x627C0, symSize: 0x10 }
  - { offsetInCU: 0x49B, offset: 0x9DA60, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCACycfC', symObjAddr: 0x750, symBinAddr: 0x627D0, symSize: 0x20 }
  - { offsetInCU: 0x4B9, offset: 0x9DA7E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCACycfc', symObjAddr: 0x770, symBinAddr: 0x627F0, symSize: 0x2C }
  - { offsetInCU: 0x51C, offset: 0x9DAE1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCACycfcTo', symObjAddr: 0x79C, symBinAddr: 0x6281C, symSize: 0x2C }
  - { offsetInCU: 0x583, offset: 0x9DB48, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCfD', symObjAddr: 0x7C8, symBinAddr: 0x62848, symSize: 0x30 }
  - { offsetInCU: 0x5FE, offset: 0x9DBC3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCMa', symObjAddr: 0x7F8, symBinAddr: 0x62878, symSize: 0x20 }
  - { offsetInCU: 0x612, offset: 0x9DBD7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor12PluginConfigCfETo', symObjAddr: 0x818, symBinAddr: 0x62898, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x9DDC2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVACycfC', symObjAddr: 0x0, symBinAddr: 0x628A8, symSize: 0xC }
  - { offsetInCU: 0x4B, offset: 0x9DDE6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVACycfC', symObjAddr: 0x0, symBinAddr: 0x628A8, symSize: 0xC }
  - { offsetInCU: 0x7A, offset: 0x9DE15, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV8basePathSSvg', symObjAddr: 0xC, symBinAddr: 0x628B4, symSize: 0x30 }
  - { offsetInCU: 0x8E, offset: 0x9DE29, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV8basePathSSvs', symObjAddr: 0x3C, symBinAddr: 0x628E4, symSize: 0x34 }
  - { offsetInCU: 0xA2, offset: 0x9DE3D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV8basePathSSvM', symObjAddr: 0x70, symBinAddr: 0x62918, symSize: 0x10 }
  - { offsetInCU: 0xBE, offset: 0x9DE59, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV8basePathSSvM.resume.0', symObjAddr: 0x80, symBinAddr: 0x62928, symSize: 0x4 }
  - { offsetInCU: 0xDA, offset: 0x9DE75, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterV5route3forS2S_tF', symObjAddr: 0x84, symBinAddr: 0x6292C, symSize: 0x12C }
  - { offsetInCU: 0x16F, offset: 0x9DF0A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVAA0B0A2aDP5route3forS2S_tFTW', symObjAddr: 0x1B0, symBinAddr: 0x62A58, symSize: 0x8 }
  - { offsetInCU: 0x19A, offset: 0x9DF35, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVAA0B0A2aDP8basePathSSvgTW', symObjAddr: 0x1B8, symBinAddr: 0x62A60, symSize: 0x2C }
  - { offsetInCU: 0x1DB, offset: 0x9DF76, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVAA0B0A2aDP8basePathSSvsTW', symObjAddr: 0x1E4, symBinAddr: 0x62A8C, symSize: 0x34 }
  - { offsetInCU: 0x20D, offset: 0x9DFA8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVAA0B0A2aDP8basePathSSvMTW', symObjAddr: 0x218, symBinAddr: 0x62AC0, symSize: 0x10 }
  - { offsetInCU: 0x229, offset: 0x9DFC4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVAA0B0A2aDP8basePathSSvMTW.resume.0', symObjAddr: 0x228, symBinAddr: 0x62AD0, symSize: 0x4 }
  - { offsetInCU: 0x246, offset: 0x9DFE1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwCP', symObjAddr: 0x22C, symBinAddr: 0x62AD4, symSize: 0x2C }
  - { offsetInCU: 0x25A, offset: 0x9DFF5, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwxx', symObjAddr: 0x258, symBinAddr: 0x62B00, symSize: 0x8 }
  - { offsetInCU: 0x26E, offset: 0x9E009, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwcp', symObjAddr: 0x260, symBinAddr: 0x62B08, symSize: 0x2C }
  - { offsetInCU: 0x282, offset: 0x9E01D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwca', symObjAddr: 0x28C, symBinAddr: 0x62B34, symSize: 0x40 }
  - { offsetInCU: 0x296, offset: 0x9E031, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwta', symObjAddr: 0x2D8, symBinAddr: 0x62B74, symSize: 0x30 }
  - { offsetInCU: 0x2AA, offset: 0x9E045, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwet', symObjAddr: 0x308, symBinAddr: 0x62BA4, symSize: 0x48 }
  - { offsetInCU: 0x2BE, offset: 0x9E059, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVwst', symObjAddr: 0x350, symBinAddr: 0x62BEC, symSize: 0x3C }
  - { offsetInCU: 0x2D2, offset: 0x9E06D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A6RouterVMa', symObjAddr: 0x38C, symBinAddr: 0x62C28, symSize: 0x10 }
  - { offsetInCU: 0x27, offset: 0x9E1D4, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerC13viewDidAppearyySbFTo', symObjAddr: 0x0, symBinAddr: 0x62C38, symSize: 0xF4 }
  - { offsetInCU: 0x75, offset: 0x9E222, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerC13viewDidAppearyySbFTo', symObjAddr: 0x0, symBinAddr: 0x62C38, symSize: 0xF4 }
  - { offsetInCU: 0xE9, offset: 0x9E296, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0xF4, symBinAddr: 0x62D2C, symSize: 0xB0 }
  - { offsetInCU: 0x15A, offset: 0x9E307, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x1A4, symBinAddr: 0x62DDC, symSize: 0x44 }
  - { offsetInCU: 0x19D, offset: 0x9E34A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerCfD', symObjAddr: 0x1E8, symBinAddr: 0x62E20, symSize: 0x30 }
  - { offsetInCU: 0x1DC, offset: 0x9E389, size: 0x8, addend: 0x0, symName: '_$s9Capacitor17TmpViewControllerCMa', symObjAddr: 0x218, symBinAddr: 0x62E50, symSize: 0x20 }
  - { offsetInCU: 0x27, offset: 0x9E53C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo7UIColorCRbzlE5color7fromHexAESgSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x62E70, symSize: 0x2B4 }
  - { offsetInCU: 0x3F, offset: 0x9E554, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo7UIColorCRbzlE5color7fromHexAESgSS_tFZ', symObjAddr: 0x0, symBinAddr: 0x62E70, symSize: 0x2B4 }
  - { offsetInCU: 0x163, offset: 0x9E678, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo7UIColorCRbzlE5color1r1g1b1aAESi_S3itFZ', symObjAddr: 0x2F8, symBinAddr: 0x63124, symSize: 0x70 }
  - { offsetInCU: 0x25B, offset: 0x9E770, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo7UIColorCRbzlE5color4argbAEs6UInt32V_tFZ', symObjAddr: 0x368, symBinAddr: 0x63194, symSize: 0x64 }
  - { offsetInCU: 0x27, offset: 0x9EA1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC18setServerAssetPathyySo13CAPPluginCallCF', symObjAddr: 0x0, symBinAddr: 0x631F8, symSize: 0x328 }
  - { offsetInCU: 0x4B, offset: 0x9EA42, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC18setServerAssetPathyySo13CAPPluginCallCF', symObjAddr: 0x0, symBinAddr: 0x631F8, symSize: 0x328 }
  - { offsetInCU: 0xB8, offset: 0x9EAAF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC18setServerAssetPathyySo13CAPPluginCallCFTo', symObjAddr: 0x328, symBinAddr: 0x63520, symSize: 0x50 }
  - { offsetInCU: 0xD4, offset: 0x9EACB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC17setServerBasePathyySo13CAPPluginCallCF', symObjAddr: 0x378, symBinAddr: 0x63570, symSize: 0x120 }
  - { offsetInCU: 0x141, offset: 0x9EB38, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC17setServerBasePathyySo13CAPPluginCallCFTo', symObjAddr: 0x498, symBinAddr: 0x63690, symSize: 0x50 }
  - { offsetInCU: 0x15D, offset: 0x9EB54, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC17getServerBasePathyySo13CAPPluginCallCF', symObjAddr: 0x4E8, symBinAddr: 0x636E0, symSize: 0x1C4 }
  - { offsetInCU: 0x272, offset: 0x9EC69, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC17getServerBasePathyySo13CAPPluginCallCFTo', symObjAddr: 0x6AC, symBinAddr: 0x638A4, symSize: 0x50 }
  - { offsetInCU: 0x28E, offset: 0x9EC85, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC21persistServerBasePathyySo13CAPPluginCallCF', symObjAddr: 0x6FC, symBinAddr: 0x638F4, symSize: 0x1D4 }
  - { offsetInCU: 0x367, offset: 0x9ED5E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC21persistServerBasePathyySo13CAPPluginCallCFTo', symObjAddr: 0x8D0, symBinAddr: 0x63AC8, symSize: 0x50 }
  - { offsetInCU: 0x383, offset: 0x9ED7A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC6bridge8pluginId0F4NameAcA17CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x920, symBinAddr: 0x63B18, symSize: 0xB4 }
  - { offsetInCU: 0x3A1, offset: 0x9ED98, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC6bridge8pluginId0F4NameAcA17CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x9D4, symBinAddr: 0x63BCC, symSize: 0xB4 }
  - { offsetInCU: 0x41A, offset: 0x9EE11, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginC6bridge8pluginId0F4NameAcA17CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0xAA8, symBinAddr: 0x63CA0, symSize: 0xD8 }
  - { offsetInCU: 0x471, offset: 0x9EE68, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginCACycfC', symObjAddr: 0xB80, symBinAddr: 0x63D78, symSize: 0x20 }
  - { offsetInCU: 0x48F, offset: 0x9EE86, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginCACycfc', symObjAddr: 0xBA0, symBinAddr: 0x63D98, symSize: 0x30 }
  - { offsetInCU: 0x4CA, offset: 0x9EEC1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginCACycfcTo', symObjAddr: 0xBD0, symBinAddr: 0x63DC8, symSize: 0x3C }
  - { offsetInCU: 0x505, offset: 0x9EEFC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginCfD', symObjAddr: 0xC0C, symBinAddr: 0x63E04, symSize: 0x30 }
  - { offsetInCU: 0x608, offset: 0x9EFFF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor16CAPWebViewPluginCMa', symObjAddr: 0xA88, symBinAddr: 0x63C80, symSize: 0x20 }
  - { offsetInCU: 0x50, offset: 0x9F1D8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor10stoppedKey33_B3B54AF19006E24BF72646DA5D63F2A4LLSvSgvp', symObjAddr: 0x8020, symBinAddr: 0xA2D78, symSize: 0x0 }
  - { offsetInCU: 0x6A, offset: 0x9F1F2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC6routerAcA6Router_p_tcfC', symObjAddr: 0x0, symBinAddr: 0x63E34, symSize: 0x30 }
  - { offsetInCU: 0x88, offset: 0x9F210, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC6routerAcA6Router_p_tcfc', symObjAddr: 0x30, symBinAddr: 0x63E64, symSize: 0x320C }
  - { offsetInCU: 0x13E, offset: 0x9F2C6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03setD4PathyySSF', symObjAddr: 0x327C, symBinAddr: 0x67070, symSize: 0x94 }
  - { offsetInCU: 0x1BF, offset: 0x9F347, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC12setServerUrlyy10Foundation3URLVSgF', symObjAddr: 0x3310, symBinAddr: 0x67104, symSize: 0xB8 }
  - { offsetInCU: 0x243, offset: 0x9F3CB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC17isUsingLiveReload33_B3B54AF19006E24BF72646DA5D63F2A4LLySb10Foundation3URLVF', symObjAddr: 0x33C8, symBinAddr: 0x671BC, symSize: 0x29C }
  - { offsetInCU: 0x2CF, offset: 0x9F457, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03webC0_5startySo05WKWebC0C_So15WKURLSchemeTask_ptF', symObjAddr: 0x3664, symBinAddr: 0x67458, symSize: 0x8 }
  - { offsetInCU: 0x2F2, offset: 0x9F47A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03webC0_5startySo05WKWebC0C_So15WKURLSchemeTask_ptFTo', symObjAddr: 0x3790, symBinAddr: 0x67584, symSize: 0x6C }
  - { offsetInCU: 0x324, offset: 0x9F4AC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03webC0_4stopySo05WKWebC0C_So15WKURLSchemeTask_ptF', symObjAddr: 0x37FC, symBinAddr: 0x675F0, symSize: 0x30 }
  - { offsetInCU: 0x385, offset: 0x9F50D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03webC0_4stopySo05WKWebC0C_So15WKURLSchemeTask_ptFTo', symObjAddr: 0x38CC, symBinAddr: 0x676C0, symSize: 0x78 }
  - { offsetInCU: 0x3CC, offset: 0x9F554, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC20mimeTypeForExtension04pathI0S2S_tF', symObjAddr: 0x3944, symBinAddr: 0x67738, symSize: 0x168 }
  - { offsetInCU: 0x47C, offset: 0x9F604, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC16isMediaExtension04pathH0SbSS_tF', symObjAddr: 0x3AAC, symBinAddr: 0x678A0, symSize: 0x12C }
  - { offsetInCU: 0x560, offset: 0x9F6E8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFyAF4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU0_', symObjAddr: 0x3BD8, symBinAddr: 0x679CC, symSize: 0x2C0 }
  - { offsetInCU: 0x605, offset: 0x9F78D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFyAF4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU0_yyScMYccfU_', symObjAddr: 0x3E98, symBinAddr: 0x67C8C, symSize: 0x77C }
  - { offsetInCU: 0x8EC, offset: 0x9FA74, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC9mimeTypesSDyS2SGvg', symObjAddr: 0x4714, symBinAddr: 0x68508, symSize: 0x10 }
  - { offsetInCU: 0x919, offset: 0x9FAA1, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCACycfC', symObjAddr: 0x4724, symBinAddr: 0x68518, symSize: 0x20 }
  - { offsetInCU: 0x937, offset: 0x9FABF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCACycfc', symObjAddr: 0x4744, symBinAddr: 0x68538, symSize: 0x2C }
  - { offsetInCU: 0x99A, offset: 0x9FB22, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCACycfcTo', symObjAddr: 0x4770, symBinAddr: 0x68564, symSize: 0x2C }
  - { offsetInCU: 0xA01, offset: 0x9FB89, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCfD', symObjAddr: 0x479C, symBinAddr: 0x68590, symSize: 0x34 }
  - { offsetInCU: 0xA6E, offset: 0x9FBF6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFTf4nndn_n', symObjAddr: 0x56C0, symBinAddr: 0x6946C, symSize: 0x824 }
  - { offsetInCU: 0xCAC, offset: 0x9FE34, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC03webC0_5startySo05WKWebC0C_So15WKURLSchemeTask_ptFTf4dnn_n', symObjAddr: 0x6148, symBinAddr: 0x69EF4, symSize: 0x17B0 }
  - { offsetInCU: 0x1713, offset: 0xA089B, size: 0x8, addend: 0x0, symName: '_$sSo15WKURLSchemeTaskP9CapacitorE7stopped33_B3B54AF19006E24BF72646DA5D63F2A4LLSbvs', symObjAddr: 0x382C, symBinAddr: 0x67620, symSize: 0xA0 }
  - { offsetInCU: 0x1833, offset: 0xA09BB, size: 0x8, addend: 0x0, symName: '_$sSo15WKURLSchemeTaskP9CapacitorE7stopped33_B3B54AF19006E24BF72646DA5D63F2A4LLSbvg', symObjAddr: 0x4614, symBinAddr: 0x68408, symSize: 0x100 }
  - { offsetInCU: 0x1896, offset: 0xA0A1E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCfETo', symObjAddr: 0x47D0, symBinAddr: 0x685C4, symSize: 0x50 }
  - { offsetInCU: 0x18C5, offset: 0xA0A4D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor10stoppedKey33_B3B54AF19006E24BF72646DA5D63F2A4LL_WZ', symObjAddr: 0x4820, symBinAddr: 0x68614, symSize: 0x20 }
  - { offsetInCU: 0x19C3, offset: 0xA0B4B, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV5merge_8isUnique16uniquingKeysWithyqd__n_Sbq_q__q_tKXEtKSTRd__x_q_t7ElementRtd__lFs11AnyHashableV_yps15LazyMapSequenceVySDyAIypGAI_yptGTg5176$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFyAF4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU0_yyScMYccfU_ypyp_yptXEfU_Tf1nncn_n', symObjAddr: 0x4840, symBinAddr: 0x68634, symSize: 0x3DC }
  - { offsetInCU: 0x1BE0, offset: 0xA0D68, size: 0x8, addend: 0x0, symName: '_$sxq_xq_Iegnnrr_x3key_q_5valuetx_q_tIegnr_SHRzr0_lTRs11AnyHashableV_ypTg5070$sSD5merge_16uniquingKeysWithySDyxq_Gn_q_q__q_tKXEtKFx_q_tx_q_tcfU_s11cD7V_ypTg5Tf3nnpf_n', symObjAddr: 0x4C1C, symBinAddr: 0x68A10, symSize: 0x30 }
  - { offsetInCU: 0x1CAF, offset: 0xA0E37, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixq_Sgx_SitSyRzs010FixedWidthB0R_r0_lFSS_SiTg5', symObjAddr: 0x4C4C, symBinAddr: 0x68A40, symSize: 0xE0 }
  - { offsetInCU: 0x1DCA, offset: 0xA0F52, size: 0x8, addend: 0x0, symName: '_$ss13_parseInteger5ascii5radixxSgSRys5UInt8VG_Sits010FixedWidthB0RzlFSi_Tg5', symObjAddr: 0x4D2C, symBinAddr: 0x68B20, symSize: 0x298 }
  - { offsetInCU: 0x1E41, offset: 0xA0FC9, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingyS2SFZ', symObjAddr: 0x4FC4, symBinAddr: 0x68DB8, symSize: 0x8C }
  - { offsetInCU: 0x1E59, offset: 0xA0FE1, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTgq5', symObjAddr: 0x5050, symBinAddr: 0x68E44, symSize: 0x4C }
  - { offsetInCU: 0x1EAE, offset: 0xA1036, size: 0x8, addend: 0x0, symName: '_$sSS8_copyingySSSsFZ', symObjAddr: 0x509C, symBinAddr: 0x68E90, symSize: 0x154 }
  - { offsetInCU: 0x1F1C, offset: 0xA10A4, size: 0x8, addend: 0x0, symName: '_$sSlsE5countSivgSs8UTF8ViewV_Tgq5', symObjAddr: 0x51F0, symBinAddr: 0x68FE4, symSize: 0xF0 }
  - { offsetInCU: 0x1F41, offset: 0xA10C9, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSs8UTF8ViewV_Tgq5', symObjAddr: 0x52E0, symBinAddr: 0x690D4, symSize: 0x214 }
  - { offsetInCU: 0x1F7A, offset: 0xA1102, size: 0x8, addend: 0x0, symName: '_$ss11_StringGutsV27_slowEnsureMatchingEncodingySS5IndexVAEF', symObjAddr: 0x54F4, symBinAddr: 0x692E8, symSize: 0x78 }
  - { offsetInCU: 0x1F92, offset: 0xA111A, size: 0x8, addend: 0x0, symName: '_$s9Capacitor6Router_pWOc', symObjAddr: 0x556C, symBinAddr: 0x69360, symSize: 0x44 }
  - { offsetInCU: 0x1FA6, offset: 0xA112E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCMa', symObjAddr: 0x55B0, symBinAddr: 0x693A4, symSize: 0x3C }
  - { offsetInCU: 0x1FBA, offset: 0xA1142, size: 0x8, addend: 0x0, symName: ___swift_mutable_project_boxed_opaque_existential_1, symObjAddr: 0x55EC, symBinAddr: 0x693E0, symSize: 0x28 }
  - { offsetInCU: 0x1FCE, offset: 0xA1156, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCs5UInt8V_Tgq5Tf4nnd_n', symObjAddr: 0x565C, symBinAddr: 0x69408, symSize: 0x64 }
  - { offsetInCU: 0x20E6, offset: 0xA126E, size: 0x8, addend: 0x0, symName: '_$sSh21_nonEmptyArrayLiteralShyxGSayxG_tcfCSo16NSURLResourceKeya_Tg5Tf4gd_n', symObjAddr: 0x5EE4, symBinAddr: 0x69C90, symSize: 0x264 }
  - { offsetInCU: 0x23B1, offset: 0xA1539, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCMU', symObjAddr: 0x7918, symBinAddr: 0x6B6C4, symSize: 0x8 }
  - { offsetInCU: 0x23C5, offset: 0xA154D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerCMr', symObjAddr: 0x7920, symBinAddr: 0x6B6CC, symSize: 0x84 }
  - { offsetInCU: 0x23D9, offset: 0xA1561, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFyAF4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU0_TA', symObjAddr: 0x7B70, symBinAddr: 0x6B7CC, symSize: 0x68 }
  - { offsetInCU: 0x23ED, offset: 0xA1575, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x7BD8, symBinAddr: 0x6B834, symSize: 0x10 }
  - { offsetInCU: 0x2401, offset: 0xA1589, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x7BE8, symBinAddr: 0x6B844, symSize: 0x8 }
  - { offsetInCU: 0x2415, offset: 0xA159D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor19WebViewAssetHandlerC06handleA11HttpRequestyySo15WKURLSchemeTask_p_10Foundation3URLVSbtFyAF4DataVSg_So13NSURLResponseCSgs5Error_pSgtYbcfU0_yyScMYccfU_TA', symObjAddr: 0x7CD4, symBinAddr: 0x6B930, symSize: 0x4C }
  - { offsetInCU: 0x2429, offset: 0xA15B1, size: 0x8, addend: 0x0, symName: '_$s10Foundation4DataVSgWOy', symObjAddr: 0x7D20, symBinAddr: 0x6B97C, symSize: 0x14 }
  - { offsetInCU: 0x243D, offset: 0xA15C5, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x7D7C, symBinAddr: 0x6B990, symSize: 0x4C }
  - { offsetInCU: 0x2451, offset: 0xA15D9, size: 0x8, addend: 0x0, symName: '_$sypSgWOb', symObjAddr: 0x7F2C, symBinAddr: 0x6BA40, symSize: 0x48 }
  - { offsetInCU: 0x2867, offset: 0xA19EF, size: 0x8, addend: 0x0, symName: '_$sSo12NSFileHandleC14forReadingFromAB10Foundation3URLV_tKcfCTO', symObjAddr: 0x366C, symBinAddr: 0x67460, symSize: 0x124 }
  - { offsetInCU: 0x43, offset: 0xA1E45, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TR', symObjAddr: 0x4, symBinAddr: 0x6BAD0, symSize: 0x8 }
  - { offsetInCU: 0x63, offset: 0xA1E65, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCACycfC', symObjAddr: 0xC, symBinAddr: 0x6BAD8, symSize: 0x20 }
  - { offsetInCU: 0x81, offset: 0xA1E83, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC6bridgeAA0A6BridgeCSgvg', symObjAddr: 0x2C, symBinAddr: 0x6BAF8, symSize: 0x48 }
  - { offsetInCU: 0xCA, offset: 0xA1ECC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC17contentControllerSo013WKUserContentG0Cvg', symObjAddr: 0xDC, symBinAddr: 0x6BBA8, symSize: 0x44 }
  - { offsetInCU: 0xF9, offset: 0xA1EFB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCACycfc', symObjAddr: 0x120, symBinAddr: 0x6BBEC, symSize: 0x16C }
  - { offsetInCU: 0x152, offset: 0xA1F54, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCACycfcTo', symObjAddr: 0x2AC, symBinAddr: 0x6BD78, symSize: 0x20 }
  - { offsetInCU: 0x16E, offset: 0xA1F70, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC7cleanUpyyF', symObjAddr: 0x2CC, symBinAddr: 0x6BD98, symSize: 0x70 }
  - { offsetInCU: 0x1F1, offset: 0xA1FF3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC15willLoadWebviewyySo05WKWebC0CSgF', symObjAddr: 0x33C, symBinAddr: 0x6BE08, symSize: 0x6C }
  - { offsetInCU: 0x28B, offset: 0xA208D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_29didStartProvisionalNavigationySo05WKWebC0C_So12WKNavigationCSgtF', symObjAddr: 0x3A8, symBinAddr: 0x6BE74, symSize: 0x4 }
  - { offsetInCU: 0x2AE, offset: 0xA20B0, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_29didStartProvisionalNavigationySo05WKWebC0C_So12WKNavigationCSgtFTo', symObjAddr: 0x3AC, symBinAddr: 0x6BE78, symSize: 0x70 }
  - { offsetInCU: 0x2E0, offset: 0xA20E2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_32requestMediaCapturePermissionFor16initiatedByFrame4type08decisionE0ySo05WKWebC0C_So16WKSecurityOriginCSo11WKFrameInfoCSo07WKMediaI4TypeVySo20WKPermissionDecisionVctF', symObjAddr: 0x41C, symBinAddr: 0x6BEE8, symSize: 0x24 }
  - { offsetInCU: 0x377, offset: 0xA2179, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_32requestMediaCapturePermissionFor16initiatedByFrame4type08decisionE0ySo05WKWebC0C_So16WKSecurityOriginCSo11WKFrameInfoCSo07WKMediaI4TypeVySo20WKPermissionDecisionVctFTo', symObjAddr: 0x440, symBinAddr: 0x6BF0C, symSize: 0xA8 }
  - { offsetInCU: 0x3D7, offset: 0xA21D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_46requestDeviceOrientationAndMotionPermissionFor16initiatedByFrame08decisionE0ySo05WKWebC0C_So16WKSecurityOriginCSo11WKFrameInfoCySo20WKPermissionDecisionVctF', symObjAddr: 0x4E8, symBinAddr: 0x6BFB4, symSize: 0x24 }
  - { offsetInCU: 0x45C, offset: 0xA225E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_46requestDeviceOrientationAndMotionPermissionFor16initiatedByFrame08decisionE0ySo05WKWebC0C_So16WKSecurityOriginCSo11WKFrameInfoCySo20WKPermissionDecisionVctFTo', symObjAddr: 0x50C, symBinAddr: 0x6BFD8, symSize: 0xA8 }
  - { offsetInCU: 0x4DA, offset: 0xA22DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_15decidePolicyFor08decisionE0ySo05WKWebC0C_So18WKNavigationActionCySo0lmH0VctF', symObjAddr: 0x5B4, symBinAddr: 0x6C080, symSize: 0x9BC }
  - { offsetInCU: 0x6D7, offset: 0xA24D9, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_15decidePolicyFor08decisionE0ySo05WKWebC0C_So18WKNavigationActionCySo0lmH0VctFTo', symObjAddr: 0xF70, symBinAddr: 0x6CA3C, symSize: 0x9C }
  - { offsetInCU: 0x709, offset: 0xA250B, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_9didFinishySo05WKWebC0C_So12WKNavigationCSgtF', symObjAddr: 0x100C, symBinAddr: 0x6CAD8, symSize: 0x4 }
  - { offsetInCU: 0x72C, offset: 0xA252E, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_9didFinishySo05WKWebC0C_So12WKNavigationCSgtFTo', symObjAddr: 0x1010, symBinAddr: 0x6CADC, symSize: 0x74 }
  - { offsetInCU: 0x75E, offset: 0xA2560, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_7didFail9withErrorySo05WKWebC0C_So12WKNavigationCSgs0J0_ptF', symObjAddr: 0x1084, symBinAddr: 0x6CB50, symSize: 0x8 }
  - { offsetInCU: 0x77A, offset: 0xA257C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_28didFailProvisionalNavigation9withErrorySo05WKWebC0C_So12WKNavigationCSgs0L0_ptF', symObjAddr: 0x1098, symBinAddr: 0x6CB64, symSize: 0x8 }
  - { offsetInCU: 0x796, offset: 0xA2598, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webcB26ContentProcessDidTerminateyySo05WKWebC0CF', symObjAddr: 0x1148, symBinAddr: 0x6CC14, symSize: 0x24 }
  - { offsetInCU: 0x7E5, offset: 0xA25E7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webcB26ContentProcessDidTerminateyySo05WKWebC0CFTo', symObjAddr: 0x116C, symBinAddr: 0x6CC38, symSize: 0x68 }
  - { offsetInCU: 0x81A, offset: 0xA261C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC21userContentController_10didReceiveySo06WKUsergH0C_So15WKScriptMessageCtF', symObjAddr: 0x11D4, symBinAddr: 0x6CCA0, symSize: 0x8 }
  - { offsetInCU: 0x83D, offset: 0xA263F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC21userContentController_10didReceiveySo06WKUsergH0C_So15WKScriptMessageCtFTo', symObjAddr: 0x11DC, symBinAddr: 0x6CCA8, symSize: 0x74 }
  - { offsetInCU: 0x86F, offset: 0xA2671, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_34runJavaScriptAlertPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCyyctF', symObjAddr: 0x1250, symBinAddr: 0x6CD1C, symSize: 0x288 }
  - { offsetInCU: 0x9A8, offset: 0xA27AA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_34runJavaScriptAlertPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCyyctFTo', symObjAddr: 0x14D8, symBinAddr: 0x6CFA4, symSize: 0xC8 }
  - { offsetInCU: 0x9DA, offset: 0xA27DC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCySbctF', symObjAddr: 0x15A0, symBinAddr: 0x6D06C, symSize: 0x14 }
  - { offsetInCU: 0x9FD, offset: 0xA27FF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCySbctFTo', symObjAddr: 0x15B4, symBinAddr: 0x6D080, symSize: 0xDC }
  - { offsetInCU: 0xA2F, offset: 0xA2831, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctF', symObjAddr: 0x1690, symBinAddr: 0x6D15C, symSize: 0xC90 }
  - { offsetInCU: 0xF11, offset: 0xA2D13, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_', symObjAddr: 0x2320, symBinAddr: 0x6DDEC, symSize: 0x50 }
  - { offsetInCU: 0xF4E, offset: 0xA2D50, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctFySo13UIAlertActionCcfU1_', symObjAddr: 0x23C4, symBinAddr: 0x6DE90, symSize: 0x184 }
  - { offsetInCU: 0x10ED, offset: 0xA2EEF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctFTo', symObjAddr: 0x2548, symBinAddr: 0x6E014, symSize: 0x100 }
  - { offsetInCU: 0x111F, offset: 0xA2F21, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_06createbC4With3for14windowFeaturesSo05WKWebC0CSgAI_So0lC13ConfigurationCSo18WKNavigationActionCSo08WKWindowK0CtF', symObjAddr: 0x268C, symBinAddr: 0x6E158, symSize: 0x8 }
  - { offsetInCU: 0x1142, offset: 0xA2F44, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_06createbC4With3for14windowFeaturesSo05WKWebC0CSgAI_So0lC13ConfigurationCSo18WKNavigationActionCSo08WKWindowK0CtFTo', symObjAddr: 0x2694, symBinAddr: 0x6E160, symSize: 0xA8 }
  - { offsetInCU: 0x1175, offset: 0xA2F77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC06scrollC16WillBeginZooming_4withySo08UIScrollC0C_So6UIViewCSgtF', symObjAddr: 0x273C, symBinAddr: 0x6E208, symSize: 0x54 }
  - { offsetInCU: 0x11DA, offset: 0xA2FDC, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC06scrollC16WillBeginZooming_4withySo08UIScrollC0C_So6UIViewCSgtFTo', symObjAddr: 0x2790, symBinAddr: 0x6E25C, symSize: 0xA4 }
  - { offsetInCU: 0x121A, offset: 0xA301C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCfD', symObjAddr: 0x2880, symBinAddr: 0x6E34C, symSize: 0x30 }
  - { offsetInCU: 0x1247, offset: 0xA3049, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_29didStartProvisionalNavigationySo05WKWebC0C_So12WKNavigationCSgtFTf4ddn_n', symObjAddr: 0x28FC, symBinAddr: 0x6E3C8, symSize: 0xCC }
  - { offsetInCU: 0x1362, offset: 0xA3164, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_15decidePolicyFor08decisionE0ySo05WKWebC0C_So18WKNavigationActionCySo0lmH0VctF06$sSo24lmH16VIeyBy_ABIegy_TRALIeyBy_Tf1nncn_nTf4dnng_n', symObjAddr: 0x2A50, symBinAddr: 0x6E4DC, symSize: 0x99C }
  - { offsetInCU: 0x15B0, offset: 0xA33B2, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_9didFinishySo05WKWebC0C_So12WKNavigationCSgtFTf4ndn_n', symObjAddr: 0x33EC, symBinAddr: 0x6EE78, symSize: 0xD8 }
  - { offsetInCU: 0x16F4, offset: 0xA34F6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_7didFail9withErrorySo05WKWebC0C_So12WKNavigationCSgs0J0_ptFTf4ndnn_n', symObjAddr: 0x34C4, symBinAddr: 0x6EF50, symSize: 0x51C }
  - { offsetInCU: 0x18FB, offset: 0xA36FD, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_28didFailProvisionalNavigation9withErrorySo05WKWebC0C_So12WKNavigationCSgs0L0_ptFTf4ndnn_n', symObjAddr: 0x39E0, symBinAddr: 0x6F46C, symSize: 0x4F4 }
  - { offsetInCU: 0x1AA4, offset: 0xA38A6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC10logJSError33_F9163AD9166938F8B4F54F28D02AA29FLLyySDySSypGFTf4nd_n', symObjAddr: 0x3ED4, symBinAddr: 0x6F960, symSize: 0x7BC }
  - { offsetInCU: 0x2032, offset: 0xA3E34, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC21userContentController_10didReceiveySo06WKUsergH0C_So15WKScriptMessageCtFTf4dnn_n', symObjAddr: 0x4690, symBinAddr: 0x7011C, symSize: 0xC8C }
  - { offsetInCU: 0x256B, offset: 0xA436D, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_34runJavaScriptAlertPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCyyctF13$sIeyB_Ieg_TRIeyB_Tf1nnncn_nTf4dndng_n', symObjAddr: 0x535C, symBinAddr: 0x70DE8, symSize: 0x2F0 }
  - { offsetInCU: 0x268A, offset: 0xA448C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_36runJavaScriptConfirmPanelWithMessage16initiatedByFrame010completionE0ySo05WKWebC0C_SSSo11WKFrameInfoCySbctFTf4dndnn_n', symObjAddr: 0x564C, symBinAddr: 0x710D8, symSize: 0x314 }
  - { offsetInCU: 0x2780, offset: 0xA4582, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctF019$sSo8NSStringCSgIeyQ12_SSSgIegg_TRSo0Y0CSgIeyBy_Tf1nnnncn_nTf4dnndng_n', symObjAddr: 0x5A34, symBinAddr: 0x71424, symSize: 0xD28 }
  - { offsetInCU: 0x2C74, offset: 0xA4A76, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_06createbC4With3for14windowFeaturesSo05WKWebC0CSgAI_So0lC13ConfigurationCSo18WKNavigationActionCSo08WKWindowK0CtFTf4ddndd_n', symObjAddr: 0x675C, symBinAddr: 0x7214C, symSize: 0x22C }
  - { offsetInCU: 0x2CFE, offset: 0xA4B00, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC17contentControllerSo013WKUserContentG0CvpACTk', symObjAddr: 0x74, symBinAddr: 0x6BB40, symSize: 0x68 }
  - { offsetInCU: 0x2D34, offset: 0xA4B36, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCMa', symObjAddr: 0x28C, symBinAddr: 0x6BD58, symSize: 0x20 }
  - { offsetInCU: 0x2F82, offset: 0xA4D84, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TR', symObjAddr: 0x2648, symBinAddr: 0x6E114, symSize: 0x44 }
  - { offsetInCU: 0x2FD8, offset: 0xA4DDA, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerCfETo', symObjAddr: 0x28B0, symBinAddr: 0x6E37C, symSize: 0x4C }
  - { offsetInCU: 0x3067, offset: 0xA4E69, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaABSHSCWl', symObjAddr: 0x2A08, symBinAddr: 0x6E494, symSize: 0x48 }
  - { offsetInCU: 0x3157, offset: 0xA4F59, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5344, symBinAddr: 0x70DD0, symSize: 0x10 }
  - { offsetInCU: 0x316B, offset: 0xA4F6D, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5354, symBinAddr: 0x70DE0, symSize: 0x8 }
  - { offsetInCU: 0x318A, offset: 0xA4F8C, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC03webC0_37runJavaScriptTextInputPanelWithPrompt07defaultJ016initiatedByFrame010completionE0ySo05WKWebC0C_S2SSgSo11WKFrameInfoCyAKctFySo11UITextFieldCcfU_TA', symObjAddr: 0x59C8, symBinAddr: 0x71410, symSize: 0x8 }
  - { offsetInCU: 0x31A9, offset: 0xA4FAB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOwet', symObjAddr: 0x6A84, symBinAddr: 0x72464, symSize: 0xA8 }
  - { offsetInCU: 0x31BD, offset: 0xA4FBF, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOwst', symObjAddr: 0x6B2C, symBinAddr: 0x7250C, symSize: 0xC4 }
  - { offsetInCU: 0x31D1, offset: 0xA4FD3, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOwug', symObjAddr: 0x6BF0, symBinAddr: 0x725D0, symSize: 0x1C }
  - { offsetInCU: 0x31E5, offset: 0xA4FE7, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOwup', symObjAddr: 0x6C0C, symBinAddr: 0x725EC, symSize: 0x4 }
  - { offsetInCU: 0x31F9, offset: 0xA4FFB, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOwui', symObjAddr: 0x6C10, symBinAddr: 0x725F0, symSize: 0x18 }
  - { offsetInCU: 0x320D, offset: 0xA500F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor24WebViewDelegationHandlerC0bC12LoadingStateOMa', symObjAddr: 0x6C28, symBinAddr: 0x72608, symSize: 0x10 }
  - { offsetInCU: 0x3221, offset: 0xA5023, size: 0x8, addend: 0x0, symName: '_$sSo8NSStringCSgIeyBy_SSSgIegg_TRTA', symObjAddr: 0x6C5C, symBinAddr: 0x7263C, symSize: 0x8 }
  - { offsetInCU: 0x3240, offset: 0xA5042, size: 0x8, addend: 0x0, symName: '_$s10ObjectiveC8ObjCBoolVIeyBy_SbIegy_TRTA', symObjAddr: 0x6CDC, symBinAddr: 0x726BC, symSize: 0x14 }
  - { offsetInCU: 0x3269, offset: 0xA506B, size: 0x8, addend: 0x0, symName: '_$sIeyB_Ieg_TRTA', symObjAddr: 0x6CF0, symBinAddr: 0x726D0, symSize: 0xC }
  - { offsetInCU: 0x3292, offset: 0xA5094, size: 0x8, addend: 0x0, symName: '_$sypSgWOc', symObjAddr: 0x6DE8, symBinAddr: 0x7276C, symSize: 0x48 }
  - { offsetInCU: 0x33E4, offset: 0xA51E6, size: 0x8, addend: 0x0, symName: '_$sSlsEy11SubSequenceQzqd__cSXRd__5BoundQyd__5IndexRtzluigSS_s16PartialRangeFromVySSAEVGTg5', symObjAddr: 0x2834, symBinAddr: 0x6E300, symSize: 0x4C }
  - { offsetInCU: 0x2B, offset: 0xA5794, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo9WKWebViewCRszlE39setKeyboardShouldRequireUserInteractionyySbSgF', symObjAddr: 0x0, symBinAddr: 0x7285C, symSize: 0x90 }
  - { offsetInCU: 0x4F, offset: 0xA57B8, size: 0x8, addend: 0x0, symName: '_$s9Capacitor28associatedKeyboardFlagHandle33_3E0898892F6945378FAE8ABD0FA44A3BLLs5UInt8Vvp', symObjAddr: 0xA48, symBinAddr: 0xA2E60, symSize: 0x0 }
  - { offsetInCU: 0x5D, offset: 0xA57C6, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo9WKWebViewCRszlE39setKeyboardShouldRequireUserInteractionyySbSgF', symObjAddr: 0x0, symBinAddr: 0x7285C, symSize: 0x90 }
  - { offsetInCU: 0xFF, offset: 0xA5868, size: 0x8, addend: 0x0, symName: '_$s9Capacitor0A20ExtensionTypeWrapperVAASo9WKWebViewCRszlE36keyboardShouldRequireUserInteractionSbSgvg', symObjAddr: 0x90, symBinAddr: 0x728EC, symSize: 0xE0 }
  - { offsetInCU: 0x14E, offset: 0xA58B7, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE27associatedKeyboardFlagValueypSgvs', symObjAddr: 0x170, symBinAddr: 0x729CC, symSize: 0x118 }
  - { offsetInCU: 0x1AF, offset: 0xA5918, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE23_swizzleKeyboardMethodsyyFZTo', symObjAddr: 0x288, symBinAddr: 0x72AE4, symSize: 0x28 }
  - { offsetInCU: 0x1E7, offset: 0xA5950, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE18oneTimeOnlySwizzle_WZ', symObjAddr: 0x2B0, symBinAddr: 0x72B0C, symSize: 0x4 }
  - { offsetInCU: 0x26F, offset: 0xA59D8, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE18oneTimeOnlySwizzleytvpZfiyyXEfU_', symObjAddr: 0x2B4, symBinAddr: 0x72B10, symSize: 0x11C }
  - { offsetInCU: 0x33D, offset: 0xA5AA6, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE18oneTimeOnlySwizzleytvpZfiyyXEfU_ABSgypSgcfU_', symObjAddr: 0x3D0, symBinAddr: 0x72C2C, symSize: 0xD8 }
  - { offsetInCU: 0x37F, offset: 0xA5AE8, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE18oneTimeOnlySwizzleytvpZfiyyXEfU_ys13OpaquePointerV_10ObjectiveC8SelectorVtcfU0_yyp_SVS3bypSgtcfU_', symObjAddr: 0x4A8, symBinAddr: 0x72D04, symSize: 0x2DC }
  - { offsetInCU: 0x452, offset: 0xA5BBB, size: 0x8, addend: 0x0, symName: '_$sSo9WKWebViewC9CapacitorE18oneTimeOnlySwizzleytvpZfiyyXEfU_ys13OpaquePointerV_10ObjectiveC8SelectorVtcfU0_yyp_SVS3bypSgtcfU_TA', symObjAddr: 0x7A8, symBinAddr: 0x73004, symSize: 0x28 }
  - { offsetInCU: 0x466, offset: 0xA5BCF, size: 0x8, addend: 0x0, symName: '_$sypSVS3bypSgIegnyyyyn_yXlSVS3byXlSgIeyByyyyyy_TR', symObjAddr: 0x7D0, symBinAddr: 0x7302C, symSize: 0xD4 }
  - { offsetInCU: 0x47E, offset: 0xA5BE7, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x8A4, symBinAddr: 0x73100, symSize: 0x10 }
  - { offsetInCU: 0x492, offset: 0xA5BFB, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x8B4, symBinAddr: 0x73110, symSize: 0x8 }
  - { offsetInCU: 0x4A6, offset: 0xA5C0F, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x998, symBinAddr: 0x73118, symSize: 0x24 }
...
