---
triple:          'arm64-apple-darwin'
binary-path:     '/Users/<USER>/Library/Developer/Xcode/DerivedData/App-atcwcrjnkywhfydrdtyvlcuhwesp/Build/Intermediates.noindex/ArchiveIntermediates/App/IntermediateBuildFilesPath/UninstalledProducts/iphoneos/CapacitorGeolocation.framework/CapacitorGeolocation'
relocations:
  - { offsetInCU: 0x34, offset: 0x59A83, size: 0x8, addend: 0x0, symName: _CapacitorGeolocationVersionString, symObjAddr: 0x0, symBinAddr: 0x11450, symSize: 0x0 }
  - { offsetInCU: 0x69, offset: 0x59AB8, size: 0x8, addend: 0x0, symName: _CapacitorGeolocationVersionNumber, symObjAddr: 0x38, symBinAddr: 0x11488, symSize: 0x0 }
  - { offsetInCU: 0x79, offset: 0x59B47, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOSQAASQ2eeoiySbx_xtFZTW', symObjAddr: 0x0, symBinAddr: 0x8000, symSize: 0x14 }
  - { offsetInCU: 0x108, offset: 0x59BD6, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOSHAASH9hashValueSivgTW', symObjAddr: 0x14, symBinAddr: 0x8014, symSize: 0x44 }
  - { offsetInCU: 0x1EB, offset: 0x59CB9, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOSHAASH4hash4intoys6HasherVz_tFTW', symObjAddr: 0x58, symBinAddr: 0x8058, symSize: 0x28 }
  - { offsetInCU: 0x387, offset: 0x59E55, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerC32clearRequestPermissionsCallbacksyyF', symObjAddr: 0xC0, symBinAddr: 0x80C0, symSize: 0x134 }
  - { offsetInCU: 0x55E, offset: 0x5A02C, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerC29sendRequestPermissionsSuccessyySSF', symObjAddr: 0x1F4, symBinAddr: 0x81F4, symSize: 0x34C }
  - { offsetInCU: 0x8FD, offset: 0x5A3CB, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerC11sendSuccess4withy17IONGeolocationLib20IONGLOCPositionModelV_tF', symObjAddr: 0x540, symBinAddr: 0x8540, symSize: 0x16C }
  - { offsetInCU: 0xAA6, offset: 0x5A574, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerCfD', symObjAddr: 0xBD0, symBinAddr: 0x8BD0, symSize: 0x3C }
  - { offsetInCU: 0xD32, offset: 0x5A800, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerC18createPluginResult33_AA466C058A3C7EC471C353D6BD1A8938LL6statusyAA04CallG6StatusAELLO_tF', symObjAddr: 0x6AC, symBinAddr: 0x86AC, symSize: 0x524 }
  - { offsetInCU: 0x14CA, offset: 0x5AF98, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC10identifierSSvpfi', symObjAddr: 0xC0C, symBinAddr: 0x8C0C, symSize: 0x1C }
  - { offsetInCU: 0x14E2, offset: 0x5AFB0, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC6jsNameSSvpfi', symObjAddr: 0xC28, symBinAddr: 0x8C28, symSize: 0x20 }
  - { offsetInCU: 0x1526, offset: 0x5AFF4, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvpfi', symObjAddr: 0xC48, symBinAddr: 0x8C48, symSize: 0x2E4 }
  - { offsetInCU: 0x1685, offset: 0x5B153, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC15locationService33_BFA717F0A29A4D831C9388058947DA51LL17IONGeolocationLib27IONGLOCAuthorisationHandler_AF022IONGLOCMonitorLocationP0AF22IONGLOCServicesCheckerAF013IONGLOCSinglerP0pSgvpfi', symObjAddr: 0xF2C, symBinAddr: 0x8F2C, symSize: 0x10 }
  - { offsetInCU: 0x169D, offset: 0x5B16B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC12cancellables33_BFA717F0A29A4D831C9388058947DA51LLShy7Combine14AnyCancellableCGvpfi', symObjAddr: 0xF3C, symBinAddr: 0x8F3C, symSize: 0xC }
  - { offsetInCU: 0x16B5, offset: 0x5B183, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC15callbackManager33_BFA717F0A29A4D831C9388058947DA51LLAA0b8CallbackE0CSgvpfi', symObjAddr: 0xF48, symBinAddr: 0x8F48, symSize: 0x8 }
  - { offsetInCU: 0x16CD, offset: 0x5B19B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13isInitialised33_BFA717F0A29A4D831C9388058947DA51LLSbvpfi', symObjAddr: 0xF50, symBinAddr: 0x8F50, symSize: 0x8 }
  - { offsetInCU: 0x16FB, offset: 0x5B1C9, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV19_getElementSlowPathyyXlSiFSo13CAPPluginCallC_Tg5', symObjAddr: 0xF58, symBinAddr: 0x8F58, symSize: 0x210 }
  - { offsetInCU: 0x17A5, offset: 0x5B273, size: 0x8, addend: 0x0, symName: '_$sSTsE21_copySequenceContents12initializing8IteratorQz_SitSry7ElementQzG_tFSD6ValuesVySSSo13CAPPluginCallC_G_Tg5', symObjAddr: 0x1168, symBinAddr: 0x9168, symSize: 0x1C4 }
  - { offsetInCU: 0x1865, offset: 0x5B333, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo13CAPPluginCallC_Tg5Tf4d_n', symObjAddr: 0x132C, symBinAddr: 0x932C, symSize: 0x64 }
  - { offsetInCU: 0x18A8, offset: 0x5B376, size: 0x8, addend: 0x0, symName: '_$ss22_ContiguousArrayBufferV19_uninitializedCount15minimumCapacityAByxGSi_SitcfCSo13CAPPluginCallC_Tg5Tf4nnd_n', symObjAddr: 0x1390, symBinAddr: 0x9390, symSize: 0x80 }
  - { offsetInCU: 0x1906, offset: 0x5B3D4, size: 0x8, addend: 0x0, symName: '_$ss29getContiguousArrayStorageType3fors01_bcD0CyxGmxm_tlFSo15CAPPluginMethodC_Tg5Tf4d_n', symObjAddr: 0x1410, symBinAddr: 0x9410, symSize: 0x64 }
  - { offsetInCU: 0x1933, offset: 0x5B401, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B15CallbackManagerCMa', symObjAddr: 0x1474, symBinAddr: 0x9474, symSize: 0x20 }
  - { offsetInCU: 0x1947, offset: 0x5B415, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x1494, symBinAddr: 0x9494, symSize: 0x40 }
  - { offsetInCU: 0x195B, offset: 0x5B429, size: 0x8, addend: 0x0, symName: '_$sSD8IteratorV8_VariantOyxq___GSHRzr0_lWOe', symObjAddr: 0x14D4, symBinAddr: 0x94D4, symSize: 0x10 }
  - { offsetInCU: 0x1976, offset: 0x5B444, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOWOb', symObjAddr: 0x14E4, symBinAddr: 0x94E4, symSize: 0x18 }
  - { offsetInCU: 0x198E, offset: 0x5B45C, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOWOb', symObjAddr: 0x14E4, symBinAddr: 0x94E4, symSize: 0x18 }
  - { offsetInCU: 0x199F, offset: 0x5B46D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwCP', symObjAddr: 0x1534, symBinAddr: 0x9534, symSize: 0x30 }
  - { offsetInCU: 0x19B3, offset: 0x5B481, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOWOy', symObjAddr: 0x1564, symBinAddr: 0x9564, symSize: 0x2C }
  - { offsetInCU: 0x19C7, offset: 0x5B495, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwxx', symObjAddr: 0x1590, symBinAddr: 0x9590, symSize: 0x14 }
  - { offsetInCU: 0x19DB, offset: 0x5B4A9, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOWOe', symObjAddr: 0x15A4, symBinAddr: 0x95A4, symSize: 0x2C }
  - { offsetInCU: 0x19EF, offset: 0x5B4BD, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwcp', symObjAddr: 0x15D0, symBinAddr: 0x95D0, symSize: 0x60 }
  - { offsetInCU: 0x1A03, offset: 0x5B4D1, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwca', symObjAddr: 0x1630, symBinAddr: 0x9630, symSize: 0x70 }
  - { offsetInCU: 0x1A17, offset: 0x5B4E5, size: 0x8, addend: 0x0, symName: ___swift_memcpy33_8, symObjAddr: 0x16A0, symBinAddr: 0x96A0, symSize: 0x14 }
  - { offsetInCU: 0x1A2B, offset: 0x5B4F9, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwta', symObjAddr: 0x16B4, symBinAddr: 0x96B4, symSize: 0x48 }
  - { offsetInCU: 0x1A3F, offset: 0x5B50D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwet', symObjAddr: 0x16FC, symBinAddr: 0x96FC, symSize: 0x48 }
  - { offsetInCU: 0x1A53, offset: 0x5B521, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwst', symObjAddr: 0x1744, symBinAddr: 0x9744, symSize: 0x48 }
  - { offsetInCU: 0x1A67, offset: 0x5B535, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwug', symObjAddr: 0x178C, symBinAddr: 0x978C, symSize: 0x8 }
  - { offsetInCU: 0x1A7B, offset: 0x5B549, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwup', symObjAddr: 0x1794, symBinAddr: 0x9794, symSize: 0x4 }
  - { offsetInCU: 0x1A8F, offset: 0x5B55D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOwui', symObjAddr: 0x1798, symBinAddr: 0x9798, symSize: 0xC }
  - { offsetInCU: 0x1AA3, offset: 0x5B571, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation16CallResultStatus33_AA466C058A3C7EC471C353D6BD1A8938LLOMa', symObjAddr: 0x17A4, symBinAddr: 0x97A4, symSize: 0x10 }
  - { offsetInCU: 0x1AB7, offset: 0x5B585, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwCP', symObjAddr: 0x17B4, symBinAddr: 0x97B4, symSize: 0x34 }
  - { offsetInCU: 0x1ACB, offset: 0x5B599, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwxx', symObjAddr: 0x17E8, symBinAddr: 0x97E8, symSize: 0x8 }
  - { offsetInCU: 0x1ADF, offset: 0x5B5AD, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwcp', symObjAddr: 0x17F0, symBinAddr: 0x97F0, symSize: 0x34 }
  - { offsetInCU: 0x1AF3, offset: 0x5B5C1, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwca', symObjAddr: 0x1824, symBinAddr: 0x9824, symSize: 0x4C }
  - { offsetInCU: 0x1B07, offset: 0x5B5D5, size: 0x8, addend: 0x0, symName: ___swift_memcpy9_8, symObjAddr: 0x1870, symBinAddr: 0x9870, symSize: 0x14 }
  - { offsetInCU: 0x1B1B, offset: 0x5B5E9, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwta', symObjAddr: 0x1884, symBinAddr: 0x9884, symSize: 0x3C }
  - { offsetInCU: 0x1B2F, offset: 0x5B5FD, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwet', symObjAddr: 0x18C0, symBinAddr: 0x98C0, symSize: 0x48 }
  - { offsetInCU: 0x1B43, offset: 0x5B611, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVwst', symObjAddr: 0x1908, symBinAddr: 0x9908, symSize: 0x40 }
  - { offsetInCU: 0x1B57, offset: 0x5B625, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B13CallbackGroup33_AA466C058A3C7EC471C353D6BD1A8938LLVMa', symObjAddr: 0x1948, symBinAddr: 0x9948, symSize: 0x10 }
  - { offsetInCU: 0x1B6B, offset: 0x5B639, size: 0x8, addend: 0x0, symName: ___swift_memcpy1_1, symObjAddr: 0x1958, symBinAddr: 0x9958, symSize: 0xC }
  - { offsetInCU: 0x1B7F, offset: 0x5B64D, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1964, symBinAddr: 0x9964, symSize: 0x4 }
  - { offsetInCU: 0x1B93, offset: 0x5B661, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOwet', symObjAddr: 0x1968, symBinAddr: 0x9968, symSize: 0x90 }
  - { offsetInCU: 0x1BA7, offset: 0x5B675, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOwst', symObjAddr: 0x19F8, symBinAddr: 0x99F8, symSize: 0xBC }
  - { offsetInCU: 0x1BBB, offset: 0x5B689, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOwug', symObjAddr: 0x1AB4, symBinAddr: 0x9AB4, symSize: 0x8 }
  - { offsetInCU: 0x1BCF, offset: 0x5B69D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOwup', symObjAddr: 0x1ABC, symBinAddr: 0x9ABC, symSize: 0x4 }
  - { offsetInCU: 0x1BE3, offset: 0x5B6B1, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOwui', symObjAddr: 0x1AC0, symBinAddr: 0x9AC0, symSize: 0x8 }
  - { offsetInCU: 0x1BF7, offset: 0x5B6C5, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOMa', symObjAddr: 0x1AC8, symBinAddr: 0x9AC8, symSize: 0x10 }
  - { offsetInCU: 0x1C0B, offset: 0x5B6D9, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOSHAASQWb', symObjAddr: 0x1AD8, symBinAddr: 0x9AD8, symSize: 0x4 }
  - { offsetInCU: 0x1C1F, offset: 0x5B6ED, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOADSQAAWl', symObjAddr: 0x1ADC, symBinAddr: 0x9ADC, symSize: 0x44 }
  - { offsetInCU: 0x1C9C, offset: 0x5B76A, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B12CallbackType33_AA466C058A3C7EC471C353D6BD1A8938LLOSHAASH13_rawHashValue4seedS2i_tFTW', symObjAddr: 0x80, symBinAddr: 0x8080, symSize: 0x40 }
  - { offsetInCU: 0x27, offset: 0x5BBDD, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO9ArgumentsO2id_WZ', symObjAddr: 0x0, symBinAddr: 0x9B20, symSize: 0x18 }
  - { offsetInCU: 0x4B, offset: 0x5BC01, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO9ArgumentsO2idSSvpZ', symObjAddr: 0x2368, symBinAddr: 0x19820, symSize: 0x0 }
  - { offsetInCU: 0x65, offset: 0x5BC1B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO9ResultKeyO8locationSSvpZ', symObjAddr: 0x2378, symBinAddr: 0x19830, symSize: 0x0 }
  - { offsetInCU: 0x7F, offset: 0x5BC35, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO9ResultKeyO14coarseLocationSSvpZ', symObjAddr: 0x2388, symBinAddr: 0x19840, symSize: 0x0 }
  - { offsetInCU: 0x99, offset: 0x5BC4F, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O6deniedSSvpZ', symObjAddr: 0x2398, symBinAddr: 0x19850, symSize: 0x0 }
  - { offsetInCU: 0xB3, offset: 0x5BC69, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O7grantedSSvpZ', symObjAddr: 0x23A8, symBinAddr: 0x19860, symSize: 0x0 }
  - { offsetInCU: 0xCD, offset: 0x5BC83, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O6promptSSvpZ', symObjAddr: 0x23B8, symBinAddr: 0x19870, symSize: 0x0 }
  - { offsetInCU: 0xE7, offset: 0x5BC9D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8altitudeSSvpZ', symObjAddr: 0x23C8, symBinAddr: 0x19880, symSize: 0x0 }
  - { offsetInCU: 0x101, offset: 0x5BCB7, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO6coordsSSvpZ', symObjAddr: 0x23D8, symBinAddr: 0x19890, symSize: 0x0 }
  - { offsetInCU: 0x11B, offset: 0x5BCD1, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO7headingSSvpZ', symObjAddr: 0x23E8, symBinAddr: 0x198A0, symSize: 0x0 }
  - { offsetInCU: 0x135, offset: 0x5BCEB, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8accuracySSvpZ', symObjAddr: 0x23F8, symBinAddr: 0x198B0, symSize: 0x0 }
  - { offsetInCU: 0x14F, offset: 0x5BD05, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8latitudeSSvpZ', symObjAddr: 0x2408, symBinAddr: 0x198C0, symSize: 0x0 }
  - { offsetInCU: 0x169, offset: 0x5BD1F, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO9longitudeSSvpZ', symObjAddr: 0x2418, symBinAddr: 0x198D0, symSize: 0x0 }
  - { offsetInCU: 0x183, offset: 0x5BD39, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO5speedSSvpZ', symObjAddr: 0x2428, symBinAddr: 0x198E0, symSize: 0x0 }
  - { offsetInCU: 0x19D, offset: 0x5BD53, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO9timestampSSvpZ', symObjAddr: 0x2438, symBinAddr: 0x198F0, symSize: 0x0 }
  - { offsetInCU: 0x1B2, offset: 0x5BD68, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO9ArgumentsO2id_WZ', symObjAddr: 0x0, symBinAddr: 0x9B20, symSize: 0x18 }
  - { offsetInCU: 0x1D3, offset: 0x5BD89, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO9ResultKeyO8location_WZ', symObjAddr: 0x18, symBinAddr: 0x9B38, symSize: 0x24 }
  - { offsetInCU: 0x1ED, offset: 0x5BDA3, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO9ResultKeyO14coarseLocation_WZ', symObjAddr: 0x3C, symBinAddr: 0x9B5C, symSize: 0x30 }
  - { offsetInCU: 0x207, offset: 0x5BDBD, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O6denied_WZ', symObjAddr: 0x6C, symBinAddr: 0x9B8C, symSize: 0x20 }
  - { offsetInCU: 0x221, offset: 0x5BDD7, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O7granted_WZ', symObjAddr: 0x8C, symBinAddr: 0x9BAC, symSize: 0x24 }
  - { offsetInCU: 0x23B, offset: 0x5BDF1, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO19AuthorisationStatusO0E0O6prompt_WZ', symObjAddr: 0xB0, symBinAddr: 0x9BD0, symSize: 0x20 }
  - { offsetInCU: 0x256, offset: 0x5BE0C, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8altitude_WZ', symObjAddr: 0xD0, symBinAddr: 0x9BF0, symSize: 0x24 }
  - { offsetInCU: 0x270, offset: 0x5BE26, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO6coords_WZ', symObjAddr: 0xF4, symBinAddr: 0x9C14, symSize: 0x20 }
  - { offsetInCU: 0x28A, offset: 0x5BE40, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO7heading_WZ', symObjAddr: 0x114, symBinAddr: 0x9C34, symSize: 0x24 }
  - { offsetInCU: 0x2A4, offset: 0x5BE5A, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8accuracy_WZ', symObjAddr: 0x138, symBinAddr: 0x9C58, symSize: 0x24 }
  - { offsetInCU: 0x2BE, offset: 0x5BE74, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO8latitude_WZ', symObjAddr: 0x15C, symBinAddr: 0x9C7C, symSize: 0x24 }
  - { offsetInCU: 0x2D8, offset: 0x5BE8E, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO9longitude_WZ', symObjAddr: 0x180, symBinAddr: 0x9CA0, symSize: 0x28 }
  - { offsetInCU: 0x2F2, offset: 0x5BEA8, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO5speed_WZ', symObjAddr: 0x1A8, symBinAddr: 0x9CC8, symSize: 0x20 }
  - { offsetInCU: 0x30C, offset: 0x5BEC2, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation9ConstantsO8PositionO9timestamp_WZ', symObjAddr: 0x1C8, symBinAddr: 0x9CE8, symSize: 0x28 }
  - { offsetInCU: 0x2B, offset: 0x5BF7A, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B5ErrorO17toCodeMessagePairSS_SStyF', symObjAddr: 0x0, symBinAddr: 0x9D10, symSize: 0xFC }
  - { offsetInCU: 0x9D, offset: 0x5BFEC, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B5ErrorO17toCodeMessagePairSS_SStyF', symObjAddr: 0x0, symBinAddr: 0x9D10, symSize: 0xFC }
  - { offsetInCU: 0x21E, offset: 0x5C16D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B5ErrorO11description33_85150F83B695848574A515D42918B5D0LLSSvg', symObjAddr: 0x168, symBinAddr: 0x9E0C, symSize: 0x174 }
  - { offsetInCU: 0x154, offset: 0x5C4CB, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvgTo', symObjAddr: 0xAD8, symBinAddr: 0xAA58, symSize: 0x60 }
  - { offsetInCU: 0x18F, offset: 0x5C506, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13pluginMethodsSaySo15CAPPluginMethodCGvg', symObjAddr: 0xB38, symBinAddr: 0xAAB8, symSize: 0x10 }
  - { offsetInCU: 0x200, offset: 0x5C577, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC4loadyyF', symObjAddr: 0xB48, symBinAddr: 0xAAC8, symSize: 0x1B0 }
  - { offsetInCU: 0x30F, offset: 0x5C686, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC4loadyyFTo', symObjAddr: 0xDBC, symBinAddr: 0xACFC, symSize: 0x2C }
  - { offsetInCU: 0x373, offset: 0x5C6EA, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC18getCurrentPositionyySo13CAPPluginCallCF', symObjAddr: 0xDE8, symBinAddr: 0xAD28, symSize: 0xA8 }
  - { offsetInCU: 0x49D, offset: 0x5C814, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC18getCurrentPositionyySo13CAPPluginCallCFTo', symObjAddr: 0x152C, symBinAddr: 0xB46C, symSize: 0x50 }
  - { offsetInCU: 0x4B9, offset: 0x5C830, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13watchPositionyySo13CAPPluginCallCF', symObjAddr: 0x157C, symBinAddr: 0xB4BC, symSize: 0x100 }
  - { offsetInCU: 0x5BE, offset: 0x5C935, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13watchPositionyySo13CAPPluginCallCFTo', symObjAddr: 0x167C, symBinAddr: 0xB5BC, symSize: 0x50 }
  - { offsetInCU: 0x5DA, offset: 0x5C951, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC10clearWatchyySo13CAPPluginCallCF', symObjAddr: 0x16CC, symBinAddr: 0xB60C, symSize: 0x414 }
  - { offsetInCU: 0x8EC, offset: 0x5CC63, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC10clearWatchyySo13CAPPluginCallCFTo', symObjAddr: 0x1AE0, symBinAddr: 0xBA20, symSize: 0x50 }
  - { offsetInCU: 0x908, offset: 0x5CC7F, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC16checkPermissionsyySo13CAPPluginCallCF', symObjAddr: 0x1B30, symBinAddr: 0xBA70, symSize: 0x4B0 }
  - { offsetInCU: 0xAD4, offset: 0x5CE4B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC16checkPermissionsyySo13CAPPluginCallCFTo', symObjAddr: 0x22DC, symBinAddr: 0xC21C, symSize: 0x50 }
  - { offsetInCU: 0xAF0, offset: 0x5CE67, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC18requestPermissionsyySo13CAPPluginCallCF', symObjAddr: 0x232C, symBinAddr: 0xC26C, symSize: 0x528 }
  - { offsetInCU: 0xC9A, offset: 0x5D011, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC18requestPermissionsyySo13CAPPluginCallCFTo', symObjAddr: 0x2854, symBinAddr: 0xC794, symSize: 0x50 }
  - { offsetInCU: 0xCB6, offset: 0x5D02D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfC', symObjAddr: 0x28A4, symBinAddr: 0xC7E4, symSize: 0xB4 }
  - { offsetInCU: 0xCD4, offset: 0x5D04B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2Stcfc', symObjAddr: 0x2958, symBinAddr: 0xC898, symSize: 0x434 }
  - { offsetInCU: 0xDBC, offset: 0x5D133, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC6bridge8pluginId0E4NameAC0A017CAPBridgeProtocol_p_S2StcfcTo', symObjAddr: 0x2D8C, symBinAddr: 0xCCCC, symSize: 0x74 }
  - { offsetInCU: 0xDD8, offset: 0x5D14F, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCACycfC', symObjAddr: 0x2E00, symBinAddr: 0xCD40, symSize: 0x20 }
  - { offsetInCU: 0xDF6, offset: 0x5D16D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCACycfc', symObjAddr: 0x2E20, symBinAddr: 0xCD60, symSize: 0x3B8 }
  - { offsetInCU: 0xEB4, offset: 0x5D22B, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCACycfcTo', symObjAddr: 0x31D8, symBinAddr: 0xD118, symSize: 0x20 }
  - { offsetInCU: 0xED0, offset: 0x5D247, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCfD', symObjAddr: 0x31F8, symBinAddr: 0xD138, symSize: 0x30 }
  - { offsetInCU: 0x10B7, offset: 0x5D42E, size: 0x8, addend: 0x0, symName: ___swift_allocate_boxed_opaque_existential_1, symObjAddr: 0xCF8, symBinAddr: 0xAC78, symSize: 0x3C }
  - { offsetInCU: 0x10CB, offset: 0x5D442, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib27IONGLOCAuthorisationHandler_AA022IONGLOCMonitorLocationD0AA22IONGLOCServicesCheckerAA013IONGLOCSinglefD0pSgWOf', symObjAddr: 0xD34, symBinAddr: 0xACB4, symSize: 0x48 }
  - { offsetInCU: 0x11A2, offset: 0x5D519, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC21handleLocationRequest33_BFA717F0A29A4D831C9388058947DA51LL_9watchUUID4callySb_SSSgSo13CAPPluginCallCtF', symObjAddr: 0xE90, symBinAddr: 0xADD0, symSize: 0x69C }
  - { offsetInCU: 0x169A, offset: 0x5DA11, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC33checkIfLocationServicesAreEnabled33_BFA717F0A29A4D831C9388058947DA51LLySbSo13CAPPluginCallCSgF', symObjAddr: 0x1FE0, symBinAddr: 0xBF20, symSize: 0x2FC }
  - { offsetInCU: 0x18F6, offset: 0x5DC6D, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCfETo', symObjAddr: 0x3228, symBinAddr: 0xD168, symSize: 0x88 }
  - { offsetInCU: 0x1925, offset: 0x5DC9C, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyF', symObjAddr: 0x32B0, symBinAddr: 0xD1F0, symSize: 0x3B8 }
  - { offsetInCU: 0x19BD, offset: 0x5DD34, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy17IONGeolocationLib20IONGLOCAuthorisationOcfU_', symObjAddr: 0x3668, symBinAddr: 0xD5A8, symSize: 0x1D0 }
  - { offsetInCU: 0x1A0F, offset: 0x5DD86, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy7Combine11SubscribersO10CompletionOy_17IONGeolocationLib20IONGLOCLocationErrorOGcfU0_', symObjAddr: 0x3FE4, symBinAddr: 0xDF24, symSize: 0x2FC }
  - { offsetInCU: 0x1C1F, offset: 0x5DF96, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy17IONGeolocationLib20IONGLOCPositionModelVcfU1_', symObjAddr: 0x42E0, symBinAddr: 0xE220, symSize: 0x84 }
  - { offsetInCU: 0x1CB0, offset: 0x5E027, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC30onLocationPermissionNotGranted33_BFA717F0A29A4D831C9388058947DA51LL5erroryAA0B5ErrorO_tF', symObjAddr: 0x3838, symBinAddr: 0xD778, symSize: 0x1EC }
  - { offsetInCU: 0x1E95, offset: 0x5E20C, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC28requestLocationAuthorisation33_BFA717F0A29A4D831C9388058947DA51LL4typey17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO_tF', symObjAddr: 0x3A24, symBinAddr: 0xD964, symSize: 0x2F8 }
  - { offsetInCU: 0x1EFD, offset: 0x5E274, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC28requestLocationAuthorisation33_BFA717F0A29A4D831C9388058947DA51LL4typey17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO_tFyyYbcfU_', symObjAddr: 0x4364, symBinAddr: 0xE2A4, symSize: 0xD4 }
  - { offsetInCU: 0x1F59, offset: 0x5E2D0, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC27onLocationPermissionGranted33_BFA717F0A29A4D831C9388058947DA51LLyyF', symObjAddr: 0x3D1C, symBinAddr: 0xDC5C, symSize: 0x2C8 }
  - { offsetInCU: 0x2135, offset: 0x5E4AC, size: 0x8, addend: 0x0, symName: '_$sIegh_IeyBh_TR', symObjAddr: 0x4438, symBinAddr: 0xE378, symSize: 0x2C }
  - { offsetInCU: 0x2163, offset: 0x5E4DA, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSS_Tg5', symObjAddr: 0x4464, symBinAddr: 0xE3A4, symSize: 0x64 }
  - { offsetInCU: 0x21C6, offset: 0x5E53D, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSS_Tg5', symObjAddr: 0x44C8, symBinAddr: 0xE408, symSize: 0xE0 }
  - { offsetInCU: 0x221B, offset: 0x5E592, size: 0x8, addend: 0x0, symName: '_$sSa034_makeUniqueAndReserveCapacityIfNotB0yyFSo13CAPPluginCallC_Tg5', symObjAddr: 0x45A8, symBinAddr: 0xE4E8, symSize: 0x90 }
  - { offsetInCU: 0x22F8, offset: 0x5E66F, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV20_consumeAndCreateNew14bufferIsUnique15minimumCapacity13growForAppendAByxGSb_SiSbtFSo13CAPPluginCallC_Tg5', symObjAddr: 0x4638, symBinAddr: 0xE578, symSize: 0x16C }
  - { offsetInCU: 0x2496, offset: 0x5E80D, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV8setValue_6forKeyyq_n_xtFSS_So13CAPPluginCallCTg5', symObjAddr: 0x47A4, symBinAddr: 0xE6E4, symSize: 0xE4 }
  - { offsetInCU: 0x2595, offset: 0x5E90C, size: 0x8, addend: 0x0, symName: '_$sSD8_VariantV11removeValue6forKeyq_Sgx_tFSS_So13CAPPluginCallCTg5', symObjAddr: 0x4888, symBinAddr: 0xE7C8, symSize: 0xCC }
  - { offsetInCU: 0x2636, offset: 0x5E9AD, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV12mutatingFind_8isUniques10_HashTableV6BucketV6bucket_Sb5foundtx_SbtFSS_So13CAPPluginCallCTg5', symObjAddr: 0x4954, symBinAddr: 0xE894, symSize: 0xD8 }
  - { offsetInCU: 0x2697, offset: 0x5EA0E, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV4copyyyFSS_So13CAPPluginCallCTg5', symObjAddr: 0x4A2C, symBinAddr: 0xE96C, symSize: 0x1C4 }
  - { offsetInCU: 0x2739, offset: 0x5EAB0, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV20_copyOrMoveAndResize8capacity12moveElementsySi_SbtFSS_So13CAPPluginCallCTg5', symObjAddr: 0x4BF0, symBinAddr: 0xEB30, symSize: 0x398 }
  - { offsetInCU: 0x2845, offset: 0x5EBBC, size: 0x8, addend: 0x0, symName: '_$ss17_NativeDictionaryV7_delete2atys10_HashTableV6BucketV_tFSS_So13CAPPluginCallCTg5', symObjAddr: 0x4F88, symBinAddr: 0xEEC8, symSize: 0x204 }
  - { offsetInCU: 0x2965, offset: 0x5ECDC, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_4, symObjAddr: 0x5374, symBinAddr: 0xF2B4, symSize: 0x24 }
  - { offsetInCU: 0x29B0, offset: 0x5ED27, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginCMa', symObjAddr: 0x54AC, symBinAddr: 0xF3EC, symSize: 0x20 }
  - { offsetInCU: 0x29C4, offset: 0x5ED3B, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib27IONGLOCAuthorisationHandler_AA022IONGLOCMonitorLocationD0AA22IONGLOCServicesCheckerAA013IONGLOCSinglefD0pWOc', symObjAddr: 0x54CC, symBinAddr: 0xF40C, symSize: 0x4C }
  - { offsetInCU: 0x29D8, offset: 0x5ED4F, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOc', symObjAddr: 0x5518, symBinAddr: 0xF458, symSize: 0x44 }
  - { offsetInCU: 0x29EC, offset: 0x5ED63, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x556C, symBinAddr: 0xF49C, symSize: 0x10 }
  - { offsetInCU: 0x2A00, offset: 0x5ED77, size: 0x8, addend: 0x0, symName: '_$s9Capacitor7JSValue_pWOb', symObjAddr: 0x557C, symBinAddr: 0xF4AC, symSize: 0x18 }
  - { offsetInCU: 0x2A4B, offset: 0x5EDC2, size: 0x8, addend: 0x0, symName: '_$ss12_ArrayBufferV13_copyContents8subRange12initializingSpyxGSnySiG_AFtFSo13CAPPluginCallC_Tg5Tf4nng_n', symObjAddr: 0x55B4, symBinAddr: 0xF4E4, symSize: 0x120 }
  - { offsetInCU: 0x2B33, offset: 0x5EEAA, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy7Combine11SubscribersO10CompletionOy_17IONGeolocationLib20IONGLOCLocationErrorOGcfU0_TA', symObjAddr: 0x56F8, symBinAddr: 0xF628, symSize: 0x8 }
  - { offsetInCU: 0x2B47, offset: 0x5EEBE, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy17IONGeolocationLib20IONGLOCPositionModelVcfU1_TA', symObjAddr: 0x5700, symBinAddr: 0xF630, symSize: 0x8 }
  - { offsetInCU: 0x2B5B, offset: 0x5EED2, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x5708, symBinAddr: 0xF638, symSize: 0x44 }
  - { offsetInCU: 0x2B6F, offset: 0x5EEE6, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC13setupBindings33_BFA717F0A29A4D831C9388058947DA51LLyyFy17IONGeolocationLib20IONGLOCAuthorisationOcfU_TA', symObjAddr: 0x574C, symBinAddr: 0xF67C, symSize: 0x8 }
  - { offsetInCU: 0x2B83, offset: 0x5EEFA, size: 0x8, addend: 0x0, symName: '_$s20CapacitorGeolocation0B6PluginC28requestLocationAuthorisation33_BFA717F0A29A4D831C9388058947DA51LL4typey17IONGeolocationLib31IONGLOCAuthorisationRequestTypeO_tFyyYbcfU_TA', symObjAddr: 0x5800, symBinAddr: 0xF730, symSize: 0x30 }
  - { offsetInCU: 0x2B97, offset: 0x5EF0E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5830, symBinAddr: 0xF760, symSize: 0x10 }
  - { offsetInCU: 0x2BAB, offset: 0x5EF22, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x5840, symBinAddr: 0xF770, symSize: 0x8 }
  - { offsetInCU: 0x2BF7, offset: 0x5EF6E, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_9Capacitor7JSValue_pSSypTg5', symObjAddr: 0x0, symBinAddr: 0x9F80, symSize: 0x39C }
  - { offsetInCU: 0x2D56, offset: 0x5F0CD, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2S9Capacitor7JSValue_pTg5', symObjAddr: 0x39C, symBinAddr: 0xA31C, symSize: 0x314 }
  - { offsetInCU: 0x2E89, offset: 0x5F200, size: 0x8, addend: 0x0, symName: '_$ss17_dictionaryUpCastySDyq0_q1_GSDyxq_GSHRzSHR0_r2_lFSS_S2SypTg5', symObjAddr: 0x6B0, symBinAddr: 0xA630, symSize: 0x37C }
  - { offsetInCU: 0x31DE, offset: 0x5F555, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_So13CAPPluginCallCTg5Tf4gd_n', symObjAddr: 0x518C, symBinAddr: 0xF0CC, symSize: 0xEC }
  - { offsetInCU: 0x3301, offset: 0x5F678, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_SSTg5Tf4gd_n', symObjAddr: 0x5278, symBinAddr: 0xF1B8, symSize: 0xFC }
  - { offsetInCU: 0x3402, offset: 0x5F779, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSS_9Capacitor7JSValue_pTg5Tf4gd_n', symObjAddr: 0x5398, symBinAddr: 0xF2D8, symSize: 0x114 }
  - { offsetInCU: 0x2B, offset: 0x5FC52, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV20CapacitorGeolocationE14coordsJSObject33_B32C48B8A7EBE778A067C3808927693BLLSDySS0E07JSValue_pGvg', symObjAddr: 0x0, symBinAddr: 0xF878, symSize: 0x278 }
  - { offsetInCU: 0xF3, offset: 0x5FD1A, size: 0x8, addend: 0x0, symName: '_$s17IONGeolocationLib20IONGLOCPositionModelV20CapacitorGeolocationE14coordsJSObject33_B32C48B8A7EBE778A067C3808927693BLLSDySS0E07JSValue_pGvg', symObjAddr: 0x0, symBinAddr: 0xF878, symSize: 0x278 }
...
