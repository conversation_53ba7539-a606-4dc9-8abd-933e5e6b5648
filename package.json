{"name": "kt_smart", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:fast": "NODE_ENV=development vite build --mode development", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "debug:ios": "npm run build:fast && ionic capacitor sync ios && ionic capacitor open ios", "debug:android": "npm run build:fast && ionic capacitor sync android && ionic capacitor open android", "live:ios": "ionic capacitor run ios --livereload --external -s -c", "live:android": "ionic capacitor run android --livereload --external", "assets-generate": "npx capacitor-assets generate", "preview": "vite preview", "test:e2e": "cypress run", "test:unit": "vitest", "lint": "eslint ./src --ext .vue --ext .ts --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@antv/g2": "^5.2.12", "@capacitor-community/bluetooth-le": "7.1.1", "@capacitor-community/keep-awake": "7.0.0", "@capacitor/android": "7.4.0", "@capacitor/app": "7.0.1", "@capacitor/core": "^7.0.1", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.4.0", "@capacitor/keyboard": "7.0.1", "@capacitor/screen-orientation": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/core": "8.6.2", "@ionic/vue": "8.6.2", "@ionic/vue-router": "^8.6.2", "@mapbox/search-js-web": "^1.1.0", "@vueuse/components": "13.4.0", "@vueuse/core": "13.4.0", "capacitor-kt-service": "file:capacitor-kt-service", "ionicons": "^7.4.0", "mapbox-gl": "^3.13.0", "pinia": "3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "terser": "5.38.1", "vue": "^3.5.17", "vue-router": "4.5.1"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "7.4.0", "@types/node": "22.13.1", "@vitejs/plugin-legacy": "6.1.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "2.4.6", "chalk": "5.4.1", "eruda": "^3.4.1", "eslint": "^8.35.0", "eslint-plugin-vue": "^9.9.0", "jsdom": "26.0.0", "prettier": "3.4.2", "sass": "1.84.0", "sass-loader": "^16.0.4", "typescript": "^5.7.3", "vite": "^6.1.0", "vitest": "3.0.5", "vue-tsc": "2.2.0"}, "description": "An Ionic project"}