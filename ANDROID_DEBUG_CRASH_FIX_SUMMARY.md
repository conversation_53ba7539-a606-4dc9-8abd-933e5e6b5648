# Android Debug 模式崩溃修复总结

## 问题描述

在 Android Studio 中运行 debug 模式时，应用在启动时崩溃，崩溃日志显示：

```
com.kunteng.plugins.kt.CapacitorKtService.getCurrentBluetoothSendData+0
```

但在 release 模式下应用运行正常。

## 根本原因分析

崩溃的根本原因是**时序问题**和**空指针异常**：

1. **时序问题**: Debug 模式下，由于调试器的介入和额外的检查，可能导致方法调用时序发生变化
2. **空指针异常**: 静态方法 `getCurrentBluetoothSendData()` 在 `BluetoothForegroundService` 的 `bluetoothManager` 完全初始化之前被调用
3. **线程安全**: Debug 模式下的线程调度可能与 Release 模式不同

## 修复方案

### 1. 增强空指针检查

在以下静态方法中添加了完善的 null 检查：

#### BluetoothForegroundService.java
- `getCurrentBluetoothSendData()`
- `getBluetoothSendingStatus()`
- `getBluetoothSendingStats()`

**修复前**:
```java
public static JSObject getCurrentBluetoothSendData() {
    JSObject result = new JSObject();
    if (bluetoothManager != null) {
        int[] currentData = bluetoothManager.getCurrentSendData();
        // 直接使用 currentData，可能为 null
    }
    return result;
}
```

**修复后**:
```java
public static JSObject getCurrentBluetoothSendData() {
    JSObject result = new JSObject();
    try {
        if (bluetoothManager != null) {
            int[] currentData = bluetoothManager.getCurrentSendData();
            if (currentData != null) {
                // 安全处理数据
                JSArray dataArray = new JSArray();
                for (int data : currentData) {
                    dataArray.put(data);
                }
                result.put("data", dataArray);
                result.put("success", true);
            } else {
                result.put("success", false);
                result.put("error", "当前发送数据为空");
            }
        } else {
            result.put("success", false);
            result.put("error", "蓝牙管理器未初始化，请确保蓝牙前台服务已启动");
            Log.w(TAG, "getCurrentBluetoothSendData called but bluetoothManager is null");
        }
    } catch (Exception e) {
        result.put("success", false);
        result.put("error", "获取蓝牙发送数据异常: " + e.getMessage());
        Log.e(TAG, "Exception in getCurrentBluetoothSendData: " + e.getMessage(), e);
    }
    return result;
}
```

### 2. 改进服务初始化

#### 安全的蓝牙管理器初始化
创建了 `safeInitializeBluetoothManager()` 方法替代原来的 `initializeBluetoothManager()`：

```java
private void safeInitializeBluetoothManager() {
    try {
        if (bluetoothManager == null) {
            Log.d(TAG, "开始初始化蓝牙管理器...");
            bluetoothManager = new NativeBluetoothManager(this);
            // 设置回调等...
            Log.d(TAG, "蓝牙管理器初始化成功");
        } else {
            Log.d(TAG, "蓝牙管理器已存在，跳过初始化");
        }
    } catch (Exception e) {
        Log.e(TAG, "蓝牙管理器初始化失败: " + e.getMessage(), e);
        scheduleBluetoothManagerRetry(); // 重试机制
    }
}
```

#### 重试机制
添加了初始化失败的重试机制：

```java
private void scheduleBluetoothManagerRetry() {
    if (keepAliveHandler != null) {
        keepAliveHandler.postDelayed(() -> {
            Log.d(TAG, "重试初始化蓝牙管理器...");
            safeInitializeBluetoothManager();
        }, 2000); // 2秒后重试
    }
}
```

### 3. 调试辅助功能

#### 服务状态检查方法
添加了 `getServiceInitializationStatus()` 方法用于调试：

```java
public static JSObject getServiceInitializationStatus() {
    JSObject status = new JSObject();
    try {
        status.put("isServiceRunning", isServiceRunning);
        status.put("bluetoothManagerInitialized", bluetoothManager != null);
        status.put("lastSendingStatusCached", lastSendingStatus != null);
        status.put("lastSendingStatsCached", lastSendingStats != null);
        status.put("timestamp", System.currentTimeMillis());
        
        if (bluetoothManager != null) {
            status.put("bluetoothManagerDetails", "已初始化");
        } else {
            status.put("bluetoothManagerDetails", "未初始化 - 可能服务尚未完全启动");
        }
    } catch (Exception e) {
        status.put("error", "获取服务状态异常: " + e.getMessage());
        Log.e(TAG, "获取服务初始化状态异常: " + e.getMessage(), e);
    }
    return status;
}
```

#### CapacitorKtService 中的调试方法
在 `CapacitorKtService.java` 中添加了对应的插件方法：

```java
@PluginMethod
public void getServiceInitializationStatus(PluginCall call) {
    try {
        JSObject ret = BluetoothForegroundService.getServiceInitializationStatus();
        call.resolve(ret);
    } catch (Exception e) {
        call.reject("Failed to get service initialization status: " + e.getMessage(), e);
    }
}
```

### 4. 改进错误处理

#### 详细的日志记录
所有方法都添加了详细的日志记录，包括：
- 警告日志：当 bluetoothManager 为 null 时
- 错误日志：当发生异常时
- 调试日志：正常操作流程

#### 安全的返回值
所有方法都确保返回安全的默认值，不会导致前端代码崩溃。

## 使用建议

### 1. 前端代码最佳实践

```javascript
// 启动服务时的推荐做法
try {
  // 1. 启动蓝牙前台服务
  await CapacitorKt.startBluetoothForegroundService();
  
  // 2. 等待服务初始化
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 3. 检查服务状态
  const status = await CapacitorKt.getServiceInitializationStatus();
  if (!status.bluetoothManagerInitialized) {
    console.warn('蓝牙管理器未初始化，请稍后重试');
    return;
  }
  
  // 4. 开始蓝牙操作
  await CapacitorKt.startNativeBluetoothSending({...});
} catch (error) {
  console.error('启动服务失败:', error);
}
```

### 2. 错误处理

```javascript
// 调用蓝牙相关方法时的错误处理
try {
  const data = await CapacitorKt.getCurrentBluetoothSendData();
  if (!data.success) {
    console.warn('获取蓝牙数据失败:', data.error);
    // 可以尝试重新初始化服务
  }
} catch (error) {
  console.error('调用失败:', error);
}
```

### 3. 开发模式下的额外检查

```javascript
// 在开发模式下添加状态监控
if (__DEV__) {
  setInterval(async () => {
    try {
      const status = await CapacitorKt.getServiceInitializationStatus();
      console.log('Service Status Check:', status);
    } catch (error) {
      console.error('Status check failed:', error);
    }
  }, 10000); // 每10秒检查一次
}
```

## 测试验证

修复后应该验证以下场景：

1. **Debug 模式启动**: 应用在 Android Studio debug 模式下能正常启动
2. **Release 模式兼容**: Release 模式仍然正常工作
3. **服务重启**: 服务停止后重新启动能正常工作
4. **异常恢复**: 在服务初始化失败时能自动重试
5. **状态查询**: 能正确查询服务初始化状态

## 文件修改清单

1. **BluetoothForegroundService.java**
   - 增强 `getCurrentBluetoothSendData()` 方法的空指针检查
   - 改进 `getBluetoothSendingStatus()` 和 `getBluetoothSendingStats()` 方法
   - 添加 `safeInitializeBluetoothManager()` 方法
   - 添加 `scheduleBluetoothManagerRetry()` 重试机制
   - 添加 `getServiceInitializationStatus()` 调试方法

2. **CapacitorKtService.java**
   - 改进 `getCurrentBluetoothSendDataSync()` 方法的错误处理
   - 添加 `getServiceInitializationStatus()` 插件方法

3. **新增文档**
   - `DEBUG_MODE_CRASH_FIX.md`: 详细的修复指南
   - `ANDROID_DEBUG_CRASH_FIX_SUMMARY.md`: 修复总结（本文件）

## 预防措施

为了避免类似问题再次发生：

1. **避免过早调用**: 确保在服务完全启动后再调用相关方法
2. **添加重试逻辑**: 对于可能失败的操作添加重试机制
3. **监控服务状态**: 定期检查服务是否正常运行
4. **完善错误处理**: 对所有可能的异常情况进行处理
5. **详细日志记录**: 添加足够的日志以便问题排查

这些修复确保了应用在 Debug 模式下的稳定性，同时保持了 Release 模式的兼容性。