# Debug 模式崩溃修复指南

## 问题描述

在 Android Studio 中运行 debug 模式时应用崩溃，但正常运行模式工作正常。

## 崩溃原因分析

根据崩溃日志分析：
```
com.kunteng.plugins.kt.CapacitorKtService.getCurrentBluetoothSendData+0
```

问题出现在 `getCurrentBluetoothSendData` 方法中，主要原因是：

1. **时序问题**: Debug 模式下，由于调试器的介入和额外的检查，可能导致方法调用时序发生变化
2. **空指针异常**: `bluetoothManager` 静态实例在服务完全初始化之前被调用
3. **线程安全**: Debug 模式下的线程调度可能与 Release 模式不同

## 已实施的修复方案

### 1. 增强空指针检查

在所有静态方法中添加了完善的 null 检查：
- `getCurrentBluetoothSendData()`
- `getBluetoothSendingStatus()`
- `getBluetoothSendingStats()`

### 2. 改进错误处理

```java
// 添加了详细的异常捕获和日志记录
try {
    if (bluetoothManager != null) {
        // 正常处理逻辑
    } else {
        // 返回安全的默认值，并记录警告
        Log.w(TAG, "bluetoothManager is null - service may not be initialized yet");
    }
} catch (Exception e) {
    Log.e(TAG, "Exception: " + e.getMessage(), e);
    // 返回安全的错误响应
}
```

### 3. 安全的服务初始化

- 使用 `safeInitializeBluetoothManager()` 替代原来的 `initializeBluetoothManager()`
- 添加重试机制，防止初始化失败
- 增加详细的初始化状态日志

### 4. 调试辅助方法

添加了 `getServiceInitializationStatus()` 方法，可以在运行时检查服务状态：

```javascript
// 在前端代码中调用
const status = await CapacitorKt.getServiceInitializationStatus();
console.log('Service Status:', status);
```

## 使用建议

### 1. 启动服务时的最佳实践

```javascript
// 确保按正确顺序启动服务
try {
  // 1. 先启动蓝牙前台服务
  await CapacitorKt.startBluetoothForegroundService();
  
  // 2. 等待一小段时间确保服务完全初始化
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 3. 检查服务状态
  const status = await CapacitorKt.getServiceInitializationStatus();
  if (!status.bluetoothManagerInitialized) {
    console.warn('蓝牙管理器未初始化，请稍后重试');
    return;
  }
  
  // 4. 开始蓝牙操作
  await CapacitorKt.startNativeBluetoothSending({...});
} catch (error) {
  console.error('启动服务失败:', error);
}
```

### 2. 错误处理

```javascript
// 在调用可能出错的方法时添加错误处理
try {
  const data = await CapacitorKt.getCurrentBluetoothSendData();
  if (!data.success) {
    console.warn('获取蓝牙数据失败:', data.error);
    // 可以尝试重新初始化服务
  }
} catch (error) {
  console.error('调用失败:', error);
}
```

### 3. Debug 模式下的额外检查

```javascript
// 在开发模式下添加更多检查
if (__DEV__) {
  // 定期检查服务状态
  setInterval(async () => {
    try {
      const status = await CapacitorKt.getServiceInitializationStatus();
      console.log('Service Status Check:', status);
    } catch (error) {
      console.error('Status check failed:', error);
    }
  }, 10000); // 每10秒检查一次
}
```

## 预防措施

1. **避免过早调用**: 确保在服务完全启动后再调用相关方法
2. **添加重试逻辑**: 对于可能失败的操作添加重试机制
3. **监控服务状态**: 定期检查服务是否正常运行
4. **完善错误处理**: 对所有可能的异常情况进行处理

## 故障排除

如果仍然遇到崩溃，可以：

1. 检查 Android Studio 的 Logcat 输出，查找相关错误日志
2. 调用 `getServiceInitializationStatus()` 检查服务状态
3. 确保应用有足够的权限（蓝牙、位置等）
4. 检查设备的电池优化设置是否影响后台服务

## 联系支持

如果问题仍然存在，请提供：
- 完整的崩溃日志
- 设备信息（型号、Android 版本）
- 服务初始化状态信息
- 复现步骤