## Capacitor Plugins

- 🟩 [@capacitor-community/bluetooth-le@7.0.0](https://github.com/capacitor-community/bluetooth-le.git)
- 🟩 [@capacitor-community/keep-awake@7.0.0](https://github.com/capacitor-community/keep-awake.git)
- 🟩 [@capacitor/app@7.0.0](https://github.com/ionic-team/capacitor-plugins.git)
- 🟩 [@capacitor/geolocation@7.1.0](https://github.com/ionic-team/capacitor-geolocation.git)
- 🟩 [@capacitor/haptics@7.0.0](https://github.com/ionic-team/capacitor-plugins.git)
- 🟩 [@capacitor/keyboard@7.0.0](https://github.com/ionic-team/capacitor-plugins.git)
- 🟩 [@capacitor/screen-orientation@7.0.0](https://github.com/ionic-team/capacitor-plugins.git)
- 🟩 [@capacitor/status-bar@7.0.0](https://github.com/ionic-team/capacitor-plugins.git)
## Cordova Plugins

## Dependencies

- 🟩 [@capacitor/android@7.0.1](https://github.com/ionic-team/capacitor.git)
- 🟩 [@capacitor/cli@7.0.1](https://github.com/ionic-team/capacitor.git)
- 🟩 [@capacitor/core@7.0.1](https://github.com/ionic-team/capacitor.git)
- 🟩 [@capacitor/ios@7.0.1](https://github.com/ionic-team/capacitor.git)
- 🟩 [@ionic/core@8.4.3](https://github.com/ionic-team/ionic-framework.git)
- 🟩 [@ionic/vue@8.4.3](https://github.com/ionic-team/ionic-framework.git)
- 🟩 [@ionic/vue-router@8.4.3](https://github.com/ionic-team/ionic-framework.git)
- 🟩 [@types/google.maps@3.58.1](https://github.com/DefinitelyTyped/DefinitelyTyped.git)
- 🟩 [@types/node@22.13.1](https://github.com/DefinitelyTyped/DefinitelyTyped.git) - (Latest 22.13.4)
- 🟩 [@vitejs/plugin-legacy@6.0.1](https://github.com/vitejs/vite.git)
- 🟩 [@vitejs/plugin-vue@5.2.1](https://github.com/vitejs/vite-plugin-vue.git)
- 🟧 [@vue/eslint-config-typescript@12.0.0](https://github.com/vuejs/eslint-config-typescript.git) - (Latest 14.4.0) - Is behind 2 major versions.
- 🟩 [@vue/test-utils@2.4.6](https://github.com/vuejs/test-utils.git)
- 🟩 [@vueuse/components@12.5.0](https://github.com/vueuse/vueuse.git) - (Latest 12.7.0)
- 🟩 [@vueuse/core@12.5.0](https://github.com/vueuse/vueuse.git) - (Latest 12.7.0)
- 🟩 [chalk@5.4.1](https://github.com/chalk/chalk.git)
- 🟩 [eruda@3.4.1](https://github.com/liriliri/eruda.git)
- 🟩 [eslint@9.20.1](https://github.com/eslint/eslint.git)
- 🟩 [eslint-plugin-vue@9.32.0](https://github.com/vuejs/eslint-plugin-vue.git)
- 🟩 [ionicons@7.4.0](https://github.com/ionic-team/ionicons.git)
- 🟩 [jsdom@26.0.0](https://github.com/jsdom/jsdom.git)
- 🟩 [pinia@3.0.1](https://github.com/vuejs/pinia.git)
- 🟩 [pinia-plugin-persistedstate@4.2.0](https://github.com/prazdevs/pinia-plugin-persistedstate.git)
- 🟩 [prettier@3.4.2](https://github.com/prettier/prettier.git) - (Latest 3.5.1)
- 🟩 [sass@1.84.0](https://github.com/sass/dart-sass.git) - (Latest 1.85.0)
- 🟩 [sass-loader@16.0.4](https://github.com/webpack-contrib/sass-loader.git) - (Latest 16.0.5)
- 🟩 [terser@5.38.1](https://github.com/terser/terser.git) - (Latest 5.39.0)
- 🟩 [typescript@5.7.3](https://github.com/microsoft/TypeScript.git)
- 🟩 [vite@6.1.0](https://github.com/vitejs/vite.git) - (Latest 6.1.1)
- 🟩 [vitest@3.0.5](https://github.com/vitest-dev/vitest.git) - (Latest 3.0.6)
- 🟩 [vue@3.5.13](https://github.com/vuejs/core.git)
- 🟩 [vue-router@4.5.0](https://github.com/vuejs/router.git)
- 🟩 [vue-tsc@2.2.0](https://github.com/vuejs/language-tools.git) - (Latest 2.2.2)
### Maintenance Score
40 out of 41 dependencies were up to date without issues.



## Nonstandard naming
The following files and folders do not follow the standard naming convention:

- /App.vue
- /components/DashboardComponent.vue
- /components/DeviceManageModal.vue
- /components/ExploreContainer.vue
- /hooks/useBluetooth-le.ts
- /hooks/useDisconnectEventBus.ts
- /hooks/useError.ts
- /hooks/useExitApp.ts
- /hooks/useGeoLocation.ts
- /hooks/useHaptics.ts
- /hooks/useMessage.ts
- /hooks/useSetting.ts
- /hooks/useTimer.ts
- /hooks/useToast.ts
- /services/GPSKalmanFilter.ts
- /store/useBleStore.ts
- /store/useDashboardStore.ts
- /store/useErrorStore.ts
- /store/useNavigationStore.ts
- /store/usePositionStore.ts
- /store/useSettingStore.ts
- /store/useTrackStore.ts
- /views/BluetoothPage.vue
- /views/GoogleMap/PlaceSearch.vue
- /views/HomePage.vue
- /views/InfoPage.vue
- /views/SettingPage.vue
- /views/Tab1Page.vue
- /views/Tab2Page.vue
- /views/TabsPage.vue
