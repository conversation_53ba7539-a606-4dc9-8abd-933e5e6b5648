# 正确的蓝牙数据流架构

## 🎯 设计原则

基于您的正确理解，我们实现了以下数据流架构：

1. **第一次发送数据**：使用Web端的完整数据
2. **导航数据更新**：基于当前蓝牙发送数据，仅更新导航部分(12-15)
3. **设置数据更新**：基于当前蓝牙发送数据，仅更新协议部分(0-11)
4. **校验和处理**：每次更新后重新计算第17位校验和

## 🔄 数据流图

```
初始化阶段:
Web端完整数据 → Native端蓝牙发送
[0-17] 全部数据

导航更新阶段:
当前蓝牙发送数据 → 更新导航部分(12-15) → 重新计算校验和(16) → Native端
[0-11] 保持不变    [12-15] 导航数据      [16] 重新计算      [17] 保持不变

设置更新阶段:
当前蓝牙发送数据 → 更新协议部分(0-11) → 保持导航部分(12-15) → 重新计算校验和(16) → Native端
[0-11] 协议数据    [12-15] 保持不变      [16] 重新计算      [17] 保持不变
```

## 📊 数据结构说明

```
字节位置    | 用途                    | 更新时机
-----------|------------------------|------------------
0          | 帧头                   | 设置更新时
1-4        | 速度、档位等设置参数      | 设置更新时
5          | 固定值(不参与校验和)     | 设置更新时
6-11       | 其他协议参数            | 设置更新时
12         | 方向+距离单位+镜像位     | 导航更新时
13         | 单次距离低位            | 导航更新时
14         | 总距离高位+单次距离高位   | 导航更新时
15         | 总距离低位              | 导航更新时
16         | 校验和                 | 每次更新后重新计算
17         | 截止位(0x0E)           | 固定不变
```

## 🔧 实现细节

### 1. Web端设置更新 (useNativeBluetoothMessage.ts)

```typescript
const updateNativeBluetoothData = async () => {
  // 步骤1：获取当前Native端正在发送的蓝牙数据
  const currentDataResult = await CapacitorKtService.getCurrentBluetoothSendData()
  let currentBluetoothData = currentDataResult.success ? 
    [...currentDataResult.data] : [...writeDataRef.value]
  
  // 步骤2：保存原有的导航数据部分（字节12-15）和镜像位
  const originalNavigationData = currentBluetoothData.slice(12, 16)
  const originalMirrorBit = currentBluetoothData[12] & 0x80
  
  // 步骤3：更新协议部分（字节0-11）
  for (let i = 0; i <= 11; i++) {
    currentBluetoothData[i] = writeDataRef.value[i]
  }
  
  // 步骤4：恢复导航数据部分和镜像位
  for (let i = 12; i <= 15; i++) {
    currentBluetoothData[i] = originalNavigationData[i - 12]
  }
  if (originalMirrorBit) {
    currentBluetoothData[12] = currentBluetoothData[12] | 0x80
  }
  
  // 步骤5：重新计算校验和（字节16）
  let checksum = 0
  for (let i = 1; i <= 15; i++) {
    if (i !== 5) checksum ^= currentBluetoothData[i]
  }
  currentBluetoothData[16] = checksum & 0xFF
  
  // 步骤6：发送更新后的数据到Native端
  await CapacitorKtService.updateBluetoothSendData(currentBluetoothData)
}
```

### 2. Native端导航更新 (NavigationManager.kt)

```kotlin
private fun sendNavigationDataToBluetooth(routeProgress: RouteProgress) {
    // 步骤1：获取当前正在发送的蓝牙数据
    val currentData = ktService.getCurrentBluetoothSendDataSync()
    var currentBluetoothData = currentData?.clone() ?: getDefaultData()
    
    // 步骤2：仅更新导航数据部分(12-15)，保持协议部分(0-11)不变
    val navigationData = dataBuilder.modifyNavigationData(currentBluetoothData, routeProgress)
    
    // 步骤3：处理镜像位（字节12的第7位）
    if (isMirrorEnabled) {
        navigationData[12] = navigationData[12] or 0x80
    } else {
        navigationData[12] = navigationData[12] and 0x7F
    }
    
    // 步骤4：重新计算校验和（字节16）
    navigationData[16] = dataBuilder.calculateChecksum(navigationData)
    
    // 步骤5：发送更新后的数据
    ktService.updateBluetoothSendDataInternal(navigationData)
}
```

### 3. Native端导航清空 (NavigationManager.kt)

```kotlin
fun clearNavigationBluetoothData() {
    // 步骤1：获取当前正在发送的蓝牙数据
    val currentData = ktService.getCurrentBluetoothSendDataSync()
    var currentBluetoothData = currentData?.clone() ?: getDefaultData()
    
    // 步骤2：保存镜像位状态
    val originalMirrorBit = currentBluetoothData[12] and 0x80
    
    // 步骤3：清空导航数据部分(12-15)，保持协议部分(0-11)不变
    currentBluetoothData[12] = 0x00
    currentBluetoothData[13] = 0x00
    currentBluetoothData[14] = 0x00
    currentBluetoothData[15] = 0x00
    
    // 步骤4：恢复镜像位
    if (originalMirrorBit != 0) {
        currentBluetoothData[12] = currentBluetoothData[12] or 0x80
    }
    
    // 步骤5：重新计算校验和（字节16）
    var checksum = 0
    for (i in 1..15) {
        if (i != 5) checksum = checksum xor currentBluetoothData[i]
    }
    currentBluetoothData[16] = checksum and 0xFF
    
    // 步骤6：发送清空后的数据
    ktService.updateBluetoothSendDataInternal(currentBluetoothData)
}
```

## ✅ 优势和好处

### 1. 数据一致性
- 始终基于当前实际发送的数据进行更新
- 避免了使用可能过时或不完整的数据源
- 确保协议部分和导航部分的正确协调

### 2. 模块化更新
- 设置更新只影响协议部分(0-11)
- 导航更新只影响导航部分(12-15)
- 每次更新后正确重新计算校验和

### 3. 状态保持
- 设置更新时保持导航数据不变
- 导航更新时保持协议设置不变
- 镜像位状态正确维护

### 4. 错误恢复
- 如果无法获取当前数据，使用合理的默认值
- 每次更新后进行数据验证
- 提供详细的日志记录便于调试

## 🧪 测试验证

### 1. 设置更新测试
```javascript
// 修改任意设置参数
// 预期：协议部分(0-11)更新，导航部分(12-15)保持不变
```

### 2. 导航更新测试
```javascript
// 启动导航，发送导航进度
// 预期：导航部分(12-15)更新，协议部分(0-11)保持不变
```

### 3. 数据一致性测试
```javascript
// 使用BluetoothDataComparisonPage.vue页面
// 预期：Vue端、Native端、串口三方数据完全一致
```

### 4. 校验和验证测试
```javascript
// 每次数据更新后验证校验和
// 预期：字节16的值等于字节1-15(除字节5)的异或结果
```

## 🔍 调试工具

### 1. 日志监控
```bash
adb logcat | grep -E "(NavigationManager|useNativeBluetoothMessage)" --color=always
```

### 2. 数据对比页面
- 使用 `BluetoothDataComparisonPage.vue`
- 实时监控数据变化
- 检查数据一致性

### 3. 浏览器调试脚本
- 运行 `quick_fix_bluetooth_data.js`
- 使用 `window.bluetoothDebugUtils` 工具函数

## 📈 性能优化

1. **减少不必要的数据传输**：只更新需要变化的部分
2. **数据验证机制**：确保更新成功后再继续
3. **错误重试机制**：失败时自动重试，提高可靠性
4. **详细日志记录**：便于问题定位和性能分析

这个架构确保了数据流的正确性、一致性和可维护性，完全符合您描述的理想数据流设计。