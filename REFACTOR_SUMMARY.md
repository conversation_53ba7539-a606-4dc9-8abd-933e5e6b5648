# MapBoxPage.vue 重构总结

## 重构目标
将导航数据的蓝牙发送逻辑从Web端（MapBoxPage.vue）迁移到Native端（capacitor-kt-service），以解决熄屏后导航数据无法发送的问题。

## 主要变更

### 1. Web端变更 (src/views/MapBoxPage.vue)

#### 移除的功能：
- ✅ 移除了 `useNavigation` hook的依赖
- ✅ 移除了 `sendNavigationProgress` 导航数据发送逻辑
- ✅ 移除了 `openMirror` 和 `closeMirror` 镜像管理逻辑
- ✅ 移除了 `isMirrorEnabled` 镜像状态跟踪
- ✅ 移除了导航进度事件中的Web端数据处理逻辑

#### 保留的功能：
- ✅ 保留了所有事件监听器（仅用于日志记录）
- ✅ 保留了导航启动和错误处理逻辑
- ✅ 保留了UI交互逻辑

#### 简化的逻辑：
- 事件监听器现在只负责日志记录，不再处理蓝牙数据发送
- 镜像状态变化事件仅输出日志信息
- 导航数据发送现在完全由Native端处理

### 2. Native端变更 (capacitor-kt-service)

#### 新增功能：

##### CapacitorKtService.java
- ✅ 新增 `updateBluetoothSendDataInternal(int[] data)` 方法
- ✅ 提供内部调用的蓝牙数据发送接口

##### NavigationActivity.kt
- ✅ 新增镜像状态管理 (`isMirrorEnabled` 静态变量)
- ✅ 新增 `sendNavigationDataToBluetooth(RouteProgress)` 方法
- ✅ 新增 `buildNavigationBluetoothData(RouteProgress)` 方法
- ✅ 新增 `getDirectionFromBannerInstructions()` 方向解析方法
- ✅ 新增 `mapDistanceToRule()` 距离规则映射方法
- ✅ 新增 `calculateChecksum()` 校验值计算方法
- ✅ 新增 `clearNavigationBluetoothData()` 数据清空方法

##### NavigationDialogFragment.kt
- ✅ 同样新增了所有导航数据处理方法
- ✅ 在 `onDestroyView()` 中添加数据清理逻辑

#### 集成逻辑：
- ✅ 在 `routeProgressObserver` 中直接调用蓝牙数据发送
- ✅ 镜像开启/关闭时更新状态并控制数据发送
- ✅ 导航结束时自动清空蓝牙数据
- ✅ 只有在镜像开启时才发送导航数据

### 3. 数据协议保持一致

Native端的数据构建逻辑完全复刻了原Web端 `useNavigation.ts` 中的逻辑：
- ✅ 保持相同的数据格式（17字节数组）
- ✅ 保持相同的字节布局和位运算逻辑
- ✅ 保持相同的方向代码映射
- ✅ 保持相同的距离规则计算
- ✅ 保持相同的校验值计算

## 解决的问题

### 1. 熄屏问题
- **原问题**: Web端在熄屏后无法继续发送导航数据
- **解决方案**: Native端在后台运行，不受屏幕状态影响

### 2. 性能优化
- **原问题**: 数据需要从Native → Web → Native的传递链路
- **解决方案**: Native端直接处理，减少跨端数据传递

### 3. 状态管理
- **原问题**: 镜像状态在Web端管理，容易出现状态不一致
- **解决方案**: Native端统一管理镜像状态

## 使用方式

重构后的使用方式保持不变：
1. 用户在MapBoxPage中输入坐标启动导航
2. 系统弹出镜像确认对话框
3. 用户选择开启镜像后，导航数据自动发送到蓝牙
4. 导航结束时自动清空数据

## 兼容性

- ✅ 保持原有的事件监听器接口
- ✅ 保持原有的用户交互流程
- ✅ 保持原有的数据协议格式
- ✅ Web端代码更加简洁，易于维护

## 测试建议

1. 测试导航启动和数据发送
2. 测试镜像开启/关闭状态切换
3. 测试熄屏状态下的数据发送
4. 测试导航结束时的数据清理
5. 测试错误情况下的异常处理